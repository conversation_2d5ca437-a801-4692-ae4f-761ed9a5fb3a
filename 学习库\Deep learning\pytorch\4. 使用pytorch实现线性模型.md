---
tags:
  - excalidraw
  - 学习
  - deep_learning
  - pytorch
---

# 使用pytorch 实现线性模型

需要以下四个步骤

- 构建数据集
- 设计模型，用于计算预测值 $\hat{y}$
- 构建 [[1. 线性模型（Linear Model）#损失函数 Loss Function 与误差函数 Cost Function|损失函数]] 和 [[2. 梯度下降算法（Gradient Descent Algorithm）#小批量梯度下降（Mini-batch Gradient Descent）|优化器]]
- 训练：设定训练周期(epochs)，（[[3. 反向传播（Back Propagation）#正向传播（Forward Propagation）|前馈]] （包括计算预测值($\hat{y}$)，计算损失（loss）），[[3. 反向传播（Back Propagation）#反向传播（Backward Propagation）|反馈]]，更新权重）

在pytorch中，不需要手动的求导数，pytorch会自动的计算导数，重点应该放在计算图的构建上
![[4. 使用pytorch实现线性模型-2025-04-29-20-46-41.png]]

## 构建数据集

```python   
import torch
import torch.nn as nn
x_train = torch.tensor([[1.0], [2.0], [3.0], [4.0]])  # 训练数据，形状为(batch_size, 1)
y_train = torch.tensor([[2.0], [4.0], [6.0], [8.0]])  # 训练标签，形状为(batch_size, 1)
```

## 构造模型

![[4. 使用pytorch实现线性模型-2025-04-29-20-52-04.png]]
以后不管构造什么模型都是类似的写法，首先定义一个类，这个类继承自`nn.Module`，然后在`__init__`方法中定义模型的参数，最后在`forward`方法中定义模型的前向传播过程

```python
import torch
import torch.nn as nn

class LinearModel(nn.Module):
    def __init__(self):
        super(LinearModel, self).__init__()  # 调用父类的构造函数，写就完了必须要用的
        self.linear = nn.Linear(1, 1) # 构造一个对象，它是继承自nn.Module的，因此可以自动反向传播，包含两个参数，输入的维度和输出的维度，并且还有一个默认开启的偏置参数b

    def forward(self, x):
        # 前向传播过程
        # 这里的x是一个batch的数据，形状为(batch_size, 1)
        y_pred = self.linear(x)  # 前向传播
        return y_pred

# 实例化模型
model = LinearModel()
```

## 构造损失函数和优化器

损失函数和优化器都是pytorch中已经实现好的，直接调用就可以了

```python
import torch.optim as optim
import torch.nn.functional as F

# 损失函数
criterion = nn.MSELoss()  # 均方误差损失函数
# 优化器
optimizer = optim.SGD(model.parameters(), lr=0.01)  # 随机梯度下降优化器，lr是学习率
```

## 训练模型

训练模型的过程就是不断的前向传播，计算损失函数，反向传播，更新权重

```python   
# 训练模型
num_epochs = 1000  # 训练轮数
for epoch in range(num_epochs):
    # 前向传播
    y_pred = model(x_train)  # x_train是训练数据，形状为(batch_size, 1)
    
    # 计算损失函数
    loss = criterion(y_pred, y_train)  # y_train是训练标签，形状为(batch_size, 1)
    
    # 反向传播
    optimizer.zero_grad()  # 清空梯度
    loss.backward()  # 反向传播
    optimizer.step()  # 更新权重

    if (epoch+1) % 100 == 0:  # 每100轮打印一次损失函数
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')
```

## 测试模型

```python

# 打印权重和偏置
print(f'Weight: {model.linear.weight.item():.4f}, Bias: {model.linear.bias.item():.4f}')

# 测试模型
x_test = torch.tensor([[4.0]])  # 测试数据
y_test = model(x_test)  # 前向传播
print(f'Test data: {x_test.item()}, Predicted value: {y_test.item():.4f}')
```

**过拟合(overfitting)**：需要注意的是训练的轮数(epochs)，因为随着epochs的增加，训练集上的损失是会越来越小的，但是测试集有可能会先减小到一个值，然后又开始增大，这就是过拟合的现象
![[4. 使用pytorch实现线性模型-2025-04-29-21-37-16.png]] ^xy35lg


## 完整代码

```python
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

# 构建数据集
x_train = torch.tensor([[1.0], [2.0], [3.0], [4.0]])  # 训练数据，形状为(batch_size, 1)
y_train = torch.tensor([[2.0], [4.0], [6.0], [8.0]])  # 训练标签，形状为(batch_size, 1)

# 构造模型
class LinearModel(nn.Module):
    def __init__(self):
        super(LinearModel, self).__init__()  # 调用父类的构造函数，写就完了必须要用的
        self.linear = nn.Linear(1, 1) # 构造一个对象，它是继承自nn.Module的，因此可以自动反向传播，包含两个参数，输入的维度和输出的维度，并且还有一个默认开启的偏置参数b

    def forward(self, x):
        # 前向传播过程
        # 这里的x是一个batch的数据，形状为(batch_size, 1)
        y_pred = self.linear(x)  # 前向传播
        return y_pred

# 实例化模型
model = LinearModel()
# 损失函数
criterion = nn.MSELoss()  # 均方误差损失函数
# 优化器
optimizer = optim.SGD(model.parameters(), lr=0.01)  # 随机梯度下降优化器，lr是学习率

# 训练模型
num_epochs = 1000  # 训练轮数
for epoch in range(num_epochs):
    # 前向传播
    y_pred = model(x_train)  # x_train是训练数据，形状为(batch_size, 1)
    
    # 计算损失函数
    loss = criterion(y_pred, y_train)  # y_train是训练标签，形状为(batch_size, 1)
    
    # 反向传播
    optimizer.zero_grad()  # 清空梯度
    loss.backward()  # 反向传播
    optimizer.step()  # 更新权重

    if (epoch+1) % 100 == 0:  # 每100轮打印一次损失函数
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.4f}')
        
# 打印权重和偏置
print(f'Weight: {model.linear.weight.item():.4f}, Bias: {model.linear.bias.item():.4f}')
# 测试模型
x_test = torch.tensor([[4.0]])  # 测试数据
y_test = model(x_test)  # 前向传播
print(f'Test data: {x_test.item()}, Predicted value: {y_test.item():.4f}')
```


