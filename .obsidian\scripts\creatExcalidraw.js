module.exports = async (params) => {
    const { app, quickAddApi: { inputPrompt } } = params;

    // 获取当前活动文件
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
        new Notice("没有打开的文件");
        return;
    }

    // 获取当前文件的路径信息
    const currentFilePath = activeFile.path;
    const currentFileName = activeFile.basename; // 不包含扩展名的文件名
    const currentFileDir = activeFile.parent.path; // 当前文件所在目录

    // 创建excalidraw文件夹路径
    const excalidrawFolderPath = currentFileDir + "/excalidraw";

    // 检查excalidraw文件夹是否存在，如果不存在则创建
    const excalidrawFolder = app.vault.getAbstractFileByPath(excalidrawFolderPath);
    if (!excalidrawFolder) {
        try {
            await app.vault.createFolder(excalidrawFolderPath);
            new Notice(`已创建文件夹: ${excalidrawFolderPath}`);
        } catch (error) {
            new Notice(`创建文件夹失败: ${error.message}`);
            return;
        }
    }

    // 生成excalidraw文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const excalidrawFileName = `${currentFileName}_${timestamp}.excalidraw.md`;
    const excalidrawFilePath = `${excalidrawFolderPath}/${excalidrawFileName}`;

    // 创建excalidraw文件内容
    const excalidrawContent = `---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==


# Text Elements
# Embedded files
links: [[${currentFileName}]]

%%
# Drawing
\`\`\`json
{"type":"excalidraw","version":2,"source":"https://github.com/zsviczian/obsidian-excalidraw-plugin/releases/tag/1.9.19","elements":[],"appState":{"gridSize":null,"viewBackgroundColor":"#ffffff"}}
\`\`\`
%%`;

    try {
        // 创建excalidraw文件
        await app.vault.create(excalidrawFilePath, excalidrawContent);

        // 打开新创建的excalidraw文件
        const newFile = app.vault.getAbstractFileByPath(excalidrawFilePath);
        if (newFile) {
            await app.workspace.getLeaf(true).openFile(newFile);
            new Notice(`已创建并打开: ${excalidrawFileName}`);
        }

    } catch (error) {
        new Notice(`创建文件失败: ${error.message}`);
    }
};