{"components": [{"id": "d74392b3-4118-4c33-9f75-1dd958678f62", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-05-13T07:28:49.882Z", "updateAt": "2024-05-13T07:28:49.882Z", "components": [{"componentId": "988560c2-3b05-473a-b563-50945fd33a14", "layout": {"mobile": {"x": 0, "y": 15, "w": 4, "h": 11}, "laptop": {"x": 0, "y": 4, "w": 8, "h": 10}}}, {"componentId": "05b418a3-237e-4555-9098-03240fd80782", "layout": {"mobile": {"x": 2, "y": 10, "w": 1, "h": 5}, "laptop": {"x": 0, "y": 0, "w": 2, "h": 4}}}, {"componentId": "67b29695-3f9f-464f-a545-952a4a26e105", "layout": {"mobile": {"x": 2, "y": 0, "w": 1, "h": 5}, "laptop": {"x": 6, "y": 0, "w": 2, "h": 4}}}, {"componentId": "24a7d727-dfd7-45f6-8877-fbbc40345881", "layout": {"mobile": {"x": 3, "y": 0, "w": 1, "h": 5}, "laptop": {"x": 4, "y": 0, "w": 2, "h": 4}}}, {"componentId": "e8267710-5569-4696-860a-e6a9ee76c8de", "layout": {"mobile": {"x": 3, "y": 10, "w": 1, "h": 5}, "laptop": {"x": 2, "y": 0, "w": 2, "h": 4}}}, {"componentId": "0a8a61bb-03eb-4ff4-b8da-12a01e6b1308", "layout": {"mobile": {"x": 0, "y": 3, "w": 2, "h": 3}, "laptop": {"x": 8, "y": 0, "w": 4, "h": 14}}}, {"componentId": "50b04de4-afd4-4bea-8836-ce223e98c337", "layout": {"mobile": {"x": 0, "y": 0, "w": 4, "h": 4}, "laptop": {"x": 0, "y": 14, "w": 12, "h": 16}}}], "layoutType": "grid"}, {"id": "05b418a3-237e-4555-9098-03240fd80782", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-05-17T04:59:06.238Z", "updateAt": "2024-05-17T04:59:06.238Z", "contentPrefix": "", "contentSuffix": "篇", "countType": "number", "query": {"valueType": "totalRecords", "value": 100, "filter": {"id": "c5722316-fa93-49dd-b146-b7185b52<PERSON>da", "type": "group", "operator": "or", "conditions": [{"id": "296dac3a-0237-4a2a-9222-fb440e7c430e", "type": "group", "operator": "and", "conditions": [{"id": "7fcd0bb1-b278-4dfe-8925-f4172c2bee47", "type": "filter", "operator": "contains", "property": "tags", "value": "学习", "conditions": []}, {"id": "cad8fd60-1e9d-472b-806e-af06d1dd0dd2", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "template", "conditions": []}, {"id": "d5cdcb83-7efe-42a3-a4ae-b4feb6a8374d", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#linux", "conditions": []}]}]}, "sort": {"orders": []}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "7d36b5ef-143c-48ba-a788-5a6d4c57bcad", "type": "group", "operator": "and", "conditions": []}}, "title": "总计"}, {"id": "67b29695-3f9f-464f-a545-952a4a26e105", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-05-17T04:59:23.459Z", "updateAt": "2024-05-17T04:59:23.459Z", "contentPrefix": "", "contentSuffix": "篇", "countType": "number", "query": {"valueType": "totalRecords", "value": 100, "filter": {"id": "c5722316-fa93-49dd-b146-b7185b52<PERSON>da", "type": "group", "operator": "and", "conditions": [{"id": "4f0ee4cc-e6c3-4d46-9827-60f14bdf9b5b", "type": "filter", "operator": "contains", "property": "tags", "value": "linux", "conditions": []}, {"id": "a892f6ea-9953-471b-ab06-38e60cf72412", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "template", "conditions": []}, {"id": "359903d6-bdb5-47c0-989f-d81e51056f2c", "type": "filter", "operator": "time_after_or_equal", "property": "createTime", "value": {"type": "$startOfWeek", "unit": "day", "direction": "before", "value": ""}, "conditions": []}, {"id": "bd3d7d89-5f57-46ce-912b-ea063c2ddc4a", "type": "filter", "operator": "time_before_or_equal", "property": "createTime", "value": {"type": "$endOfWeek", "unit": "day", "direction": "before", "value": ""}, "conditions": []}]}, "sort": {"orders": []}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "7d36b5ef-143c-48ba-a788-5a6d4c57bcad", "type": "group", "operator": "and", "conditions": []}}, "title": "本周笔记"}, {"id": "24a7d727-dfd7-45f6-8877-fbbc40345881", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-05-17T05:08:33.822Z", "updateAt": "2024-05-17T05:08:33.822Z", "contentPrefix": "", "contentSuffix": "篇", "countType": "number", "query": {"valueType": "totalRecords", "value": 100, "filter": {"id": "c5722316-fa93-49dd-b146-b7185b52<PERSON>da", "type": "group", "operator": "and", "conditions": [{"id": "ef20cbeb-27ba-45ca-9763-4b76e8fa806c", "type": "filter", "operator": "contains", "property": "tags", "value": "linux", "conditions": []}, {"id": "0f2eefa5-89e5-43a6-84fa-a1191cf4c1dd", "type": "filter", "operator": "not_contains", "property": "${file.parent}", "value": "template", "conditions": []}, {"id": "b0595218-feb0-41ca-a618-221dfd9d3af3", "type": "filter", "operator": "time_after_or_equal", "property": "createTime", "value": {"type": "$startOfMonth", "unit": "day", "direction": "before", "value": ""}, "conditions": []}, {"id": "352f2edd-b9b5-4a4f-a5e3-46413c860fef", "type": "filter", "operator": "time_before_or_equal", "property": "createTime", "value": {"type": "$endOfMonth", "unit": "day", "direction": "before", "value": ""}, "conditions": []}]}, "sort": {"orders": []}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "7d36b5ef-143c-48ba-a788-5a6d4c57bcad", "type": "group", "operator": "and", "conditions": []}}, "title": "本月笔记"}, {"id": "988560c2-3b05-473a-b563-50945fd33a14", "type": "chart", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-12-27T05:57:54.921Z", "updateAt": "2024-12-27T05:57:54.921Z", "chartType": "line", "filter": {"id": "e6f7d84a-499d-48a6-b779-351efdb7bfd8", "type": "group", "operator": "and", "conditions": [{"id": "44b8903e-0e33-4598-bd39-9c1be1e49c3e", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#linux", "conditions": []}, {"id": "43f124aa-3aaa-44b1-b87f-d2b8e6e3dfd7", "type": "filter", "operator": "equals", "property": "${file.parent}", "value": "学习库/linux", "conditions": []}]}, "labelProperty": "${file.ctime}", "labelFormat": "$timeFormat", "valueProperty": "$file_count", "valuePrecision": 0, "sortField": "xAxisValue", "sortOrder": "asc", "maxHeight": 367, "chartColorSet": "default", "chartLabelPosition": "hidden", "showDataValue": true, "valueScaleStartFromZero": true, "smoothLine": true, "fillArea": true, "options": {"minValue": 0, "maxValue": 10, "cellSize": 17, "numberOfSegments": 2, "fullWidth": true, "showSplitLine": false, "hideChartLabel": false, "firstDayOfWeek": 1, "dateRange": {"type": "currentYear", "latestValue": 180, "latestUnit": "day"}}, "title": "今年的学习\n\n", "valueFormat": "$sum", "labelRotation": 32}, {"id": "e8267710-5569-4696-860a-e6a9ee76c8de", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-05-17T05:08:33.822Z", "updateAt": "2024-05-17T05:08:33.822Z", "contentPrefix": "", "contentSuffix": "", "countType": "number", "query": {"valueType": "propertyValue", "value": 100, "filter": {"id": "c5722316-fa93-49dd-b146-b7185b52<PERSON>da", "type": "group", "operator": "and", "conditions": [{"id": "4ce6ceee-adce-44a5-b001-22b752bf9af4", "type": "filter", "operator": "contains", "property": "tags", "value": "学习", "conditions": []}, {"id": "d44e0292-b030-48aa-a617-9242af0d2583", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#linux", "conditions": []}]}, "sort": {"orders": []}, "property": "${file.words}"}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "7d36b5ef-143c-48ba-a788-5a6d4c57bcad", "type": "group", "operator": "and", "conditions": []}}, "title": "总词数", "countValueAlign": "center"}, {"id": "0a8a61bb-03eb-4ff4-b8da-12a01e6b1308", "type": "card", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2024-12-27T12:14:48.052Z", "updateAt": "2024-12-27T12:14:48.052Z", "title": "", "description": "", "coverFit": "cover", "coverPosition": "left", "clickAction": {"type": "None", "id": "75e665e8-b597-40d3-9a76-746ad5f4629f", "options": {"commandId": "daily-notes", "commandName": "日记: 打开/创建今天的日记", "targetFolder": "工作库", "fileName": "{{date: YYMMDD}}", "openPageIn": "tab", "templateFilePath": "工作库/template/项目模板.md"}}, "cover": "att/picture/dog.gif"}, {"id": "4cecc04a-2f7f-4db7-bbd0-342784d21172", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "目录索引", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-08-04T00:56:14.657Z", "updateAt": "2025-08-04T00:56:14.657Z", "viewType": "gallary", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {}}], "templates": [], "groups": [{"id": "学习库/linux", "name": "学习库/linux", "items": ["学习库/linux/01-文件与目录操作.md", "学习库/linux/02-文件内容查看与处理.md", "学习库/linux/03-系统信息与进程管理.md", "学习库/linux/04-网络命令.md", "学习库/linux/05-权限与安全管理.md", "学习库/linux/06-Vim编辑器.md", "学习库/linux/07-软件包管理与系统维护.md", "学习库/linux/shell.md", "学习库/linux/wsl2.md", "学习库/linux/常用Linux命令.md", "学习库/linux/常用Linux命令_优化版.md"]}], "colorfulGroups": true, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-large", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "none", "source": "default", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "top"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": ["__$uncategorizedGroup", "学习库/linux"], "orders": [], "hiddens": ["__$uncategorizedGroup"], "collapseds": []}, "groupBy": "${file.parent}", "filter": {"id": "319f5244-1667-45a7-a81d-d47ffcc5cb24", "type": "group", "operator": "and", "conditions": [{"id": "a0bd4150-01fd-4206-a517-5c547f3db0f9", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#linux", "conditions": []}]}, "sort": {"orders": []}}, {"id": "50b04de4-afd4-4bea-8836-ce223e98c337", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "showBorder": true, "showShadow": false, "createAt": "2025-08-04T01:47:17.438Z", "updateAt": "2025-08-04T01:47:17.438Z", "components": [{"componentId": "4cecc04a-2f7f-4db7-bbd0-342784d21172"}, {"componentId": "b71f0703-e501-4971-acbc-1cf51e361f80"}], "layoutType": "tab", "locked": false, "layoutOptions": {}}, {"id": "b71f0703-e501-4971-acbc-1cf51e361f80", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "标签索引", "maxWidthRatio": -1, "showBorder": false, "showShadow": false, "createAt": "2025-08-04T01:49:19.617Z", "updateAt": "2025-08-04T01:49:19.617Z", "viewType": "table", "newPageNameFormat": "{{date:YYYYMMDDHHmmss}} ", "properties": [{"id": "__componentsTitleProperty_0x7c00", "name": "文件名", "type": "text", "isShow": true, "wrap": false, "options": {}}], "templates": [], "groups": [], "colorfulGroups": true, "viewOptions": {"openPageIn": "tab", "openPageAfterCreate": true, "itemSize": "components--page-card-medium", "showPropertyName": false, "showAllProperties": false, "items": [], "cover": {"type": "none", "source": "property", "fit": "contains", "layout": "components--page-card-cover-landscape", "position": "left", "sourceValue": "tags"}, "pinFiltersToMenuBar": false, "showGrid": false, "heightType": "auto", "heightValue": 600}, "groupStates": {"sort": "nameAsc", "format": "none", "statics": [], "orders": [], "hiddens": ["学习", "linux"], "collapseds": []}, "filter": {"id": "319f5244-1667-45a7-a81d-d47ffcc5cb24", "type": "group", "operator": "and", "conditions": [{"id": "23349232-7401-46ef-96f3-0338be75263f", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "#linux", "conditions": []}, {"id": "93a97a32-420f-4c99-8988-9c5de2f79672", "type": "filter", "operator": "equals", "property": "${file.parent}", "value": "学习库/linux", "conditions": []}]}, "groupBy": "tags"}], "rootComponentId": "d74392b3-4118-4c33-9f75-1dd958678f62"}