2025-04-13 08:28:21 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:28:21 [info] indexing created file components/logs/2025-04-13.components.log  [object Object] 
2025-04-13 08:28:21 [info] refresh page data from created listeners 0 799   
2025-04-13 08:28:21 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:28:21 [info] index finished after resolve  [object Object] 
2025-04-13 08:28:21 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:28:26 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:28:26 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:28:26 [info] index finished after resolve  [object Object] 
2025-04-13 08:28:26 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:28:29 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:28:29 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:28:29 [info] index finished after resolve  [object Object] 
2025-04-13 08:28:29 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:03 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:03 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:08 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:08 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:11 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:11 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:14 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:14 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:14 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:14 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:33 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:33 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:33 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:35 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:35 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:39 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:39 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:39 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:39 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:29:45 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:29:45 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:29:45 [info] index finished after resolve  [object Object] 
2025-04-13 08:29:45 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:23 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:23 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:23 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:23 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:26 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:26 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:26 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:26 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:28 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:28 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:31 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:31 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:31 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:31 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:34 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:34 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:37 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:37 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:37 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:37 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:41 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:41 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:43 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:43 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:43 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:43 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:46 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:46 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:46 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:46 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:48 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:48 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:30:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:30:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:30:51 [info] index finished after resolve  [object Object] 
2025-04-13 08:30:51 [info] refresh page data from resolve listeners 0 799   
2025-04-13 08:32:24 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-32-24.png  [object Object] 
2025-04-13 08:32:25 [info] refresh page data from created listeners 0 800   
2025-04-13 08:32:26 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:26 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:26 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:26 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:32:30 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:30 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:30 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:30 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:32:32 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:32 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:32 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:32 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:32:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:35 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:35 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:32:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:38 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:38 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:32:40 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:32:40 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:32:40 [info] index finished after resolve  [object Object] 
2025-04-13 08:32:40 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:22 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:22 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:22 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:22 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:24 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:24 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:24 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:24 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:27 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:27 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:27 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:27 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:30 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:30 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:30 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:30 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:32 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:32 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:32 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:32 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:37 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:37 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:37 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:37 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:42 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:42 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:42 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:42 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:45 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:45 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:45 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:45 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:48 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:48 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:50 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:50 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:50 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:50 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:33:56 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:33:56 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:33:56 [info] index finished after resolve  [object Object] 
2025-04-13 08:33:56 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:01 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:01 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:01 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:01 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:05 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:05 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:05 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:05 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:07 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:07 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:07 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:07 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:09 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:09 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:11 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:11 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:14 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:14 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:14 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:14 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:16 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:16 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:18 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:18 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:21 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:21 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:21 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:21 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:25 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:25 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:27 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:27 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:27 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:27 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:30 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:30 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:30 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:30 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:32 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:32 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:32 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:32 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:34 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:34 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:36 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:36 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:36 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:36 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:38 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:38 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:42 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:42 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:42 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:42 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:34:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:34:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:34:51 [info] index finished after resolve  [object Object] 
2025-04-13 08:34:51 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:02 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:02 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:05 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:05 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:05 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:05 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:07 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:07 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:07 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:07 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:09 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:09 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:12 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:12 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:14 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:14 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:14 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:14 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:17 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:17 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:17 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:17 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:19 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:19 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:20 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:20 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:28 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:28 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:35 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:35 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:37 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:37 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:37 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:37 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:39 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:39 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:39 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:39 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:42 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:42 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:42 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:42 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:45 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:45 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:45 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:45 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:48 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:48 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:50 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:50 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:50 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:50 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:52 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:52 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:52 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:52 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:35:56 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:35:56 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:35:56 [info] index finished after resolve  [object Object] 
2025-04-13 08:35:56 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:00 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:00 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:00 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:00 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:02 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:02 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:11 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:11 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:13 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:13 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:13 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:13 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:16 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:16 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:18 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:18 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:20 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:20 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:22 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:22 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:22 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:22 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:26 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:26 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:26 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:26 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:32 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:32 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:32 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:32 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:36:44 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:36:44 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:36:44 [info] index finished after resolve  [object Object] 
2025-04-13 08:36:44 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:25 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:25 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:28 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:28 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:34 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:34 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:39 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:39 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:40 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:40 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:44 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:44 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:44 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:44 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:46 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:46 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:46 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:46 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:49 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:49 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:49 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:49 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:51 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:51 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:55 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:55 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:55 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:55 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:37:58 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:37:58 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:37:58 [info] index finished after resolve  [object Object] 
2025-04-13 08:37:58 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:00 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:00 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:00 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:00 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:05 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:05 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:05 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:05 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:07 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:07 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:07 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:07 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:13 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:13 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:13 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:13 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:15 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:15 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:15 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:15 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:17 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:18 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:18 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:20 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:20 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:24 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:24 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:24 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:24 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:30 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:30 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:30 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:30 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:33 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:33 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:33 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:37 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:37 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:37 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:37 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:39 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:39 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:39 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:39 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:43 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:43 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:43 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:43 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:46 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:46 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:46 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:46 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:48 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:48 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:51 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:51 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:54 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:54 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:54 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:54 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:57 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:57 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:57 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:57 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:38:59 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:38:59 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:38:59 [info] index finished after resolve  [object Object] 
2025-04-13 08:38:59 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:02 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:02 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:06 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:06 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:12 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:12 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:16 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:16 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:19 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:19 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:19 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:19 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:35 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:35 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:38 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:38 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:39:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:39:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:39:41 [info] index finished after resolve  [object Object] 
2025-04-13 08:39:41 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:40:05 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:40:05 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:40:05 [info] index finished after resolve  [object Object] 
2025-04-13 08:40:05 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:49:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:49:33 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:49:33 [info] index finished after resolve  [object Object] 
2025-04-13 08:49:33 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:50:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:50:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:50:20 [info] index finished after resolve  [object Object] 
2025-04-13 08:50:20 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:50:31 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:50:31 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:50:31 [info] index finished after resolve  [object Object] 
2025-04-13 08:50:31 [info] refresh page data from resolve listeners 0 800   
2025-04-13 08:51:10 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-51-10.png  [object Object] 
2025-04-13 08:51:10 [info] refresh page data from created listeners 0 801   
2025-04-13 08:51:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:12 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:12 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:18 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:18 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:34 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:34 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:40 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:40 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:40 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:40 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:42 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:42 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:42 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:42 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:52 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:52 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:52 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:52 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:51:58 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:51:58 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:51:58 [info] index finished after resolve  [object Object] 
2025-04-13 08:51:58 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:00 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:00 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:00 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:00 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:16 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:16 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:19 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:19 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:19 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:25 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:25 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:28 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:28 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:34 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:34 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:36 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:36 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:36 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:36 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:38 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:38 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:52:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:52:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:52:51 [info] index finished after resolve  [object Object] 
2025-04-13 08:52:51 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:53:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:53:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:53:06 [info] index finished after resolve  [object Object] 
2025-04-13 08:53:06 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:53:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:53:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:53:10 [info] index finished after resolve  [object Object] 
2025-04-13 08:53:10 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:53:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:53:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:53:25 [info] index finished after resolve  [object Object] 
2025-04-13 08:53:25 [info] refresh page data from resolve listeners 0 801   
2025-04-13 08:53:29 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 08:53:29 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 08:53:29 [info] index finished after resolve  [object Object] 
2025-04-13 08:53:29 [info] refresh page data from resolve listeners 0 801   
2025-04-13 19:40:14 [info] components database created cost 6 ms   
2025-04-13 19:40:14 [info] components index initializing...   
2025-04-13 19:40:16 [info] start to batch put pages: 6   
2025-04-13 19:40:17 [info] batch persist cost 6  914 
2025-04-13 19:40:17 [info] components index initialized, 801 files cost 3020 ms   
2025-04-13 19:40:17 [info] refresh page data from init listeners 0 801   
2025-04-13 19:40:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-13 19:40:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-13 19:40:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-13 19:40:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-13 19:40:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-13 19:40:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-13 19:40:56 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 19:40:56 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 19:40:56 [info] index finished after resolve  [object Object] 
2025-04-13 19:40:56 [info] refresh page data from resolve listeners 0 801   
2025-04-13 19:40:58 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 19:40:58 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 19:40:58 [info] index finished after resolve  [object Object] 
2025-04-13 19:40:58 [info] refresh page data from resolve listeners 0 801   
2025-04-13 19:41:01 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 19:41:01 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 19:41:01 [info] index finished after resolve  [object Object] 
2025-04-13 19:41:01 [info] refresh page data from resolve listeners 0 801   
2025-04-13 19:41:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-13 19:41:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-13 19:41:03 [info] index finished after resolve  [object Object] 
2025-04-13 19:41:03 [info] refresh page data from resolve listeners 0 801   
