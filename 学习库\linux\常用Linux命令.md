---
tags:
  - 学习
  - ros
  - linux
---

![[Pasted image 20250317012600.jpg]]

# 文件/目录操作
| 命令      | 描述              | 常用选项/示例                                                      |
| ------- | --------------- | ------------------------------------------------------------ |
| `ls`    | 列出目录内容          | -l (详细信息), -a (包括隐藏文件), -h (人类可读), -t (按时间排序)                |
| `cd`    | 改变当前目录          | cd /path/to/directory, cd .., cd ~, cd -                     |
| `pwd`   | 显示当前工作目录        |                                                              |
| `mkdir` | 创建新目录           | mkdir new_directory                                          |
| `rmdir` | 删除空目录           | rmdir empty_directory                                        |
| `rm`    | 删除文件或目录         | rm file.txt, rm -r directory, rm -f, rm -i                   |
| `cp`    | 复制文件或目录         | cp source.txt destination.txt, cp -r source_dir dest_dir     |
| `mv`    | 移动或重命名文件/目录     | mv old_name.txt new_name.txt, mv file.txt /path/to/directory |
| `touch` | 创建空文件或更新文件时间戳   | touch new_file.txt                                           |
| `find`  | 查找文件            | find /path -name "filename.txt", find . -type f -mtime -7    |
| `ln`    | 创建硬链接或符号链接(软链接) | ln file.txt hardlink.txt,ln -s file.txt symlink.txt          |
# 文件内容查看
| 命令     | 描述               | 常用选项/示例                                     |     |
| ------ | ---------------- | ------------------------------------------- | --- |
| `cat`  | 连接并显示文件内容        | cat file.txt                                |     |
| `more` | 分页显示文件内容         | more long_file.txt                          |     |
| `less` | 分页显示文件内容 (可上下滚动) | less long_file.txt                          |     |
| `head` | 显示文件开头部分         | head file.txt, head -n 20 file.txt          |     |
| `tail` | 显示文件结尾部分         | tail file.txt, tail -n 20 file.txt, tail -f |     |
| `grep` | 在文件中搜索文本         | grep "pattern" file.txt, grep -i, grep -v   |     |
| `wc`   | 统计文件行数、字数和字节数。   | wc file.txt                                 |     |
# 文本处理
| 命令     | 描述                  | 常用选项/示例                                  |
| ------ | ------------------- | ---------------------------------------- |
| `sort` | 对文本行进行排序            | sort file.txt, sort -r file.txt, sort -n |
| `uniq` | 报告或忽略重复的行           | uniq file.txt, uniq -c file.txt          |
| `cut`  | 从每行中提取字段            | cut -d ',' -f 1 file.csv                 |
| `tr`   | 转换或删除字符             | tr 'a-z' 'A-Z' < file.txt                |
| `sed`  | 流编辑器，用于文本查找、替换等     | sed 's/old/new/g' file.txt, sed -i       |
| `awk`  | 强大的文本处理语言，用于数据提取和报告 | awk '{print $1, $3}' file.txt            |
|        |                     |                                          |
# 系统信息/进程
| 命令        | 描述            | 常用选项/示例                      |
| --------- | ------------- | ---------------------------- |
| `uname`   | 显示系统信息        | uname -a, uname -s, uname -r |
| `whoami`  | 显示当前用户名       |                              |
| `date`    | 显示或设置系统日期和时间  | date                         |
| `uptime`  | 显示系统运行时间和负载   |                              |
| `ps`      | 显示进程状态        | ps aux, ps -ef               |
| `top`     | 动态显示进程信息      |                              |
| `kill`    | 终止进程          | kill PID, kill -9 PID        |
| `df`      | 显示磁盘空间使用情况    | df -h                        |
| `du`      | 显示文件/目录磁盘使用情况 | du -sh directory             |
| `free`    | 显示内存使用情况      | free -h                      |
| `history` | 显示命令历史记录      | history                      |
# 网络
| 命令              | 描述          | 常用选项/示例                                                         |
| --------------- | ----------- | --------------------------------------------------------------- |
| `ping`          | 测试网络连接      | ping google.com                                                 |
| `ifconfig / ip` | 显示或配置网络接口   | ifconfig, ip addr                                               |
| `netstat / ss`  | 显示网络连接、路由表等 | netstat -tulnp, ss -tulnp                                       |
| `ssh`           | 安全远程登录      | ssh user@host                                                   |
| `scp`           | 安全远程文件复制    | scp user@host:/path/to/file ., scp file.txt user@host:/path/to/ |
| `wget`          | 下载文件        | wget https://example.com/file.zip                               |
| `curl`          | 数据传输工具      | curl https://example.com                                        |
# 权限
| 命令      | 描述                  | 常用选项/示例                                 |
| ------- | ------------------- | --------------------------------------- |
| `chmod` | 更改文件或目录的权限          | chmod 755 file.txt, chmod u+x script.sh |
| `chown` | 更改文件或目录的所有者和组       | chown user:group file.txt               |
| `sudo`  | 以超级用户 (root) 权限执行命令 | sudo command                            |
# 压缩归档
| 命令       | 描述                  | 常用选项/示例                                                    |
| -------- | ------------------- | ---------------------------------------------------------- |
| `tar`    | 创建或提取 tar 归档文件      | tar -cvf archive.tar file1 file2, tar -xvf archive.tar     |
| `gzip`   | 压缩文件                | gzip file.txt                                              |
| `gunzip` | 解压缩文件               | gunzip file.txt.gz                                         |
| `zip`    | 创建 zip 压缩文件         | zip archive.zip file1 file2                                |
| `unzip`  | 解压缩 zip 文件，解压到指定文件夹 | unzip archive.zip, `unzip file.zip -d /home/<USER>/docs`<br> |
# 其他
| 命令      | 描述       |
| ------- | -------- |
| `man`   | 显示命令的手册页 |
| `clear` | 清除终端屏幕   |
| `echo`  | 打印文本     |
| `which` | 查找命令路径   |
| `alias` | 创建命令别名   |

# 换源

先备份
```shell
sudo cp /etc/apt/sources.list /etc/apt/sources.list.bak
```

再编辑
```shell
sudo vim /etc/apt/sources.list
```
> vim 中的全选：按 ESC 确保处于普通模式，输入 gg，将会跳转至第一行，按 V 处于可视化模式，可以选中所有内容后按 d 删除，或者按 y 复制

阿里源，这里是 ubuntu 20.04，也就是 focal
```shell
deb https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-proposed main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-proposed main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
```

更新
```shell
sudo apt-get update
sudo apt-get upgrade
```

# vim
| 命令                          | 描述                             |     |
| --------------------------- | ------------------------------ | --- |
|                             |                                |     |
| h                           | 向左移动一个字符                       |     |
| j                           | 向下移动一行                         |     |
| k                           | 向上移动一行                         |     |
| l                           | 向右移动一个字符                       |     |
| w                           | 移动到下一个单词的开头（标点符号被视为单词的一部分）     |     |
| W                           | 移动到下一个以空格分隔的“单词”的开头            |     |
| b                           | 移动到上一个单词的开头（标点符号被视为单词的一部分）     |     |
| B                           | 移动到上一个以空格分隔的“单词”的开头            |     |
| e                           | 移动到下一个单词的末尾（标点符号被视为单词的一部分）     |     |
| E                           | 移动到下一个以空格分隔的“单词”的末尾            |     |
| ge                          | 移动到上一个单词的末尾（标点符号被视为单词的一部分）     |     |
| gE                          | 移动到上一个以空格分隔的“单词”的末尾            |     |
| 0                           | 移动到当前行的开头                      |     |
| ^                           | 移动到当前行的第一个非空白字符                |     |
| $                           | 移动到当前行的末尾                      |     |
| g_                          | 移动到当前行的最后一个非空白字符               |     |
| gg 或 1G                     | 移动到文档的第一行                      |     |
| G                           | 移动到文档的最后一行                     |     |
| :n 或 nG                     | 移动到第n行                         |     |
| }                           | 向前移动一个段落（到下一个空行）               |     |
| {                           | 向后移动一个段落（到上一个空行）               |     |
| H                           | 移动到屏幕顶部                        |     |
| M                           | 移动到屏幕中间                        |     |
| L                           | 移动到屏幕底部                        |     |
| Ctrl+f                      | 向下翻一页                          |     |
| Ctrl+b                      | 向上翻一页                          |     |
| Ctrl+d                      | 向下移动半页                         |     |
| Ctrl+u                      | 向上移动半页                         |     |
| zz                          | 将当前行置于屏幕中央                     |     |
| zt                          | 将当前行置于屏幕顶部                     |     |
| zb                          | 将当前行置于屏幕底部                     |     |
| %                           | 移动到匹配的括号（包括圆括号、方括号和大括号）        |     |
| f<char>                     | 跳转到当前行中下一个出现的字符x               |     |
| t<char>                     | 跳转到当前行中下一个出现的字符x之前             |     |
| F<char>                     | 跳转到当前行中上一个出现的字符x               |     |
| T<char>                     | 跳转到当前行中上一个出现的字符x之后             |     |
| ;                           | 重复上一个f/t/F/T命令                 |     |
| ,                           | 以相反方向重复上一个f/t/F/T命令            |     |
| i                           | 在光标位置之前插入文本                    |     |
| I                           | 在当前行的开头插入文本                    |     |
| a                           | 在光标位置之后附加文本                    |     |
| A                           | 在当前行的末尾附加文本                    |     |
| o                           | 在光标下方打开一个新行并进入插入模式             |     |
| O                           | 在光标上方打开一个新行并进入插入模式             |     |
| s                           | 替换光标下的字符并进入插入模式                |     |
| S                           | 替换整行并进入插入模式                    |     |
| ea                          | 在当前单词的末尾进入插入模式                 |     |
| x 或 dl                      | 删除光标所在的字符                      |     |
| X                           | 删除光标左侧的字符                      |     |
| dw                          | 删除从光标位置开始的单词（包括光标位置的字符，直到遇到符号） |     |
| dW                          | 删除从光标位置开始的单词（包括光标位置的字符，直到遇到空格） |     |
| db                          | 删除到上一个单词的开头（直到遇到符号）            |     |
| dB                          | 向后删除一个单词（直到遇到空格）               |     |
| dd                          | 删除光标所在的整行                      |     |
| D 或 d$                      | 删除从光标位置开始到行尾的内容                |     |
| d^                          | 删除从光标位置开始到行首的内容                |     |
| dG                          | 删除从当前行到文件末尾的所有内容               |     |
| dnj (例如 d5j)                | 删除当前行和接下来的n行                   |     |
| dnk (例如 d5k)                | 删除当前行和之前的n行                    |     |
| yy 或 Y                      | 复制当前行                          |     |
| yw                          | 复制一个单词                         |     |
| ynw (例如 y2w)                | 复制n个单词                         |     |
| y$ 或 YG                     | 从当前光标位置复制到行尾                   |     |
| p                           | 在光标之后（或下方）粘贴                   |     |
| P                           | 在光标之前（或上方）粘贴                   |     |
| ddp                         | 交换当前行和下一行                      |     |
| "*p 或 "+p                   | 从系统剪贴板粘贴                       |     |
| "*y 或 "+y                   | 复制到系统剪贴板                       |     |
| :w                          | 写入（保存）文件                       |     |
| :w!                         | 强制写入（保存）文件                     |     |
| :wq 或 :x 或 ZZ               | 写入（保存）并退出                      |     |
| :q                          | 退出（如果有未保存的更改则失败）               |     |
| :q!                         | 退出并丢弃未保存的更改（强制退出）              |     |
| :wqa                        | 写入（保存）并退出所有标签页                 |     |
| :qa                         | 退出所有打开的缓冲区                     |     |
| /pattern                    | 在文件中向前搜索                       |     |
| ?pattern                    | 在文件中向后搜索                       |     |
| n                           | 重复在同一方向的搜索                     |     |
| N                           | 重复在相反方向的搜索                     |     |
| :%s/old/new/g               | 替换文件中所有出现的“old”为“new”          |     |
| :%s/old/new/gc              | 替换文件中所有出现的“old”为“new”，并进行确认    |     |
| :s/old/new/g                | 替换当前行中所有出现的“old”为“new”         |     |
| :sp 或 :split                | 水平分割窗口                         |     |
| :sp <filename>              | 水平分割窗口并打开另一个文件                 |     |
| :vs 或 :vsplit               | 垂直分割窗口                         |     |
| :vs <filename>              | 垂直分割窗口并打开另一个文件                 |     |
| Ctrl+w down-arrow 或 Ctrl+wj | 将焦点切换到下面的子窗口                   |     |
| Ctrl+w up-arrow 或 Ctrl+wk   | 将焦点切换到上面的子窗口                   |     |
| Ctrl+ww                     | 在窗口之间切换                        |     |
| Ctrl+wh                     | 将光标移动到左侧窗口（垂直分割）               |     |
| Ctrl+wl                     | 将光标移动到右侧窗口（垂直分割）               |     |
| :q 或 Ctrl+wq                | 退出当前窗口                         |     |
| u                           | 撤销上一次编辑                        |     |
| Ctrl+r                      | 重做                             |     |
| .                           | 重复上一个命令                        |     |
| J                           | 将下一行连接到当前行                     |     |
| gJ                          | 将下一行连接到当前行，不添加空格               |     |
| cc 或 S                      | 更改（替换）整行                       |     |
| cw                          | 更改单词（执行dw，然后进入插入模式）            |     |
| C 或 c$                      | 更改（替换）到行尾                      |     |
| r                           | 替换单个字符                         |     |
| R                           | 进入替换模式                         |     |
| >>                          | 右缩进当前行                         |     |
| <<                          | 左缩进当前行                         |     |
| v (后跟动作)                    | 启动可视模式（字符选择）                   |     |
| V (后跟动作)                    | 启动可视模式（行选择）                    |     |
| Ctrl+v (后跟动作)               | 启动可视块模式                        |     |
| y (在可视模式下)                  | 复制选定的文本                        |     |
| d (在可视模式下)                  | 删除选定的文本                        |     |