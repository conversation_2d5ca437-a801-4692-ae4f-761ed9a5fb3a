---
tags:
  - 学习
  - deep_learning
  - Artifical_Intelligence
---
# 评价指标

用于评估模型的效果

---

## 混淆矩阵
混淆矩阵就是统计分类模型的分类结果，即统计归对类和归错类的样本个数，将这些结果放在一个表格里展示。 ^5uu670
### 二分类混淆矩阵
对于一个二分类的混淆矩阵，如下所示：
![[Pasted image 20240729155300.png]]
- TP（True Positive）：真正例，模型预测为正例，实际是正例
- FP（False Positive）：假正例，模型预测为正例，实际是反例 
- FN（False Negative）：假反例，模型预测为反例，实际是正例 
- TN（True Negative）：真反例，模型预测为反例，实际是反例
第二个字母是预测值，先把第二个字母写上，再根据真实值写上第一个字母  

```ad-attention
title: 分析要点
对角全对，横为真实，竖为预测
1. **对角线上都是正确的**（预测为真，实际为真 or 预测为假，实际为假）；非对角线都是错误的
2. 矩阵中的每一行进行求和，得到的是这个类别中的真实数量
3. 矩阵中的每一列进行求和，得到的是这个类别中的模型预测数量
```

### 推导公式 

```ad-thm
title: 准确率（Accuracy）
color: 198,155,64
对角线计算，**预测正确的数量占所有预测结果的比例**

$$Accuracy = \frac{TP+TN}{TP+TN+FP+FN}$$

- **适用场景**: 数据集中的各个类别分布均衡时，这是一个很好的宏观指标。
- **注意**：如果样本中不同类别的样本严重失衡时，占比大的会影响准确率（例如：某个数据集中正例的数量是1000，负例的数量是10，正例占了99%的比例，用这个数据集去训练模型会得到很高的分数）
```

^m0odtj
```ad-thm
title: 精确率（Precision）
color: 198,155,64
> 宁缺毋滥，求的是“预测的准不准”。（别冤枉好人）

竖着计算，在所有**被预测为类别1**的样本中，**实际上也属于类别1**的样本所占的比例。也就是**“在模型认为是类别1的所有结果中，有多少是预测正确的？”**

$$Precision_{1} = \frac{TP}{TP+FP}$$
$$Precision_{2} = \frac{TN}{TN+FN}$$

- **例子**：垃圾邮件检测。将一封重要邮件错误地标记为垃圾邮件 (FP) 的后果，比漏掉一封垃圾邮件 (FN) 更严重。
```

^9954d7

^4bzs30
```ad-thm
title: 召回率（Recall）
color: 198,155,64
> 宁滥勿缺，求的是“找的全不全”。（别放过坏人）

横着计算，在所有**实际上是类别1**的样本中，被模型成功找出来的比例。也就是**“所有真正的类别1样本中，有多少被模型找到了？”**
$$Recall_{1} = \frac{TP}{TP+FN}$$
$$Recall_{2} = \frac{TN}{TN+FP}$$
- **例子**：癌症诊断。漏诊一个癌症病例 (FN) 的后果，远比将健康人误诊为癌症 (FP) 更严重。
```

^bzipds

### 举一反三

````ad-faq
title: 二分类例题
collapse: open
![[Pasted image 20240729162504.png|665x249]]

```ad-example
title: 该宠物店总共有多少只动物，几只猫？几只狗？
collapse: open
横着表示真实值，
狗的真实数量等于5+1=6；猫的真实值数量等于0+4=4
总数可得6+4=10
```

```ad-example
title: 计算 Acc，并分别计算猫和狗的P 和 R
collapse: open
- ACC是斜着计算，如下所示
$$
Accuracy=\frac{TP+FN}{TP+TN+FP+FN}=\frac{5+4}{5+0+1+4}=90 \%
$$
- P是竖着计算，如下所示
$$
Precision_{狗}=\frac{TP}{TP+FP}=\frac{5}{5+0}=100\%
$$
$$
Precision_{猫}=\frac{FN}{FN+TN}=\frac{4}{1+4}=25\%
$$
- R是横着计算，如下所示
$$
Recall_{狗}=\frac{TP}{TP+TN}=\frac{5}{5+1}=83.3\%
$$
$$
Recall_{猫}=\frac{FN}{FN+FP}=\frac{4}{4+0}=100\%
$$

```

````

````ad-faq
title: 三分类例题
collapse: open
![[Pasted image 20240729165304.png|659x268]]

```ad-example
title: 该宠物店总共有多少只动物，几只猫？几只狗？几只猪？
collapse: open
横着表示真实值，
狗的真实数量等于1+15+6=22；猫的真实值数量等于10+3+5=18；猪的真实数量等于2+4+20=26
总数可得6+4=10
```

```ad-example
title: 计算 Acc，并分别计算猫，狗，和猪的P 和 R
collapse: open
- ACC是斜着计算，如下所示
$$
Accuracy=\frac{10+15+20}{10+3+5+1+15+6+2+4+20}=68.2 \%
$$
在这里我们可以把这个三分类混淆矩阵改成二分类混淆矩阵
![[Pasted image 20240729170234.png]]

- P是竖着计算，如下所示
$$
Precision_{猫}=\frac{TP}{TP+FP}=\frac{10}{10+3}=76.9\%
$$
$$
Precision_{狗}=\frac{TP}{TP+FP}=\frac{15}{15+7}=68.2\%
$$
$$
Precision_{猪}=\frac{TP}{TP+FP}=\frac{20}{20+11}=64.5\%
$$
- R是横着计算，如下所示
$$
Recall_{猫}=\frac{TP}{TP+TN}=\frac{10}{10+8}=55.6\%
$$
$$
Recall_{狗}=\frac{TP}{TP+TN}=\frac{15}{15+7}=68.2\%
$$
$$
Recall_{猪}=\frac{TP}{TP+TN}=\frac{20}{20+6}=74.9\%
$$
```

````

 
## 常见的评价指标

### PA (Pixel Accuracy)：像素精确率

```ad-thm
title: PA (Pixel Accuracy)：像素精确率
color: 198,155,64

$$
PA=\frac{\left ( TP+TN \right ) }{\left ( TP+TN+FP+FN \right ) } 
$$

- **含义**：预测类别正确的像素点数占总像素的比例，在混淆矩阵中相当于：对角线元素之和➗矩阵所有元素之和（[[评价指标#^m0odtj|准确率, Accuracy]]）


```

```ad-thm
title: CPA (Class Pixel Accuracy)：类别像素精确率
color: 198,155,64
类别1：
$$
CPA_{1}=\frac{\left ( TP_{1} \right ) }{\left ( TP_{1}+FP_{1} \right ) } 
$$
类别2：
$$
CPA_{2}=\frac{\left ( TP_{2} \right ) }{\left ( TP_{2}+FP_{2} \right ) } 
$$
类别3：
$$
CPA_{3}=\frac{\left ( TP_{3} \right ) }{\left ( TP_{3}+FP_{3} \right ) } 
$$

- 含义：在类别 i 的预测值中，真实属于 i 类的像素准确率，换言之：模型对类别 i 的预测值有很多，其中有对有错，预测对的值占预测总值的比例，对应于（[[评价指标#^4bzs30|精确率，Precision]]）

```

```ad-thm
title: MPA (Mean Pixel Accuracy)：类别平均像素准确率
color: 198,155,64

$$
MPA=\frac{1}{k+1}\sum_{k}^{0}\frac{TP}{TP+FP}  
$$

- 含义：预测类别正确的像素点数占总像素的比例
在混淆矩阵中相当于：对角线元素之和➗矩阵所有元素之和
这里是二分类任务


```

### IoU：交并比

```ad-example
title: IoU
collapse: open

![[Pasted image 20240729153712.png]]
$$
IoU=\frac{TP}{TP+FP+FN}  
$$
这里的IoU衡量的是模型预测的正例（TP）,占所有实际正例（包括真正例，假负例，真负例）的比值
- 含义：预测结果与真实值的交集和并集的比值 
- TP（True Positive）：
       - 橙色，TP = A ∩ B
       - 预测正确，真正例，模型预测为正例，实际是正例（模型预测为类别1，实际是类别1）
- FP（False Positive）：
       - 黄色，FP = B - (A ∩ B)
       - 预测错误，假正例，模型预测为正例，实际是反例 （模型预测为类别1，实际是类别2）
- FN（False Negative）：
       - 红色，FN = A - (A ∩ B)
       - 预测错误，假反例，模型预测为反例，实际是正例 （模型预测为类别2，实际是类别1）
- TN（True Negative）：
       - 白色，TN = ~(A ∪ B)
       - 预测正确，真反例，模型预测为反例，实际是反例 （模型预测为类别2，实际是类别2

```

```ad-hibox

```
```ad-example
title: MIoU
collapse: open
$$
MIoU=\frac{IoU_{正类}+IoU_{负类}}{2}=\frac{\frac{TP}{TP+FN+FP}+\frac{TN}{TN+FN+FP}}{2}=\frac{1}{K} \sum_{K}^{i=1} \frac{A_{K}\cap B_{K}}{A_{K}\cup B_{K}}  
$$

- 含义：将模型对**每一类**预测结果和真实值之间的交集与并集的比值进行求和取平均值


```

````ad-faq
title: 计算各个类别的IoU
collapse: open
![[Pasted image 20240730002523.png]]
```ad-col2
title: 可以将多类别的混淆矩阵改成 2 分类的混淆矩阵
collapse: open
color: 78,15,6

![[Pasted image 20240730092903.png|350x512]]


$$
IoU_{0}=\frac{TP_{0}}{TP_{0}+FP_{0}+FN_{0}}=\frac{16}{16+4+2}=0.73
$$
$$
IoU_{1}=\frac{TP_{1}}{TP_{1}+FP_{1}+FN_{1}}=\frac{3}{3+1+1}=0.6
$$
$$
IoU_{2}=\frac{TP_{2}}{TP_{2}+FP_{2}+FN_{2}}=\frac{16}{16+0+2}=0.89
$$
<center>.....</center>
```
````
### Dice 系数
```ad-thm
title: Dice 系数 (Dice Coefficient)
color: 198,155,64
![[Pasted image 20240806155210.png|580x305]]

$$
Dice=\frac{2×\left | {A}\cap{B} \right | }{\left| A \right |+\left| B \right | }=\frac{2TP}{2TP+FN+FP}
$$

- **定义**: Dice 系数衡量的是两个集合的相似度。在分割任务中，它是预测区域（A）与真实区域（B）交集面积的2倍，除以两个区域的总面积（像素总和），Dice的范围在0到1。
- **优点**:
    
    - 与F1-Score在数学形式上是等价的，因此很好地平衡了精确率和召回率。
        
    - 对正样本（即要分割的目标）的关注度更高。
        
- **缺点**:
    
    - 与IoU类似，对小物体的评估也比较敏感。
```


### AP (Average Precision) / 平均精度

```ad-thm
title: AP (Average Precision) / 平均精度
color: 198,155,64

![[评价指标-2025-07-12-11-00-15.png]]

- **定义**: AP 是**精确率-召回率曲线 (Precision-Recall Curve)** 下的面积。它衡量的是模型在单个类别上的检测性能。
    
- **计算过程**:
    
    1. 模型输出一系列检测框，每个框都有一个置信度分数。
        
    2. 通过不断改变置信度阈值，可以得到一系列的精确率和召回率（通常IoU大于某个值如0.5才算TP）。
        
    3. 将这些([[评价指标#^bzipds|召回率]], [[评价指标#^4bzs30|精确率]])的点连接起来，就构成了P-R曲线。
        
    4. AP就是这条曲线下的面积，综合评估了模型在所有召回率水平下的精确率表现。
        
- **适用场景**:
    
    - 评估模型在**单个物体类别**（如“猫”或“狗”）上的检测好坏。AP越高，说明模型在该类别上又准又全。
```
```ad-thm
title: mAP (mean Average Precision) / 平均精度均值
color: 198,155,64
- **定义**: mAP 就是对数据集中**所有类别**的 AP 值求平均。
    
- **公式**: $mAP = (Σ AP_i) / N$，其中$AP_i$是第$i$个类别的AP，$N$是总类别数。
    
- **适用场景**:
    
    - **目标检测任务中最核心、最通用的评价指标**。它全面地衡量了模型在所有类别上的整体性能。
        
    - 当你在看PASCAL VOC、COCO等知名数据集的排行榜时，mAP是评估模型好坏的黄金标准。
        
- **变体**:
    
    - **mAP\@0.5**: 表示在计算AP时，判断TP的IoU阈值固定为0.5。
        
    - **mAP\@.5:.95 (COCO-style)**: 这是COCO数据集采用的更严格的指标，它计算了从0.5到0.95（步长0.05）共10个IoU阈值下的mAP，然后将这10个mAP值再取平均。这要求模型不仅要检测到物体，还要定位得非常精准。
```
### F1-Score
```ad-thm
title: F1 分数 (F1-Score)
color: 198,155,64

![[评价指标-2025-07-12-11-13-53.png]]

- **核心思想**：旨在**同时兼顾模型的[[评价指标#^bzipds|召回率]]和[[评价指标#^4bzs30|精确率]]**，并在这两者之间找到一个平衡点。它被定义为精确率和召回率的**调和平均数**。
- **公式**：$2 * (Precision * Recall) / (Precision + Recall)$
- **适用场景**：
	- **类别不平衡的数据集**: 这是F1-Score最主要的应用场景。例如，在信用卡欺诈检测中，99.9%的交易是正常的，只有0.1%是欺诈。此时，一个只预测“正常”的模型[[评价指标#^m0odtj|Accuracy]]）高达99.9%，但它毫无用处。而F1-Score因为同时关注召回率（找出欺诈交易），会给这个模型一个极低的分数。
	- **当精确率和召回率同等重要时**: 当你既不希望漏掉太多正样本，也不希望把太多负样本误判为正样本时。
		- **例子**: 在产品缺陷检测中，你希望尽可能多地找出所有有缺陷的产品（[[评价指标#^bzipds|recall]]），同时也希望不要把太多合格产品误判为次品而增加成本（[[评价指标#^9954d7|precision]]）。
```

###  ROC 曲线和 AUC
```ad-thm
title: ROC和AUC
color: 198,155,64

![[评价指标-2025-07-12-11-16-24.png]]

**ROC (Receiver Operating Characteristic)** 曲线是一个可视化工具，它展示了分类器在**所有可能的分类阈值下**的性能表现。它描绘了两个关键指标之间的权衡关系：

- **Y轴：真正例率 (True Positive Rate, TPR)**
    
    - 它和**召回率 (Recall)** 是同一个东西。
        
    - $TPR = TP / (TP + FN)$
        
    - 代表模型能将正样本成功预测出来的能力。
        
- **X轴：假正例率 (False Positive Rate, FPR)**
    
    - $FPR = FP / (FP + TN)$
        
    - 代表模型将负样本错误地预测为正样本的比例。

一个理想的模型，其ROC曲线会尽可能地向左上角靠近，这意味着在保持很低的假正例率（成本）的同时，能获得很高的真正例率（收益）。

**AUC** 指的是 **ROC 曲线下的面积**。它将整个曲线的性能量化为一个单一的数值。

- **取值范围**: 0 到 1。
    
- **解读**:
    
    - **AUC = 1**: 完美的分类器。
        
    - **AUC = 0.5**: 模型性能等同于随机猜测（ROC曲线是一条对角线）。
        
    - **AUC > 0.5**: 模型具有预测价值，AUC越大，性能越好。
        
    - **AUC < 0.5**: 模型比随机猜测还差（但只需将其预测结果反转，就能得到一个AUC > 0.5的模型）。
        

AUC有一个非常直观的概率解释：它等于**从正样本中随机抽取一个样本，其预测分数大于从负样本中随机抽取一个样本的预测分数的概率**。
```

