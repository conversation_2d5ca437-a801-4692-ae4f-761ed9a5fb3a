2025-06-17 10:34:19.413 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-06-17 10:34:19.413 [info] components database created cost 1 ms   
2025-06-17 10:34:19.414 [info] components index initializing...   
2025-06-17 10:34:19.663 [info] start to batch put pages: 5   
2025-06-17 10:34:19.689 [info] batch persist cost 5  26 
2025-06-17 10:34:19.731 [info] components index initialized, 907 files cost 319 ms   
2025-06-17 10:34:19.732 [info] refresh page data from init listeners 0 907   
2025-06-17 10:34:21.322 [info] indexing created file components/logs/2025-06-17.components.log  [object Object] 
2025-06-17 10:34:21.331 [info] refresh page data from created listeners 0 908   
2025-06-17 10:34:21.547 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 10:34:21.899 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 10:34:22.330 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-17 10:34:22.334 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-17 10:34:22.337 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-17 10:34:22.353 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-17 10:45:06.473 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 10:45:06.594 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 10:45:06.607 [info] index finished after resolve  [object Object] 
2025-06-17 10:45:06.608 [info] refresh page data from resolve listeners 0 908   
2025-06-17 10:45:32.034 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md  [object Object] 
2025-06-17 10:45:32.034 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:45:32.041 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:45:32.042 [info] index finished after resolve  [object Object] 
2025-06-17 10:45:32.044 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:45:32.413 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 10:45:32.817 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-17 10:45:32.823 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-17 10:45:32.827 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-17 10:45:32.831 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-17 10:45:50.176 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:45:50.241 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:45:50.242 [info] index finished after resolve  [object Object] 
2025-06-17 10:45:50.243 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:45:55.534 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:45:55.540 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:45:55.541 [info] index finished after resolve  [object Object] 
2025-06-17 10:45:55.542 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:45:58.197 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:45:58.201 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:45:58.202 [info] index finished after resolve  [object Object] 
2025-06-17 10:45:58.204 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:46:00.067 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:46:00.074 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:46:00.076 [info] index finished after resolve  [object Object] 
2025-06-17 10:46:00.077 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:46:15.616 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:46:15.620 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:46:15.621 [info] index finished after resolve  [object Object] 
2025-06-17 10:46:15.622 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:46:23.047 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:46:23.052 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:46:23.053 [info] index finished after resolve  [object Object] 
2025-06-17 10:46:23.054 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:46:38.662 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:46:38.666 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:46:38.667 [info] index finished after resolve  [object Object] 
2025-06-17 10:46:38.668 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:46:46.550 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:46:46.559 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:46:46.561 [info] index finished after resolve  [object Object] 
2025-06-17 10:46:46.562 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:47:17.481 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:47:17.485 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:47:17.486 [info] index finished after resolve  [object Object] 
2025-06-17 10:47:17.487 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:47:34.750 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:47:34.754 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:47:34.755 [info] index finished after resolve  [object Object] 
2025-06-17 10:47:34.756 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:47:50.534 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:47:50.538 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:47:50.539 [info] index finished after resolve  [object Object] 
2025-06-17 10:47:50.540 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:48:07.564 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:48:07.569 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:48:07.571 [info] index finished after resolve  [object Object] 
2025-06-17 10:48:07.572 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:48:26.835 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:48:26.840 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:48:26.843 [info] index finished after resolve  [object Object] 
2025-06-17 10:48:26.845 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:48:31.285 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:48:31.290 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:48:31.291 [info] index finished after resolve  [object Object] 
2025-06-17 10:48:31.292 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:48:48.131 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:48:48.135 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:48:48.137 [info] index finished after resolve  [object Object] 
2025-06-17 10:48:48.138 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:49:03.221 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:49:03.227 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:49:03.229 [info] index finished after resolve  [object Object] 
2025-06-17 10:49:03.230 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:49:19.987 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:49:19.990 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:49:19.993 [info] index finished after resolve  [object Object] 
2025-06-17 10:49:19.994 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:49:36.720 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:49:36.724 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:49:36.727 [info] index finished after resolve  [object Object] 
2025-06-17 10:49:36.728 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:50:03.798 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:50:03.803 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:50:03.805 [info] index finished after resolve  [object Object] 
2025-06-17 10:50:03.806 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:50:19.359 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:50:19.363 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:50:19.366 [info] index finished after resolve  [object Object] 
2025-06-17 10:50:19.367 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:50:20.635 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:50:20.640 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:50:20.642 [info] index finished after resolve  [object Object] 
2025-06-17 10:50:20.643 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:50:36.432 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:50:36.437 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:50:36.440 [info] index finished after resolve  [object Object] 
2025-06-17 10:50:36.441 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:58:57.883 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:58:57.892 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:58:57.895 [info] index finished after resolve  [object Object] 
2025-06-17 10:58:57.896 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:59:09.459 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-17 10:59:09.465 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-17 10:59:09.468 [info] index finished after resolve  [object Object] 
2025-06-17 10:59:09.469 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:59:46.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 10:59:46.068 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 10:59:46.069 [info] index finished after resolve  [object Object] 
2025-06-17 10:59:46.070 [info] refresh page data from resolve listeners 0 909   
2025-06-17 10:59:46.900 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 10:59:47.198 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-17 10:59:47.203 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-17 10:59:47.210 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-17 10:59:47.213 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-17 10:59:59.074 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 10:59:59.083 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 10:59:59.084 [info] index finished after resolve  [object Object] 
2025-06-17 10:59:59.085 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:00:01.705 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 11:00:01.805 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-17 11:00:01.809 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-17 11:00:01.812 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-17 11:00:01.815 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-17 11:00:01.827 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:00:01.839 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:00:01.839 [info] index finished after resolve  [object Object] 
2025-06-17 11:00:01.841 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:00:02.613 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-17 11:00:02.637 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-17 11:00:02.640 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-17 11:00:02.645 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-17 11:00:02.648 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-17 11:00:42.750 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:00:42.819 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:00:42.823 [info] index finished after resolve  [object Object] 
2025-06-17 11:00:42.824 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:00:51.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:00:51.067 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:00:51.068 [info] index finished after resolve  [object Object] 
2025-06-17 11:00:51.068 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:06:36.088 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:06:36.094 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:06:36.095 [info] index finished after resolve  [object Object] 
2025-06-17 11:06:36.096 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:06:55.521 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:06:55.527 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:06:55.528 [info] index finished after resolve  [object Object] 
2025-06-17 11:06:55.529 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:03.936 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:03.942 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:03.943 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:03.944 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:23.016 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:23.026 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:23.027 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:23.028 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:25.898 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:25.907 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:25.908 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:25.909 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:35.733 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:35.740 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:35.741 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:35.742 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:39.769 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:39.775 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:39.775 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:39.776 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:07:41.914 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:07:41.921 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:07:41.923 [info] index finished after resolve  [object Object] 
2025-06-17 11:07:41.924 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:16.234 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:16.243 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:16.244 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:16.245 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:18.690 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:18.696 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:18.697 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:18.698 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:20.815 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:20.822 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:20.823 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:20.824 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:23.228 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:23.234 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:23.235 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:23.237 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:44.808 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:44.813 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:44.814 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:44.815 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:20:47.499 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:20:47.508 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:20:47.509 [info] index finished after resolve  [object Object] 
2025-06-17 11:20:47.510 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:02.804 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:02.960 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:02.962 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:02.964 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:12.475 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:12.560 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:12.574 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:12.581 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:25.850 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:25.865 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:25.866 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:25.867 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:29.113 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:29.118 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:29.120 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:29.121 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:32.453 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:32.461 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:32.462 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:32.463 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:36.950 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:36.957 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:36.958 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:36.959 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:39.132 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:39.141 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:39.143 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:39.145 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:42.961 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:42.966 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:42.967 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:42.968 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:45.700 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:45.709 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:45.710 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:45.712 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:48.444 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:48.449 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:48.450 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:48.451 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:54.060 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:54.065 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:54.066 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:54.067 [info] refresh page data from resolve listeners 0 909   
2025-06-17 11:21:58.889 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-06-17 11:21:58.900 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-06-17 11:21:58.904 [info] index finished after resolve  [object Object] 
2025-06-17 11:21:58.906 [info] refresh page data from resolve listeners 0 909   
