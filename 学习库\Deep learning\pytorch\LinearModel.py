import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline

# --- 1. 生成数据 ---

# a) 生成完美的线性数据 (无噪声)
X_linear = np.linspace(0, 10, 50).reshape(-1, 1)
y_linear = 2 * X_linear + 1

# b) 生成非线性数据 (正弦曲线 + 噪声)
np.random.seed(0) # 保证每次运行结果一致
X_nonlinear = np.linspace(0, 10, 50).reshape(-1, 1)
# ravel()将多维数组降为一维
y_nonlinear = np.sin(X_nonlinear).ravel() + np.random.randn(50) * 0.5

# --- 2. 训练模型 ---

# a) 训练线性模型 (用于前两个图)
linear_model = LinearRegression()
linear_model.fit(X_linear, y_linear)
y_linear_pred = linear_model.predict(X_linear)

linear_model_on_nonlinear = LinearRegression()
linear_model_on_nonlinear.fit(X_nonlinear, y_nonlinear)
y_nonlinear_linear_pred = linear_model_on_nonlinear.predict(X_nonlinear)

# b) 训练非线性模型 (多项式回归, 用于第三个图)
# 我们使用一个7次多项式来拟合曲线
# make_pipeline 会将步骤串联起来：1.生成多项式特征 2.进行线性拟合
polynomial_model = make_pipeline(PolynomialFeatures(degree=7), LinearRegression())
polynomial_model.fit(X_nonlinear, y_nonlinear)

# 为了画出平滑的曲线，我们创建更多的数据点进行预测
X_plot = np.linspace(0, 10, 100).reshape(-1, 1)
y_nonlinear_poly_pred = polynomial_model.predict(X_plot)


# --- 3. 使用 Matplotlib 绘图 ---

# 创建一个包含三个子图的画布
fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 6))
fig.suptitle('Model Fitting Comparison', fontsize=16)

# --- 左图: 线性模型 vs 线性数据 ---
ax1.scatter(X_linear, y_linear, color='blue', label='Perfect Linear Data')
ax1.plot(X_linear, y_linear_pred, color='red', linewidth=3, label='Linear Fit')
ax1.set_title('Good Fit: Linear Model on Linear Data')
ax1.set_xlabel('X')
ax1.set_ylabel('Y')
ax1.legend()
ax1.grid(True)

# --- 中图: 线性模型 vs 非线性数据 ---
ax2.scatter(X_nonlinear, y_nonlinear, color='green', alpha=0.7, label='Non-Linear Data')
ax2.plot(X_nonlinear, y_nonlinear_linear_pred, color='red', linewidth=3, label='Linear Fit')
ax2.set_title('Underfitting: Linear Model on Non-Linear Data')
ax2.set_xlabel('X')
ax2.set_ylabel('Y')
ax2.legend()
ax2.grid(True)

# --- 右图: 非线性模型 vs 非线性数据 ---
ax3.scatter(X_nonlinear, y_nonlinear, color='green', alpha=0.7, label='Non-Linear Data')
ax3.plot(X_plot, y_nonlinear_poly_pred, color='purple', linewidth=3, label='Polynomial Fit (Non-Linear)')
ax3.set_title('Good Fit: Non-Linear Model on Non-Linear Data')
ax3.set_xlabel('X')
ax3.set_ylabel('Y')
ax3.legend()
ax3.grid(True)

# 调整布局并显示图像
plt.tight_layout(rect=[0, 0, 1, 0.96]) # 调整布局以适应主标题
plt.show()