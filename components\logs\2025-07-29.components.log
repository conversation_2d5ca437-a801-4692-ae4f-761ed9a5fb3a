2025-07-29 10:28:55.528 [info] components database created cost 1 ms   
2025-07-29 10:28:55.529 [info] components index initializing...   
2025-07-29 10:28:55.770 [info] start to batch put pages: 5   
2025-07-29 10:28:55.780 [info] batch persist cost 5  10 
2025-07-29 10:28:55.812 [info] components index initialized, 1013 files cost 285 ms   
2025-07-29 10:28:55.813 [info] refresh page data from init listeners 0 1013   
2025-07-29 10:28:57.231 [info] indexing created file components/logs/2025-07-29.components.log  [object Object] 
2025-07-29 10:28:57.231 [info] refresh page data from created listeners 0 1014   
2025-07-29 10:28:57.385 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-29 10:28:57.700 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-29 10:28:58.050 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-29 10:28:58.055 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-29 10:28:58.059 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-29 10:28:58.064 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-29 10:29:10.537 [error] check new version failed  Error: net::ERR_CONNECTION_CLOSED 
2025-07-29 10:30:16.571 [info] indexing created file 学习库/Artificial Intelligence/未命名.md  [object Object] 
2025-07-29 10:30:16.571 [info] indexing created ignore file 学习库/Artificial Intelligence/未命名.md   
2025-07-29 10:30:16.575 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-29 10:30:16.624 [info] trigger 学习库/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-29 10:30:16.626 [info] index finished after resolve  [object Object] 
2025-07-29 10:30:16.626 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:30:19.685 [info] refresh page data from rename listeners 0 1015   
2025-07-29 10:30:19.688 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-29 10:30:23.501 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:30:23.508 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:30:23.509 [info] index finished after resolve  [object Object] 
2025-07-29 10:30:23.510 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:30:26.838 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:30:26.844 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:30:26.846 [info] index finished after resolve  [object Object] 
2025-07-29 10:30:26.846 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:30:30.624 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:30:30.628 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:30:30.629 [info] index finished after resolve  [object Object] 
2025-07-29 10:30:30.630 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:20.351 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:20.370 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:20.371 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:20.372 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:23.005 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:23.010 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:23.011 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:23.011 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:26.372 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:26.378 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:26.379 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:26.380 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:28.919 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:28.937 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:28.939 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:28.939 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:31.529 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:31.548 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:31.549 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:31.549 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:33.715 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:33.719 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:33.720 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:33.720 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:36.373 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:36.379 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:36.380 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:36.380 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:39.558 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:39.563 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:39.584 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:39.585 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:41.582 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:41.588 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:41.588 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:41.589 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:43.645 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:43.666 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:43.672 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:43.672 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:45.685 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:45.689 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:45.690 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:45.691 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:47.763 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:47.770 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:47.770 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:47.771 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:34:50.521 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:34:50.525 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:34:50.525 [info] index finished after resolve  [object Object] 
2025-07-29 10:34:50.526 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:35:26.029 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:35:26.034 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:35:26.035 [info] index finished after resolve  [object Object] 
2025-07-29 10:35:26.035 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:44:01.296 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:44:01.549 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:44:01.551 [info] index finished after resolve  [object Object] 
2025-07-29 10:44:01.552 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:47:11.733 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:47:12.540 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:47:12.549 [info] index finished after resolve  [object Object] 
2025-07-29 10:47:12.550 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:48:32.218 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:48:32.516 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:48:32.522 [info] index finished after resolve  [object Object] 
2025-07-29 10:48:32.523 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:48:34.555 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:48:35.113 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:48:35.117 [info] index finished after resolve  [object Object] 
2025-07-29 10:48:35.117 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:51:28.705 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-29 10:51:29.013 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-29 10:51:29.018 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-29 10:51:29.026 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-29 10:51:29.030 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-29 10:52:18.191 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:52:18.211 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:52:18.213 [info] index finished after resolve  [object Object] 
2025-07-29 10:52:18.216 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:52:24.656 [info] refresh page data from rename listeners 0 1015   
2025-07-29 10:52:44.489 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:52:44.498 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:52:44.499 [info] index finished after resolve  [object Object] 
2025-07-29 10:52:44.499 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:53:18.188 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:53:18.198 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:53:18.198 [info] index finished after resolve  [object Object] 
2025-07-29 10:53:18.199 [info] refresh page data from resolve listeners 0 1015   
2025-07-29 10:53:23.129 [info] indexing created file 学习库/Anki/Artificial Intelligence/未命名 1.md  [object Object] 
2025-07-29 10:53:23.129 [info] indexing created ignore file 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-07-29 10:53:23.196 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-07-29 10:53:23.197 [info] index finished after resolve  [object Object] 
2025-07-29 10:53:23.197 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:53:28.492 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:53:28.502 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:53:28.502 [info] index finished after resolve  [object Object] 
2025-07-29 10:53:28.503 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:53:46.010 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-07-29 10:53:46.015 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-07-29 10:53:46.016 [info] index finished after resolve  [object Object] 
2025-07-29 10:53:46.017 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:53:50.067 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-07-29 10:53:50.071 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-07-29 10:53:50.074 [info] index finished after resolve  [object Object] 
2025-07-29 10:53:50.076 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:54:54.945 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:54:54.953 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:54:54.954 [info] index finished after resolve  [object Object] 
2025-07-29 10:54:54.954 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:54:59.783 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:54:59.790 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:54:59.809 [info] index finished after resolve  [object Object] 
2025-07-29 10:54:59.810 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:55:02.976 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:55:02.982 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:55:02.983 [info] index finished after resolve  [object Object] 
2025-07-29 10:55:02.983 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:55:07.656 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:55:07.661 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:55:07.662 [info] index finished after resolve  [object Object] 
2025-07-29 10:55:07.663 [info] refresh page data from resolve listeners 0 1016   
2025-07-29 10:55:09.978 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-07-29 10:55:09.983 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-07-29 10:55:09.984 [info] index finished after resolve  [object Object] 
2025-07-29 10:55:09.984 [info] refresh page data from resolve listeners 0 1016   
