---
tags:
  - 学习
  - linux
  - 系统管理
  - 进程管理
---

# Linux系统信息与进程管理

> [!info] 说明
> 本文档整理了Linux系统中查看系统信息、管理进程和监控系统资源的常用命令。

## 💻 系统信息查看

### 基本系统信息
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `uname` | 系统信息 | `-a` 全部信息<br>`-s` 系统名<br>`-r` 内核版本<br>`-m` 硬件架构 | `uname -a`<br>`uname -r` |
| `hostname` | 主机名 | `-f` 完整域名<br>`-i` IP地址 | `hostname`<br>`hostname -f` |
| `whoami` | 当前用户 | | `whoami` |
| `id` | 用户和组信息 | `-u` 用户ID<br>`-g` 组ID<br>`-G` 所有组 | `id`<br>`id username` |
| `w` | 当前登录用户 | | `w` |
| `who` | 登录用户信息 | `-b` 系统启动时间<br>`-r` 运行级别 | `who`<br>`who -b` |

### 时间与日期
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `date` | 显示/设置日期时间 | `+%Y-%m-%d` 格式化<br>`-s` 设置时间 | `date +"%Y-%m-%d %H:%M:%S"`<br>`date -s "2023-01-01 12:00:00"` |
| `cal` | 显示日历 | `-y` 显示全年<br>`-m` 指定月份 | `cal`<br>`cal -y 2023` |
| `uptime` | 系统运行时间和负载 | | `uptime` |

### 系统版本信息
| 命令 | 功能 | 示例 |
|------|------|------|
| `lsb_release` | 发行版信息 | `lsb_release -a` |
| `cat /etc/os-release` | 系统版本详情 | `cat /etc/os-release` |
| `cat /proc/version` | 内核版本信息 | `cat /proc/version` |

## 🔄 进程管理

### 进程查看
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ps` | 显示进程状态 | `aux` 详细信息<br>`-ef` 全格式<br>`-u` 指定用户<br>`--forest` 树状显示 | `ps aux`<br>`ps -ef \| grep nginx`<br>`ps --forest` |
| `pstree` | 进程树显示 | `-p` 显示PID<br>`-u` 显示用户 | `pstree`<br>`pstree -p` |
| `top` | 动态进程信息 | `q` 退出<br>`k` 杀死进程<br>`M` 按内存排序<br>`P` 按CPU排序 | `top` |
| `htop` | 增强版top | 需要安装<br>交互式界面 | `htop` |

> [!tip] top命令快捷键
> - `M`: 按内存使用率排序
> - `P`: 按CPU使用率排序
> - `T`: 按运行时间排序
> - `k`: 杀死进程（输入PID）
> - `r`: 改变进程优先级
> - `1`: 显示所有CPU核心
> - `q`: 退出

### 进程控制
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `kill` | 终止进程 | `-9` 强制终止<br>`-15` 正常终止<br>`-l` 列出信号 | `kill 1234`<br>`kill -9 1234`<br>`kill -15 1234` |
| `killall` | 按名称杀死进程 | `-9` 强制终止<br>`-i` 交互确认 | `killall firefox`<br>`killall -9 chrome` |
| `pkill` | 按模式杀死进程 | `-f` 匹配完整命令行<br>`-u` 指定用户 | `pkill -f "python script.py"`<br>`pkill -u username` |
| `nohup` | 后台运行命令 | | `nohup command &` |

### 作业控制
| 命令 | 功能 | 示例 |
|------|------|------|
| `jobs` | 显示作业列表 | `jobs` |
| `bg` | 后台运行作业 | `bg %1` |
| `fg` | 前台运行作业 | `fg %1` |
| `Ctrl+Z` | 暂停当前进程 | |
| `Ctrl+C` | 终止当前进程 | |
| `&` | 后台运行命令 | `command &` |

### 会话管理 - Screen

> [!info] Screen简介
> Screen是一个终端复用器，允许用户在单个终端窗口中运行多个会话，并且可以在断开连接后重新连接到这些会话。这对于远程服务器管理和长时间运行的任务非常有用。

#### Screen基本操作
| 命令 | 功能 | 示例 |
|------|------|------|
| `screen` | 启动新会话 | `screen` |
| `screen -S name` | 创建命名会话 | `screen -S mywork` |
| `screen -ls` | 列出所有会话 | `screen -ls` |
| `screen -r` | 重新连接会话 | `screen -r` |
| `screen -r name` | 连接指定会话 | `screen -r mywork` |
| `screen -d` | 分离会话 | `screen -d mywork` |
| `screen -x` | 共享会话 | `screen -x mywork` |

#### Screen快捷键 (Ctrl+A前缀)
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+A d` | 分离会话 | detach |
| `Ctrl+A c` | 创建新窗口 | create |
| `Ctrl+A n` | 下一个窗口 | next |
| `Ctrl+A p` | 上一个窗口 | previous |
| `Ctrl+A 0-9` | 切换到指定窗口 | 窗口编号 |
| `Ctrl+A "` | 窗口列表 | 选择窗口 |
| `Ctrl+A A` | 重命名当前窗口 | |
| `Ctrl+A k` | 杀死当前窗口 | kill |
| `Ctrl+A [` | 进入复制模式 | 可滚动查看历史 |
| `Ctrl+A ]` | 粘贴 | |
| `Ctrl+A ?` | 帮助信息 | |
| `Ctrl+A Ctrl+A` | 切换到上次窗口 | |

#### Screen分屏操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+A S` | 水平分屏 | Split |
| `Ctrl+A \|` | 垂直分屏 | 需要新版本 |
| `Ctrl+A Tab` | 切换分屏区域 | |
| `Ctrl+A Q` | 关闭除当前外的分屏 | |
| `Ctrl+A X` | 关闭当前分屏 | |

#### Screen实用示例
```bash
# 创建命名会话并运行长时间任务
screen -S backup
rsync -av /home/<USER>/ /backup/
# Ctrl+A d 分离会话

# 稍后重新连接
screen -r backup

# 创建多窗口工作环境
screen -S development
# Ctrl+A c 创建新窗口用于编辑
# Ctrl+A c 再创建窗口用于测试
# Ctrl+A c 再创建窗口用于日志监控

# 共享会话（多人协作）
screen -S shared
# 其他用户可以通过以下命令加入
screen -x shared

# 强制分离僵死会话
screen -D -r session_name
```

#### Screen配置文件 (~/.screenrc)
```bash
# 示例配置文件
# 设置状态栏
hardstatus alwayslastline
hardstatus string '%{= kG}[ %{G}%H %{g}][%= %{=kw}%?%-Lw%?%{r}(%{W}%n*%f%t%?(%u)%?%{r})%{w}%?%+Lw%?%?%= %{g}][%{B}%Y-%m-%d %{W}%c %{g}]'

# 设置滚动缓冲区大小
defscrollback 10000

# 启动时自动创建窗口
screen -t "shell" 0
screen -t "editor" 1
screen -t "logs" 2

# 设置默认shell
shell -$SHELL

# 禁用启动消息
startup_message off

# 设置编码
encoding UTF-8

# 自动分离死掉的会话
autodetach on
```

#### Screen vs Tmux对比
| 特性 | Screen | Tmux |
|------|--------|------|
| 学习曲线 | 较简单 | 稍复杂 |
| 配置文件 | ~/.screenrc | ~/.tmux.conf |
| 分屏支持 | 基础支持 | 强大的分屏 |
| 状态栏 | 基础 | 高度可定制 |
| 社区活跃度 | 较低 | 很高 |
| 默认前缀键 | Ctrl+A | Ctrl+B |

> [!tip] Screen使用技巧
> 1. **命名会话**: 总是为重要会话命名，便于管理
> 2. **定期清理**: 使用 `screen -wipe` 清理死掉的会话
> 3. **配置文件**: 创建个人配置文件提高效率
> 4. **日志记录**: 使用 `Ctrl+A H` 开启日志记录
> 5. **安全考虑**: 在共享系统上注意会话权限

#### 常见问题解决
```bash
# 会话列表显示 (Attached) 但无法连接
screen -D -r session_name

# 清理死掉的会话
screen -wipe

# 查看所有会话（包括其他用户的）
screen -ls -U

# 强制创建新会话（即使已存在同名会话）
screen -S name -d -m

# 在会话中执行命令而不进入
screen -S session_name -X stuff "command\n"
```

## 📊 系统资源监控

### 内存使用
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `free` | 内存使用情况 | `-h` 人类可读<br>`-m` MB单位<br>`-s` 持续监控 | `free -h`<br>`free -s 5` |
| `vmstat` | 虚拟内存统计 | `interval count` | `vmstat 2 5` |

### 磁盘使用
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `df` | 磁盘空间使用 | `-h` 人类可读<br>`-T` 显示文件系统类型<br>`-i` inode信息 | `df -h`<br>`df -T /home` |
| `du` | 目录大小 | `-sh` 总大小<br>`-h` 人类可读<br>`--max-depth=1` 限制深度 | `du -sh /home/<USER>
| `lsblk` | 列出块设备 | `-f` 显示文件系统 | `lsblk`<br>`lsblk -f` |
| `fdisk` | 磁盘分区管理 | `-l` 列出分区 | `sudo fdisk -l` |

### CPU信息
| 命令 | 功能 | 示例 |
|------|------|------|
| `lscpu` | CPU信息 | `lscpu` |
| `cat /proc/cpuinfo` | 详细CPU信息 | `cat /proc/cpuinfo` |
| `nproc` | CPU核心数 | `nproc` |

### 系统负载
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `uptime` | 系统负载 | | `uptime` |
| `iostat` | I/O统计 | `-x` 扩展统计<br>`interval count` | `iostat -x 2 5` |
| `sar` | 系统活动报告 | `-u` CPU<br>`-r` 内存<br>`-d` 磁盘 | `sar -u 2 5` |

## 🔍 系统监控工具

### 实时监控
```bash
# 监控系统负载
watch -n 1 uptime

# 监控磁盘使用
watch -n 5 'df -h'

# 监控内存使用
watch -n 2 'free -h'

# 监控网络连接
watch -n 3 'netstat -tulnp'
```

### 进程监控
```bash
# 查找占用CPU最多的进程
ps aux --sort=-%cpu | head -10

# 查找占用内存最多的进程
ps aux --sort=-%mem | head -10

# 查找特定用户的进程
ps -u username

# 查找僵尸进程
ps aux | grep -w Z
```

### 系统日志
| 命令 | 功能 | 示例 |
|------|------|------|
| `dmesg` | 内核消息 | `dmesg \| tail`<br>`dmesg \| grep -i error` |
| `journalctl` | systemd日志 | `journalctl -f`<br>`journalctl -u service_name` |
| `last` | 登录历史 | `last`<br>`last username` |
| `lastlog` | 最后登录时间 | `lastlog` |

## ⚡ 性能分析

### 系统性能概览
```bash
# 快速系统检查
echo "=== 系统信息 ==="
uname -a
echo "=== 运行时间和负载 ==="
uptime
echo "=== 内存使用 ==="
free -h
echo "=== 磁盘使用 ==="
df -h
echo "=== CPU使用率最高的进程 ==="
ps aux --sort=-%cpu | head -5
```

### 性能瓶颈分析
```bash
# CPU瓶颈分析
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

# 内存瓶颈分析
free | grep Mem | awk '{printf("%.2f%%\n", $3/$2 * 100.0)}'

# 磁盘I/O分析
iostat -x 1 1 | grep -v "^$"

# 网络连接分析
netstat -i
```

## 🛠️ 系统服务管理

### systemctl命令
| 命令 | 功能 | 示例 |
|------|------|------|
| `systemctl status` | 查看服务状态 | `systemctl status nginx` |
| `systemctl start` | 启动服务 | `systemctl start nginx` |
| `systemctl stop` | 停止服务 | `systemctl stop nginx` |
| `systemctl restart` | 重启服务 | `systemctl restart nginx` |
| `systemctl enable` | 开机自启 | `systemctl enable nginx` |
| `systemctl disable` | 禁用自启 | `systemctl disable nginx` |
| `systemctl list-units` | 列出所有服务 | `systemctl list-units --type=service` |

### 传统服务管理
```bash
# SysV init 系统
service nginx status
service nginx start
service nginx stop
service nginx restart

# 查看所有服务
service --status-all
```

## 📈 系统优化建议

### 性能监控脚本
```bash
#!/bin/bash
# 系统性能监控脚本

echo "系统性能报告 - $(date)"
echo "================================"

# CPU使用率
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print "用户: " $2 ", 系统: " $4 ", 空闲: " $8}'

# 内存使用
echo -e "\n内存使用:"
free -h | grep "Mem:" | awk '{print "总计: " $2 ", 已用: " $3 ", 可用: " $7}'

# 磁盘使用
echo -e "\n磁盘使用:"
df -h | grep -E "^/dev/" | awk '{print $1 ": " $5 " 已用"}'

# 系统负载
echo -e "\n系统负载:"
uptime | awk '{print "负载: " $(NF-2) " " $(NF-1) " " $NF}'
```

> [!success] 监控最佳实践
> 1. **定期检查**: 建立定期的系统监控习惯
> 2. **设置阈值**: 为关键指标设置告警阈值
> 3. **日志分析**: 定期分析系统日志发现问题
> 4. **性能基线**: 建立系统性能基线用于对比

---

**相关笔记链接:**
- [[01-文件与目录操作]]
- [[04-网络命令]]
- [[05-权限与安全管理]]
