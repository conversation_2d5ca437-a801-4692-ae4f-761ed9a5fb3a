2025-04-15 12:49:08 [info] components database created cost 2 ms   
2025-04-15 12:49:08 [info] components index initializing...   
2025-04-15 12:49:08 [info] start to batch put pages: 5   
2025-04-15 12:49:10 [info] batch persist cost 5  1667 
2025-04-15 12:49:10 [info] components index initialized, 740 files cost 1795 ms   
2025-04-15 12:49:10 [info] refresh page data from init listeners 0 740   
2025-04-15 12:49:11 [info] indexing created file components/logs/2025-04-15.components.log  [object Object] 
2025-04-15 12:49:11 [info] refresh page data from created listeners 0 741   
2025-04-15 12:49:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-15 12:49:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-15 12:49:13 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-15 12:49:13 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-15 12:49:13 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-15 12:49:13 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-15 12:49:15 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-15 12:49:15 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-15 12:49:15 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:15 [info] refresh page data from resolve listeners 0 741   
2025-04-15 12:49:16 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-15 12:49:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-15 12:49:16 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:16 [info] refresh page data from resolve listeners 0 741   
2025-04-15 12:49:17 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-04-15 12:49:17 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-04-15 12:49:17 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:17 [info] refresh page data from resolve listeners 0 741   
2025-04-15 12:49:18 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-09-50-40.png  [object Object] 
2025-04-15 12:49:18 [info] refresh page data from created listeners 0 742   
2025-04-15 12:49:19 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-13-55.png  [object Object] 
2025-04-15 12:49:19 [info] refresh page data from created listeners 0 743   
2025-04-15 12:49:19 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-18-24.png  [object Object] 
2025-04-15 12:49:19 [info] refresh page data from created listeners 0 744   
2025-04-15 12:49:20 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-27-20.png  [object Object] 
2025-04-15 12:49:20 [info] refresh page data from created listeners 0 745   
2025-04-15 12:49:22 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-30-23.png  [object Object] 
2025-04-15 12:49:22 [info] refresh page data from created listeners 0 746   
2025-04-15 12:49:22 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-14-15-25.png  [object Object] 
2025-04-15 12:49:22 [info] refresh page data from created listeners 0 747   
2025-04-15 12:49:23 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-24-16.png  [object Object] 
2025-04-15 12:49:23 [info] refresh page data from created listeners 0 748   
2025-04-15 12:49:23 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-32-10.png  [object Object] 
2025-04-15 12:49:23 [info] refresh page data from created listeners 0 749   
2025-04-15 12:49:24 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-35-20.png  [object Object] 
2025-04-15 12:49:24 [info] refresh page data from created listeners 0 750   
2025-04-15 12:49:25 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-21-45-18.png  [object Object] 
2025-04-15 12:49:25 [info] refresh page data from created listeners 0 751   
2025-04-15 12:49:27 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-09-39-05.png  [object Object] 
2025-04-15 12:49:27 [info] refresh page data from created listeners 0 752   
2025-04-15 12:49:27 [info] indexing created file 学习库/Anki/stm32/SPI.md  [object Object] 
2025-04-15 12:49:27 [info] indexing created ignore file 学习库/Anki/stm32/SPI.md   
2025-04-15 12:49:27 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-15 12:49:27 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:27 [info] refresh page data from resolve listeners 0 753   
2025-04-15 12:49:28 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-10-16-13.png  [object Object] 
2025-04-15 12:49:28 [info] refresh page data from created listeners 0 754   
2025-04-15 12:49:28 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-03-37.png  [object Object] 
2025-04-15 12:49:28 [info] refresh page data from created listeners 0 755   
2025-04-15 12:49:29 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-03-59.png  [object Object] 
2025-04-15 12:49:29 [info] refresh page data from created listeners 0 756   
2025-04-15 12:49:29 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-04-18.png  [object Object] 
2025-04-15 12:49:29 [info] refresh page data from created listeners 0 757   
2025-04-15 12:49:30 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-04-45.png  [object Object] 
2025-04-15 12:49:30 [info] refresh page data from created listeners 0 758   
2025-04-15 12:49:31 [info] indexing created file 学习库/stm32/5 SPI.md  [object Object] 
2025-04-15 12:49:31 [info] indexing created ignore file 学习库/stm32/5 SPI.md   
2025-04-15 12:49:31 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-15 12:49:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-15 12:49:31 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:31 [info] refresh page data from resolve listeners 0 759   
2025-04-15 12:49:31 [info] indexing created file 学习库/ROS/Attachments/6 中断-2025-04-10-15-34-24.png  [object Object] 
2025-04-15 12:49:31 [info] refresh page data from created listeners 0 760   
2025-04-15 12:49:32 [info] indexing created file 学习库/ROS/Attachments/6 中断-2025-04-10-15-51-01.png  [object Object] 
2025-04-15 12:49:32 [info] refresh page data from created listeners 0 761   
2025-04-15 12:49:33 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-07-21.png  [object Object] 
2025-04-15 12:49:33 [info] refresh page data from created listeners 0 762   
2025-04-15 12:49:33 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-23-35.png  [object Object] 
2025-04-15 12:49:33 [info] refresh page data from created listeners 0 763   
2025-04-15 12:49:34 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-49-57.png  [object Object] 
2025-04-15 12:49:34 [info] refresh page data from created listeners 0 764   
2025-04-15 12:49:34 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-19-36-10.png  [object Object] 
2025-04-15 12:49:34 [info] refresh page data from created listeners 0 765   
2025-04-15 12:49:35 [info] indexing created file 学习库/stm32/6 中断.md  [object Object] 
2025-04-15 12:49:35 [info] indexing created ignore file 学习库/stm32/6 中断.md   
2025-04-15 12:49:35 [info] trigger 学习库/stm32/6 中断.md resolve  [object Object] 
2025-04-15 12:49:35 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:35 [info] refresh page data from resolve listeners 0 766   
2025-04-15 12:49:35 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-20-50-24.png  [object Object] 
2025-04-15 12:49:35 [info] refresh page data from created listeners 0 767   
2025-04-15 12:49:36 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-20-56-24.png  [object Object] 
2025-04-15 12:49:36 [info] refresh page data from created listeners 0 768   
2025-04-15 12:49:37 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-21-01-00.png  [object Object] 
2025-04-15 12:49:37 [info] refresh page data from created listeners 0 769   
2025-04-15 12:49:37 [info] indexing created file 学习库/stm32/7 EXTI.md  [object Object] 
2025-04-15 12:49:37 [info] indexing created ignore file 学习库/stm32/7 EXTI.md   
2025-04-15 12:49:37 [info] trigger 学习库/stm32/7 EXTI.md resolve  [object Object] 
2025-04-15 12:49:37 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:37 [info] refresh page data from resolve listeners 0 770   
2025-04-15 12:49:38 [info] indexing created file components/logs/2025-04-10.components.log  [object Object] 
2025-04-15 12:49:38 [info] refresh page data from created listeners 0 771   
2025-04-15 12:49:38 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-04-15 12:49:38 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-04-15 12:49:38 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:38 [info] refresh page data from resolve listeners 0 771   
2025-04-15 12:49:39 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-04-41.png  [object Object] 
2025-04-15 12:49:39 [info] refresh page data from created listeners 0 772   
2025-04-15 12:49:39 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-10-15.png  [object Object] 
2025-04-15 12:49:39 [info] refresh page data from created listeners 0 773   
2025-04-15 12:49:40 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-23-14.png  [object Object] 
2025-04-15 12:49:40 [info] refresh page data from created listeners 0 774   
2025-04-15 12:49:41 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-16-26.png  [object Object] 
2025-04-15 12:49:41 [info] refresh page data from created listeners 0 775   
2025-04-15 12:49:41 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-36-40.png  [object Object] 
2025-04-15 12:49:42 [info] refresh page data from created listeners 0 776   
2025-04-15 12:49:42 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-14-57-08.png  [object Object] 
2025-04-15 12:49:42 [info] refresh page data from created listeners 0 777   
2025-04-15 12:49:43 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-14-59-00.png  [object Object] 
2025-04-15 12:49:43 [info] refresh page data from created listeners 0 778   
2025-04-15 12:49:44 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-15-03-04.png  [object Object] 
2025-04-15 12:49:44 [info] refresh page data from created listeners 0 779   
2025-04-15 12:49:44 [info] ignore file modify evnet 学习库/stm32/1 启动.md   
2025-04-15 12:49:44 [info] trigger 学习库/stm32/1 启动.md resolve  [object Object] 
2025-04-15 12:49:44 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:44 [info] refresh page data from resolve listeners 0 779   
2025-04-15 12:49:45 [info] indexing created file 学习库/stm32/8 时钟.md  [object Object] 
2025-04-15 12:49:45 [info] indexing created ignore file 学习库/stm32/8 时钟.md   
2025-04-15 12:49:45 [info] trigger 学习库/stm32/8 时钟.md resolve  [object Object] 
2025-04-15 12:49:45 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:45 [info] refresh page data from resolve listeners 0 780   
2025-04-15 12:49:45 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-15-53-32.png  [object Object] 
2025-04-15 12:49:45 [info] refresh page data from created listeners 0 781   
2025-04-15 12:49:46 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-02-37.png  [object Object] 
2025-04-15 12:49:46 [info] refresh page data from created listeners 0 782   
2025-04-15 12:49:46 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-14.png  [object Object] 
2025-04-15 12:49:46 [info] refresh page data from created listeners 0 783   
2025-04-15 12:49:47 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-28.png  [object Object] 
2025-04-15 12:49:47 [info] refresh page data from created listeners 0 784   
2025-04-15 12:49:47 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-42.png  [object Object] 
2025-04-15 12:49:47 [info] refresh page data from created listeners 0 785   
2025-04-15 12:49:48 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-19-44-54.png  [object Object] 
2025-04-15 12:49:48 [info] refresh page data from created listeners 0 786   
2025-04-15 12:49:51 [info] indexing created file components/logs/2025-04-11.components.log  [object Object] 
2025-04-15 12:49:51 [info] refresh page data from created listeners 0 787   
2025-04-15 12:49:51 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-01-26.png  [object Object] 
2025-04-15 12:49:51 [info] refresh page data from created listeners 0 788   
2025-04-15 12:49:52 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-06-33.png  [object Object] 
2025-04-15 12:49:52 [info] refresh page data from created listeners 0 789   
2025-04-15 12:49:53 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-09-52.png  [object Object] 
2025-04-15 12:49:53 [info] refresh page data from created listeners 0 790   
2025-04-15 12:49:53 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-17-31.png  [object Object] 
2025-04-15 12:49:53 [info] refresh page data from created listeners 0 791   
2025-04-15 12:49:54 [info] indexing created file components/logs/2025-04-12.components.log  [object Object] 
2025-04-15 12:49:54 [info] refresh page data from created listeners 0 792   
2025-04-15 12:49:54 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-32-24.png  [object Object] 
2025-04-15 12:49:54 [info] refresh page data from created listeners 0 793   
2025-04-15 12:49:55 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-51-10.png  [object Object] 
2025-04-15 12:49:55 [info] refresh page data from created listeners 0 794   
2025-04-15 12:49:55 [info] indexing created file 学习库/stm32/9 定时器.md  [object Object] 
2025-04-15 12:49:55 [info] indexing created ignore file 学习库/stm32/9 定时器.md   
2025-04-15 12:49:55 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-15 12:49:55 [info] index finished after resolve  [object Object] 
2025-04-15 12:49:55 [info] refresh page data from resolve listeners 0 795   
2025-04-15 12:49:56 [info] indexing created file components/logs/2025-04-13.components.log  [object Object] 
2025-04-15 12:49:56 [info] refresh page data from created listeners 0 796   
