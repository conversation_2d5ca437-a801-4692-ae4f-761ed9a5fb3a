2025-05-08 16:49:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-05-08 16:49:32 [info] indexing created file components/logs/2025-05-08.components.log  [object Object] 
2025-05-08 16:49:32 [info] refresh page data from created listeners 0 882   
2025-05-08 16:49:32 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-05-08 16:49:32 [info] index finished after resolve  [object Object] 
2025-05-08 16:49:32 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:10:27 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:10:27 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:10:27 [info] index finished after resolve  [object Object] 
2025-05-08 17:10:27 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:10:57 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:10:57 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:10:57 [info] index finished after resolve  [object Object] 
2025-05-08 17:10:57 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:14 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:14 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:14 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:14 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:17 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:17 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:17 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:17 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:19 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:19 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:19 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:19 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:21 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:21 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:21 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:21 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:32 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:32 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:32 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:32 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:36 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:36 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:36 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:36 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:55 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:55 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:55 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:55 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:11:57 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:11:57 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:11:57 [info] index finished after resolve  [object Object] 
2025-05-08 17:11:57 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:12:00 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:12:00 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:12:00 [info] index finished after resolve  [object Object] 
2025-05-08 17:12:00 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:12:05 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:12:06 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:12:06 [info] index finished after resolve  [object Object] 
2025-05-08 17:12:06 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:12:08 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:12:08 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:12:08 [info] index finished after resolve  [object Object] 
2025-05-08 17:12:08 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:12:10 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:12:10 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:12:10 [info] index finished after resolve  [object Object] 
2025-05-08 17:12:10 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:13:51 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:13:51 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:13:51 [info] index finished after resolve  [object Object] 
2025-05-08 17:13:51 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:02 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:02 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:02 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:02 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:05 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:05 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:05 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:05 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:07 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:07 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:07 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:07 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:09 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:09 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:09 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:09 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:12 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:12 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:12 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:12 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:14 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:14 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:14 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:14 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:18 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:18 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:18 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:18 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:20 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:20 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:20 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:20 [info] refresh page data from resolve listeners 0 882   
2025-05-08 17:14:23 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-05-08 17:14:23 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-05-08 17:14:23 [info] index finished after resolve  [object Object] 
2025-05-08 17:14:23 [info] refresh page data from resolve listeners 0 882   
2025-05-08 20:23:12 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入 resolve  [object Object] 
2025-05-08 20:23:12 [info] index finished after resolve  [object Object] 
2025-05-08 20:23:12 [info] refresh page data from modify listeners 0 882   
2025-05-08 20:25:19 [info] indexing created file 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md  [object Object] 
2025-05-08 20:25:19 [info] indexing created ignore file 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:25:19 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:25:19 [info] index finished after resolve  [object Object] 
2025-05-08 20:25:19 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:25:19 [info] refresh page data from delete listeners 0 882   
2025-05-08 20:25:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:25:51 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:25:51 [info] index finished after resolve  [object Object] 
2025-05-08 20:25:51 [info] refresh page data from resolve listeners 0 882   
2025-05-08 20:28:47 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-28-47.png  [object Object] 
2025-05-08 20:28:47 [info] refresh page data from created listeners 0 883   
2025-05-08 20:28:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:28:49 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:28:49 [info] index finished after resolve  [object Object] 
2025-05-08 20:28:49 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:31:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:31:17 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:31:17 [info] index finished after resolve  [object Object] 
2025-05-08 20:31:17 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:32:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:32:46 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:32:46 [info] index finished after resolve  [object Object] 
2025-05-08 20:32:46 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:33:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:33:55 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:33:55 [info] index finished after resolve  [object Object] 
2025-05-08 20:33:55 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:34:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:34:47 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:34:47 [info] index finished after resolve  [object Object] 
2025-05-08 20:34:47 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:34:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:34:56 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:34:56 [info] index finished after resolve  [object Object] 
2025-05-08 20:34:56 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:39:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:39:14 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:39:14 [info] index finished after resolve  [object Object] 
2025-05-08 20:39:14 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:40:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:40:28 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:40:28 [info] index finished after resolve  [object Object] 
2025-05-08 20:40:28 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:41:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:41:43 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:41:43 [info] index finished after resolve  [object Object] 
2025-05-08 20:41:43 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:41:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:41:55 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:41:55 [info] index finished after resolve  [object Object] 
2025-05-08 20:41:55 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:41:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:41:57 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:41:57 [info] index finished after resolve  [object Object] 
2025-05-08 20:41:57 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:41:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:41:58 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:41:58 [info] index finished after resolve  [object Object] 
2025-05-08 20:41:58 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:42:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:42:13 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:42:13 [info] index finished after resolve  [object Object] 
2025-05-08 20:42:13 [info] refresh page data from resolve listeners 0 883   
2025-05-08 20:42:18 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-42-18.png  [object Object] 
2025-05-08 20:42:18 [info] refresh page data from created listeners 0 884   
2025-05-08 20:42:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:42:20 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:42:20 [info] index finished after resolve  [object Object] 
2025-05-08 20:42:20 [info] refresh page data from resolve listeners 0 884   
2025-05-08 20:42:32 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-42-32.png  [object Object] 
2025-05-08 20:42:32 [info] refresh page data from created listeners 0 885   
2025-05-08 20:42:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:42:34 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:42:34 [info] index finished after resolve  [object Object] 
2025-05-08 20:42:34 [info] refresh page data from resolve listeners 0 885   
2025-05-08 20:44:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-05-08 20:44:45 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-05-08 20:44:45 [info] index finished after resolve  [object Object] 
2025-05-08 20:44:45 [info] refresh page data from resolve listeners 0 885   
2025-05-08 20:56:26 [info] indexing created file 学习库/Deep learning/pytorch/7. 加载数据集.md  [object Object] 
2025-05-08 20:56:26 [info] indexing created ignore file 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-08 20:56:26 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-08 20:56:26 [info] index finished after resolve  [object Object] 
2025-05-08 20:56:26 [info] refresh page data from resolve listeners 0 886   
2025-05-08 21:13:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-08 21:13:06 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-08 21:13:06 [info] index finished after resolve  [object Object] 
2025-05-08 21:13:06 [info] refresh page data from resolve listeners 0 886   
2025-05-08 21:14:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-08 21:14:10 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-08 21:14:10 [info] index finished after resolve  [object Object] 
2025-05-08 21:14:10 [info] refresh page data from resolve listeners 0 886   
