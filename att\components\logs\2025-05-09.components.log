2025-05-09 08:19:50 [info] indexing created file 学习库/linux/Attachments/wsl2-2025-04-25-09-00-55.png  [object Object] 
2025-05-09 08:19:50 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:19:50 [info] refresh page data from created listeners 0 887   
2025-05-09 08:19:50 [info] indexing created file components/logs/2025-05-09.components.log  [object Object] 
2025-05-09 08:19:50 [info] refresh page data from created listeners 0 888   
2025-05-09 08:28:59 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:28:59 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:28:59 [info] index finished after resolve  [object Object] 
2025-05-09 08:28:59 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:02 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:02 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:02 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:02 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:04 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:04 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:04 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:04 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:06 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:06 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:06 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:06 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:08 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:08 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:08 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:08 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:10 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:10 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:10 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:10 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:12 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:12 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:12 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:12 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:16 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:16 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:16 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:16 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:21 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:21 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:21 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:21 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:24 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:24 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:24 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:24 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:26 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:26 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:26 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:26 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:29 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:29 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:29 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:29 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:31 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:31 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:31 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:31 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:34 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:34 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:34 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:34 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:36 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:36 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:36 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:36 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:29:39 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:29:39 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:29:39 [info] index finished after resolve  [object Object] 
2025-05-09 08:29:39 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:08 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:08 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:08 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:08 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:10 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:10 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:10 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:10 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:13 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:13 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:13 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:13 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:17 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:17 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:17 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:17 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:19 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:19 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:19 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:19 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:22 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:22 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:22 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:22 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:24 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:24 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:24 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:24 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:26 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:26 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:26 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:26 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:29 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:29 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:29 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:29 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:30:31 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:30:31 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:30:31 [info] index finished after resolve  [object Object] 
2025-05-09 08:30:31 [info] refresh page data from resolve listeners 0 888   
2025-05-09 08:31:23 [info] indexing created file 学习库/linux/attachments/wsl2-2025-05-09-08-31-23.png  [object Object] 
2025-05-09 08:31:23 [info] refresh page data from created listeners 0 889   
2025-05-09 08:31:23 [info] indexing created file 学习库/linux/Attachments/wsl2-2025-05-09-08-31-23.png  [object Object] 
2025-05-09 08:31:23 [info] refresh page data from created listeners 0 890   
2025-05-09 08:31:24 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-05-09 08:31:24 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-05-09 08:31:24 [info] index finished after resolve  [object Object] 
2025-05-09 08:31:24 [info] refresh page data from resolve listeners 0 890   
2025-05-09 09:11:26 [info] components database created cost 1 ms   
2025-05-09 09:11:26 [info] components index initializing...   
2025-05-09 09:11:26 [info] start to batch put pages: 9   
2025-05-09 09:11:26 [info] batch persist cost 9  3 
2025-05-09 09:11:27 [info] components index initialized, 890 files cost 1261 ms   
2025-05-09 09:11:27 [info] refresh page data from init listeners 0 890   
2025-05-09 09:11:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-09 09:11:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-09 09:11:29 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-09 09:11:29 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-09 09:11:29 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-09 09:11:29 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-09 09:11:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 09:11:49 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 09:11:49 [info] index finished after resolve  [object Object] 
2025-05-09 09:11:49 [info] refresh page data from resolve listeners 0 890   
2025-05-09 09:46:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 09:46:49 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 09:46:49 [info] index finished after resolve  [object Object] 
2025-05-09 09:46:49 [info] refresh page data from resolve listeners 0 890   
2025-05-09 10:00:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 10:00:58 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 10:00:58 [info] index finished after resolve  [object Object] 
2025-05-09 10:00:58 [info] refresh page data from resolve listeners 0 890   
2025-05-09 10:01:01 [info] indexing created file 学习库/Deep learning/pytorch/attachments/7. 加载数据集-2025-05-09-10-01-01.png  [object Object] 
2025-05-09 10:01:01 [info] refresh page data from created listeners 0 891   
2025-05-09 10:01:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 10:01:03 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 10:01:03 [info] index finished after resolve  [object Object] 
2025-05-09 10:01:03 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:02:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 10:02:24 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 10:02:24 [info] index finished after resolve  [object Object] 
2025-05-09 10:02:24 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:03:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 10:03:08 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 10:03:08 [info] index finished after resolve  [object Object] 
2025-05-09 10:03:08 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:49:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 10:49:47 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 10:49:47 [info] index finished after resolve  [object Object] 
2025-05-09 10:49:47 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:56:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:56:05 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:56:05 [info] index finished after resolve  [object Object] 
2025-05-09 10:56:05 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:10 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:10 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:10 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:15 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:15 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:17 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:17 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:17 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:20 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:20 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:22 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:22 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:22 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:28 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:28 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:28 [info] refresh page data from resolve listeners 0 891   
2025-05-09 10:57:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 10:57:30 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 10:57:30 [info] index finished after resolve  [object Object] 
2025-05-09 10:57:30 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:00:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:00:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:00:43 [info] index finished after resolve  [object Object] 
2025-05-09 11:00:43 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:01:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:01:07 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:01:07 [info] index finished after resolve  [object Object] 
2025-05-09 11:01:07 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:01:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:01:09 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:01:09 [info] index finished after resolve  [object Object] 
2025-05-09 11:01:09 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:01:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:01:12 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:01:12 [info] index finished after resolve  [object Object] 
2025-05-09 11:01:12 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:01:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:01:26 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:01:26 [info] index finished after resolve  [object Object] 
2025-05-09 11:01:26 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:05:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:05:00 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:05:00 [info] index finished after resolve  [object Object] 
2025-05-09 11:05:00 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:05:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:05:14 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:05:14 [info] index finished after resolve  [object Object] 
2025-05-09 11:05:14 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:06:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-05-09 11:06:06 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-05-09 11:06:06 [info] index finished after resolve  [object Object] 
2025-05-09 11:06:06 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:17:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:17:19 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:17:19 [info] index finished after resolve  [object Object] 
2025-05-09 11:17:19 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:17:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:17:45 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:17:45 [info] index finished after resolve  [object Object] 
2025-05-09 11:17:45 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:17:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:17:50 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:17:50 [info] index finished after resolve  [object Object] 
2025-05-09 11:17:50 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:02 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:02 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:02 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:06 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:06 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:06 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:13 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:13 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:13 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:15 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:15 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:15 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:19 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:19 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:19 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:24 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:24 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:24 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:49 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:49 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:49 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:55 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:55 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:55 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:18:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:18:58 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:18:58 [info] index finished after resolve  [object Object] 
2025-05-09 11:18:58 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:06 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:06 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:06 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:10 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:10 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:10 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:13 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:13 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:13 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:25 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:25 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:25 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:28 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:28 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:28 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:30 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:30 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:30 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:32 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:32 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:32 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:42 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:42 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:42 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:48 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:48 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:48 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:51 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:51 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:51 [info] refresh page data from resolve listeners 0 891   
2025-05-09 11:19:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-09 11:19:57 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-09 11:19:57 [info] index finished after resolve  [object Object] 
2025-05-09 11:19:57 [info] refresh page data from resolve listeners 0 891   
