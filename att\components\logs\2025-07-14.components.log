2025-07-14 07:50:36.694 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 07:50:36.711 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 07:50:36.772 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 07:50:36.772 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 07:50:36.775 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 07:50:36.776 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 07:50:38.324 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 07:50:38.449 [info] components database created cost 17 ms   
2025-07-14 07:50:38.449 [info] components index initializing...   
2025-07-14 07:50:38.681 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 07:50:39.163 [info] start to batch put pages: 5   
2025-07-14 07:50:39.164 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 07:50:39.206 [info] batch persist cost 5  43 
2025-07-14 07:50:39.207 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 07:50:39.213 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 07:50:39.250 [info] components index initialized, 972 files cost 818 ms   
2025-07-14 07:50:39.250 [info] refresh page data from init listeners 0 972   
2025-07-14 07:50:39.894 [info] indexing created file components/logs/2025-07-14.components.log  [object Object] 
2025-07-14 07:50:39.917 [info] refresh page data from created listeners 0 973   
2025-07-14 07:50:40.083 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 07:50:40.099 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 07:50:40.102 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 07:50:40.117 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 07:50:40.235 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 07:50:40.726 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 07:50:40.734 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 07:50:40.738 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 07:50:40.741 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 07:50:41.754 [info] refresh page data from delete listeners 0 972   
2025-07-14 07:50:44.863 [info] indexing created file copilot-custom-prompts/Summarize.md  [object Object] 
2025-07-14 07:50:44.863 [info] indexing created ignore file copilot-custom-prompts/Summarize.md   
2025-07-14 07:50:44.889 [info] trigger copilot-custom-prompts/Summarize.md resolve  [object Object] 
2025-07-14 07:50:44.891 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:44.892 [info] refresh page data from resolve listeners 0 973   
2025-07-14 07:50:45.442 [info] indexing created file copilot-custom-prompts/Remove URLs.md  [object Object] 
2025-07-14 07:50:45.443 [info] indexing created ignore file copilot-custom-prompts/Remove URLs.md   
2025-07-14 07:50:45.455 [info] trigger copilot-custom-prompts/Remove URLs.md resolve  [object Object] 
2025-07-14 07:50:45.457 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:45.458 [info] refresh page data from resolve listeners 0 974   
2025-07-14 07:50:46.056 [info] indexing created file copilot-custom-prompts/Fix grammar and spelling.md  [object Object] 
2025-07-14 07:50:46.056 [info] indexing created ignore file copilot-custom-prompts/Fix grammar and spelling.md   
2025-07-14 07:50:46.067 [info] trigger copilot-custom-prompts/Fix grammar and spelling.md resolve  [object Object] 
2025-07-14 07:50:46.068 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:46.068 [info] refresh page data from resolve listeners 0 975   
2025-07-14 07:50:46.566 [info] indexing created file copilot-custom-prompts/Generate glossary.md  [object Object] 
2025-07-14 07:50:46.566 [info] indexing created ignore file copilot-custom-prompts/Generate glossary.md   
2025-07-14 07:50:46.578 [info] trigger copilot-custom-prompts/Generate glossary.md resolve  [object Object] 
2025-07-14 07:50:46.579 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:46.580 [info] refresh page data from resolve listeners 0 976   
2025-07-14 07:50:47.096 [info] indexing created file copilot-custom-prompts/Generate table of contents.md  [object Object] 
2025-07-14 07:50:47.096 [info] indexing created ignore file copilot-custom-prompts/Generate table of contents.md   
2025-07-14 07:50:47.109 [info] trigger copilot-custom-prompts/Generate table of contents.md resolve  [object Object] 
2025-07-14 07:50:47.111 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:47.111 [info] refresh page data from resolve listeners 0 977   
2025-07-14 07:50:47.609 [info] indexing created file copilot-custom-prompts/Translate to Chinese.md  [object Object] 
2025-07-14 07:50:47.609 [info] indexing created ignore file copilot-custom-prompts/Translate to Chinese.md   
2025-07-14 07:50:47.624 [info] trigger copilot-custom-prompts/Translate to Chinese.md resolve  [object Object] 
2025-07-14 07:50:47.625 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:47.626 [info] refresh page data from resolve listeners 0 978   
2025-07-14 07:50:48.080 [info] indexing created file copilot-custom-prompts/Simplify.md  [object Object] 
2025-07-14 07:50:48.080 [info] indexing created ignore file copilot-custom-prompts/Simplify.md   
2025-07-14 07:50:48.097 [info] trigger copilot-custom-prompts/Simplify.md resolve  [object Object] 
2025-07-14 07:50:48.099 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:48.100 [info] refresh page data from resolve listeners 0 979   
2025-07-14 07:50:48.508 [info] indexing created file copilot-custom-prompts/Make shorter.md  [object Object] 
2025-07-14 07:50:48.508 [info] indexing created ignore file copilot-custom-prompts/Make shorter.md   
2025-07-14 07:50:48.520 [info] trigger copilot-custom-prompts/Make shorter.md resolve  [object Object] 
2025-07-14 07:50:48.523 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:48.524 [info] refresh page data from resolve listeners 0 980   
2025-07-14 07:50:49.011 [info] indexing created file copilot-custom-prompts/Rewrite as tweet.md  [object Object] 
2025-07-14 07:50:49.011 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet.md   
2025-07-14 07:50:49.024 [info] trigger copilot-custom-prompts/Rewrite as tweet.md resolve  [object Object] 
2025-07-14 07:50:49.025 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:49.026 [info] refresh page data from resolve listeners 0 981   
2025-07-14 07:50:49.718 [info] indexing created file copilot-custom-prompts/Make longer.md  [object Object] 
2025-07-14 07:50:49.719 [info] indexing created ignore file copilot-custom-prompts/Make longer.md   
2025-07-14 07:50:49.730 [info] trigger copilot-custom-prompts/Make longer.md resolve  [object Object] 
2025-07-14 07:50:49.731 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:49.732 [info] refresh page data from resolve listeners 0 982   
2025-07-14 07:50:50.167 [info] indexing created file copilot-custom-prompts/Explain like I am 5.md  [object Object] 
2025-07-14 07:50:50.167 [info] indexing created ignore file copilot-custom-prompts/Explain like I am 5.md   
2025-07-14 07:50:50.179 [info] trigger copilot-custom-prompts/Explain like I am 5.md resolve  [object Object] 
2025-07-14 07:50:50.181 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:50.182 [info] refresh page data from resolve listeners 0 983   
2025-07-14 07:50:50.609 [info] indexing created file copilot-custom-prompts/Rewrite as press release.md  [object Object] 
2025-07-14 07:50:50.609 [info] indexing created ignore file copilot-custom-prompts/Rewrite as press release.md   
2025-07-14 07:50:50.620 [info] trigger copilot-custom-prompts/Rewrite as press release.md resolve  [object Object] 
2025-07-14 07:50:50.621 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:50.621 [info] refresh page data from resolve listeners 0 984   
2025-07-14 07:50:51.053 [info] indexing created file copilot-custom-prompts/Emojify.md  [object Object] 
2025-07-14 07:50:51.053 [info] indexing created ignore file copilot-custom-prompts/Emojify.md   
2025-07-14 07:50:51.066 [info] trigger copilot-custom-prompts/Emojify.md resolve  [object Object] 
2025-07-14 07:50:51.067 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:51.067 [info] refresh page data from resolve listeners 0 985   
2025-07-14 07:50:51.574 [info] indexing created file copilot-custom-prompts/Rewrite as tweet thread.md  [object Object] 
2025-07-14 07:50:51.574 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet thread.md   
2025-07-14 07:50:51.688 [info] trigger copilot-custom-prompts/Rewrite as tweet thread.md resolve  [object Object] 
2025-07-14 07:50:51.699 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:51.699 [info] refresh page data from resolve listeners 0 986   
2025-07-14 07:50:58.537 [info] indexing created file 学习库/stm32/attachments/2 GPIO-2025-07-13-08-53-44.png  [object Object] 
2025-07-14 07:50:58.542 [info] refresh page data from created listeners 0 987   
2025-07-14 07:50:59.574 [debug] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-07-14 07:50:59.605 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-07-14 07:50:59.607 [info] index finished after resolve  [object Object] 
2025-07-14 07:50:59.608 [info] refresh page data from resolve listeners 0 987   
2025-07-14 07:51:00.024 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 07:51:00.052 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 07:51:00.055 [info] index finished after resolve  [object Object] 
2025-07-14 07:51:00.056 [info] refresh page data from resolve listeners 0 987   
2025-07-14 07:51:00.544 [info] indexing created file components/logs/2025-07-13.components.log  [object Object] 
2025-07-14 07:51:00.548 [info] refresh page data from created listeners 0 988   
2025-07-14 08:03:33.267 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:03:33.309 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:03:33.313 [info] index finished after resolve  [object Object] 
2025-07-14 08:03:33.316 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:04:01.581 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:04:01.601 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:04:01.602 [info] index finished after resolve  [object Object] 
2025-07-14 08:04:01.603 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:04:32.229 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:04:32.257 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:04:32.259 [info] index finished after resolve  [object Object] 
2025-07-14 08:04:32.260 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:05:06.986 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:05:07.006 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:05:07.008 [info] index finished after resolve  [object Object] 
2025-07-14 08:05:07.009 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:05:39.362 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:05:39.388 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:05:39.391 [info] index finished after resolve  [object Object] 
2025-07-14 08:05:39.391 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:06:00.952 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-14 08:06:00.976 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-14 08:06:00.979 [info] index finished after resolve  [object Object] 
2025-07-14 08:06:00.980 [info] refresh page data from resolve listeners 0 988   
2025-07-14 08:06:03.337 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 08:06:03.487 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 08:06:03.492 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 08:06:03.545 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 08:06:03.574 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 08:06:03.657 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 08:06:03.838 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 08:06:03.847 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 08:06:03.859 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 08:06:03.862 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:27:28.271 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.273 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.275 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.276 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.278 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.279 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.295 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.296 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.297 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:28.298 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:27:29.643 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:27:30.072 [info] components database created cost 1 ms   
2025-07-14 18:27:30.073 [info] components index initializing...   
2025-07-14 18:27:30.597 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:27:30.648 [info] start to batch put pages: 5   
2025-07-14 18:27:30.650 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:27:30.652 [info] batch persist cost 5  4 
2025-07-14 18:27:30.664 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:27:30.670 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:27:30.715 [info] components index initialized, 988 files cost 644 ms   
2025-07-14 18:27:30.715 [info] refresh page data from init listeners 0 988   
2025-07-14 18:27:31.387 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:27:31.431 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:27:31.452 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:27:31.531 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:27:31.748 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:27:32.418 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:27:32.423 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:27:32.427 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:27:32.430 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:27:35.558 [info] indexing created file Home/components/未命名.components  [object Object] 
2025-07-14 18:27:35.560 [info] refresh page data from created listeners 0 989   
2025-07-14 18:27:35.994 [debug] ignore file modify evnet Home/Home.md   
2025-07-14 18:27:36.082 [info] trigger Home/Home.md resolve  [object Object] 
2025-07-14 18:27:36.083 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:36.084 [info] refresh page data from resolve listeners 0 989   
2025-07-14 18:27:36.630 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-14 18:27:36.647 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-14 18:27:36.649 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:36.650 [info] refresh page data from resolve listeners 0 989   
2025-07-14 18:27:37.075 [info] indexing created file 日记库/day/2025-07-14.md  [object Object] 
2025-07-14 18:27:37.075 [info] indexing created ignore file 日记库/day/2025-07-14.md   
2025-07-14 18:27:37.089 [info] trigger 日记库/day/2025-07-14.md resolve  [object Object] 
2025-07-14 18:27:37.092 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:37.093 [info] refresh page data from resolve listeners 0 990   
2025-07-14 18:27:37.948 [debug] ignore file modify evnet 日记库/template/fleeting_note.md   
2025-07-14 18:27:37.958 [info] trigger 日记库/template/fleeting_note.md resolve  [object Object] 
2025-07-14 18:27:37.960 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:37.961 [info] refresh page data from resolve listeners 0 990   
2025-07-14 18:27:39.100 [info] indexing created file 日记库/fleeting_notes/2025-07-14.md  [object Object] 
2025-07-14 18:27:39.100 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-14.md   
2025-07-14 18:27:39.196 [info] trigger 日记库/fleeting_notes/2025-07-14.md resolve  [object Object] 
2025-07-14 18:27:39.199 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:39.200 [info] refresh page data from resolve listeners 0 991   
2025-07-14 18:27:41.002 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:27:42.433 [info] indexing created file 学习库/Docker/未命名.md  [object Object] 
2025-07-14 18:27:42.433 [info] indexing created ignore file 学习库/Docker/未命名.md   
2025-07-14 18:27:42.435 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-14 18:27:42.448 [info] trigger 学习库/Docker/未命名.md resolve  [object Object] 
2025-07-14 18:27:42.449 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:42.450 [info] refresh page data from resolve listeners 0 992   
2025-07-14 18:27:42.865 [info] indexing created file 学习库/python笔记/项目管理.md  [object Object] 
2025-07-14 18:27:42.865 [info] indexing created ignore file 学习库/python笔记/项目管理.md   
2025-07-14 18:27:42.899 [info] trigger 学习库/python笔记/项目管理.md resolve  [object Object] 
2025-07-14 18:27:42.901 [info] index finished after resolve  [object Object] 
2025-07-14 18:27:42.902 [info] refresh page data from resolve listeners 0 993   
2025-07-14 18:45:16.931 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:16.960 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:17.053 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:45:17.055 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-14 18:45:18.567 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:18.616 [info] components database created cost 2 ms   
2025-07-14 18:45:18.616 [info] components index initializing...   
2025-07-14 18:45:18.654 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:45:18.815 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:45:18.877 [info] start to batch put pages: 5   
2025-07-14 18:45:18.892 [info] batch persist cost 5  15 
2025-07-14 18:45:18.972 [info] components index initialized, 993 files cost 358 ms   
2025-07-14 18:45:18.973 [info] refresh page data from init listeners 0 993   
2025-07-14 18:45:21.052 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:45:21.067 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:45:21.237 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:21.835 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:45:21.838 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:45:21.852 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:45:21.856 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:45:21.866 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:45:21.870 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:45:48.233 [info] components database created cost 1 ms   
2025-07-14 18:45:48.234 [info] components index initializing...   
2025-07-14 18:45:48.384 [info] start to batch put pages: 5   
2025-07-14 18:45:48.409 [info] batch persist cost 5  25 
2025-07-14 18:45:48.467 [info] components index initialized, 993 files cost 235 ms   
2025-07-14 18:45:48.468 [info] refresh page data from init listeners 0 993   
2025-07-14 18:45:49.921 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:50.307 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-14 18:45:50.911 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-14 18:45:50.914 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-14 18:45:50.923 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-14 18:45:50.927 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-14 18:46:09.911 [info] query  [object Object] 
2025-07-14 18:46:09.911 [info] query  [object Object] 
2025-07-14 18:46:09.911 [info] query  [object Object] 
2025-07-14 18:46:09.911 [info] query  [object Object] 
2025-07-14 18:46:09.912 [info] query  [object Object] 
2025-07-14 18:46:09.913 [info] query  [object Object] 
2025-07-14 18:46:09.914 [info] query  [object Object] 
2025-07-14 18:46:09.914 [info] query  [object Object] 
2025-07-14 18:46:09.914 [info] query  [object Object] 
2025-07-14 18:46:09.960 [info] query changed, compare cost 0ms, data length diff 0/65   
2025-07-14 18:46:09.987 [info] query changed, compare cost 0ms, data length diff 0/15   
2025-07-14 18:46:09.993 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-07-14 18:46:09.994 [info] query changed, compare cost 0ms, data length diff 0/65   
2025-07-14 20:11:11.776 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:11.796 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:11.802 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:11.810 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:24.754 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:24.807 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:24.811 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:24.812 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:27.026 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:27.130 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:27.149 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:27.151 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:29.051 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:29.185 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:29.189 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:29.190 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:31.985 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:32.078 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:32.082 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:32.084 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:48.029 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:48.149 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:48.173 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:48.175 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:11:55.285 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:11:55.348 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:11:55.350 [info] index finished after resolve  [object Object] 
2025-07-14 20:11:55.351 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:00.757 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:00.768 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:00.768 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:00.769 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:03.167 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:03.173 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:03.174 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:03.174 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:05.799 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:05.914 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:05.916 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:05.917 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:07.881 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:07.889 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:07.893 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:07.894 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:10.130 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:10.190 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:10.192 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:10.192 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:15.941 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:16.026 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:16.030 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:16.031 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:18.201 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:18.275 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:18.280 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:18.281 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:20.840 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:20.900 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:20.903 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:20.904 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:23.036 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:23.102 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:23.103 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:23.104 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:27.698 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:27.708 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:27.711 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:27.711 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:29.737 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:29.746 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:29.746 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:29.747 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:32.192 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:32.268 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:32.271 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:32.272 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:40.779 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:40.839 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:40.841 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:40.841 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:12:52.373 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:12:52.449 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:12:52.452 [info] index finished after resolve  [object Object] 
2025-07-14 20:12:52.453 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:00.511 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:00.519 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:00.520 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:00.521 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:04.581 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:04.590 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:04.591 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:04.592 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:07.644 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:07.679 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:07.682 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:07.683 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:10.299 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:10.310 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:10.311 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:10.312 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:49.383 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:49.405 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:49.407 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:49.408 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:52.719 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:52.726 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:52.727 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:52.729 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:55.351 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:55.361 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:55.362 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:55.362 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:13:58.644 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:13:58.709 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:13:58.712 [info] index finished after resolve  [object Object] 
2025-07-14 20:13:58.713 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:20.522 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:20.634 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:20.637 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:20.638 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:22.621 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:22.631 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:22.632 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:22.633 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:24.889 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:24.910 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:24.910 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:24.911 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:30.536 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:30.666 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:30.670 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:30.671 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:32.642 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:32.650 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:32.651 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:32.651 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:34.782 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:34.790 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:34.792 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:34.793 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:36.916 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:37.009 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:37.011 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:37.011 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:49.450 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:49.563 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:49.564 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:49.564 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:54.098 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:54.106 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:54.106 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:54.107 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:57.438 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:57.453 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:57.455 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:57.456 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:14:59.756 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:14:59.767 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:14:59.767 [info] index finished after resolve  [object Object] 
2025-07-14 20:14:59.768 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:01.866 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:01.877 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:01.878 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:01.879 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:05.252 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:05.261 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:05.262 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:05.263 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:08.998 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:09.070 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:09.072 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:09.073 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:30.098 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:30.180 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:30.182 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:30.182 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:32.134 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:32.270 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:32.301 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:32.302 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:35.597 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:35.663 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:35.667 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:35.668 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:38.119 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:38.129 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:38.130 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:38.131 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:15:52.084 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:15:52.093 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:15:52.095 [info] index finished after resolve  [object Object] 
2025-07-14 20:15:52.096 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:16:36.865 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:16:36.874 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:16:36.875 [info] index finished after resolve  [object Object] 
2025-07-14 20:16:36.876 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:16:38.938 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:16:38.966 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:16:38.967 [info] index finished after resolve  [object Object] 
2025-07-14 20:16:38.969 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:16:41.032 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:16:41.109 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:16:41.113 [info] index finished after resolve  [object Object] 
2025-07-14 20:16:41.114 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:16:51.466 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:16:51.548 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:16:51.551 [info] index finished after resolve  [object Object] 
2025-07-14 20:16:51.552 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:14.384 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:14.441 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:14.446 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:14.447 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:17.128 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:17.227 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:17.229 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:17.230 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:19.271 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:19.373 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:19.376 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:19.376 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:21.875 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:21.968 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:21.972 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:21.972 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:25.546 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:25.638 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:25.660 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:25.661 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:29.949 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:30.058 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:30.063 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:30.064 [info] refresh page data from resolve listeners 0 993   
2025-07-14 20:33:33.023 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 20:33:33.103 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 20:33:33.107 [info] index finished after resolve  [object Object] 
2025-07-14 20:33:33.108 [info] refresh page data from resolve listeners 0 993   
2025-07-14 21:16:13.199 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 21:16:13.323 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 21:16:13.361 [info] index finished after resolve  [object Object] 
2025-07-14 21:16:13.363 [info] refresh page data from resolve listeners 0 993   
2025-07-14 21:16:16.861 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 21:16:16.879 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 21:16:16.880 [info] index finished after resolve  [object Object] 
2025-07-14 21:16:16.881 [info] refresh page data from resolve listeners 0 993   
2025-07-14 21:16:21.212 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 21:16:21.223 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 21:16:21.227 [info] index finished after resolve  [object Object] 
2025-07-14 21:16:21.228 [info] refresh page data from resolve listeners 0 993   
2025-07-14 21:16:24.288 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-14 21:16:24.299 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-14 21:16:24.300 [info] index finished after resolve  [object Object] 
2025-07-14 21:16:24.300 [info] refresh page data from resolve listeners 0 993   
