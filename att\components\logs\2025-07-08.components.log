2025-07-08 16:17:41 [info] components database created cost 4 ms   
2025-07-08 16:17:41 [info] components index initializing...   
2025-07-08 16:17:43 [info] start to batch put pages: 5   
2025-07-08 16:17:43 [info] batch persist cost 5  180 
2025-07-08 16:17:43 [info] components index initialized, 832 files cost 2468 ms   
2025-07-08 16:17:43 [info] refresh page data from init listeners 0 832   
2025-07-08 16:17:46 [info] indexing created file components/logs/2025-07-08.components.log  [object Object] 
2025-07-08 16:17:46 [info] refresh page data from created listeners 0 833   
2025-07-08 16:17:46 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-08 16:17:47 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-07-08 16:17:47 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-07-08 16:17:47 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:47 [info] refresh page data from resolve listeners 0 833   
2025-07-08 16:17:47 [info] indexing created file 学习库/python笔记/Numpy/4. 数组的变形.md  [object Object] 
2025-07-08 16:17:47 [info] indexing created ignore file 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-07-08 16:17:47 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-07-08 16:17:47 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:47 [info] refresh page data from resolve listeners 0 834   
2025-07-08 16:17:48 [info] indexing created file 学习库/python笔记/Numpy/5. 数组的运算.md  [object Object] 
2025-07-08 16:17:48 [info] indexing created ignore file 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-07-08 16:17:48 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-07-08 16:17:48 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:48 [info] refresh page data from resolve listeners 0 835   
2025-07-08 16:17:48 [info] indexing created file 学习库/python笔记/Numpy/6. 数组的函数.md  [object Object] 
2025-07-08 16:17:48 [info] indexing created ignore file 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-07-08 16:17:48 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-07-08 16:17:48 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:48 [info] refresh page data from resolve listeners 0 836   
2025-07-08 16:17:49 [info] indexing created file 学习库/python笔记/Numpy/7. 布尔型数组.md  [object Object] 
2025-07-08 16:17:49 [info] indexing created ignore file 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-07-08 16:17:49 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-07-08 16:17:49 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:49 [info] refresh page data from resolve listeners 0 837   
2025-07-08 16:17:50 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-08 16:17:50 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-08 16:17:51 [info] ignore file modify evnet Home/components/view/remember.components   
2025-07-08 16:17:51 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-08 16:17:51 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-08 16:17:51 [info] indexing created file 学习库/python笔记/Numpy/8. 从数组到张量.md  [object Object] 
2025-07-08 16:17:51 [info] indexing created ignore file 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-07-08 16:17:51 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-07-08 16:17:51 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:51 [info] refresh page data from resolve listeners 0 838   
2025-07-08 16:17:51 [info] refresh page data from delete listeners 0 837   
2025-07-08 16:17:51 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-08 16:17:52 [info] indexing created file 学习库/Deep learning/Unet.md  [object Object] 
2025-07-08 16:17:52 [info] indexing created ignore file 学习库/Deep learning/Unet.md   
2025-07-08 16:17:52 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-08 16:17:52 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:52 [info] refresh page data from resolve listeners 0 838   
2025-07-08 16:17:52 [info] refresh page data from delete listeners 0 837   
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:52 [info] refresh page data from delete listeners 0 836   
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-07-08 16:17:52 [info] refresh page data from delete listeners 0 835   
2025-07-08 16:17:52 [info] refresh page data from delete listeners 0 834   
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:52 [info] refresh page data from delete listeners 0 833   
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:52 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-07-08 16:17:53 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123205552.png  [object Object] 
2025-07-08 16:17:53 [info] refresh page data from created listeners 0 834   
2025-07-08 16:17:53 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-07-08 16:17:54 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123221549.png  [object Object] 
2025-07-08 16:17:54 [info] refresh page data from created listeners 0 835   
2025-07-08 16:17:55 [info] indexing created file 学习库/Deep learning/attachments/目标检测的尺度.png  [object Object] 
2025-07-08 16:17:55 [info] refresh page data from created listeners 0 836   
2025-07-08 16:17:55 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:55 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-07-08 16:17:56 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123163728.png  [object Object] 
2025-07-08 16:17:56 [info] refresh page data from created listeners 0 837   
2025-07-08 16:17:56 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:59 [info] indexing created file 学习库/Deep learning/attachments/157381276-6e8429f3-c759-4aef-aea8-034438919457.png  [object Object] 
2025-07-08 16:17:59 [info] refresh page data from created listeners 0 838   
2025-07-08 16:17:59 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-08 16:17:59 [info] refresh page data from delete listeners 0 837   
2025-07-08 16:17:59 [info] indexing created file 学习库/Deep learning/YOLOv5.md  [object Object] 
2025-07-08 16:17:59 [info] indexing created ignore file 学习库/Deep learning/YOLOv5.md   
2025-07-08 16:17:59 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-08 16:17:59 [info] index finished after resolve  [object Object] 
2025-07-08 16:17:59 [info] refresh page data from resolve listeners 0 838   
2025-07-08 16:18:00 [info] indexing created file 学习库/Deep learning/self-attention.md  [object Object] 
2025-07-08 16:18:00 [info] indexing created ignore file 学习库/Deep learning/self-attention.md   
2025-07-08 16:18:00 [info] trigger 学习库/Deep learning/self-attention.md resolve  [object Object] 
2025-07-08 16:18:00 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:00 [info] refresh page data from resolve listeners 0 839   
2025-07-08 16:18:01 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-07-08 16:18:01 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-07-08 16:18:01 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:01 [info] refresh page data from resolve listeners 0 839   
2025-07-08 16:18:05 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型-2025-04-27-15-04-26.png  [object Object] 
2025-07-08 16:18:05 [info] refresh page data from created listeners 0 840   
2025-07-08 16:18:11 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_02_Linear_Model.pdf  [object Object] 
2025-07-08 16:18:11 [info] refresh page data from created listeners 0 841   
2025-07-08 16:18:16 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_03_Gradient_Descent.pdf  [object Object] 
2025-07-08 16:18:16 [info] refresh page data from created listeners 0 842   
2025-07-08 16:18:17 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-16-59-13.png  [object Object] 
2025-07-08 16:18:17 [info] refresh page data from created listeners 0 843   
2025-07-08 16:18:17 [info] ignore file modify evnet 学习库/python笔记/Numpy/2. 数组的创建.md   
2025-07-08 16:18:17 [info] trigger 学习库/python笔记/Numpy/2. 数组的创建.md resolve  [object Object] 
2025-07-08 16:18:17 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:17 [info] refresh page data from resolve listeners 0 843   
2025-07-08 16:18:18 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-01-56.png  [object Object] 
2025-07-08 16:18:18 [info] refresh page data from created listeners 0 844   
2025-07-08 16:18:19 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-02-56.png  [object Object] 
2025-07-08 16:18:19 [info] refresh page data from created listeners 0 845   
2025-07-08 16:18:20 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-17-12.png  [object Object] 
2025-07-08 16:18:20 [info] refresh page data from created listeners 0 846   
2025-07-08 16:18:21 [info] indexing created file components/logs/2025-04-27.components.log  [object Object] 
2025-07-08 16:18:21 [info] refresh page data from created listeners 0 847   
2025-07-08 16:18:23 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-04-28-16-12-33.png  [object Object] 
2025-07-08 16:18:23 [info] refresh page data from created listeners 0 848   
2025-07-08 16:18:24 [info] indexing created file components/logs/2025-04-28.components.log  [object Object] 
2025-07-08 16:18:24 [info] refresh page data from created listeners 0 849   
2025-07-08 16:18:30 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf  [object Object] 
2025-07-08 16:18:30 [info] refresh page data from created listeners 0 850   
2025-07-08 16:18:31 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md  [object Object] 
2025-07-08 16:18:31 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-07-08 16:18:31 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-07-08 16:18:31 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:31 [info] refresh page data from resolve listeners 0 851   
2025-07-08 16:18:32 [info] indexing created file 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md  [object Object] 
2025-07-08 16:18:32 [info] indexing created ignore file 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-08 16:18:32 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-08 16:18:32 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:32 [info] refresh page data from resolve listeners 0 852   
2025-07-08 16:18:37 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-46-41.png  [object Object] 
2025-07-08 16:18:37 [info] refresh page data from created listeners 0 853   
2025-07-08 16:18:40 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-52-04.png  [object Object] 
2025-07-08 16:18:40 [info] refresh page data from created listeners 0 854   
2025-07-08 16:18:41 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-07-08 16:18:41 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-07-08 16:18:41 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:41 [info] refresh page data from resolve listeners 0 854   
2025-07-08 16:18:42 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-21-37-16.png  [object Object] 
2025-07-08 16:18:42 [info] refresh page data from created listeners 0 855   
2025-07-08 16:18:42 [info] indexing created file 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md  [object Object] 
2025-07-08 16:18:42 [info] indexing created ignore file 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-07-08 16:18:42 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-07-08 16:18:42 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:42 [info] refresh page data from resolve listeners 0 856   
2025-07-08 16:18:44 [info] indexing created file components/logs/2025-04-29.components.log  [object Object] 
2025-07-08 16:18:44 [info] refresh page data from created listeners 0 857   
2025-07-08 16:18:47 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-08-58-20.png  [object Object] 
2025-07-08 16:18:47 [info] refresh page data from created listeners 0 858   
2025-07-08 16:18:47 [info] indexing created file 学习库/Anki/Deep learning/pytorch.md  [object Object] 
2025-07-08 16:18:47 [info] indexing created ignore file 学习库/Anki/Deep learning/pytorch.md   
2025-07-08 16:18:47 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-07-08 16:18:47 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:47 [info] refresh page data from resolve listeners 0 859   
2025-07-08 16:18:53 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-10-54-27.png  [object Object] 
2025-07-08 16:18:53 [info] refresh page data from created listeners 0 860   
2025-07-08 16:18:54 [info] indexing created file 学习库/Deep learning/pytorch/PDF/test.py  [object Object] 
2025-07-08 16:18:54 [info] refresh page data from created listeners 0 861   
2025-07-08 16:18:55 [info] indexing created file components/logs/2025-04-30.components.log  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from created listeners 0 862   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 861   
2025-07-08 16:18:55 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 860   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 859   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 858   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 857   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 856   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 855   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 854   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 853   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 852   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 851   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 850   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 849   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 848   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 847   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 846   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 845   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 844   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 843   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 842   
2025-07-08 16:18:55 [info] refresh page data from delete listeners 0 841   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-08 16:18:55 [info] index finished after resolve   
2025-07-08 16:18:55 [info] indexing created file 学习库/Deep learning/概念库/concatenate和add.md  [object Object] 
2025-07-08 16:18:55 [info] indexing created ignore file 学习库/Deep learning/概念库/concatenate和add.md   
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-08 16:18:55 [info] trigger 学习库/Deep learning/概念库/concatenate和add.md resolve  [object Object] 
2025-07-08 16:18:55 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:55 [info] refresh page data from resolve listeners 0 842   
2025-07-08 16:18:56 [info] indexing created file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md  [object Object] 
2025-07-08 16:18:56 [info] indexing created ignore file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-07-08 16:18:56 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-07-08 16:18:56 [info] index finished after resolve  [object Object] 
2025-07-08 16:18:56 [info] refresh page data from resolve listeners 0 843   
2025-07-08 16:18:56 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730111724.png  [object Object] 
2025-07-08 16:18:56 [info] refresh page data from created listeners 0 844   
2025-07-08 16:18:57 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730155551.png  [object Object] 
2025-07-08 16:18:57 [info] refresh page data from created listeners 0 845   
2025-07-08 16:18:58 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730110119.png  [object Object] 
2025-07-08 16:18:58 [info] refresh page data from created listeners 0 846   
2025-07-08 16:18:59 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730171955.png  [object Object] 
2025-07-08 16:18:59 [info] refresh page data from created listeners 0 847   
2025-07-08 16:18:59 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120114804.png  [object Object] 
2025-07-08 16:18:59 [info] refresh page data from created listeners 0 848   
2025-07-08 16:19:00 [info] indexing created file 学习库/Deep learning/概念库/attachments/tmp6067.png  [object Object] 
2025-07-08 16:19:00 [info] refresh page data from created listeners 0 849   
2025-07-08 16:19:01 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120113636.png  [object Object] 
2025-07-08 16:19:01 [info] refresh page data from created listeners 0 850   
2025-07-08 16:19:02 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730172720.png  [object Object] 
2025-07-08 16:19:02 [info] refresh page data from created listeners 0 851   
2025-07-08 16:19:03 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730145412.png  [object Object] 
2025-07-08 16:19:03 [info] refresh page data from created listeners 0 852   
2025-07-08 16:19:04 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120121420.png  [object Object] 
2025-07-08 16:19:04 [info] refresh page data from created listeners 0 853   
2025-07-08 16:19:05 [info] indexing created file 学习库/Deep learning/概念库/attachments/bda132ec94ebe94bd598dd9ae9fa04d5.gif  [object Object] 
2025-07-08 16:19:05 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-08 16:19:05 [info] refresh page data from created listeners 0 854   
2025-07-08 16:19:06 [info] indexing created file 学习库/Deep learning/概念库/attachments/3d8813449101f99a106a74cee1754184.gif  [object Object] 
2025-07-08 16:19:06 [info] refresh page data from created listeners 0 855   
2025-07-08 16:19:07 [info] indexing created file 学习库/Deep learning/概念库/attachments/3bb7fc0d90165becc7ed5033534fdac4.gif  [object Object] 
2025-07-08 16:19:07 [info] refresh page data from created listeners 0 856   
2025-07-08 16:19:09 [info] indexing created file 学习库/Deep learning/概念库/attachments/b6ca1dfbf6eacd96531ac2fd26a5eec9.gif  [object Object] 
2025-07-08 16:19:09 [info] refresh page data from created listeners 0 857   
2025-07-08 16:19:11 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101220640484.gif  [object Object] 
2025-07-08 16:19:11 [info] refresh page data from created listeners 0 858   
2025-07-08 16:19:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101220640484 - 副本.jpeg  [object Object] 
2025-07-08 16:19:12 [info] refresh page data from created listeners 0 859   
2025-07-08 16:19:15 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101212350912.gif  [object Object] 
2025-07-08 16:19:15 [info] refresh page data from created listeners 0 860   
2025-07-08 16:19:16 [info] indexing created file components/logs/2025-05-01.components.log  [object Object] 
2025-07-08 16:19:16 [info] refresh page data from created listeners 0 861   
2025-07-08 16:19:16 [info] indexing created file components/logs/2025-05-02.components.log  [object Object] 
2025-07-08 16:19:16 [info] refresh page data from created listeners 0 862   
2025-07-08 16:19:17 [info] indexing created file components/logs/2025-05-03.components.log  [object Object] 
2025-07-08 16:19:17 [info] refresh page data from created listeners 0 863   
2025-07-08 16:19:17 [info] indexing created file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md  [object Object] 
2025-07-08 16:19:17 [info] indexing created ignore file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-07-08 16:19:18 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:18 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:18 [info] refresh page data from resolve listeners 0 864   
2025-07-08 16:19:18 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md  [object Object] 
2025-07-08 16:19:18 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md   
2025-07-08 16:19:18 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:18 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:18 [info] refresh page data from resolve listeners 0 865   
2025-07-08 16:19:19 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md  [object Object] 
2025-07-08 16:19:19 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md   
2025-07-08 16:19:19 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:19 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:19 [info] refresh page data from resolve listeners 0 866   
2025-07-08 16:19:19 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md  [object Object] 
2025-07-08 16:19:19 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md   
2025-07-08 16:19:19 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:19 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:19 [info] refresh page data from resolve listeners 0 867   
2025-07-08 16:19:20 [info] indexing created file 学习库/Deep learning/训练实践/attachments/Netron 可视化-2025-05-04-09-22-21.png  [object Object] 
2025-07-08 16:19:20 [info] refresh page data from created listeners 0 868   
2025-07-08 16:19:20 [info] indexing created file 学习库/Deep learning/训练实践/Netron 可视化.md  [object Object] 
2025-07-08 16:19:20 [info] indexing created ignore file 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-07-08 16:19:20 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-07-08 16:19:20 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:20 [info] refresh page data from resolve listeners 0 869   
2025-07-08 16:19:21 [info] indexing created file components/logs/2025-05-05.components.log  [object Object] 
2025-07-08 16:19:21 [info] refresh page data from created listeners 0 870   
2025-07-08 16:19:21 [info] ignore file modify evnet 学习库/Deep learning/概念库/激活函数.md   
2025-07-08 16:19:21 [info] trigger 学习库/Deep learning/概念库/激活函数.md resolve  [object Object] 
2025-07-08 16:19:21 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:21 [info] refresh page data from resolve listeners 0 870   
2025-07-08 16:19:22 [info] indexing created file components/logs/2025-05-04.components.log  [object Object] 
2025-07-08 16:19:22 [info] refresh page data from created listeners 0 871   
2025-07-08 16:19:22 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md  [object Object] 
2025-07-08 16:19:22 [info] indexing created ignore file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md   
2025-07-08 16:19:22 [info] trigger 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:22 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:22 [info] refresh page data from resolve listeners 0 872   
2025-07-08 16:19:23 [info] indexing created file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md  [object Object] 
2025-07-08 16:19:23 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md   
2025-07-08 16:19:23 [info] trigger Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md resolve  [object Object] 
2025-07-08 16:19:23 [info] index finished after resolve  [object Object] 
2025-07-08 16:19:23 [info] refresh page data from resolve listeners 0 873   
2025-07-08 16:20:00 [info] indexing created file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md  [object Object] 
2025-07-08 16:20:00 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md   
2025-07-08 16:20:00 [info] trigger Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md resolve  [object Object] 
2025-07-08 16:20:00 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:00 [info] refresh page data from resolve listeners 0 874   
2025-07-08 16:20:01 [info] indexing created file Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md  [object Object] 
2025-07-08 16:20:01 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-07-08 16:20:01 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-07-08 16:20:01 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:01 [info] refresh page data from resolve listeners 0 875   
2025-07-08 16:20:02 [info] indexing created file components/logs/2025-05-06.components.log  [object Object] 
2025-07-08 16:20:02 [info] refresh page data from created listeners 0 876   
2025-07-08 16:20:02 [info] indexing created file components/logs/2025-05-07.components.log  [object Object] 
2025-07-08 16:20:02 [info] refresh page data from created listeners 0 877   
2025-07-08 16:20:03 [info] ignore file modify evnet 学习库/python笔记/数据容器/序列.md   
2025-07-08 16:20:03 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-07-08 16:20:03 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:03 [info] refresh page data from resolve listeners 0 877   
2025-07-08 16:20:04 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-28-47.png  [object Object] 
2025-07-08 16:20:04 [info] refresh page data from created listeners 0 878   
2025-07-08 16:20:05 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-42-18.png  [object Object] 
2025-07-08 16:20:05 [info] refresh page data from created listeners 0 879   
2025-07-08 16:20:05 [info] indexing created file 学习库/Deep learning/pytorch/attachments/6. 处理多维特征的输入-2025-05-08-20-42-32.png  [object Object] 
2025-07-08 16:20:05 [info] refresh page data from created listeners 0 880   
2025-07-08 16:20:06 [info] indexing created file 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md  [object Object] 
2025-07-08 16:20:06 [info] indexing created ignore file 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md   
2025-07-08 16:20:06 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入.md resolve  [object Object] 
2025-07-08 16:20:06 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:06 [info] refresh page data from resolve listeners 0 881   
2025-07-08 16:20:07 [info] indexing created file components/logs/2025-05-08.components.log  [object Object] 
2025-07-08 16:20:07 [info] refresh page data from created listeners 0 882   
2025-07-08 16:20:08 [info] indexing created file 学习库/linux/Attachments/wsl2-2025-04-25-09-00-55.png  [object Object] 
2025-07-08 16:20:08 [info] refresh page data from created listeners 0 883   
2025-07-08 16:20:08 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-07-08 16:20:08 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-07-08 16:20:08 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-07-08 16:20:08 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:08 [info] refresh page data from resolve listeners 0 883   
2025-07-08 16:20:09 [info] indexing created file 学习库/linux/Attachments/wsl2-2025-05-09-08-31-23.png  [object Object] 
2025-07-08 16:20:09 [info] refresh page data from created listeners 0 884   
2025-07-08 16:20:09 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-07-08 16:20:15 [info] indexing created file 学习库/Deep learning/pytorch/attachments/7. 加载数据集-2025-05-09-10-01-01.png  [object Object] 
2025-07-08 16:20:15 [info] refresh page data from created listeners 0 885   
2025-07-08 16:20:15 [info] indexing created file 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md  [object Object] 
2025-07-08 16:20:15 [info] indexing created ignore file 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-07-08 16:20:15 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-07-08 16:20:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-07-08 16:20:15 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:15 [info] refresh page data from resolve listeners 0 886   
2025-07-08 16:20:16 [info] indexing created file components/logs/2025-05-09.components.log  [object Object] 
2025-07-08 16:20:16 [info] refresh page data from created listeners 0 887   
2025-07-08 16:20:17 [info] indexing created file components/logs/2025-05-14.components.log  [object Object] 
2025-07-08 16:20:17 [info] refresh page data from created listeners 0 888   
2025-07-08 16:20:17 [info] indexing created file components/logs/2025-05-17.components.log  [object Object] 
2025-07-08 16:20:17 [info] refresh page data from created listeners 0 889   
2025-07-08 16:20:19 [info] indexing created file 工作库/项目/舌诊/attachments/Efficient KAN-2025-05-19-17-08-30.png  [object Object] 
2025-07-08 16:20:19 [info] refresh page data from created listeners 0 890   
2025-07-08 16:20:19 [info] indexing created file 工作库/项目/舌诊/Efficient KAN.md  [object Object] 
2025-07-08 16:20:19 [info] indexing created ignore file 工作库/项目/舌诊/Efficient KAN.md   
2025-07-08 16:20:19 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-07-08 16:20:19 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:19 [info] refresh page data from resolve listeners 0 891   
2025-07-08 16:20:20 [info] indexing created file components/logs/2025-05-19.components.log  [object Object] 
2025-07-08 16:20:20 [info] refresh page data from created listeners 0 892   
2025-07-08 16:20:21 [info] indexing created file 工作库/记录/2025_05_20.md  [object Object] 
2025-07-08 16:20:21 [info] indexing created ignore file 工作库/记录/2025_05_20.md   
2025-07-08 16:20:21 [info] trigger 工作库/记录/2025_05_20.md resolve  [object Object] 
2025-07-08 16:20:21 [info] index finished after resolve  [object Object] 
2025-07-08 16:20:21 [info] refresh page data from resolve listeners 0 893   
2025-07-08 16:20:21 [info] indexing created file components/logs/2025-05-20.components.log  [object Object] 
2025-07-08 16:20:21 [info] refresh page data from created listeners 0 894   
2025-07-08 16:20:22 [info] indexing created file components/logs/2025-05-21.components.log  [object Object] 
2025-07-08 16:20:22 [info] refresh page data from created listeners 0 895   
2025-07-08 16:20:22 [info] indexing created file components/logs/2025-05-26.components.log  [object Object] 
2025-07-08 16:20:22 [info] refresh page data from created listeners 0 896   
2025-07-08 16:21:02 [info] indexing created file 工作库/项目/舌诊/tensorRT.md  [object Object] 
2025-07-08 16:21:02 [info] indexing created ignore file 工作库/项目/舌诊/tensorRT.md   
2025-07-08 16:21:02 [info] trigger 工作库/项目/舌诊/tensorRT.md resolve  [object Object] 
2025-07-08 16:21:02 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:02 [info] refresh page data from resolve listeners 0 897   
2025-07-08 16:21:03 [info] indexing created file components/logs/2025-05-27.components.log  [object Object] 
2025-07-08 16:21:03 [info] refresh page data from created listeners 0 898   
2025-07-08 16:21:03 [info] indexing created file components/logs/2025-05-29.components.log  [object Object] 
2025-07-08 16:21:03 [info] refresh page data from created listeners 0 899   
2025-07-08 16:21:04 [info] indexing created file components/logs/2025-05-30.components.log  [object Object] 
2025-07-08 16:21:04 [info] refresh page data from created listeners 0 900   
2025-07-08 16:21:04 [info] indexing created file components/logs/2025-05-31.components.log  [object Object] 
2025-07-08 16:21:04 [info] refresh page data from created listeners 0 901   
2025-07-08 16:21:05 [info] ignore file modify evnet 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md   
2025-07-08 16:21:05 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-07-08 16:21:05 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:05 [info] refresh page data from resolve listeners 0 901   
2025-07-08 16:21:05 [info] indexing created file components/logs/2025-06-01.components.log  [object Object] 
2025-07-08 16:21:05 [info] refresh page data from created listeners 0 902   
2025-07-08 16:21:06 [info] indexing created file components/logs/2025-06-02.components.log  [object Object] 
2025-07-08 16:21:06 [info] refresh page data from created listeners 0 903   
2025-07-08 16:21:06 [info] indexing created file components/logs/2025-06-06.components.log  [object Object] 
2025-07-08 16:21:06 [info] refresh page data from created listeners 0 904   
2025-07-08 16:21:07 [info] indexing created file components/logs/2025-06-13.components.log  [object Object] 
2025-07-08 16:21:07 [info] refresh page data from created listeners 0 905   
2025-07-08 16:21:08 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-07-08 16:21:08 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-07-08 16:21:08 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:08 [info] refresh page data from resolve listeners 0 905   
2025-07-08 16:21:09 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md  [object Object] 
2025-07-08 16:21:09 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-07-08 16:21:09 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-07-08 16:21:09 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:09 [info] refresh page data from resolve listeners 0 906   
2025-07-08 16:21:09 [info] indexing created file components/logs/2025-06-17.components.log  [object Object] 
2025-07-08 16:21:09 [info] refresh page data from created listeners 0 907   
2025-07-08 16:21:10 [info] indexing created file 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md  [object Object] 
2025-07-08 16:21:10 [info] indexing created ignore file 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md   
2025-07-08 16:21:10 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md resolve  [object Object] 
2025-07-08 16:21:10 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:10 [info] refresh page data from resolve listeners 0 908   
2025-07-08 16:21:12 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-09-35-32.png  [object Object] 
2025-07-08 16:21:12 [info] refresh page data from created listeners 0 909   
2025-07-08 16:21:14 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-10-12-17.png  [object Object] 
2025-07-08 16:21:14 [info] refresh page data from created listeners 0 910   
2025-07-08 16:21:16 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-10-31-59.png  [object Object] 
2025-07-08 16:21:16 [info] refresh page data from created listeners 0 911   
2025-07-08 16:21:16 [info] indexing created file 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md  [object Object] 
2025-07-08 16:21:16 [info] indexing created ignore file 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-07-08 16:21:16 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-07-08 16:21:16 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-07-08 16:21:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-07-08 16:21:16 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:16 [info] refresh page data from resolve listeners 0 912   
2025-07-08 16:21:20 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-11-22-04.png  [object Object] 
2025-07-08 16:21:20 [info] refresh page data from created listeners 0 913   
2025-07-08 16:21:20 [info] indexing created file 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md  [object Object] 
2025-07-08 16:21:20 [info] indexing created ignore file 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-07-08 16:21:20 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-07-08 16:21:20 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:20 [info] refresh page data from resolve listeners 0 914   
2025-07-08 16:21:23 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-15-20-52.png  [object Object] 
2025-07-08 16:21:23 [info] refresh page data from created listeners 0 915   
2025-07-08 16:21:23 [info] indexing created file crossentropy_analysis.py  [object Object] 
2025-07-08 16:21:23 [info] refresh page data from created listeners 0 916   
2025-07-08 16:21:24 [info] indexing created file likelihood_demo.py  [object Object] 
2025-07-08 16:21:24 [info] refresh page data from created listeners 0 917   
2025-07-08 16:21:24 [info] indexing created file logits_explanation.py  [object Object] 
2025-07-08 16:21:24 [info] refresh page data from created listeners 0 918   
2025-07-08 16:21:25 [info] indexing created file nll_vs_crossentropy.py  [object Object] 
2025-07-08 16:21:25 [info] refresh page data from created listeners 0 919   
2025-07-08 16:21:25 [info] indexing created file comparison  [object Object] 
2025-07-08 16:21:25 [info] refresh page data from created listeners 0 920   
2025-07-08 16:21:35 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-16-47-41.png  [object Object] 
2025-07-08 16:21:35 [info] refresh page data from created listeners 0 921   
2025-07-08 16:21:37 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-12-30.png  [object Object] 
2025-07-08 16:21:37 [info] refresh page data from created listeners 0 922   
2025-07-08 16:21:38 [info] indexing created file tensor_reshape_demo.py  [object Object] 
2025-07-08 16:21:38 [info] refresh page data from created listeners 0 923   
2025-07-08 16:21:38 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-33-09.png  [object Object] 
2025-07-08 16:21:38 [info] refresh page data from created listeners 0 924   
2025-07-08 16:21:39 [info] indexing created file deep_network_flow.py  [object Object] 
2025-07-08 16:21:39 [info] refresh page data from created listeners 0 925   
2025-07-08 16:21:42 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-46-45.png  [object Object] 
2025-07-08 16:21:42 [info] refresh page data from created listeners 0 926   
2025-07-08 16:21:43 [info] indexing created file components/logs/2025-06-18.components.log  [object Object] 
2025-07-08 16:21:43 [info] refresh page data from created listeners 0 927   
2025-07-08 16:21:44 [info] indexing created file components/logs/2025-06-21.components.log  [object Object] 
2025-07-08 16:21:44 [info] refresh page data from created listeners 0 928   
2025-07-08 16:21:45 [info] indexing created file components/logs/2025-06-14.components.log  [object Object] 
2025-07-08 16:21:45 [info] refresh page data from created listeners 0 929   
2025-07-08 16:21:45 [info] indexing created file components/logs/2025-06-22.components.log  [object Object] 
2025-07-08 16:21:45 [info] refresh page data from created listeners 0 930   
2025-07-08 16:21:46 [info] indexing created file components/logs/2025-06-23.components.log  [object Object] 
2025-07-08 16:21:46 [info] refresh page data from created listeners 0 931   
2025-07-08 16:21:46 [info] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-07-08 16:21:46 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-07-08 16:21:46 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:46 [info] refresh page data from resolve listeners 0 931   
2025-07-08 16:21:47 [info] indexing created file components/logs/2025-06-25.components.log  [object Object] 
2025-07-08 16:21:47 [info] refresh page data from created listeners 0 932   
2025-07-08 16:21:48 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-07-08 16:21:48 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-07-08 16:21:48 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:48 [info] refresh page data from resolve listeners 0 932   
2025-07-08 16:21:48 [info] indexing created file components/logs/2025-06-29.components.log  [object Object] 
2025-07-08 16:21:48 [info] refresh page data from created listeners 0 933   
2025-07-08 16:21:49 [info] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-07-08 16:21:49 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-07-08 16:21:49 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:49 [info] refresh page data from resolve listeners 0 933   
2025-07-08 16:21:49 [info] indexing created file components/logs/2025-06-30.components.log  [object Object] 
2025-07-08 16:21:49 [info] refresh page data from created listeners 0 934   
2025-07-08 16:21:50 [info] indexing created file components/logs/2025-07-01.components.log  [object Object] 
2025-07-08 16:21:50 [info] refresh page data from created listeners 0 935   
2025-07-08 16:21:51 [info] indexing created file components/logs/2025-07-03.components.log  [object Object] 
2025-07-08 16:21:51 [info] refresh page data from created listeners 0 936   
2025-07-08 16:21:51 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/未命名.md  [object Object] 
2025-07-08 16:21:51 [info] indexing created ignore file 学习库/ROS/机器人学/机器人运动学/未命名.md   
2025-07-08 16:21:51 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-08 16:21:51 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-08 16:21:51 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-07-08 16:21:51 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-08 16:21:51 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-07-08 16:21:51 [info] trigger 学习库/ROS/机器人学/机器人运动学/未命名.md resolve  [object Object] 
2025-07-08 16:21:51 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:51 [info] refresh page data from resolve listeners 0 937   
2025-07-08 16:21:52 [info] indexing created file 工作库/项目/舌诊/人脸识别.md  [object Object] 
2025-07-08 16:21:52 [info] indexing created ignore file 工作库/项目/舌诊/人脸识别.md   
2025-07-08 16:21:52 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-08 16:21:52 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:52 [info] refresh page data from resolve listeners 0 938   
2025-07-08 16:21:52 [info] indexing created file components/logs/2025-07-04.components.log  [object Object] 
2025-07-08 16:21:52 [info] refresh page data from created listeners 0 939   
2025-07-08 16:21:53 [info] indexing created file components/logs/2025-07-05.components.log  [object Object] 
2025-07-08 16:21:53 [info] refresh page data from created listeners 0 940   
2025-07-08 16:21:53 [info] indexing created file 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md  [object Object] 
2025-07-08 16:21:53 [info] indexing created ignore file 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md   
2025-07-08 16:21:53 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-07-08 16:21:53 [info] index finished after resolve  [object Object] 
2025-07-08 16:21:53 [info] refresh page data from resolve listeners 0 941   
2025-07-08 16:21:56 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-21-37.png  [object Object] 
2025-07-08 16:21:56 [info] refresh page data from created listeners 0 942   
2025-07-08 16:21:57 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-41-38.png  [object Object] 
2025-07-08 16:21:57 [info] refresh page data from created listeners 0 943   
2025-07-08 16:22:01 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-46-01.png  [object Object] 
2025-07-08 16:22:01 [info] refresh page data from created listeners 0 944   
2025-07-08 16:22:02 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730110336.png  [object Object] 
2025-07-08 16:22:02 [info] refresh page data from created listeners 0 945   
2025-07-08 16:22:04 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-01-26.png  [object Object] 
2025-07-08 16:22:04 [info] refresh page data from created listeners 0 946   
2025-07-08 16:22:07 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-10-09-21.png  [object Object] 
2025-07-08 16:22:07 [info] refresh page data from created listeners 0 947   
2025-07-08 16:22:09 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-25-06.png  [object Object] 
2025-07-08 16:22:09 [info] refresh page data from created listeners 0 948   
2025-07-08 16:22:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-28-53.png  [object Object] 
2025-07-08 16:22:12 [info] refresh page data from created listeners 0 949   
2025-07-08 16:22:12 [info] indexing created file 学习库/Deep learning/概念库/卷积和转置卷积.md  [object Object] 
2025-07-08 16:22:12 [info] indexing created ignore file 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-07-08 16:22:12 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-08 16:22:12 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:22:12 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:12 [info] refresh page data from resolve listeners 0 950   
2025-07-08 16:22:13 [info] ignore file modify evnet 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md   
2025-07-08 16:22:13 [info] trigger 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md resolve  [object Object] 
2025-07-08 16:22:13 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:13 [info] refresh page data from resolve listeners 0 950   
2025-07-08 16:22:14 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样.md  [object Object] 
2025-07-08 16:22:14 [info] indexing created ignore file 学习库/Deep learning/概念库/上采样；下采样.md   
2025-07-08 16:22:14 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-08 16:22:14 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-08 16:22:14 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-08 16:22:14 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:14 [info] refresh page data from resolve listeners 0 951   
2025-07-08 16:22:14 [info] indexing created file 学习库/Deep learning/pytorch/LinearModel.py  [object Object] 
2025-07-08 16:22:14 [info] refresh page data from created listeners 0 952   
2025-07-08 16:22:16 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-16-36.png  [object Object] 
2025-07-08 16:22:16 [info] refresh page data from created listeners 0 953   
2025-07-08 16:22:17 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-21-40.png  [object Object] 
2025-07-08 16:22:17 [info] refresh page data from created listeners 0 954   
2025-07-08 16:22:18 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-16-48-37.png  [object Object] 
2025-07-08 16:22:18 [info] refresh page data from created listeners 0 955   
2025-07-08 16:22:19 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-17-18-19.png  [object Object] 
2025-07-08 16:22:19 [info] refresh page data from created listeners 0 956   
2025-07-08 16:22:20 [info] indexing created file 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md  [object Object] 
2025-07-08 16:22:20 [info] indexing created ignore file 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-07-08 16:22:20 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-07-08 16:22:20 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:20 [info] refresh page data from resolve listeners 0 957   
2025-07-08 16:22:21 [info] indexing created file components/logs/2025-07-07.components.log  [object Object] 
2025-07-08 16:22:21 [info] refresh page data from created listeners 0 958   
2025-07-08 16:22:21 [info] indexing created file 学习库/Latex/Latex 从入门到如土.md  [object Object] 
2025-07-08 16:22:21 [info] indexing created ignore file 学习库/Latex/Latex 从入门到如土.md   
2025-07-08 16:22:21 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-08 16:22:21 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:21 [info] refresh page data from resolve listeners 0 959   
2025-07-08 16:22:22 [info] ignore file modify evnet 日记库/读博.md   
2025-07-08 16:22:22 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-08 16:22:22 [info] index finished after resolve  [object Object] 
2025-07-08 16:22:22 [info] refresh page data from resolve listeners 0 959   
2025-07-08 16:22:25 [info] indexing created file components/logs/2025-07-06.components.log  [object Object] 
2025-07-08 16:22:25 [info] refresh page data from created listeners 0 960   
