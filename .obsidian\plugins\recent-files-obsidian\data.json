{"recentFiles": [{"basename": "常用Linux命令", "path": "学习库/linux/常用Linux命令.md"}, {"basename": "读博", "path": "日记库/读博.md"}, {"basename": "3. 反向传播（Back Propagation）", "path": "学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md"}, {"basename": "pytorch", "path": "学习库/Anki/Deep learning/pytorch.md"}, {"basename": "4. 使用pytorch实现线性模型", "path": "学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md"}, {"basename": "Lecture_04_Back_Propagation", "path": "学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf"}, {"basename": "Home", "path": "Home/Home.md"}, {"basename": "未命名", "path": "学习库/An<PERSON>/Artificial Intelligence/未命名.md"}, {"basename": "2. 梯度下降算法（Gradient Descent Algorithm）", "path": "学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md"}, {"basename": "<PERSON><PERSON>ji<PERSON>", "path": "copilot-custom-prompts/Emojify.md"}, {"basename": "Explain like I am 5", "path": "copilot-custom-prompts/Explain like I am 5.md"}, {"basename": "Fix grammar and spelling", "path": "copilot-custom-prompts/Fix grammar and spelling.md"}, {"basename": "未命名 1", "path": "学习库/An<PERSON>/Artificial Intelligence/未命名 1.md"}, {"basename": "1. 线性模型（Linear Model）", "path": "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md"}, {"basename": "1. 线性模型（Linear Model）-2025-08-03,09-08-04", "path": "学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-03,09-08-04.webp"}, {"basename": "3. 反向传播（Back Propagation）.excalidraw", "path": "学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md"}, {"basename": "一些基础", "path": "学习库/Deep learning/训练实践/一些基础.md"}, {"basename": "YOLOv5", "path": "学习库/Deep learning/YOLOv5.md"}, {"basename": "Unet", "path": "学习库/Deep learning/Unet.md"}, {"basename": "self-attention", "path": "学习库/Deep learning/self-attention.md"}, {"basename": "未命名", "path": "学习库/Deep learning/未命名.md"}, {"basename": "人脸识别", "path": "工作库/项目/舌诊/人脸识别.md"}, {"basename": "激活函数", "path": "学习库/Deep learning/概念库/激活函数.md"}, {"basename": "wsl2", "path": "学习库/linux/wsl2.md"}, {"basename": "Netron 可视化", "path": "学习库/Deep learning/训练实践/Netron 可视化.md"}, {"basename": "读取文件", "path": "学习库/Deep learning/训练实践/读取文件.md"}, {"basename": "图像格式", "path": "工作库/项目/舌诊/图像格式.md"}, {"basename": "2025_05_20", "path": "工作库/记录/2025_05_20.md"}, {"basename": "文件操作", "path": "学习库/python笔记/文件操作/文件操作.md"}, {"basename": "QuickAdd", "path": "学习库/obsidian 插件使用说明/QuickAdd.md"}, {"basename": "mcp", "path": "学习库/Artificial Intelligence/mcp.md"}, {"basename": "mcp.excalidraw", "path": "学习库/Artificial Intelligence/excalidraw/mcp.excalidraw.md"}, {"basename": "2 核心语法", "path": "学习库/c/2 核心语法.md"}, {"basename": "学习库", "path": "学习库/学习库.components"}, {"basename": "工作库", "path": "工作库/工作库.components"}, {"basename": "Latex 从入门到如土", "path": "学习库/Latex/Latex 从入门到如土.md"}, {"basename": "5 SPI", "path": "学习库/stm32/5 SPI.md"}, {"basename": "语义分割", "path": "学习库/Deep learning/概念库/语义分割.md"}, {"basename": "3 串口", "path": "学习库/stm32/3 串口.md"}, {"basename": "SPI", "path": "学习库/Anki/stm32/SPI.md"}, {"basename": "2 GPIO", "path": "学习库/stm32/2 GPIO.md"}, {"basename": "项目管理", "path": "学习库/python笔记/项目管理.md"}, {"basename": "未命名", "path": "学习库/Docker/未命名.md"}, {"basename": "公共基础", "path": "学习库/二级/公共基础.md"}, {"basename": "数据结构", "path": "学习库/二级/数据结构.md"}, {"basename": "数据库", "path": "学习库/二级/数据库.md"}, {"basename": "Drawing 2025-03-18 15.06.26.excalidraw", "path": "学习库/二级/Drawing 2025-03-18 15.06.26.excalidraw.md"}, {"basename": "Drawing 2025-03-18 15.23.48.excalidraw", "path": "学习库/二级/Drawing 2025-03-18 15.23.48.excalidraw.md"}, {"basename": "Drawing 2025-03-18 16.01.06.excalidraw", "path": "学习库/二级/Drawing 2025-03-18 16.01.06.excalidraw.md"}, {"basename": "7 指针", "path": "学习库/c/7 指针.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}