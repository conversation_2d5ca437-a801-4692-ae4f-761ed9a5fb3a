# 功能
用于快速添加信息，采用模板新建笔记和添加宏命令
# 使用方法
## 基本使用
在设置中可以添加四种模式的命令
![[QuickAdd-1.png]]
- Template:：创建一个模板笔记
- Capture：捕获思考，想法，添加进一个文件
- Macro：快速创建一个宏
- Multi：创建一个命令组
### Capture：快速捕捉灵感
快速捕捉想法或者灵感，将其放置在当前文件夹或选择一个特定的文件中
![[QuickAdd-2.png]]
### Template：利用模板创建笔记
![[QuickAdd-3.png]]
### Multi：命令组
创建了一个命令组，相当于对多个命令进行了分组，这样当命令比较多的时候，便于进行分类查找
### Marco：宏
通过宏执行自动化的命令
- 宏命令管理![[QuickAdd-4.png]]
- 宏命令设置![[QuickAdd-5.png]]
- 宏命令配置![[QuickAdd-6.png]]

# 我的设置

`````ad-abstract
title: 快速添加 python 笔记
collapse: open
````ad-bug
title: 语法
```
{{VALUE:这个内容的标题是什么呢}}
{{VALUE:请输入它的语法格式把}} 
{{value:请给出具体的代码示例吧}}

```
````
`````

```ad-summary
title: 快速创建excalidraw文件
- **功能**：可以创建一个同名的绘图文件在同级的 excalidraw 文件夹中
- **实现方法**：通过 quickadd 的 Marco 功能运行在 att 目录下的 quickadd 中的 creatExcalidraw.js 脚本
```


# 参考方法
- https://sspai.com/post/69375

