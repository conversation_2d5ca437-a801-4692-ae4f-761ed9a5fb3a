2025-04-26 09:29:17 [info] components database created cost 37 ms   
2025-04-26 09:29:17 [info] components index initializing...   
2025-04-26 09:29:18 [info] start to batch put pages: 2   
2025-04-26 09:29:19 [info] batch persist cost 2  144 
2025-04-26 09:29:19 [info] components index initialized, 827 files cost 1794 ms   
2025-04-26 09:29:19 [info] refresh page data from init listeners 0 827   
2025-04-26 09:29:20 [info] indexing created file components/logs/2025-04-26.components.log  [object Object] 
2025-04-26 09:29:20 [info] refresh page data from created listeners 0 828   
2025-04-26 09:29:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:04 [info] components database created cost 0 ms   
2025-04-26 09:34:04 [info] components index initializing...   
2025-04-26 09:34:04 [info] start to batch put pages: 1   
2025-04-26 09:34:04 [info] batch persist cost 1  2 
2025-04-26 09:34:06 [info] components index initialized, 828 files cost 1438 ms   
2025-04-26 09:34:06 [info] refresh page data from init listeners 0 828   
2025-04-26 09:34:07 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:07 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:08 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:34:08 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:34:08 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:34:08 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:34:35 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:35 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:36 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:34:36 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:34:36 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:34:36 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:34:36 [info] components database created cost 11 ms   
2025-04-26 09:34:36 [info] components index initializing...   
2025-04-26 09:34:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:34:36 [info] start to batch put pages: 5   
2025-04-26 09:34:37 [info] batch persist cost 5  1014 
2025-04-26 09:34:37 [info] components index initialized, 828 files cost 1320 ms   
2025-04-26 09:34:37 [info] refresh page data from init listeners 0 828   
2025-04-26 09:34:37 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:34:37 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:34:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:38 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:34:38 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:34:38 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:34:38 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:34:39 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:34:39 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:35:39 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:35:39 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:35:39 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:35:39 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:35:39 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:39:12 [info] refresh page data from delete listeners 0 827   
2025-04-26 09:39:12 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-26 09:42:18 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-26 09:42:18 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:42:18 [info] components database created cost 46 ms   
2025-04-26 09:42:18 [info] components index initializing...   
2025-04-26 09:42:18 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:42:20 [info] start to batch put pages: 5   
2025-04-26 09:42:20 [info] batch persist cost 5  106 
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:42:20 [info] components index initialized, 827 files cost 1581 ms   
2025-04-26 09:42:20 [info] refresh page data from init listeners 0 827   
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:42:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:42:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 09:42:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 09:44:05 [info] indexing created file 工作库/项目/大队长手把手带你发论文_V2_工房版.pdf  [object Object] 
2025-04-26 09:44:05 [info] refresh page data from created listeners 0 828   
2025-04-26 09:45:40 [info] refresh page data from delete listeners 0 830   
2025-04-26 09:45:54 [info] indexing created file 学习库/心得/大队长手把手带你发论文_V2_工房版.pdf  [object Object] 
2025-04-26 09:45:54 [info] refresh page data from created listeners 0 831   
2025-04-26 09:46:31 [info] indexing created file 学习库/心得/未命名.md  [object Object] 
2025-04-26 09:46:31 [info] indexing created ignore file 学习库/心得/未命名.md   
2025-04-26 09:46:31 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-26 09:46:31 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-26 09:46:31 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-26 09:46:31 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-26 09:46:31 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-26 09:46:31 [info] trigger 学习库/心得/未命名.md resolve  [object Object] 
2025-04-26 09:46:31 [info] index finished after resolve  [object Object] 
2025-04-26 09:46:31 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:46:45 [info] refresh page data from delete listeners 0 831   
2025-04-26 09:46:45 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-26 09:46:45 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-26 09:46:45 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-26 09:46:45 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-26 09:46:45 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-26 09:46:47 [info] indexing created file 学习库/心得/缝合笔记.md  [object Object] 
2025-04-26 09:46:47 [info] indexing created ignore file 学习库/心得/缝合笔记.md   
2025-04-26 09:46:47 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:46:47 [info] index finished after resolve  [object Object] 
2025-04-26 09:46:47 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:46:57 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:46:57 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:46:57 [info] index finished after resolve  [object Object] 
2025-04-26 09:46:57 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:56:12 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:56:29 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:56:29 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:56:29 [info] index finished after resolve  [object Object] 
2025-04-26 09:56:29 [info] refresh page data from resolve listeners 0 829   
2025-04-26 09:56:54 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:56:54 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:56:54 [info] index finished after resolve  [object Object] 
2025-04-26 09:56:54 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:57:05 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:57:05 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:57:05 [info] index finished after resolve  [object Object] 
2025-04-26 09:57:05 [info] refresh page data from resolve listeners 0 829   
2025-04-26 09:57:30 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:57:30 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:57:30 [info] index finished after resolve  [object Object] 
2025-04-26 09:57:30 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:57:55 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:57:55 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:57:55 [info] index finished after resolve  [object Object] 
2025-04-26 09:57:55 [info] refresh page data from resolve listeners 0 829   
2025-04-26 09:58:07 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:58:07 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:58:07 [info] index finished after resolve  [object Object] 
2025-04-26 09:58:07 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:58:18 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:58:18 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:58:18 [info] index finished after resolve  [object Object] 
2025-04-26 09:58:18 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:58:29 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:58:29 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:58:29 [info] index finished after resolve  [object Object] 
2025-04-26 09:58:29 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:58:57 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:58:57 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:58:57 [info] index finished after resolve  [object Object] 
2025-04-26 09:58:57 [info] refresh page data from resolve listeners 0 832   
2025-04-26 09:59:15 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:59:15 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:59:15 [info] index finished after resolve  [object Object] 
2025-04-26 09:59:15 [info] refresh page data from resolve listeners 0 829   
2025-04-26 09:59:17 [info] ignore file modify evnet 学习库/心得/缝合笔记.md   
2025-04-26 09:59:17 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-04-26 09:59:17 [info] index finished after resolve  [object Object] 
2025-04-26 09:59:17 [info] refresh page data from resolve listeners 0 829   
2025-04-26 10:53:31 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 10:53:31 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 10:53:31 [info] index finished after resolve  [object Object] 
2025-04-26 10:53:31 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:26:34 [info] components database created cost 1 ms   
2025-04-26 14:26:34 [info] components index initializing...   
2025-04-26 14:26:34 [info] start to batch put pages: 6   
2025-04-26 14:26:35 [info] batch persist cost 6  1448 
2025-04-26 14:26:35 [info] components index initialized, 829 files cost 1569 ms   
2025-04-26 14:26:35 [info] refresh page data from init listeners 0 829   
2025-04-26 14:26:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 14:26:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-26 14:26:37 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-26 14:26:37 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-26 14:26:37 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-26 14:26:37 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-26 14:27:27 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:27 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:27 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:27 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:34 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:34 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:34 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:34 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:36 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:36 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:36 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:36 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:39 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:39 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:39 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:39 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:44 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:44 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:44 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:44 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:45 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:45 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:45 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:45 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:50 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:50 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:50 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:50 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:27:57 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:27:57 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:27:57 [info] index finished after resolve  [object Object] 
2025-04-26 14:27:57 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:00 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:00 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:00 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:00 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:01 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:01 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:01 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:01 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:05 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:05 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:05 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:05 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:11 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:11 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:11 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:11 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:14 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:14 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:14 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:14 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:17 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:17 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:17 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:17 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:19 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:19 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:19 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:19 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:25 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:25 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:25 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:25 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:28 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:28 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:28 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:28 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:28:31 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:28:31 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:28:31 [info] index finished after resolve  [object Object] 
2025-04-26 14:28:31 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:29:09 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:29:09 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:29:09 [info] index finished after resolve  [object Object] 
2025-04-26 14:29:09 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:29:14 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:29:14 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:29:14 [info] index finished after resolve  [object Object] 
2025-04-26 14:29:14 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:29:20 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:29:20 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:29:20 [info] index finished after resolve  [object Object] 
2025-04-26 14:29:20 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:29:22 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:29:22 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:29:22 [info] index finished after resolve  [object Object] 
2025-04-26 14:29:22 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:30:53 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:30:53 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:30:53 [info] index finished after resolve  [object Object] 
2025-04-26 14:30:53 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:30:59 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:30:59 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:30:59 [info] index finished after resolve  [object Object] 
2025-04-26 14:30:59 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:31:02 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:31:02 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:31:02 [info] index finished after resolve  [object Object] 
2025-04-26 14:31:02 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:31:10 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:31:10 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:31:10 [info] index finished after resolve  [object Object] 
2025-04-26 14:31:10 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:31:40 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:31:41 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:31:41 [info] index finished after resolve  [object Object] 
2025-04-26 14:31:41 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:40:26 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:40:26 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:40:26 [info] index finished after resolve  [object Object] 
2025-04-26 14:40:26 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:40:30 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:40:31 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:40:31 [info] index finished after resolve  [object Object] 
2025-04-26 14:40:31 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:40:41 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:40:41 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:40:41 [info] index finished after resolve  [object Object] 
2025-04-26 14:40:41 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:49:21 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:49:21 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:49:21 [info] index finished after resolve  [object Object] 
2025-04-26 14:49:21 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:53:11 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:53:11 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:53:11 [info] index finished after resolve  [object Object] 
2025-04-26 14:53:11 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:55:01 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:55:01 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:55:01 [info] index finished after resolve  [object Object] 
2025-04-26 14:55:01 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:55:53 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:55:53 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:55:53 [info] index finished after resolve  [object Object] 
2025-04-26 14:55:53 [info] refresh page data from resolve listeners 0 829   
2025-04-26 14:59:11 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 14:59:11 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 14:59:11 [info] index finished after resolve  [object Object] 
2025-04-26 14:59:11 [info] refresh page data from resolve listeners 0 829   
2025-04-26 15:01:07 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 15:01:07 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 15:01:07 [info] index finished after resolve  [object Object] 
2025-04-26 15:01:07 [info] refresh page data from resolve listeners 0 829   
2025-04-26 15:02:46 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 15:02:46 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 15:02:46 [info] index finished after resolve  [object Object] 
2025-04-26 15:02:46 [info] refresh page data from resolve listeners 0 829   
2025-04-26 15:03:05 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 15:03:05 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 15:03:05 [info] index finished after resolve  [object Object] 
2025-04-26 15:03:05 [info] refresh page data from resolve listeners 0 829   
2025-04-26 15:03:24 [info] indexing created file 学习库/python笔记/Numpy/4. 数组的变形.md  [object Object] 
2025-04-26 15:03:24 [info] indexing created ignore file 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:03:24 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:03:24 [info] index finished after resolve  [object Object] 
2025-04-26 15:03:24 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:03:58 [info] ignore file modify evnet 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-04-26 15:03:58 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-04-26 15:03:58 [info] index finished after resolve  [object Object] 
2025-04-26 15:03:58 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:13:30 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:13:30 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:13:30 [info] index finished after resolve  [object Object] 
2025-04-26 15:13:30 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:17:14 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:17:14 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:17:14 [info] index finished after resolve  [object Object] 
2025-04-26 15:17:14 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:17:18 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:17:18 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:17:18 [info] index finished after resolve  [object Object] 
2025-04-26 15:17:18 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:17:24 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:17:24 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:17:24 [info] index finished after resolve  [object Object] 
2025-04-26 15:17:24 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:19:51 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:19:51 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:19:51 [info] index finished after resolve  [object Object] 
2025-04-26 15:19:51 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:21:42 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:21:42 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:21:42 [info] index finished after resolve  [object Object] 
2025-04-26 15:21:42 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:23:04 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:23:04 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:23:04 [info] index finished after resolve  [object Object] 
2025-04-26 15:23:04 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:23:27 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:23:27 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:23:27 [info] index finished after resolve  [object Object] 
2025-04-26 15:23:27 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:26:31 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:26:31 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:26:31 [info] index finished after resolve  [object Object] 
2025-04-26 15:26:31 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:28:51 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:28:51 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:28:51 [info] index finished after resolve  [object Object] 
2025-04-26 15:28:51 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:36:03 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:36:03 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:36:03 [info] index finished after resolve  [object Object] 
2025-04-26 15:36:03 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:39:04 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:39:04 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:39:04 [info] index finished after resolve  [object Object] 
2025-04-26 15:39:04 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:41:43 [info] ignore file modify evnet 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-04-26 15:41:43 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-04-26 15:41:43 [info] index finished after resolve  [object Object] 
2025-04-26 15:41:43 [info] refresh page data from resolve listeners 0 830   
2025-04-26 15:43:00 [info] indexing created file 学习库/python笔记/Numpy/5. 数组的运算.md  [object Object] 
2025-04-26 15:43:00 [info] indexing created ignore file 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-04-26 15:43:00 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-04-26 15:43:00 [info] index finished after resolve  [object Object] 
2025-04-26 15:43:00 [info] refresh page data from resolve listeners 0 831   
2025-04-26 15:47:34 [info] ignore file modify evnet 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-04-26 15:47:34 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-04-26 15:47:34 [info] index finished after resolve  [object Object] 
2025-04-26 15:47:34 [info] refresh page data from resolve listeners 0 831   
2025-04-26 16:09:44 [info] indexing created file 学习库/python笔记/Numpy/6. 数组的函数.md  [object Object] 
2025-04-26 16:09:44 [info] indexing created ignore file 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:09:44 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:09:44 [info] index finished after resolve  [object Object] 
2025-04-26 16:09:44 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:13:46 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:13:46 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:13:46 [info] index finished after resolve  [object Object] 
2025-04-26 16:13:46 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:13:47 [info] ignore file modify evnet 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-04-26 16:13:47 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-04-26 16:13:47 [info] index finished after resolve  [object Object] 
2025-04-26 16:13:47 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:13:59 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:13:59 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:13:59 [info] index finished after resolve  [object Object] 
2025-04-26 16:13:59 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:14:10 [info] ignore file modify evnet 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-04-26 16:14:10 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-04-26 16:14:10 [info] index finished after resolve  [object Object] 
2025-04-26 16:14:10 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:14:16 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:14:16 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:14:16 [info] index finished after resolve  [object Object] 
2025-04-26 16:14:16 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:14:21 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:14:21 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:14:21 [info] index finished after resolve  [object Object] 
2025-04-26 16:14:21 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:14:23 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:14:23 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:14:23 [info] index finished after resolve  [object Object] 
2025-04-26 16:14:23 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:19:51 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:19:51 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:19:51 [info] index finished after resolve  [object Object] 
2025-04-26 16:19:51 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:20:32 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:20:32 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:20:32 [info] index finished after resolve  [object Object] 
2025-04-26 16:20:32 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:22:55 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:22:55 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:22:55 [info] index finished after resolve  [object Object] 
2025-04-26 16:22:55 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:24:33 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:24:33 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:24:33 [info] index finished after resolve  [object Object] 
2025-04-26 16:24:33 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:26:23 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:26:23 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:26:23 [info] index finished after resolve  [object Object] 
2025-04-26 16:26:23 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:27:51 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:27:51 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:27:51 [info] index finished after resolve  [object Object] 
2025-04-26 16:27:51 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:28:45 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:28:45 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:28:45 [info] index finished after resolve  [object Object] 
2025-04-26 16:28:45 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:37:07 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:37:07 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:37:07 [info] index finished after resolve  [object Object] 
2025-04-26 16:37:07 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:40:48 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:40:48 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:40:48 [info] index finished after resolve  [object Object] 
2025-04-26 16:40:48 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:43:03 [info] ignore file modify evnet 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-04-26 16:43:03 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-04-26 16:43:03 [info] index finished after resolve  [object Object] 
2025-04-26 16:43:03 [info] refresh page data from resolve listeners 0 832   
2025-04-26 16:43:33 [info] indexing created file 学习库/python笔记/Numpy/7. 布尔型数组.md  [object Object] 
2025-04-26 16:43:33 [info] indexing created ignore file 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-04-26 16:43:33 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-04-26 16:43:33 [info] index finished after resolve  [object Object] 
2025-04-26 16:43:33 [info] refresh page data from resolve listeners 0 833   
2025-04-26 16:48:16 [info] ignore file modify evnet 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-04-26 16:48:16 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-04-26 16:48:16 [info] index finished after resolve  [object Object] 
2025-04-26 16:48:16 [info] refresh page data from resolve listeners 0 833   
2025-04-26 16:54:55 [info] ignore file modify evnet 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-04-26 16:54:55 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-04-26 16:54:55 [info] index finished after resolve  [object Object] 
2025-04-26 16:54:55 [info] refresh page data from resolve listeners 0 833   
2025-04-26 17:04:59 [info] ignore file modify evnet 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-04-26 17:04:59 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-04-26 17:04:59 [info] index finished after resolve  [object Object] 
2025-04-26 17:04:59 [info] refresh page data from resolve listeners 0 833   
2025-04-26 17:05:38 [info] ignore file modify evnet 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-04-26 17:05:38 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-04-26 17:05:38 [info] index finished after resolve  [object Object] 
2025-04-26 17:05:38 [info] refresh page data from resolve listeners 0 833   
2025-04-26 17:06:57 [info] indexing created file 学习库/python笔记/Numpy/8. 从数组到张量.md  [object Object] 
2025-04-26 17:06:57 [info] indexing created ignore file 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:06:57 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:06:57 [info] index finished after resolve  [object Object] 
2025-04-26 17:06:57 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:11:44 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:11:44 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:11:44 [info] index finished after resolve  [object Object] 
2025-04-26 17:11:44 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:14:34 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:14:34 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:14:34 [info] index finished after resolve  [object Object] 
2025-04-26 17:14:34 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:14:59 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:14:59 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:14:59 [info] index finished after resolve  [object Object] 
2025-04-26 17:14:59 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:03 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:03 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:03 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:03 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:05 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:05 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:05 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:05 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:06 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:06 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:06 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:06 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:08 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:08 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:08 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:08 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:09 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:09 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:09 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:09 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:11 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:11 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:11 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:11 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:13 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:13 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:13 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:13 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:15:16 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:15:16 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:15:16 [info] index finished after resolve  [object Object] 
2025-04-26 17:15:16 [info] refresh page data from resolve listeners 0 834   
2025-04-26 17:16:23 [info] ignore file modify evnet 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-04-26 17:16:23 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-04-26 17:16:23 [info] index finished after resolve  [object Object] 
2025-04-26 17:16:23 [info] refresh page data from resolve listeners 0 834   
2025-04-26 21:01:37 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:01:37 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-26 21:02:18 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:02:18 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:02:18 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-04-26 21:02:18 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:02:18 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-04-26 21:02:18 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-04-26 21:02:18 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:02:18 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-04-26 21:02:18 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:02:18 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-04-26 21:02:51 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:03:22 [info] refresh page data from rename listeners 0 834   
2025-04-26 21:03:27 [info] indexing created file 学习库/Deep learning/未命名.md  [object Object] 
2025-04-26 21:03:27 [info] indexing created ignore file 学习库/Deep learning/未命名.md   
2025-04-26 21:03:27 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-26 21:03:27 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-26 21:03:27 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-26 21:03:27 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-26 21:03:27 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-26 21:03:27 [info] trigger 学习库/Deep learning/未命名.md resolve  [object Object] 
2025-04-26 21:03:27 [info] index finished after resolve  [object Object] 
2025-04-26 21:03:27 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:03:37 [info] refresh page data from rename listeners 0 835   
2025-04-26 21:03:37 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-26 21:03:37 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-26 21:03:37 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-26 21:03:37 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-26 21:03:37 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-26 21:03:43 [info] ignore file modify evnet 学习库/Deep learning/self-attention.md   
2025-04-26 21:03:43 [info] trigger 学习库/Deep learning/self-attention.md resolve  [object Object] 
2025-04-26 21:03:43 [info] index finished after resolve  [object Object] 
2025-04-26 21:03:43 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:03:48 [info] ignore file modify evnet 学习库/Deep learning/self-attention.md   
2025-04-26 21:03:48 [info] trigger 学习库/Deep learning/self-attention.md resolve  [object Object] 
2025-04-26 21:03:48 [info] index finished after resolve  [object Object] 
2025-04-26 21:03:48 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:03:50 [info] ignore file modify evnet 学习库/Deep learning/self-attention.md   
2025-04-26 21:03:50 [info] trigger 学习库/Deep learning/self-attention.md resolve  [object Object] 
2025-04-26 21:03:50 [info] index finished after resolve  [object Object] 
2025-04-26 21:03:50 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:08 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:08 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:08 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:08 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:16 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:16 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:16 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:16 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:44 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:44 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:44 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:44 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:50 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:50 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:50 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:50 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:53 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:53 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:53 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:53 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:37:55 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:37:55 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:37:55 [info] index finished after resolve  [object Object] 
2025-04-26 21:37:55 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:38:11 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:38:11 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:38:11 [info] index finished after resolve  [object Object] 
2025-04-26 21:38:11 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:38:14 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:38:14 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:38:14 [info] index finished after resolve  [object Object] 
2025-04-26 21:38:14 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:38:28 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:38:28 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:38:28 [info] index finished after resolve  [object Object] 
2025-04-26 21:38:28 [info] refresh page data from resolve listeners 0 835   
2025-04-26 21:38:30 [info] ignore file modify evnet 学习库/Deep learning/概念库/Attention.md   
2025-04-26 21:38:30 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-04-26 21:38:30 [info] index finished after resolve  [object Object] 
2025-04-26 21:38:30 [info] refresh page data from resolve listeners 0 835   
