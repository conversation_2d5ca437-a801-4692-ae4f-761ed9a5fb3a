2025-03-30 20:14:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:14:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:14:20 [info] index finished after resolve  [object Object] 
2025-03-30 20:14:20 [info] refresh page data from resolve listeners 0 685   
2025-03-30 20:14:20 [info] indexing created file components/logs/2025-03-30.components.log  [object Object] 
2025-03-30 20:14:20 [info] refresh page data from created listeners 0 686   
2025-03-30 20:17:07 [info] components database created cost 20 ms   
2025-03-30 20:17:07 [info] components index initializing...   
2025-03-30 20:17:09 [info] start to batch put pages: 6   
2025-03-30 20:17:09 [info] batch persist cost 6  762 
2025-03-30 20:17:10 [info] components index initialized, 686 files cost 2717 ms   
2025-03-30 20:17:10 [info] refresh page data from init listeners 0 686   
2025-03-30 20:17:10 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:17:11 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:17:12 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-30 20:17:12 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-30 20:17:12 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-30 20:17:12 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-30 20:18:41 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:18:41 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:18:41 [info] index finished after resolve  [object Object] 
2025-03-30 20:18:41 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:18:44 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:18:44 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:18:44 [info] index finished after resolve  [object Object] 
2025-03-30 20:18:44 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:18:49 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:18:49 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:18:49 [info] index finished after resolve  [object Object] 
2025-03-30 20:18:49 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:18:52 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:18:52 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:18:52 [info] index finished after resolve  [object Object] 
2025-03-30 20:18:52 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:18:57 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:18:57 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:18:57 [info] index finished after resolve  [object Object] 
2025-03-30 20:18:57 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:14 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:19:14 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:19:14 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:15 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:17 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:19:17 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:19:17 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:17 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:22 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:19:22 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:19:22 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:22 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:24 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-30 20:19:24 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-30 20:19:24 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:24 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:55 [info] ignore file modify evnet 学习库/python笔记/数据容器/字典.md   
2025-03-30 20:19:55 [info] trigger 学习库/python笔记/数据容器/字典.md resolve  [object Object] 
2025-03-30 20:19:55 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:55 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:19:58 [info] ignore file modify evnet 学习库/python笔记/数据容器/字典.md   
2025-03-30 20:19:58 [info] trigger 学习库/python笔记/数据容器/字典.md resolve  [object Object] 
2025-03-30 20:19:58 [info] index finished after resolve  [object Object] 
2025-03-30 20:19:58 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:24 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:24 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:24 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:24 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:26 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:26 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:26 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:26 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:33 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:33 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:33 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:33 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:36 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:36 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:36 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:36 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:45 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:45 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:45 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:45 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:48 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:48 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:48 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:48 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:20:52 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:20:52 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:20:52 [info] index finished after resolve  [object Object] 
2025-03-30 20:20:52 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:13 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:13 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:13 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:13 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:15 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:15 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:15 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:15 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:17 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:18 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:18 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:18 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:36 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:36 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:36 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:36 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:40 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:40 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:40 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:40 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:44 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:44 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:44 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:44 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:49 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:49 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:49 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:49 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:51 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:51 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:51 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:51 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:53 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:53 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:21:59 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:21:59 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:21:59 [info] index finished after resolve  [object Object] 
2025-03-30 20:21:59 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:22:01 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:22:01 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:22:01 [info] index finished after resolve  [object Object] 
2025-03-30 20:22:01 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:22:03 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-30 20:22:03 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-30 20:22:03 [info] index finished after resolve  [object Object] 
2025-03-30 20:22:03 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:22:17 [info] components database created cost 14 ms   
2025-03-30 20:22:17 [info] components index initializing...   
2025-03-30 20:22:18 [info] start to batch put pages: 5   
2025-03-30 20:22:19 [info] batch persist cost 5  549 
2025-03-30 20:22:19 [info] components index initialized, 686 files cost 2392 ms   
2025-03-30 20:22:19 [info] refresh page data from init listeners 0 686   
2025-03-30 20:22:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:22:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:22:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-30 20:22:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-30 20:22:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-30 20:22:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-30 20:22:56 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-30 20:22:56 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-30 20:22:56 [info] index finished after resolve  [object Object] 
2025-03-30 20:22:56 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:22:59 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-30 20:22:59 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-30 20:22:59 [info] index finished after resolve  [object Object] 
2025-03-30 20:22:59 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:23:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:23:19 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:19 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:23:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:23:25 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:25 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:46 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:23:46 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:23:46 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:46 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:48 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:23:48 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:23:48 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:48 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:55 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:23:55 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:23:55 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:55 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:23:58 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:23:58 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:23:58 [info] index finished after resolve  [object Object] 
2025-03-30 20:23:58 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:24:15 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:24:15 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:24:15 [info] index finished after resolve  [object Object] 
2025-03-30 20:24:15 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:24:20 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-30 20:24:20 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-30 20:24:20 [info] index finished after resolve  [object Object] 
2025-03-30 20:24:20 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:24:39 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-30 20:24:39 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-30 20:24:39 [info] index finished after resolve  [object Object] 
2025-03-30 20:24:39 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:24:41 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-30 20:24:41 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-30 20:24:41 [info] index finished after resolve  [object Object] 
2025-03-30 20:24:41 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:24:46 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-30 20:24:46 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-30 20:24:46 [info] index finished after resolve  [object Object] 
2025-03-30 20:24:46 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:25:07 [info] ignore file modify evnet 学习库/ROS/机器人学/机器人运动学/导论.md   
2025-03-30 20:25:07 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-30 20:25:07 [info] index finished after resolve  [object Object] 
2025-03-30 20:25:07 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:26:22 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:26:22 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-30 20:26:23 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-30 20:26:23 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-30 20:27:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:27:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:27:20 [info] index finished after resolve  [object Object] 
2025-03-30 20:27:20 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:27:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:27:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:27:23 [info] index finished after resolve  [object Object] 
2025-03-30 20:27:23 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:27:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:27:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:27:26 [info] index finished after resolve  [object Object] 
2025-03-30 20:27:26 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:27:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:27:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:27:57 [info] index finished after resolve  [object Object] 
2025-03-30 20:27:57 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:28:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:28:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:28:00 [info] index finished after resolve  [object Object] 
2025-03-30 20:28:00 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:29:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:29:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:29:04 [info] index finished after resolve  [object Object] 
2025-03-30 20:29:04 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:29:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:29:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:29:58 [info] index finished after resolve  [object Object] 
2025-03-30 20:29:58 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:01 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:01 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:08 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:08 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:11 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:11 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:13 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:13 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:15 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:15 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:17 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:17 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:21 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:21 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:23 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:23 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:30:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:30:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:30:27 [info] index finished after resolve  [object Object] 
2025-03-30 20:30:27 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:03 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:03 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:05 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:05 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:09 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:09 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:11 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:11 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:13 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:13 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:16 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:16 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:18 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:18 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:22 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:22 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:25 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:25 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:28 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:28 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:32 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:32 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:34 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:34 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:39 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:39 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:42 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:42 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:44 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:44 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:48 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:48 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:51 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:51 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:55 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:55 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:35:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:35:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:35:59 [info] index finished after resolve  [object Object] 
2025-03-30 20:35:59 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:36:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:36:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:36:07 [info] index finished after resolve  [object Object] 
2025-03-30 20:36:07 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:06 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:07 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:07 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:07 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:09 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:09 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:09 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:09 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:12 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:12 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:12 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:12 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:16 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:16 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:16 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:16 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:22 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:23 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:23 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:23 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:50 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:50 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:50 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:50 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:38:53 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:38:53 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:38:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:38:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:22 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:22 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:22 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:22 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:24 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:25 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:25 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:25 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:27 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:27 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:27 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:27 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:29 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:29 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:29 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:29 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:44 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:44 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:44 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:44 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:47 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:47 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:47 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:47 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:50 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:50 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:50 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:50 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:53 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:39:53 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:39:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:39:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:39:59 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:00 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:00 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:00 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:02 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:02 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:02 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:02 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:05 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:05 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:05 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:05 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:07 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:07 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:07 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:07 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:27 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:27 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:27 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:27 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:29 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:29 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:29 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:29 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:38 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:38 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:38 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:38 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:50 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:50 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:50 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:50 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:52 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:52 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:52 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:52 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:40:56 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:40:56 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:40:56 [info] index finished after resolve  [object Object] 
2025-03-30 20:40:56 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:01 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:01 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:01 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:01 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:05 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:05 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:05 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:05 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:08 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:08 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:08 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:08 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:14 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:14 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:14 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:14 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:21 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:21 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:21 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:21 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:33 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:33 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:33 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:33 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:41 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:41 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:41 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:41 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:49 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:49 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:49 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:49 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:41:53 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:41:53 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:41:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:41:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:09 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:10 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:10 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:10 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:27 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:27 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:27 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:27 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:31 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:31 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:31 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:31 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:38 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:38 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:38 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:38 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:40 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:40 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:40 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:40 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:52 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:52 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:52 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:52 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:42:55 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:42:55 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:42:55 [info] index finished after resolve  [object Object] 
2025-03-30 20:42:55 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:00 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:00 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:00 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:00 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:02 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:03 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:03 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:03 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:13 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:13 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:13 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:13 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:17 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:17 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:17 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:17 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:20 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:20 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:20 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:20 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:26 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:26 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:26 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:26 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:29 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:29 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:29 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:29 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:38 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:38 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:38 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:38 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:42 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:42 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:42 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:42 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:43:57 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:43:57 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:43:57 [info] index finished after resolve  [object Object] 
2025-03-30 20:43:57 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:00 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:01 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:01 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:01 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:03 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:03 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:03 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:03 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:11 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:11 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:11 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:11 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:14 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:14 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:14 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:14 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:16 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:17 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:17 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:17 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:19 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:19 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:19 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:19 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:44:26 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:44:26 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:44:26 [info] index finished after resolve  [object Object] 
2025-03-30 20:44:26 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:50:49 [info] components database created cost 4 ms   
2025-03-30 20:50:49 [info] components index initializing...   
2025-03-30 20:50:49 [info] start to batch put pages: 5   
2025-03-30 20:50:51 [info] batch persist cost 5  1917 
2025-03-30 20:50:51 [info] components index initialized, 686 files cost 2504 ms   
2025-03-30 20:50:51 [info] refresh page data from init listeners 0 686   
2025-03-30 20:50:52 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:50:53 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-30 20:50:53 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-30 20:50:53 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-30 20:50:53 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-30 20:50:53 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-30 20:54:44 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:54:44 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:54:44 [info] index finished after resolve  [object Object] 
2025-03-30 20:54:44 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:54:50 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:54:50 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:54:50 [info] index finished after resolve  [object Object] 
2025-03-30 20:54:50 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:54:53 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:54:53 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:54:53 [info] index finished after resolve  [object Object] 
2025-03-30 20:54:53 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:54:55 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:54:55 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:54:55 [info] index finished after resolve  [object Object] 
2025-03-30 20:54:55 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:54:58 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:54:58 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:54:58 [info] index finished after resolve  [object Object] 
2025-03-30 20:54:58 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:01 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:01 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:01 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:01 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:03 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:03 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:03 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:03 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:06 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:06 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:06 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:06 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:08 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:08 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:08 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:08 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:12 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:12 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:12 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:12 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:15 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:15 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:15 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:15 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:17 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:17 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:17 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:17 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:22 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:22 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:22 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:22 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:25 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:25 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:25 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:25 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:28 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:28 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:28 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:28 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:30 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:30 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:30 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:30 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:32 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:32 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:32 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:32 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:36 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:36 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:36 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:36 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:38 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:38 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:38 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:38 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:55:40 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-03-30 20:55:40 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-03-30 20:55:40 [info] index finished after resolve  [object Object] 
2025-03-30 20:55:40 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:05 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:05 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:07 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:07 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:13 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:13 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:19 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:19 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:22 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:22 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:24 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:24 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:26 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:26 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:28 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:28 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:31 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:31 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:36 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:36 [info] refresh page data from resolve listeners 0 686   
2025-03-30 20:56:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 20:56:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 20:56:38 [info] index finished after resolve  [object Object] 
2025-03-30 20:56:38 [info] refresh page data from resolve listeners 0 686   
2025-03-30 21:00:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 21:00:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 21:00:42 [info] index finished after resolve  [object Object] 
2025-03-30 21:00:42 [info] refresh page data from resolve listeners 0 686   
2025-03-30 21:00:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 21:00:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 21:00:46 [info] index finished after resolve  [object Object] 
2025-03-30 21:00:46 [info] refresh page data from resolve listeners 0 686   
2025-03-30 21:00:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 21:00:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 21:00:49 [info] index finished after resolve  [object Object] 
2025-03-30 21:00:49 [info] refresh page data from resolve listeners 0 686   
2025-03-30 21:00:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-30 21:00:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-30 21:00:51 [info] index finished after resolve  [object Object] 
2025-03-30 21:00:51 [info] refresh page data from resolve listeners 0 686   
