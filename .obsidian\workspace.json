{"main": {"id": "9a92b85be99e6512", "type": "split", "children": [{"id": "a140d4f0cca3ee9c", "type": "tabs", "children": [{"id": "5c3e65087019d3a5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/linux/常用Linux命令.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "常用Linux命令"}}, {"id": "a02ba399b7b74704", "type": "leaf", "state": {"type": "pdf", "state": {"file": "学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf", "page": 28, "left": -18, "top": 544, "zoom": 0.934375}, "icon": "lucide-file-text", "title": "Lecture_04_Back_Propagation"}}]}], "direction": "vertical"}, "left": {"id": "ccafe5c1edb39392", "type": "split", "children": [{"id": "ff985ebb9e64a67a", "type": "tabs", "children": [{"id": "0471e214e11a0e3e", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "df897e713d50ad26", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "byModifiedTime"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "a54f5d1b66aa9af0", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "ea807e711cd67a77", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 244.5}, "right": {"id": "589685af648bfa09", "type": "split", "children": [{"id": "ebf068d98b8f5d7e", "type": "tabs", "dimension": 67.8921568627451, "children": [{"id": "503da8d536964a67", "type": "leaf", "state": {"type": "backlink", "state": {"file": "学习库/linux/常用Linux命令.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "常用Linux命令 的反向链接列表"}}, {"id": "4eb73336137b77fc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "dd44a90d63472d6d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3c9fff9b1aac3ed1", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}, {"id": "01747ffb46c2e2db", "type": "leaf", "state": {"type": "comment-view", "state": {}, "icon": "highlighter", "title": "<PERSON><PERSON><PERSON>"}}, {"id": "30bb109630e92a1d", "type": "leaf", "state": {"type": "chinese-calendar-view", "state": {}, "icon": "calendar-with-checkmark", "title": "日历"}}, {"id": "d6fd2d8d71bdc2af", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "SpacedRepIcon", "title": "笔记复习序列"}}]}, {"id": "362ffd7addd9ee9d", "type": "tabs", "dimension": 32.1078431372549, "children": [{"id": "4734f418b64acbcd", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}, {"id": "6b8468d83e031abe", "type": "leaf", "state": {"type": "outline", "state": {"file": "文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "算法卡 -CIOU（完全交并比，complete intersection over union）- 的大纲"}}]}], "direction": "horizontal", "width": 221.5}, "left-ribbon": {"hiddenItems": {"hi-note:HiNote": false, "pdf-plus:PDF++: Toggle auto-copy": false, "pdf-plus:PDF++: Toggle auto-focus": false, "pdf-plus:PDF++: Toggle auto-paste": false, "copilot:Open Copilot Chat": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "obsidian-spaced-repetition:复习卡片": false, "obsidian-kanban:创建新看板": false, "omnisearch:Omnisearch": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false}}, "active": "5c3e65087019d3a5", "lastOpenFiles": ["components/logs/2025-08-04.components.log", "日记库/读博.md", "学习库/linux/常用Linux命令.md", "学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md", "学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-12-57.webp", "学习库/Anki/Deep learning/pytorch.md", "学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-06-10.webp", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-05-21.webp", "学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf", "Home/Home.md", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,09-08-11.webp", "学习库/An<PERSON>/Artificial Intelligence/未命名.md", "copilot-custom-prompts/Emojify.md", "copilot-custom-prompts/Explain like I am 5.md", "copilot-custom-prompts/Fix grammar and spelling.md", "学习库/An<PERSON>/Artificial Intelligence/未命名 1.md", "学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md", "学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-03,09-08-04.webp", "学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md", "学习库/Deep learning/训练实践/一些基础.md", "components/logs/2025-08-03.components.log", "学习库/Deep learning/YOLOv5.md", "学习库/Deep learning/Unet.md", "学习库/Deep learning/self-attention.md", "学习库/Deep learning/未命名.md", "工作库/项目/舌诊/人脸识别.md", "学习库/Deep learning/概念库/激活函数.md", "学习库/linux/wsl2.md", "学习库/Deep learning/训练实践/Netron 可视化.md", "学习库/Deep learning/训练实践/读取文件.md", "工作库/项目/舌诊/图像格式.md", "工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_51.png", "工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_44.png", "工作库/项目/舌诊/attachments/人脸识别-{DATE}-{TIME}.png", "工作库/项目/舌诊/attachments/人脸识别-{DATE}-{TIME}.webp", "学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802085952_097.svg", "学习库/Deep learning/pytorch/excalidraw/attachments", "components/logs/2025-08-02.components.log", "学习库/Artificial Intelligence/excalidraw", "学习库/Deep learning/pytorch/excalidraw", "att/QuickAdd/scripts/creatExcalidraw.js", "components/logs/2025-08-01.components.log", "components/logs/2025-07-31.components.log", "学习库/Artificial Intelligence"]}