{"main": {"id": "9a92b85be99e6512", "type": "split", "children": [{"id": "a140d4f0cca3ee9c", "type": "tabs", "children": [{"id": "f2e8821914c1bd54", "type": "leaf", "state": {"type": "components-file-view", "state": {"file": "Wiki Home.components"}, "icon": "gantt-chart", "title": "Wiki Home"}}, {"id": "574c55dbcf445fe7", "type": "leaf", "state": {"type": "components-file-view", "state": {"file": "学习库/linux/00-导航.components"}, "icon": "gantt-chart", "title": "00-导航"}}, {"id": "dbecf0e81b0ded0d", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/linux/01-文件与目录操作.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "01-文件与目录操作"}}, {"id": "03468c4ec9150d6f", "type": "leaf", "state": {"type": "markdown", "state": {"file": "学习库/linux/03-系统信息与进程管理.md", "mode": "source", "source": false, "backlinks": false}, "icon": "lucide-file", "title": "03-系统信息与进程管理"}}], "currentTab": 3}], "direction": "vertical"}, "left": {"id": "ccafe5c1edb39392", "type": "split", "children": [{"id": "ff985ebb9e64a67a", "type": "tabs", "children": [{"id": "0471e214e11a0e3e", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "df897e713d50ad26", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "byModifiedTime"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "a54f5d1b66aa9af0", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "ea807e711cd67a77", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "clock", "title": "Recent Files"}}]}], "direction": "horizontal", "width": 244.5}, "right": {"id": "589685af648bfa09", "type": "split", "children": [{"id": "ebf068d98b8f5d7e", "type": "tabs", "dimension": 67.8921568627451, "children": [{"id": "503da8d536964a67", "type": "leaf", "state": {"type": "backlink", "state": {"file": "学习库/linux/03-系统信息与进程管理.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "03-系统信息与进程管理 的反向链接列表"}}, {"id": "4eb73336137b77fc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "dd44a90d63472d6d", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3c9fff9b1aac3ed1", "type": "leaf", "state": {"type": "copilot-chat-view", "state": {}, "icon": "message-square", "title": "Copilot"}}, {"id": "01747ffb46c2e2db", "type": "leaf", "state": {"type": "comment-view", "state": {}, "icon": "highlighter", "title": "<PERSON><PERSON><PERSON>"}}, {"id": "30bb109630e92a1d", "type": "leaf", "state": {"type": "chinese-calendar-view", "state": {}, "icon": "calendar-with-checkmark", "title": "日历"}}, {"id": "d6fd2d8d71bdc2af", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "SpacedRepIcon", "title": "笔记复习序列"}}], "currentTab": 6}, {"id": "362ffd7addd9ee9d", "type": "tabs", "dimension": 32.1078431372549, "children": [{"id": "4734f418b64acbcd", "type": "leaf", "state": {"type": "advanced-tables-toolbar", "state": {}, "icon": "spreadsheet", "title": "Advanced Tables"}}, {"id": "6b8468d83e031abe", "type": "leaf", "state": {"type": "outline", "state": {"file": "文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "算法卡 -CIOU（完全交并比，complete intersection over union）- 的大纲"}}]}], "direction": "horizontal", "width": 254.5}, "left-ribbon": {"hiddenItems": {"hi-note:HiNote": false, "pdf-plus:PDF++: Toggle auto-copy": false, "pdf-plus:PDF++: Toggle auto-focus": false, "pdf-plus:PDF++: Toggle auto-paste": false, "copilot:Open Copilot Chat": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian-excalidraw-plugin:新建绘图文件": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "obsidian-spaced-repetition:复习卡片": false, "obsidian-kanban:创建新看板": false, "omnisearch:Omnisearch": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false}}, "active": "03468c4ec9150d6f", "lastOpenFiles": ["学习库/linux/00-Linux学习指南.md", "学习库/linux/06-Vim编辑器.md", "学习库/linux/07-软件包管理与系统维护.md", "学习库/linux/00-导航.components", "学习库/linux/01-文件与目录操作.md", "学习库/linux/05-权限与安全管理.md", "学习库/linux/04-网络命令.md", "学习库/linux/03-系统信息与进程管理.md", "学习库/linux/02-文件内容查看与处理.md", "att/picture/running.gif", "att/picture/running.gif.crdownload", "att/picture/dog.gif", "att/img/dog.gif", "日记库/fleeting_notes/2025-07-19.md", "日记库/fleeting_notes/2025-07-14.md", "Wiki Home.components", "Home.components", "Area 导航.md", "学习库/linux/20250804092802 .md", "学习库/template/通用学习模板.md", "20250804092635 .md", "Home/Home.md", "att/未命名.md", "att/components/250804_w15814452020@gmail.com_components_license.md", "components/logs/2025-08-04.components.log", "components/logs", "components", "att/components 1/logs/2025-08-04.components.log", "att/components 1/logs", "att/components 1", "学习库/linux/常用Linux命令_优化版.md", "学习库/linux/常用Linux命令.md", "日记库/读博.md", "学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md", "学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-12-57.webp", "学习库/Anki/Deep learning/pytorch.md", "学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-06-10.webp", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-05-21.webp", "学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,09-08-11.webp", "学习库/An<PERSON>/Artificial Intelligence/未命名.md", "学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-03,09-08-04.webp", "工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_51.png", "工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_44.png"]}