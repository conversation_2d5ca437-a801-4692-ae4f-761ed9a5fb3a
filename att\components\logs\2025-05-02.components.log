2025-05-02 08:16:21 [info] indexing created file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md  [object Object] 
2025-05-02 08:16:21 [info] indexing created ignore file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:16:21 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:16:21 [info] index finished after resolve  [object Object] 
2025-05-02 08:16:21 [info] refresh page data from resolve listeners 0 866   
2025-05-02 08:16:21 [info] indexing created file components/logs/2025-05-02.components.log  [object Object] 
2025-05-02 08:16:21 [info] refresh page data from created listeners 0 867   
2025-05-02 08:16:22 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-02 08:16:22 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-02 08:16:22 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-02 08:16:22 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-02 08:16:22 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-02 08:16:43 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:16:43 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:16:43 [info] index finished after resolve  [object Object] 
2025-05-02 08:16:43 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:17:00 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:17:00 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:17:00 [info] index finished after resolve  [object Object] 
2025-05-02 08:17:00 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:17:31 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:17:31 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:17:31 [info] index finished after resolve  [object Object] 
2025-05-02 08:17:31 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:17:49 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:17:49 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:17:49 [info] index finished after resolve  [object Object] 
2025-05-02 08:17:49 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:18:05 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:18:05 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:18:05 [info] index finished after resolve  [object Object] 
2025-05-02 08:18:05 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:18:20 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:18:20 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:18:20 [info] index finished after resolve  [object Object] 
2025-05-02 08:18:20 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:18:35 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:18:35 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:18:35 [info] index finished after resolve  [object Object] 
2025-05-02 08:18:35 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:18:58 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-02 08:18:58 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-02 08:18:58 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-02 08:18:58 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-02 08:18:58 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-02 08:19:51 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:19:51 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:19:51 [info] index finished after resolve  [object Object] 
2025-05-02 08:19:51 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:20:35 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:20:35 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:20:35 [info] index finished after resolve  [object Object] 
2025-05-02 08:20:35 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:20:42 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:20:42 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:20:42 [info] index finished after resolve  [object Object] 
2025-05-02 08:20:42 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:20:56 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:20:56 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:20:56 [info] index finished after resolve  [object Object] 
2025-05-02 08:20:56 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:21:08 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:21:08 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:21:08 [info] index finished after resolve  [object Object] 
2025-05-02 08:21:08 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:21:22 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:21:22 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:21:22 [info] index finished after resolve  [object Object] 
2025-05-02 08:21:22 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:21:27 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:21:27 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:21:27 [info] index finished after resolve  [object Object] 
2025-05-02 08:21:27 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:21:31 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:21:31 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:21:31 [info] index finished after resolve  [object Object] 
2025-05-02 08:21:31 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:22:04 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:22:04 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:22:04 [info] index finished after resolve  [object Object] 
2025-05-02 08:22:04 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:22:20 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:22:20 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:22:20 [info] index finished after resolve  [object Object] 
2025-05-02 08:22:20 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:22:24 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:22:24 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:22:24 [info] index finished after resolve  [object Object] 
2025-05-02 08:22:24 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:22:40 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:22:40 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:22:40 [info] index finished after resolve  [object Object] 
2025-05-02 08:22:40 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:23:21 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:23:21 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:23:21 [info] index finished after resolve  [object Object] 
2025-05-02 08:23:21 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:23:37 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:23:37 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:23:37 [info] index finished after resolve  [object Object] 
2025-05-02 08:23:37 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:23:49 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:23:49 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:23:49 [info] index finished after resolve  [object Object] 
2025-05-02 08:23:49 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:24:07 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:24:07 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:24:07 [info] index finished after resolve  [object Object] 
2025-05-02 08:24:07 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:25:13 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:25:13 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:25:13 [info] index finished after resolve  [object Object] 
2025-05-02 08:25:13 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:25:16 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:25:16 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:25:16 [info] index finished after resolve  [object Object] 
2025-05-02 08:25:16 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:25:32 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:25:32 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:25:32 [info] index finished after resolve  [object Object] 
2025-05-02 08:25:32 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:25:49 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:25:49 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:25:49 [info] index finished after resolve  [object Object] 
2025-05-02 08:25:49 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:27:17 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:27:17 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:27:17 [info] index finished after resolve  [object Object] 
2025-05-02 08:27:17 [info] refresh page data from resolve listeners 0 867   
2025-05-02 08:27:28 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-05-02 08:27:28 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-05-02 08:27:28 [info] index finished after resolve  [object Object] 
2025-05-02 08:27:28 [info] refresh page data from resolve listeners 0 867   
