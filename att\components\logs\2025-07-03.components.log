2025-07-03 07:43:42.440 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-03 07:43:42.463 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-03 07:43:42.544 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-03 07:43:42.545 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-03 07:43:42.719 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-03 07:43:44.068 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-07-03 07:43:44.068 [info] components database created cost 1 ms   
2025-07-03 07:43:44.069 [info] components index initializing...   
2025-07-03 07:43:44.328 [info] start to batch put pages: 5   
2025-07-03 07:43:44.346 [info] batch persist cost 5  18 
2025-07-03 07:43:44.347 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-03 07:43:44.355 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-03 07:43:44.375 [info] components index initialized, 934 files cost 308 ms   
2025-07-03 07:43:44.375 [info] refresh page data from init listeners 0 934   
2025-07-03 07:43:45.178 [info] indexing created file components/logs/2025-07-03.components.log  [object Object] 
2025-07-03 07:43:45.179 [info] refresh page data from created listeners 0 935   
2025-07-03 07:43:45.378 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-03 07:43:45.395 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-03 07:43:45.558 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-03 07:43:46.130 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-03 07:43:46.133 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-03 07:43:46.138 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-03 07:43:46.141 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-03 07:43:46.148 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-03 07:43:46.151 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-03 07:43:55.890 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:43:55.901 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:43:55.902 [info] index finished after resolve  [object Object] 
2025-07-03 07:43:55.903 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:43:58.451 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:43:58.457 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:43:58.458 [info] index finished after resolve  [object Object] 
2025-07-03 07:43:58.459 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:01.159 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:01.162 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:01.162 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:01.164 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:06.859 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:06.862 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:06.863 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:06.865 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:08.979 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:09.001 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:09.002 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:09.003 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:11.636 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:11.641 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:11.642 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:11.643 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:14.589 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:14.611 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:14.612 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:14.613 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:16.991 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:17.025 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:17.027 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:17.029 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:19.018 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:19.024 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:19.025 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:19.027 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:24.819 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:24.824 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:24.825 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:24.826 [info] refresh page data from resolve listeners 0 935   
2025-07-03 07:44:26.821 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-03 07:44:26.825 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-03 07:44:26.826 [info] index finished after resolve  [object Object] 
2025-07-03 07:44:26.827 [info] refresh page data from resolve listeners 0 935   
