2025-07-18 10:45:28.657 [info] components database created cost 22 ms   
2025-07-18 10:45:28.671 [info] components index initializing...   
2025-07-18 10:45:30.758 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-18 10:45:31.192 [info] indexing created file components/logs/2025-07-18.components.log  [object Object] 
2025-07-18 10:45:31.230 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-18 10:45:31.464 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-18 10:45:31.509 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-18 10:45:31.554 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-18 10:45:31.584 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-18 10:45:32.119 [info] start to batch put pages: 7   
2025-07-18 10:45:32.142 [info] batch persist cost 7  23 
2025-07-18 10:45:32.163 [info] components index initialized, 997 files cost 3528 ms   
2025-07-18 10:45:32.163 [info] refresh page data from init listeners 0 997   
2025-07-18 10:45:38.329 [info] indexing created file 日记库/fleeting_notes/2025-07-18.md  [object Object] 
2025-07-18 10:45:38.329 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:45:38.340 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:45:38.343 [info] index finished after resolve  [object Object] 
2025-07-18 10:45:38.345 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:45:38.359 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:45:38.928 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:45:38.929 [info] index finished after resolve  [object Object] 
2025-07-18 10:45:38.931 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:48:41.458 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:48:41.810 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:48:41.826 [info] index finished after resolve  [object Object] 
2025-07-18 10:48:41.831 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:05.902 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:05.922 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:05.931 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:05.934 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:10.926 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:10.943 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:10.948 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:10.951 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:13.999 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:14.019 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:14.024 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:14.026 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:25.841 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:25.864 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:25.869 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:25.871 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:28.094 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:28.105 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:28.107 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:28.109 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:31.349 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:31.357 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:31.358 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:31.359 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:33.400 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:33.412 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:33.413 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:33.414 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:35.462 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:35.469 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:35.471 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:35.472 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:37.541 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:37.558 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:37.560 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:37.562 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:39.818 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:39.832 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:39.837 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:39.839 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:49:51.329 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:49:51.345 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:49:51.348 [info] index finished after resolve  [object Object] 
2025-07-18 10:49:51.350 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:50:00.059 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:50:00.071 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:50:00.075 [info] index finished after resolve  [object Object] 
2025-07-18 10:50:00.076 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:16.930 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:16.973 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:16.979 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:16.981 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:18.953 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:18.966 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:18.967 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:18.969 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:20.985 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:20.997 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:21.000 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:21.001 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:23.108 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:23.125 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:23.130 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:23.132 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:29.094 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:29.115 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:29.120 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:29.123 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:31.793 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:31.807 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:31.810 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:31.811 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:33.828 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:33.840 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:33.845 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:33.847 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:35.882 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:35.891 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:35.892 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:35.893 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:37.924 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:37.944 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:37.948 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:37.950 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:40.671 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:40.677 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:40.679 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:40.680 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:43.030 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:43.049 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:43.053 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:43.056 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:45.154 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:45.164 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:45.166 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:45.167 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:47.234 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:47.256 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:47.258 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:47.260 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:51:49.568 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:51:49.584 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:51:49.586 [info] index finished after resolve  [object Object] 
2025-07-18 10:51:49.588 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:10.909 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:10.919 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:10.921 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:10.922 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:12.954 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:12.969 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:12.973 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:12.975 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:25.498 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:25.520 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:25.524 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:25.526 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:32.533 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:32.547 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:32.549 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:32.551 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:39.055 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:39.067 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:39.068 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:39.069 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:41.181 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:41.201 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:41.203 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:41.205 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:44.330 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:44.338 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:44.339 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:44.340 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:46.877 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:46.886 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:46.889 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:46.890 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:48.875 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:48.885 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:48.887 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:48.888 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:51.002 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:51.020 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:51.022 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:51.024 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:52:54.592 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:52:54.614 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:52:54.617 [info] index finished after resolve  [object Object] 
2025-07-18 10:52:54.618 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:00.348 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:00.360 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:00.366 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:00.368 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:06.679 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:06.690 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:06.693 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:06.694 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:10.580 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:10.598 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:10.601 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:10.603 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:13.006 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:13.019 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:13.021 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:13.022 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:15.113 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:15.121 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:15.122 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:15.123 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:17.158 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:17.173 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:17.175 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:17.176 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:19.308 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:19.320 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:19.325 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:19.327 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:22.229 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:22.248 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:22.251 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:22.252 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:25.238 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:25.257 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:25.264 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:25.266 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:28.391 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:28.409 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:28.427 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:28.428 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:36.310 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:36.327 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:36.332 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:36.334 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:39.134 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:39.146 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:39.148 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:39.150 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:41.354 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:41.367 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:41.370 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:41.372 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:43.733 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:43.750 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:43.753 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:43.756 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:50.239 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:50.250 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:50.252 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:50.252 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:52.841 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:52.858 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:52.862 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:52.864 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:56.817 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:56.824 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:56.826 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:56.826 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:53:58.861 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:53:58.876 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:53:58.880 [info] index finished after resolve  [object Object] 
2025-07-18 10:53:58.881 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:07.697 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:07.718 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:07.721 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:07.722 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:10.653 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:10.663 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:10.664 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:10.666 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:12.941 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:12.953 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:12.955 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:12.957 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:15.070 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:15.078 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:15.082 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:15.083 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:17.114 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:17.130 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:17.133 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:17.135 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:19.704 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:19.714 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:19.717 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:19.719 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:22.076 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:22.092 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:22.096 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:22.098 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:47.993 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:48.009 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:48.013 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:48.016 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:50.421 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:50.434 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:50.436 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:50.438 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:53.022 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:53.035 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:53.038 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:53.040 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:54:55.975 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:54:55.986 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:54:55.987 [info] index finished after resolve  [object Object] 
2025-07-18 10:54:55.988 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:01.735 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:01.752 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:01.757 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:01.758 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:04.132 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:04.149 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:04.152 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:04.154 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:07.316 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:07.327 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:07.330 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:07.332 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:09.918 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:09.933 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:09.938 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:09.939 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:14.894 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:14.909 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:14.912 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:14.913 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:45.374 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:45.388 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:45.392 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:45.394 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:48.720 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:48.733 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:48.737 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:48.739 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:51.323 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:51.335 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:51.338 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:51.340 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:53.434 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:53.449 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:53.452 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:53.454 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:55:56.183 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:55:56.198 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:55:56.201 [info] index finished after resolve  [object Object] 
2025-07-18 10:55:56.202 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:12.944 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:12.960 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:12.963 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:12.965 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:15.096 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:15.104 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:15.106 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:15.107 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:17.256 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:17.266 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:17.267 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:17.269 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:19.730 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:19.737 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:19.741 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:19.742 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:22.628 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:22.643 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:22.645 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:22.647 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:38.268 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:38.283 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:38.285 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:38.286 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:41.073 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:41.082 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:41.085 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:41.087 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:44.543 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:44.558 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:44.560 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:44.562 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:56:47.427 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:56:47.440 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:56:47.443 [info] index finished after resolve  [object Object] 
2025-07-18 10:56:47.444 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:57:01.726 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:57:01.740 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:57:01.744 [info] index finished after resolve  [object Object] 
2025-07-18 10:57:01.746 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:57:04.443 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:57:04.452 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:57:04.455 [info] index finished after resolve  [object Object] 
2025-07-18 10:57:04.456 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:57:06.478 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:57:06.485 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:57:06.486 [info] index finished after resolve  [object Object] 
2025-07-18 10:57:06.487 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:57:13.870 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:57:13.881 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:57:13.893 [info] index finished after resolve  [object Object] 
2025-07-18 10:57:13.896 [info] refresh page data from resolve listeners 0 998   
2025-07-18 10:57:25.161 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-18.md   
2025-07-18 10:57:25.176 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-18 10:57:25.179 [info] index finished after resolve  [object Object] 
2025-07-18 10:57:25.182 [info] refresh page data from resolve listeners 0 998   
