2025-03-27 08:06:21 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-27 08:06:21 [info] indexing created file components/logs/2025-03-27.components.log  [object Object] 
2025-03-27 08:06:21 [info] refresh page data from created listeners 0 664   
2025-03-27 08:06:21 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-27 08:06:21 [info] index finished after resolve  [object Object] 
2025-03-27 08:06:21 [info] refresh page data from resolve listeners 0 664   
2025-03-27 08:06:22 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-27 08:06:22 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-27 08:06:22 [info] index finished after resolve  [object Object] 
2025-03-27 08:06:22 [info] refresh page data from resolve listeners 0 664   
2025-03-27 08:06:22 [info] ignore file modify evnet 学习库/Anki/python/语句.md   
2025-03-27 08:06:22 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-27 08:06:22 [info] index finished after resolve  [object Object] 
2025-03-27 08:06:22 [info] refresh page data from resolve listeners 0 664   
2025-03-27 08:06:23 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-27 08:06:23 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-27 08:06:23 [info] index finished after resolve  [object Object] 
2025-03-27 08:06:23 [info] refresh page data from resolve listeners 0 664   
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query  [object Object] 
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-27 08:32:33 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-27 08:32:38 [info] match pageData is  [object Object] 
2025-03-27 08:32:48 [info] indexing created file 日记库/day/2025-03-26.md  [object Object] 
2025-03-27 08:32:48 [info] indexing created ignore file 日记库/day/2025-03-26.md   
2025-03-27 08:32:48 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:32:48 [info] index finished after resolve  [object Object] 
2025-03-27 08:32:48 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:32:57 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:32:57 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:32:57 [info] index finished after resolve  [object Object] 
2025-03-27 08:32:57 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:32:59 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:32:59 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:32:59 [info] index finished after resolve  [object Object] 
2025-03-27 08:32:59 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:01 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:01 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:01 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:01 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:03 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:03 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:03 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:03 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:05 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:05 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:05 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:05 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:09 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:09 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:09 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:09 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:11 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:11 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:11 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:11 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:13 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:13 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:13 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:13 [info] refresh page data from resolve listeners 0 665   
2025-03-27 08:33:46 [info] indexing created file 日记库/day/attachments/2025-03-26-2025-03-27-08-33-46.png  [object Object] 
2025-03-27 08:33:47 [info] refresh page data from created listeners 0 666   
2025-03-27 08:33:48 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:48 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:48 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:48 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:33:50 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:50 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:50 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:50 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:33:53 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:53 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:53 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:53 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:33:55 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:55 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:55 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:55 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:33:57 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:33:57 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:33:57 [info] index finished after resolve  [object Object] 
2025-03-27 08:33:57 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:14 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:14 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:14 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:14 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:16 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:16 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:16 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:16 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:18 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:19 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:19 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:19 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:21 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:21 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:21 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:21 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:24 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:24 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:24 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:24 [info] refresh page data from resolve listeners 0 666   
2025-03-27 08:34:27 [info] ignore file modify evnet 日记库/day/2025-03-26.md   
2025-03-27 08:34:27 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-03-27 08:34:27 [info] index finished after resolve  [object Object] 
2025-03-27 08:34:27 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:17:58 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:17:58 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:17:58 [info] index finished after resolve  [object Object] 
2025-03-27 09:17:58 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:18:05 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:18:05 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:18:05 [info] index finished after resolve  [object Object] 
2025-03-27 09:18:05 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:18:10 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:18:11 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:18:11 [info] index finished after resolve  [object Object] 
2025-03-27 09:18:11 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:18:16 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:18:16 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:18:16 [info] index finished after resolve  [object Object] 
2025-03-27 09:18:16 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:18:18 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:18:18 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:18:18 [info] index finished after resolve  [object Object] 
2025-03-27 09:18:18 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:18:22 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:18:22 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:18:22 [info] index finished after resolve  [object Object] 
2025-03-27 09:18:22 [info] refresh page data from resolve listeners 0 666   
2025-03-27 09:19:15 [info] indexing created file 学习库/c/attachments/4 流程控制语句-2025-03-27-09-19-14.png  [object Object] 
2025-03-27 09:19:15 [info] refresh page data from created listeners 0 667   
2025-03-27 09:19:17 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:19:17 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:19:17 [info] index finished after resolve  [object Object] 
2025-03-27 09:19:17 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:02 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:02 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:02 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:02 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:04 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:05 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:05 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:05 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:06 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:07 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:07 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:07 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:09 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:09 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:09 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:09 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:11 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:11 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:11 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:11 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:13 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:13 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:13 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:13 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:15 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:15 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:15 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:15 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:18 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:18 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:18 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:18 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:21 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:21 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:21 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:21 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:22:23 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:22:23 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:22:23 [info] index finished after resolve  [object Object] 
2025-03-27 09:22:23 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:24:51 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:24:51 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:24:51 [info] index finished after resolve  [object Object] 
2025-03-27 09:24:51 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:24:55 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:24:55 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:24:55 [info] index finished after resolve  [object Object] 
2025-03-27 09:24:55 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:24:57 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:24:57 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:24:57 [info] index finished after resolve  [object Object] 
2025-03-27 09:24:57 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:02 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:03 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:03 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:03 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:05 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:05 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:05 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:05 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:07 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:07 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:07 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:07 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:09 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:09 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:09 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:09 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:35 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:35 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:35 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:35 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:41 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:41 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:41 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:41 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:43 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:43 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:43 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:46 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:46 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:46 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:46 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:52 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:52 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:52 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:52 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:54 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:54 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:54 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:54 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:57 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:57 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:57 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:57 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:25:59 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:25:59 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:25:59 [info] index finished after resolve  [object Object] 
2025-03-27 09:25:59 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:03 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:03 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:03 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:03 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:07 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:07 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:07 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:07 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:13 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:13 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:13 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:13 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:20 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:20 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:20 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:20 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:22 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:22 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:22 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:22 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:26 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:26 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:26 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:26 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:28 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:28 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:28 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:28 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:30 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:30 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:30 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:30 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:32 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:32 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:32 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:32 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:36 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:36 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:36 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:36 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:38 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:38 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:38 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:38 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:40 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:40 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:40 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:40 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:43 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:43 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:43 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:45 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:45 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:45 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:45 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:48 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:48 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:48 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:48 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:50 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:50 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:50 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:50 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:56 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:56 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:56 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:56 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:26:59 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:26:59 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:26:59 [info] index finished after resolve  [object Object] 
2025-03-27 09:26:59 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:07 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:07 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:07 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:07 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:11 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:11 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:11 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:11 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:16 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:16 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:16 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:16 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:19 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:19 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:19 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:19 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:21 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:21 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:21 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:21 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:32 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:32 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:32 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:32 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:37 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:37 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:37 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:37 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:40 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:40 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:40 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:40 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:27:46 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:27:46 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:27:46 [info] index finished after resolve  [object Object] 
2025-03-27 09:27:46 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:32 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:32 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:32 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:32 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:34 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:34 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:34 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:34 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:36 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:36 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:36 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:36 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:39 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:39 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:39 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:39 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:41 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:41 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:41 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:41 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:43 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:43 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:43 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:46 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:46 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:46 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:46 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:48 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:48 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:48 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:48 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:50 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:50 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:50 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:50 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:53 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:53 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:53 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:53 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:55 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:55 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:55 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:55 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:57 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:57 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:57 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:57 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:28:59 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:28:59 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:28:59 [info] index finished after resolve  [object Object] 
2025-03-27 09:28:59 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:29:02 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:29:02 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:29:02 [info] index finished after resolve  [object Object] 
2025-03-27 09:29:02 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:29:04 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:29:04 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:29:04 [info] index finished after resolve  [object Object] 
2025-03-27 09:29:04 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:29:06 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:29:06 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:29:06 [info] index finished after resolve  [object Object] 
2025-03-27 09:29:06 [info] refresh page data from resolve listeners 0 667   
2025-03-27 09:29:08 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 09:29:08 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 09:29:08 [info] index finished after resolve  [object Object] 
2025-03-27 09:29:08 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:33 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:33 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:33 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:33 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:41 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:41 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:41 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:41 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:44 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:44 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:44 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:46 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:46 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:46 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:46 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:48 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:48 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:48 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:48 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:51 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:51 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:51 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:51 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:53 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:53 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:53 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:53 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:56 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:56 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:56 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:56 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:02:58 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:02:58 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:02:58 [info] index finished after resolve  [object Object] 
2025-03-27 10:02:58 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:03:00 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:03:00 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:03:00 [info] index finished after resolve  [object Object] 
2025-03-27 10:03:00 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:03:02 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:03:02 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:03:02 [info] index finished after resolve  [object Object] 
2025-03-27 10:03:02 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:29 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:30 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:30 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:30 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:33 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:33 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:33 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:33 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:35 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:35 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:35 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:35 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:38 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:38 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:38 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:38 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:41 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:41 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:41 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:41 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:44 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:44 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:44 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:44 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:04:47 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:04:47 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:04:47 [info] index finished after resolve  [object Object] 
2025-03-27 10:04:47 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:05 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:06 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:06 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:06 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:08 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:08 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:08 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:08 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:10 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:10 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:10 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:10 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:12 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:12 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:12 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:12 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:15 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:15 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:15 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:15 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:17 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:17 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:17 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:17 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:20 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:20 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:20 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:20 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:35 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:36 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:36 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:36 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:38 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:38 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:38 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:38 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:41 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:41 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:41 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:41 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:44 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:44 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:44 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:44 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:47 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:47 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:47 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:47 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:52 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:52 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:52 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:52 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:55 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:55 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:55 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:55 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:05:57 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:05:58 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:05:58 [info] index finished after resolve  [object Object] 
2025-03-27 10:05:58 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:06:03 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:06:03 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:06:03 [info] index finished after resolve  [object Object] 
2025-03-27 10:06:03 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:06:08 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:06:08 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:06:08 [info] index finished after resolve  [object Object] 
2025-03-27 10:06:08 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:06:10 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:06:10 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:06:10 [info] index finished after resolve  [object Object] 
2025-03-27 10:06:10 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:09:12 [info] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-03-27 10:09:13 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-03-27 10:09:13 [info] index finished after resolve  [object Object] 
2025-03-27 10:09:13 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:43 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:43 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:43 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:45 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:45 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:45 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:45 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:48 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:48 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:48 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:48 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:50 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:50 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:50 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:50 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:52 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:52 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:52 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:52 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:55:54 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 10:55:54 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 10:55:54 [info] index finished after resolve  [object Object] 
2025-03-27 10:55:54 [info] refresh page data from resolve listeners 0 667   
2025-03-27 10:56:35 [info] indexing created file 学习库/c/未命名.md  [object Object] 
2025-03-27 10:56:35 [info] indexing created ignore file 学习库/c/未命名.md   
2025-03-27 10:56:35 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 10:56:36 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 10:56:36 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 10:56:36 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 10:56:36 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 10:56:36 [info] trigger 学习库/c/未命名.md resolve  [object Object] 
2025-03-27 10:56:36 [info] index finished after resolve  [object Object] 
2025-03-27 10:56:36 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:56:45 [info] refresh page data from rename listeners 0 668   
2025-03-27 10:56:45 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 10:56:45 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 10:56:45 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 10:56:45 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 10:56:45 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 10:56:50 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:56:50 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:56:50 [info] index finished after resolve  [object Object] 
2025-03-27 10:56:50 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:56:52 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:56:52 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:56:52 [info] index finished after resolve  [object Object] 
2025-03-27 10:56:52 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:56:54 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:56:54 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:56:54 [info] index finished after resolve  [object Object] 
2025-03-27 10:56:54 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:56:56 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:56:56 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:56:56 [info] index finished after resolve  [object Object] 
2025-03-27 10:56:56 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:00 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:00 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:00 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:00 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:02 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:02 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:02 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:02 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:05 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:05 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:05 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:05 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:08 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:08 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:08 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:08 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:10 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:10 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:10 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:10 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:12 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:12 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:12 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:12 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:14 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:14 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:14 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:14 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:17 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:17 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:17 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:17 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:20 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:20 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:20 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:20 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:22 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:22 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:22 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:22 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:24 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:24 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:24 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:24 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:26 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:26 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:26 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:26 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:29 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:29 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:29 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:29 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:31 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:31 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:31 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:31 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:35 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:35 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:35 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:35 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:39 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:39 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:39 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:39 [info] refresh page data from resolve listeners 0 668   
2025-03-27 10:57:41 [info] ignore file modify evnet 学习库/c/0 一些操作.md   
2025-03-27 10:57:41 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-03-27 10:57:41 [info] index finished after resolve  [object Object] 
2025-03-27 10:57:41 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:28 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:28 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:28 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:28 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:30 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:30 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:30 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:30 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:32 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:32 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:32 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:32 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:38 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:38 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:38 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:38 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:40 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:40 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:40 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:40 [info] refresh page data from resolve listeners 0 668   
2025-03-27 15:35:43 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-27 15:35:43 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-27 15:35:43 [info] index finished after resolve  [object Object] 
2025-03-27 15:35:43 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:02 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:02 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:02 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:02 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:05 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:05 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:05 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:05 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:09 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:09 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:09 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:09 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:11 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:11 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:11 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:11 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:13 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:13 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:13 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:13 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:15 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:15 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:15 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:15 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:17 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:17 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:18 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:18 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:28 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:29 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:29 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:29 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:31 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:31 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:31 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:31 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:33 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:33 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:33 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:33 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:35 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:35 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:35 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:35 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:42 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:42 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:42 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:42 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:44 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:44 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:44 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:44 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:46 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:46 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:46 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:46 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:54 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:54 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:54 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:54 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:56 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:56 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:56 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:56 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:06:59 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:06:59 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:06:59 [info] index finished after resolve  [object Object] 
2025-03-27 16:06:59 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:01 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:01 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:01 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:01 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:03 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:03 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:03 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:03 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:05 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:05 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:05 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:05 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:07 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:07 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:07 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:07 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:16 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:16 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:16 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:16 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:18 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:18 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:18 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:18 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:21 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:21 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:21 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:21 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:23 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:23 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:23 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:23 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:25 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:25 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:25 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:25 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:28 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:28 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:28 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:28 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:35 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:35 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:35 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:35 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:07:37 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-27 16:07:37 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-27 16:07:37 [info] index finished after resolve  [object Object] 
2025-03-27 16:07:37 [info] refresh page data from resolve listeners 0 668   
2025-03-27 16:32:03 [info] indexing created file 学习库/linux/未命名.md  [object Object] 
2025-03-27 16:32:03 [info] indexing created ignore file 学习库/linux/未命名.md   
2025-03-27 16:32:03 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 16:32:03 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 16:32:03 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 16:32:03 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 16:32:03 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 16:32:03 [info] trigger 学习库/linux/未命名.md resolve  [object Object] 
2025-03-27 16:32:03 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:03 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:09 [info] refresh page data from rename listeners 0 669   
2025-03-27 16:32:09 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 16:32:09 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 16:32:09 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 16:32:09 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 16:32:09 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 16:32:12 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:12 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:12 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:12 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:15 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:15 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:15 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:15 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:17 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:17 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:17 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:17 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:19 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:19 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:19 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:19 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:22 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:22 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:22 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:22 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:25 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:25 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:25 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:25 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:47 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:47 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:47 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:47 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:49 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:49 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:49 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:49 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:53 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:53 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:53 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:53 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:55 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:55 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:55 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:55 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:32:58 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:32:58 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:32:58 [info] index finished after resolve  [object Object] 
2025-03-27 16:32:58 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:08 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:08 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:08 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:08 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:10 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:10 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:10 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:10 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:16 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:17 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:17 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:17 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:19 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:19 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:19 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:19 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:21 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:21 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:21 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:21 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:24 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:24 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:24 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:24 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:27 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:27 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:27 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:27 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:30 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:30 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:30 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:30 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:32 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:32 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:32 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:32 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:34 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:34 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:34 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:34 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:37 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:37 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:37 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:37 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:39 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:39 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:39 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:39 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:33:46 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-27 16:33:46 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-27 16:33:46 [info] index finished after resolve  [object Object] 
2025-03-27 16:33:46 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:40:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:40:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:40:58 [info] index finished after resolve  [object Object] 
2025-03-27 16:40:58 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:06 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:06 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:08 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:08 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:10 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:10 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:12 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:12 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:12 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:12 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:20 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:20 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:41:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:41:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:41:27 [info] index finished after resolve  [object Object] 
2025-03-27 16:41:27 [info] refresh page data from resolve listeners 0 669   
2025-03-27 16:44:04 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-27-16-44-04.png  [object Object] 
2025-03-27 16:44:04 [info] refresh page data from created listeners 0 670   
2025-03-27 16:44:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:06 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:06 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:23 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:23 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:25 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:25 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:27 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:27 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:29 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:29 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:31 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:31 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:33 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:33 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:44:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:44:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:44:36 [info] index finished after resolve  [object Object] 
2025-03-27 16:44:36 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:53:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:53:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:53:59 [info] index finished after resolve  [object Object] 
2025-03-27 16:53:59 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:20 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:20 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:27 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:27 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:31 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:31 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:33 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:33 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:37 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:37 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:50 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:50 [info] refresh page data from resolve listeners 0 670   
2025-03-27 16:54:52 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 16:54:52 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 16:54:52 [info] index finished after resolve  [object Object] 
2025-03-27 16:54:52 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:12 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:12 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:12 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:12 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:15 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:15 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:17 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:17 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:20 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:20 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:22 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:22 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:24 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:24 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:26 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:26 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:28 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:28 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:34 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:34 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:36 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:36 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:39 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:39 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:41 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:41 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:44 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:44 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:42:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:42:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:42:47 [info] index finished after resolve  [object Object] 
2025-03-27 19:42:47 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:28 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:28 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:30 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:30 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:33 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:33 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:37 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:37 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:43 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:43 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:45 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:45 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:49 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:49 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:49 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:51 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:51 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:54 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:54 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:44:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:44:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:44:58 [info] index finished after resolve  [object Object] 
2025-03-27 19:44:58 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:05 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:05 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:08 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:08 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:10 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:10 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:12 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:12 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:12 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:12 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:15 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:15 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:18 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:18 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:21 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:21 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:23 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:23 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:44 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:44 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:47 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:47 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:45:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:45:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:45:59 [info] index finished after resolve  [object Object] 
2025-03-27 19:45:59 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:46:03 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:46:03 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:46:03 [info] index finished after resolve  [object Object] 
2025-03-27 19:46:03 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:46:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:46:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:46:05 [info] index finished after resolve  [object Object] 
2025-03-27 19:46:05 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:46:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:46:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:46:07 [info] index finished after resolve  [object Object] 
2025-03-27 19:46:07 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:46:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:46:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:46:21 [info] index finished after resolve  [object Object] 
2025-03-27 19:46:21 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:46:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:46:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:46:24 [info] index finished after resolve  [object Object] 
2025-03-27 19:46:24 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:22 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:22 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:26 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:26 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:28 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:28 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:30 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:30 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:32 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:32 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:38 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:38 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:40 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:40 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:43 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:43 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:45 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:45 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:49 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:49 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:51 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:51 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:53 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:53 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:47:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:47:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:47:57 [info] index finished after resolve  [object Object] 
2025-03-27 19:47:57 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:00 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:00 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:02 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:02 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:04 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:04 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:11 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:11 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:13 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:13 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:16 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:16 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:18 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:18 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:20 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:20 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:22 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:22 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:26 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:26 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:28 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:28 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:32 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:32 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:34 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:34 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:38 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:38 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:48:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:48:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:48:43 [info] index finished after resolve  [object Object] 
2025-03-27 19:48:43 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:49:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:49:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:49:15 [info] index finished after resolve  [object Object] 
2025-03-27 19:49:15 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:49:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:49:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:49:19 [info] index finished after resolve  [object Object] 
2025-03-27 19:49:19 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:49:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:49:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:49:21 [info] index finished after resolve  [object Object] 
2025-03-27 19:49:21 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:40 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:40 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:43 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:43 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:45 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:45 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:47 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:47 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:55 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:55 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:57 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:57 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:52:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:52:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:52:59 [info] index finished after resolve  [object Object] 
2025-03-27 19:52:59 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:53:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:53:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:53:05 [info] index finished after resolve  [object Object] 
2025-03-27 19:53:05 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:53:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:53:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:53:07 [info] index finished after resolve  [object Object] 
2025-03-27 19:53:07 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:01 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:01 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:01 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:01 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:03 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:03 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:03 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:03 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:06 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:06 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:10 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:10 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:13 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:13 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:15 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:15 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:17 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:17 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:19 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:19 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:19 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:21 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:21 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:23 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:23 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:26 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:26 [info] refresh page data from resolve listeners 0 670   
2025-03-27 19:56:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-27 19:56:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-27 19:56:29 [info] index finished after resolve  [object Object] 
2025-03-27 19:56:29 [info] refresh page data from resolve listeners 0 670   
2025-03-27 20:36:46 [info] indexing created file 学习库/stm32/未命名.md  [object Object] 
2025-03-27 20:36:46 [info] indexing created ignore file 学习库/stm32/未命名.md   
2025-03-27 20:36:46 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 20:36:46 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 20:36:46 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 20:36:46 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 20:36:46 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 20:36:46 [info] trigger 学习库/stm32/未命名.md resolve  [object Object] 
2025-03-27 20:36:46 [info] index finished after resolve  [object Object] 
2025-03-27 20:36:46 [info] refresh page data from resolve listeners 0 671   
2025-03-27 20:36:50 [info] refresh page data from rename listeners 0 671   
2025-03-27 20:36:50 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-27 20:36:50 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-27 20:36:50 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-27 20:36:50 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-27 20:36:50 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-27 20:36:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:36:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:36:55 [info] index finished after resolve  [object Object] 
2025-03-27 20:36:55 [info] refresh page data from resolve listeners 0 671   
2025-03-27 20:37:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:37:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:37:00 [info] index finished after resolve  [object Object] 
2025-03-27 20:37:00 [info] refresh page data from resolve listeners 0 671   
2025-03-27 20:37:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:37:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:37:47 [info] index finished after resolve  [object Object] 
2025-03-27 20:37:47 [info] refresh page data from resolve listeners 0 671   
2025-03-27 20:39:47 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-20-39-47.png  [object Object] 
2025-03-27 20:39:47 [info] refresh page data from created listeners 0 672   
2025-03-27 20:39:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:39:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:39:49 [info] index finished after resolve  [object Object] 
2025-03-27 20:39:49 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:43:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:43:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:43:53 [info] index finished after resolve  [object Object] 
2025-03-27 20:43:53 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:43:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:43:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:43:56 [info] index finished after resolve  [object Object] 
2025-03-27 20:43:56 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:44:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:44:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:44:01 [info] index finished after resolve  [object Object] 
2025-03-27 20:44:01 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:44:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:44:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:44:14 [info] index finished after resolve  [object Object] 
2025-03-27 20:44:14 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:44:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:44:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:44:17 [info] index finished after resolve  [object Object] 
2025-03-27 20:44:17 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:47:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:47:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:47:38 [info] index finished after resolve  [object Object] 
2025-03-27 20:47:38 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:47:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:47:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:47:40 [info] index finished after resolve  [object Object] 
2025-03-27 20:47:40 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:47:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:47:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:47:57 [info] index finished after resolve  [object Object] 
2025-03-27 20:47:57 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:48:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:00 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:00 [info] refresh page data from resolve listeners 0 672   
2025-03-27 20:48:10 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-20-48-10.png  [object Object] 
2025-03-27 20:48:10 [info] refresh page data from created listeners 0 673   
2025-03-27 20:48:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:12 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:12 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:14 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:14 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:17 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:17 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:23 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:23 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:25 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:25 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:35 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:35 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:48:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:48:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:48:38 [info] index finished after resolve  [object Object] 
2025-03-27 20:48:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:49:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:49:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:49:38 [info] index finished after resolve  [object Object] 
2025-03-27 20:49:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:49:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:49:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:49:47 [info] index finished after resolve  [object Object] 
2025-03-27 20:49:47 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:49:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:49:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:49:57 [info] index finished after resolve  [object Object] 
2025-03-27 20:49:57 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:07 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:07 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:10 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:10 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:13 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:13 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:15 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:15 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:17 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:17 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:19 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:19 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:22 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:22 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:52:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:52:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:52:25 [info] index finished after resolve  [object Object] 
2025-03-27 20:52:25 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:40 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:43 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:43 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:45 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:45 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:49 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:49 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:56 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:58:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:58:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:58:58 [info] index finished after resolve  [object Object] 
2025-03-27 20:58:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:00 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:00 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:02 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:02 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:05 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:05 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:07 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:07 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:37 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:38 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:41 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:41 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:41 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:41 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:50 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:50 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:52 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:52 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:54 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:54 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:56 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 20:59:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 20:59:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 20:59:58 [info] index finished after resolve  [object Object] 
2025-03-27 20:59:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:00 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:02 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:02 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:07 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:07 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:15 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:18 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:21 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:21 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:21 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:23 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:23 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:25 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:27 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:31 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:33 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:33 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:35 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:35 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:42 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:44 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:47 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:50 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:53 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:53 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:00:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:00:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:00:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:00:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:01 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:03 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:06 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:06 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:08 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:13 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:13 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:16 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:16 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:16 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:16 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:18 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:20 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:22 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:25 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:31 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:42 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:54 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:01:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:01:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:01:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:01:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:00 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:03 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:08 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:13 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:13 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:15 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:21 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:21 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:21 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:23 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:23 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:26 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:29 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:33 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:33 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:42 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:46 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:48 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:51 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:51 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:54 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:02:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:02:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:02:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:02:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:01 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:03 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:06 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:06 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:08 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:11 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:12 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:15 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:17 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:17 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:20 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:24 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:24 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:26 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:29 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:32 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:42 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:44 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:03:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:03:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:03:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:03:46 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:09:03 [info] trigger 学习库/stm32/attachments/串口-2025-03-27-20-48-10.png resolve  [object Object] 
2025-03-27 21:09:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:09:03 [info] refresh page data from modify listeners 0 673   
2025-03-27 21:17:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:20 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:22 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:28 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:28 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:30 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:32 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:34 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:37 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:37 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:37 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:37 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:41 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:41 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:41 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:41 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:44 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:48 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:50 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:54 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:56 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:17:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:17:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:17:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:17:58 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:01 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:03 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:06 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:06 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:08 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:10 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:10 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:12 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:15 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:18 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:20 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:23 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:23 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:26 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:28 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:28 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:30 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:33 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:33 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:35 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:35 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:38 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:40 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:42 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:49 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:49 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:52 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:52 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:54 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:57 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:57 [info] refresh page data from resolve listeners 0 673   
2025-03-27 21:18:58 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-18-58.png  [object Object] 
2025-03-27 21:18:58 [info] refresh page data from created listeners 0 674   
2025-03-27 21:18:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:18:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:18:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:18:59 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:03 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:05 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:08 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:10 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:10 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:12 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:15 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:18 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:20 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:22 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:25 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:27 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:29 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:31 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:34 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:36 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:38 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:40 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:43 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:43 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:45 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:45 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:50 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:19:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:19:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:19:57 [info] index finished after resolve  [object Object] 
2025-03-27 21:19:57 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:01 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:10 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:10 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:15 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:18 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:20 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:24 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:24 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:26 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:32 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:34 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:36 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:38 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:44 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:46 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:49 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:49 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:51 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:51 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:54 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:56 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:20:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:20:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:20:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:20:58 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:00 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:02 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:02 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:08 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:10 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:10 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:13 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:13 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:20 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:25 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:30 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:32 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:34 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:36 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:46 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:48 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:51 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:51 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:53 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:53 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:21:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:21:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:21:55 [info] index finished after resolve  [object Object] 
2025-03-27 21:21:55 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:03 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:09 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:09 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:09 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:09 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:14 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:14 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:20 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:25 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:31 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:34 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:37 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:38 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:40 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:42 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:44 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:47 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:50 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:52 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:52 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:54 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:22:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:22:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:22:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:22:58 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:00 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:02 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:02 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:04 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:04 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:26 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:36 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:38 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:23:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:23:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:23:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:23:40 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:24:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:24:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:24:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:24:01 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:24:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:24:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:24:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:24:05 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:24:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:24:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:24:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:24:22 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:24:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:24:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:24:24 [info] index finished after resolve  [object Object] 
2025-03-27 21:24:24 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:24:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:24:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:24:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:24:26 [info] refresh page data from resolve listeners 0 674   
2025-03-27 21:25:03 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-25-03.png  [object Object] 
2025-03-27 21:25:03 [info] refresh page data from created listeners 0 675   
2025-03-27 21:25:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:05 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:40 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:45 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:45 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:47 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:50 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:52 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:52 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:54 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:56 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:25:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:25:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:25:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:25:59 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:02 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:02 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:07 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:07 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:10 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:10 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:10 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:10 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:12 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:14 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:14 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:17 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:17 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:19 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:19 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:21 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:21 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:21 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:24 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:24 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:27 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:32 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:36 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:39 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:39 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:26:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:26:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:26:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:26:42 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:28:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:28:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:28:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:28:54 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:28:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:28:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:28:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:28:56 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:28:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:28:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:28:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:28:59 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:29:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:03 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:29:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:05 [info] refresh page data from resolve listeners 0 675   
2025-03-27 21:29:06 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-29-06.png  [object Object] 
2025-03-27 21:29:06 [info] refresh page data from created listeners 0 676   
2025-03-27 21:29:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:08 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:31 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:33 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:33 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:35 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:35 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:37 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:37 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:37 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:37 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:39 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:39 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:42 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:44 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:47 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:56 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:29:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:29:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:29:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:29:59 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:01 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:21 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:21 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:21 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:23 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:23 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:26 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:28 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:28 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:30 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:43 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:43 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:45 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:45 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:48 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:50 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:53 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:53 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:55 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:55 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:30:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:30:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:30:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:30:59 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:01 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:03 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:08 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:14 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:14 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:27 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:34 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:39 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:39 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:42 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:44 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:44 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:46 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:48 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:51 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:51 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:31:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:31:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:31:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:31:59 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:32:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:32:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:32:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:32:01 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:32:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:32:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:32:06 [info] index finished after resolve  [object Object] 
2025-03-27 21:32:06 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:32:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:32:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:32:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:32:12 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:32:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:32:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:32:14 [info] index finished after resolve  [object Object] 
2025-03-27 21:32:14 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:11 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:11 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:12 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:17 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:17 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:19 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:19 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:22 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:23 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-34-23.png  [object Object] 
2025-03-27 21:34:23 [info] refresh page data from created listeners 0 677   
2025-03-27 21:34:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:25 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:34:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:30 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:34:36 [info] refresh page data from delete listeners 0 676   
2025-03-27 21:34:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:38 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:38 [info] refresh page data from resolve listeners 0 676   
2025-03-27 21:34:52 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-34-52.png  [object Object] 
2025-03-27 21:34:52 [info] refresh page data from created listeners 0 677   
2025-03-27 21:34:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:54 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:34:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:56 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:34:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:34:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:34:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:34:59 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:01 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:03 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:05 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:07 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:07 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:11 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:11 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:11 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:11 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:13 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:13 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:20 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:22 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:27 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:29 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:31 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:31 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:34 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:36 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:40 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:48 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:54 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:35:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:35:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:35:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:35:56 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:00 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:03 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:05 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:07 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:07 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:12 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:12 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:15 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:15 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:17 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:17 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:17 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:17 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:19 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:19 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:22 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:24 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:24 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:26 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:28 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:28 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:30 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:30 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:32 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:53 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:53 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:56 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:36:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:36:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:36:58 [info] index finished after resolve  [object Object] 
2025-03-27 21:36:58 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:37:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:37:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:37:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:37:00 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:37:02 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:37:02 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:37:02 [info] index finished after resolve  [object Object] 
2025-03-27 21:37:02 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:37:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:37:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:37:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:37:05 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:39:21 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-27 21:39:21 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-27 21:39:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:39:21 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:39:25 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-27 21:39:25 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-27 21:39:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:39:25 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:39:34 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-27 21:39:34 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-27 21:39:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:39:34 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:39:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:39:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:39:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:39:40 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:04 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:04 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:08 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:23 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:23 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:26 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:26 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:29 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:33 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:33 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:35 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:35 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:42 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:42 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:55 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:55 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:57 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:57 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:40:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:40:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:40:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:40:59 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:21 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:21 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:21 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:21 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:40 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:40 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:47 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:51 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:51 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:54 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:41:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:41:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:41:57 [info] index finished after resolve  [object Object] 
2025-03-27 21:41:57 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:43 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:43 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:45 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:45 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:47 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:47 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:49 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:49 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:52 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:52 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:54 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:54 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:56 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:56 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:42:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:42:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:42:59 [info] index finished after resolve  [object Object] 
2025-03-27 21:42:59 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:01 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:01 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:03 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:03 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:05 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:05 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:05 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:05 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:08 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:22 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:25 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:25 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:25 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:25 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:27 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:29 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:32 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:34 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:36 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:36 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:39 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:39 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:41 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:41 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:41 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:41 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:43 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:43 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:46 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:46 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:48 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:48 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:50 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:50 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:53 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:53 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:43:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:43:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:43:55 [info] index finished after resolve  [object Object] 
2025-03-27 21:43:55 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:00 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:00 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:00 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:04 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:04 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:06 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:06 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:08 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:08 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:13 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:13 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:16 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:16 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:16 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:16 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:18 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:18 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:20 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:20 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:22 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:22 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:27 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:27 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:29 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:29 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:32 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:32 [info] refresh page data from resolve listeners 0 677   
2025-03-27 21:44:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-27 21:44:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-27 21:44:34 [info] index finished after resolve  [object Object] 
2025-03-27 21:44:34 [info] refresh page data from resolve listeners 0 677   
