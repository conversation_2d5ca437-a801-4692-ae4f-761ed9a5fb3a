2025-04-08 09:57:24 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:24 [info] indexing created file components/logs/2025-04-08.components.log  [object Object] 
2025-04-08 09:57:24 [info] refresh page data from created listeners 0 733   
2025-04-08 09:57:24 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:24 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:24 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:30 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:30 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:30 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:30 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:32 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:32 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:32 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:32 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:36 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:37 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:37 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:37 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:39 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:39 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:39 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:39 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:44 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:45 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:45 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:45 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:57:48 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:57:48 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:57:48 [info] index finished after resolve  [object Object] 
2025-04-08 09:57:48 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:00 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:01 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:01 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:01 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:03 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:04 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:04 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:04 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:12 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:12 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:12 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:12 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:14 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:14 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:14 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:14 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:17 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:17 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:17 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:17 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:19 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:20 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:20 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:20 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:58:26 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:58:26 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:58:26 [info] index finished after resolve  [object Object] 
2025-04-08 09:58:26 [info] refresh page data from resolve listeners 0 733   
2025-04-08 09:59:46 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 09:59:46 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 09:59:46 [info] index finished after resolve  [object Object] 
2025-04-08 09:59:46 [info] refresh page data from resolve listeners 0 733   
2025-04-08 10:04:22 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211848.png  [object Object] 
2025-04-08 10:04:22 [info] refresh page data from created listeners 0 734   
2025-04-08 10:04:37 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-25-46.png  [object Object] 
2025-04-08 10:04:37 [info] refresh page data from created listeners 0 735   
2025-04-08 10:04:37 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 10:04:40 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-17-10.png  [object Object] 
2025-04-08 10:04:40 [info] refresh page data from created listeners 0 736   
2025-04-08 10:04:40 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 10:04:41 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-16-02.png  [object Object] 
2025-04-08 10:04:41 [info] refresh page data from created listeners 0 737   
2025-04-08 10:04:41 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 10:04:42 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211103.png  [object Object] 
2025-04-08 10:04:42 [info] refresh page data from created listeners 0 738   
2025-04-08 10:04:43 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015213508.png  [object Object] 
2025-04-08 10:04:43 [info] refresh page data from created listeners 0 739   
2025-04-08 10:04:44 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241014093021.png  [object Object] 
2025-04-08 10:04:45 [info] refresh page data from created listeners 0 740   
2025-04-08 10:04:45 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015210604.png  [object Object] 
2025-04-08 10:04:45 [info] refresh page data from created listeners 0 741   
2025-04-08 10:07:40 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-08 10:07:40 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-08 10:07:40 [info] index finished after resolve  [object Object] 
2025-04-08 10:07:40 [info] refresh page data from resolve listeners 0 741   
2025-04-08 10:20:10 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 10:20:10 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 10:20:10 [info] index finished after resolve  [object Object] 
2025-04-08 10:20:10 [info] refresh page data from resolve listeners 0 741   
2025-04-08 10:21:40 [info] indexing created file 学习库/Anki/stm32/未命名.md  [object Object] 
2025-04-08 10:21:40 [info] indexing created ignore file 学习库/Anki/stm32/未命名.md   
2025-04-08 10:21:40 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-08 10:21:40 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-08 10:21:40 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-08 10:21:40 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-08 10:21:40 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-08 10:21:40 [info] trigger 学习库/Anki/stm32/未命名.md resolve  [object Object] 
2025-04-08 10:21:40 [info] index finished after resolve  [object Object] 
2025-04-08 10:21:40 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:21:45 [info] ignore file modify evnet 学习库/Anki/stm32/未命名.md   
2025-04-08 10:21:45 [info] trigger 学习库/Anki/stm32/未命名.md resolve  [object Object] 
2025-04-08 10:21:45 [info] index finished after resolve  [object Object] 
2025-04-08 10:21:45 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:22:07 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-04-08 10:22:07 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-08 10:22:07 [info] index finished after resolve  [object Object] 
2025-04-08 10:22:07 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:22:17 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-04-08 10:22:17 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-08 10:22:17 [info] index finished after resolve  [object Object] 
2025-04-08 10:22:17 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:22:30 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-04-08 10:22:30 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-08 10:22:30 [info] index finished after resolve  [object Object] 
2025-04-08 10:22:30 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:02 [info] ignore file modify evnet 学习库/Anki/stm32/未命名.md   
2025-04-08 10:23:02 [info] trigger 学习库/Anki/stm32/未命名.md resolve  [object Object] 
2025-04-08 10:23:02 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:02 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:13 [info] refresh page data from rename listeners 0 742   
2025-04-08 10:23:13 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-08 10:23:13 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-08 10:23:13 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-08 10:23:13 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-08 10:23:13 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-08 10:23:27 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:27 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:27 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:27 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:30 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:30 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:30 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:30 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:32 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:32 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:32 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:32 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:37 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:37 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:37 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:37 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:39 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:39 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:39 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:39 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:42 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:42 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:42 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:42 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:45 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:45 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:45 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:45 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:47 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:47 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:47 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:47 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:49 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:49 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:49 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:49 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:51 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:51 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:51 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:51 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:54 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:54 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:54 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:54 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:23:57 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:23:57 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:23:57 [info] index finished after resolve  [object Object] 
2025-04-08 10:23:57 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:09 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:09 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:09 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:09 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:11 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:11 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:11 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:11 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:14 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:14 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:14 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:14 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:16 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:16 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:16 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:16 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:19 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:19 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:19 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:19 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:22 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:22 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:22 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:22 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:25 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:25 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:25 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:25 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:27 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:27 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:27 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:27 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:29 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:29 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:29 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:29 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:31 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:31 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:31 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:31 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:33 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:33 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:33 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:33 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:35 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:35 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:35 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:35 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:37 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:37 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:37 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:37 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:40 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:40 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:40 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:40 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:42 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:42 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:42 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:42 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:48 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:48 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:48 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:48 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:50 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:50 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:50 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:50 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:54 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:54 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:54 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:54 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:24:56 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:24:56 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:24:56 [info] index finished after resolve  [object Object] 
2025-04-08 10:24:56 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:25:00 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:25:00 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:25:00 [info] index finished after resolve  [object Object] 
2025-04-08 10:25:00 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:26:37 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 10:26:38 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 10:26:38 [info] index finished after resolve  [object Object] 
2025-04-08 10:26:38 [info] refresh page data from resolve listeners 0 742   
2025-04-08 10:26:44 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 10:26:44 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 10:26:44 [info] index finished after resolve  [object Object] 
2025-04-08 10:26:44 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:18 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:18 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:18 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:18 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:23 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:23 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:23 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:23 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:25 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:25 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:25 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:25 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:29 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:29 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:29 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:29 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:43 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:43 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:43 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:43 [info] refresh page data from resolve listeners 0 742   
2025-04-08 11:06:56 [info] ignore file modify evnet 学习库/Anki/stm32/I2C.md   
2025-04-08 11:06:56 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-08 11:06:56 [info] index finished after resolve  [object Object] 
2025-04-08 11:06:56 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:29:56 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:29:56 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:29:56 [info] index finished after resolve  [object Object] 
2025-04-08 14:29:56 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:29:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:29:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:29:59 [info] index finished after resolve  [object Object] 
2025-04-08 14:29:59 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:01 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:01 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:01 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:01 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:10 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:11 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:11 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:11 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:15 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:15 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:15 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:15 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:18 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:18 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:18 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:18 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:23 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:23 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:23 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:23 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:33 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:33 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:33 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:33 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:36 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:36 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:36 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:36 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:38 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:38 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:38 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:38 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:30:41 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:30:41 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:30:41 [info] index finished after resolve  [object Object] 
2025-04-08 14:30:41 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:17 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:17 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:17 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:17 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:28 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:28 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:28 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:28 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:31 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:31 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:31 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:31 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:33 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:33 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:33 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:33 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:38 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:38 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:38 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:38 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:40 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:40 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:40 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:40 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:42 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:42 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:45 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:45 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:45 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:47 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:47 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:47 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:47 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:31:49 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:31:49 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:31:49 [info] index finished after resolve  [object Object] 
2025-04-08 14:31:49 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:34 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:34 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:34 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:34 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:38 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:38 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:38 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:38 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:40 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:40 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:40 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:40 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:42 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:42 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:49 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:49 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:49 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:49 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:35:51 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:35:51 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:35:51 [info] index finished after resolve  [object Object] 
2025-04-08 14:35:51 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:36:08 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:36:08 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:36:08 [info] index finished after resolve  [object Object] 
2025-04-08 14:36:08 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:36:17 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:36:17 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:36:17 [info] index finished after resolve  [object Object] 
2025-04-08 14:36:17 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:36:21 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:36:21 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:36:21 [info] index finished after resolve  [object Object] 
2025-04-08 14:36:21 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:36:24 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:36:24 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:36:24 [info] index finished after resolve  [object Object] 
2025-04-08 14:36:24 [info] refresh page data from resolve listeners 0 742   
2025-04-08 14:36:45 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-08-14-36-45.png  [object Object] 
2025-04-08 14:36:45 [info] refresh page data from created listeners 0 743   
2025-04-08 14:36:47 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-08 14:36:47 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-08 14:36:47 [info] index finished after resolve  [object Object] 
2025-04-08 14:36:47 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:01 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:01 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:01 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:13 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:13 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:14 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:18 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:18 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:18 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:28 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:28 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:38 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:39 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:39 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:43 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:47 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:47 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:47 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:49 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:53 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:53 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:56:55 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:56:59 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:56:59 [info] index finished after resolve  [object Object] 
2025-04-08 21:56:59 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:57:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:57:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:57:05 [info] index finished after resolve  [object Object] 
2025-04-08 21:57:05 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:57:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:57:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:57:11 [info] index finished after resolve  [object Object] 
2025-04-08 21:57:25 [info] components database created cost 3 ms   
2025-04-08 21:57:25 [info] components index initializing...   
2025-04-08 21:57:25 [info] start to batch put pages: 9   
2025-04-08 21:57:28 [info] batch persist cost 9  2400 
2025-04-08 21:57:28 [info] components index initialized, 743 files cost 2572 ms   
2025-04-08 21:57:28 [info] refresh page data from init listeners 0 743   
2025-04-08 21:57:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-08 21:57:30 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-08 21:57:30 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-08 21:57:30 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-08 21:57:30 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-08 21:57:30 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-08 21:58:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:01 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:01 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:01 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:58:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:09 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:09 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:09 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:58:14 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:16 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:16 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:58:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:23 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:23 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:58:25 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:25 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:25 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:25 [info] refresh page data from resolve listeners 0 743   
2025-04-08 21:58:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-08 21:58:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-08 21:58:28 [info] index finished after resolve  [object Object] 
2025-04-08 21:58:28 [info] refresh page data from resolve listeners 0 743   
