2025-07-19 11:18:57.135 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.145 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.150 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.150 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.151 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.152 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.219 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.219 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.220 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:18:57.221 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-19 11:19:00.063 [info] components database created cost 1 ms   
2025-07-19 11:19:00.063 [info] components index initializing...   
2025-07-19 11:19:00.193 [info] Found 6 orphaned pages in database, cleaning up...   
2025-07-19 11:19:00.193 [info] start to batch delete index: 6   
2025-07-19 11:19:00.194 [info] Cleaned up 6 orphaned pages from database   
2025-07-19 11:19:00.330 [info] delete index completed - 6, cost 137ms   
2025-07-19 11:19:00.406 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-19 11:19:00.466 [info] start to batch put pages: 10   
2025-07-19 11:19:00.470 [info] batch persist cost 10  4 
2025-07-19 11:19:00.543 [info] components index initialized, 964 files cost 481 ms   
2025-07-19 11:19:00.543 [info] refresh page data from init listeners 0 964   
2025-07-19 11:19:00.564 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-19 11:19:00.570 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-19 11:19:00.574 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-19 11:19:00.599 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-19 11:19:00.602 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-19 11:19:00.606 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-19 11:19:00.610 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-19 11:19:02.582 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-19 11:19:02.608 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-19 11:19:02.623 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-19 11:19:02.625 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-19 11:19:03.043 [info] indexing created file components/logs/2025-07-19.components.log  [object Object] 
2025-07-19 11:19:03.066 [info] refresh page data from created listeners 0 965   
2025-07-19 11:19:03.261 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-19 11:19:03.264 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-19 11:19:03.266 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-19 11:19:03.268 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-19 11:19:03.835 [debug] ignore file modify evnet 学习库/Anki/c/指针.md   
2025-07-19 11:19:03.852 [info] trigger 学习库/Anki/c/指针.md resolve  [object Object] 
2025-07-19 11:19:03.854 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:03.854 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:04.146 [debug] ignore file modify evnet 学习库/c/6 数组.md   
2025-07-19 11:19:04.164 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-07-19 11:19:04.165 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:04.166 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:04.309 [debug] ignore file modify evnet 学习库/Anki/c/数组.md   
2025-07-19 11:19:04.315 [info] trigger 学习库/Anki/c/数组.md resolve  [object Object] 
2025-07-19 11:19:04.316 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:04.317 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:04.575 [debug] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-07-19 11:19:04.589 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-19 11:19:04.590 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:04.592 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:04.912 [debug] ignore file modify evnet 学习库/c/2 核心语法.md   
2025-07-19 11:19:04.920 [info] refresh page data from delete listeners 0 964   
2025-07-19 11:19:04.938 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-07-19 11:19:04.939 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:04.940 [info] refresh page data from resolve listeners 0 964   
2025-07-19 11:19:05.045 [info] indexing created file Pasted image 20231221112017.png.md  [object Object] 
2025-07-19 11:19:05.045 [info] indexing created ignore file Pasted image 20231221112017.png.md   
2025-07-19 11:19:05.046 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/本来训练没问题，突然出现Permission denied/运行train.py出现permission denied.md resolve  [object Object] 
2025-07-19 11:19:05.050 [info] trigger Pasted image 20231221112017.png.md resolve  [object Object] 
2025-07-19 11:19:05.050 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:05.051 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:11.662 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-19 11:19:11.663 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-19 11:19:29.021 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-19 11:19:29.027 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-19 11:19:29.029 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:29.030 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:30.969 [info] indexing created file draw.py  [object Object] 
2025-07-19 11:19:30.971 [info] refresh page data from created listeners 0 966   
2025-07-19 11:19:31.185 [debug] ignore file modify evnet 学习库/Deep learning/概念库/激活函数.md   
2025-07-19 11:19:31.196 [info] trigger 学习库/Deep learning/概念库/激活函数.md resolve  [object Object] 
2025-07-19 11:19:31.197 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:31.198 [info] refresh page data from resolve listeners 0 966   
2025-07-19 11:19:31.675 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-07-19 11:19:31.682 [info] refresh page data from delete listeners 0 965   
2025-07-19 11:19:31.685 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-07-19 11:19:31.687 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-07-19 11:19:31.699 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:31.700 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:31.719 [info] refresh page data from delete listeners 0 964   
2025-07-19 11:19:31.720 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-07-19 11:19:32.357 [info] indexing created file 学习库/Deep learning/概念库/Drawing 2025-07-12 09.54.59.excalidraw.md  [object Object] 
2025-07-19 11:19:32.357 [info] indexing created ignore file 学习库/Deep learning/概念库/Drawing 2025-07-12 09.54.59.excalidraw.md   
2025-07-19 11:19:32.382 [info] trigger 学习库/Deep learning/概念库/Drawing 2025-07-12 09.54.59.excalidraw.md resolve  [object Object] 
2025-07-19 11:19:32.383 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:32.385 [info] refresh page data from resolve listeners 0 965   
2025-07-19 11:19:33.053 [info] trigger 学习库/Deep learning/attachments/激活函数-2025-04-24-15-09-01.png resolve  [object Object] 
2025-07-19 11:19:33.054 [info] index finished after resolve  [object Object] 
2025-07-19 11:19:33.055 [info] refresh page data from modify listeners 0 965   
2025-07-19 11:19:34.993 [info] indexing created file 学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-00-15.png  [object Object] 
2025-07-19 11:19:34.995 [info] refresh page data from created listeners 0 966   
2025-07-19 11:19:36.602 [info] indexing created file 学习库/Deep learning/概念库/attachments/激活函数-2025-07-12-10-08-54.png  [object Object] 
2025-07-19 11:19:36.604 [info] refresh page data from created listeners 0 967   
2025-07-19 11:19:39.667 [info] indexing created file 学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-13-53.png  [object Object] 
2025-07-19 11:19:39.669 [info] refresh page data from created listeners 0 968   
2025-07-19 11:19:51.117 [info] indexing created file 学习库/Deep learning/概念库/attachments/评价指标-2025-07-12-11-16-24.png  [object Object] 
2025-07-19 11:19:51.119 [info] refresh page data from created listeners 0 969   
2025-07-19 11:20:17.606 [info] indexing created file components/logs/2025-07-11.components.log  [object Object] 
2025-07-19 11:20:17.629 [info] refresh page data from created listeners 0 970   
2025-07-19 11:20:18.020 [info] indexing created file 学习库/Deep learning/未命名.md  [object Object] 
2025-07-19 11:20:18.020 [info] indexing created ignore file 学习库/Deep learning/未命名.md   
2025-07-19 11:20:18.039 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-19 11:20:18.046 [info] trigger 学习库/Deep learning/未命名.md resolve  [object Object] 
2025-07-19 11:20:18.047 [info] index finished after resolve  [object Object] 
2025-07-19 11:20:18.048 [info] refresh page data from resolve listeners 0 971   
2025-07-19 11:23:21.668 [info] indexing created file copilot-custom-prompts/Summarize.md  [object Object] 
2025-07-19 11:23:21.668 [info] indexing created ignore file copilot-custom-prompts/Summarize.md   
2025-07-19 11:23:21.702 [info] trigger copilot-custom-prompts/Summarize.md resolve  [object Object] 
2025-07-19 11:23:21.703 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:21.705 [info] refresh page data from resolve listeners 0 972   
2025-07-19 11:23:22.173 [info] indexing created file copilot-custom-prompts/Remove URLs.md  [object Object] 
2025-07-19 11:23:22.174 [info] indexing created ignore file copilot-custom-prompts/Remove URLs.md   
2025-07-19 11:23:22.208 [info] trigger copilot-custom-prompts/Remove URLs.md resolve  [object Object] 
2025-07-19 11:23:22.210 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:22.212 [info] refresh page data from resolve listeners 0 973   
2025-07-19 11:23:22.703 [info] indexing created file copilot-custom-prompts/Fix grammar and spelling.md  [object Object] 
2025-07-19 11:23:22.703 [info] indexing created ignore file copilot-custom-prompts/Fix grammar and spelling.md   
2025-07-19 11:23:22.738 [info] trigger copilot-custom-prompts/Fix grammar and spelling.md resolve  [object Object] 
2025-07-19 11:23:22.739 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:22.740 [info] refresh page data from resolve listeners 0 974   
2025-07-19 11:23:23.343 [info] indexing created file copilot-custom-prompts/Generate glossary.md  [object Object] 
2025-07-19 11:23:23.343 [info] indexing created ignore file copilot-custom-prompts/Generate glossary.md   
2025-07-19 11:23:23.368 [info] trigger copilot-custom-prompts/Generate glossary.md resolve  [object Object] 
2025-07-19 11:23:23.369 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:23.370 [info] refresh page data from resolve listeners 0 975   
2025-07-19 11:23:23.982 [info] indexing created file copilot-custom-prompts/Generate table of contents.md  [object Object] 
2025-07-19 11:23:23.982 [info] indexing created ignore file copilot-custom-prompts/Generate table of contents.md   
2025-07-19 11:23:24.020 [info] trigger copilot-custom-prompts/Generate table of contents.md resolve  [object Object] 
2025-07-19 11:23:24.021 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:24.023 [info] refresh page data from resolve listeners 0 976   
2025-07-19 11:23:24.520 [info] indexing created file copilot-custom-prompts/Translate to Chinese.md  [object Object] 
2025-07-19 11:23:24.520 [info] indexing created ignore file copilot-custom-prompts/Translate to Chinese.md   
2025-07-19 11:23:24.552 [info] trigger copilot-custom-prompts/Translate to Chinese.md resolve  [object Object] 
2025-07-19 11:23:24.554 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:24.555 [info] refresh page data from resolve listeners 0 977   
2025-07-19 11:23:25.019 [info] indexing created file copilot-custom-prompts/Simplify.md  [object Object] 
2025-07-19 11:23:25.019 [info] indexing created ignore file copilot-custom-prompts/Simplify.md   
2025-07-19 11:23:25.106 [info] trigger copilot-custom-prompts/Simplify.md resolve  [object Object] 
2025-07-19 11:23:25.107 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:25.108 [info] refresh page data from resolve listeners 0 978   
2025-07-19 11:23:25.595 [info] indexing created file copilot-custom-prompts/Make shorter.md  [object Object] 
2025-07-19 11:23:25.595 [info] indexing created ignore file copilot-custom-prompts/Make shorter.md   
2025-07-19 11:23:25.627 [info] trigger copilot-custom-prompts/Make shorter.md resolve  [object Object] 
2025-07-19 11:23:25.628 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:25.629 [info] refresh page data from resolve listeners 0 979   
2025-07-19 11:23:26.103 [info] indexing created file copilot-custom-prompts/Rewrite as tweet.md  [object Object] 
2025-07-19 11:23:26.104 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet.md   
2025-07-19 11:23:26.141 [info] trigger copilot-custom-prompts/Rewrite as tweet.md resolve  [object Object] 
2025-07-19 11:23:26.142 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:26.143 [info] refresh page data from resolve listeners 0 980   
2025-07-19 11:23:26.702 [info] indexing created file copilot-custom-prompts/Make longer.md  [object Object] 
2025-07-19 11:23:26.703 [info] indexing created ignore file copilot-custom-prompts/Make longer.md   
2025-07-19 11:23:26.738 [info] trigger copilot-custom-prompts/Make longer.md resolve  [object Object] 
2025-07-19 11:23:26.739 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:26.740 [info] refresh page data from resolve listeners 0 981   
2025-07-19 11:23:27.209 [info] indexing created file copilot-custom-prompts/Explain like I am 5.md  [object Object] 
2025-07-19 11:23:27.209 [info] indexing created ignore file copilot-custom-prompts/Explain like I am 5.md   
2025-07-19 11:23:27.240 [info] trigger copilot-custom-prompts/Explain like I am 5.md resolve  [object Object] 
2025-07-19 11:23:27.240 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:27.242 [info] refresh page data from resolve listeners 0 982   
2025-07-19 11:23:27.739 [info] indexing created file copilot-custom-prompts/Rewrite as press release.md  [object Object] 
2025-07-19 11:23:27.739 [info] indexing created ignore file copilot-custom-prompts/Rewrite as press release.md   
2025-07-19 11:23:27.773 [info] trigger copilot-custom-prompts/Rewrite as press release.md resolve  [object Object] 
2025-07-19 11:23:27.774 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:27.775 [info] refresh page data from resolve listeners 0 983   
2025-07-19 11:23:28.308 [info] indexing created file copilot-custom-prompts/Emojify.md  [object Object] 
2025-07-19 11:23:28.308 [info] indexing created ignore file copilot-custom-prompts/Emojify.md   
2025-07-19 11:23:28.344 [info] trigger copilot-custom-prompts/Emojify.md resolve  [object Object] 
2025-07-19 11:23:28.345 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:28.346 [info] refresh page data from resolve listeners 0 984   
2025-07-19 11:23:28.947 [info] indexing created file copilot-custom-prompts/Rewrite as tweet thread.md  [object Object] 
2025-07-19 11:23:28.947 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet thread.md   
2025-07-19 11:23:28.981 [info] trigger copilot-custom-prompts/Rewrite as tweet thread.md resolve  [object Object] 
2025-07-19 11:23:28.982 [info] index finished after resolve  [object Object] 
2025-07-19 11:23:28.984 [info] refresh page data from resolve listeners 0 985   
2025-07-19 11:25:00.145 [info] indexing created file 学习库/stm32/attachments/2 GPIO-2025-07-13-08-53-44.png  [object Object] 
2025-07-19 11:25:00.166 [info] refresh page data from created listeners 0 986   
2025-07-19 11:25:00.807 [debug] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-07-19 11:25:00.843 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-07-19 11:25:00.845 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:00.846 [info] refresh page data from resolve listeners 0 986   
2025-07-19 11:25:01.057 [info] indexing created file components/logs/2025-07-13.components.log  [object Object] 
2025-07-19 11:25:01.080 [info] refresh page data from created listeners 0 987   
2025-07-19 11:25:02.353 [info] indexing created file components/logs/2025-07-12.components.log  [object Object] 
2025-07-19 11:25:02.449 [info] refresh page data from created listeners 0 988   
2025-07-19 11:25:02.581 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-19 11:25:02.615 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-19 11:25:02.617 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:02.618 [info] refresh page data from resolve listeners 0 988   
2025-07-19 11:25:02.721 [info] indexing created file Home/components/未命名.components  [object Object] 
2025-07-19 11:25:02.742 [info] refresh page data from created listeners 0 989   
2025-07-19 11:25:02.857 [debug] ignore file modify evnet Home/Home.md   
2025-07-19 11:25:02.977 [info] trigger Home/Home.md resolve  [object Object] 
2025-07-19 11:25:02.978 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:02.979 [info] refresh page data from resolve listeners 0 989   
2025-07-19 11:25:03.235 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-19 11:25:03.270 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-19 11:25:03.272 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:03.273 [info] refresh page data from resolve listeners 0 989   
2025-07-19 11:25:03.378 [info] indexing created file 日记库/day/2025-07-14.md  [object Object] 
2025-07-19 11:25:03.378 [info] indexing created ignore file 日记库/day/2025-07-14.md   
2025-07-19 11:25:03.413 [info] trigger 日记库/day/2025-07-14.md resolve  [object Object] 
2025-07-19 11:25:03.415 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:03.416 [info] refresh page data from resolve listeners 0 990   
2025-07-19 11:25:03.516 [debug] ignore file modify evnet 日记库/template/fleeting_note.md   
2025-07-19 11:25:03.627 [info] trigger 日记库/template/fleeting_note.md resolve  [object Object] 
2025-07-19 11:25:03.628 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:03.629 [info] refresh page data from resolve listeners 0 990   
2025-07-19 11:25:03.755 [info] indexing created file 日记库/fleeting_notes/2025-07-14.md  [object Object] 
2025-07-19 11:25:03.755 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-14.md   
2025-07-19 11:25:03.794 [info] trigger 日记库/fleeting_notes/2025-07-14.md resolve  [object Object] 
2025-07-19 11:25:03.795 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:03.796 [info] refresh page data from resolve listeners 0 991   
2025-07-19 11:25:04.462 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-19 11:25:04.803 [info] indexing created file 学习库/Docker/未命名.md  [object Object] 
2025-07-19 11:25:04.804 [info] indexing created ignore file 学习库/Docker/未命名.md   
2025-07-19 11:25:04.839 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-19 11:25:05.090 [info] trigger 学习库/Docker/未命名.md resolve  [object Object] 
2025-07-19 11:25:05.093 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:05.094 [info] refresh page data from resolve listeners 0 992   
2025-07-19 11:25:05.131 [info] indexing created file 学习库/python笔记/项目管理.md  [object Object] 
2025-07-19 11:25:05.131 [info] indexing created ignore file 学习库/python笔记/项目管理.md   
2025-07-19 11:25:05.180 [info] trigger 学习库/python笔记/项目管理.md resolve  [object Object] 
2025-07-19 11:25:05.181 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:05.182 [info] refresh page data from resolve listeners 0 993   
2025-07-19 11:25:07.079 [info] indexing created file components/logs/2025-07-14.components.log  [object Object] 
2025-07-19 11:25:07.107 [info] refresh page data from created listeners 0 994   
2025-07-19 11:25:07.377 [debug] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-07-19 11:25:07.425 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-07-19 11:25:07.427 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:07.428 [info] refresh page data from resolve listeners 0 994   
2025-07-19 11:25:07.731 [info] indexing created file components/logs/2025-07-15.components.log  [object Object] 
2025-07-19 11:25:07.753 [info] refresh page data from created listeners 0 995   
2025-07-19 11:25:07.933 [info] indexing created file components/logs/2025-07-16.components.log  [object Object] 
2025-07-19 11:25:07.959 [info] refresh page data from created listeners 0 996   
2025-07-19 11:25:08.148 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-19 11:25:08.185 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-19 11:25:08.186 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:08.187 [info] refresh page data from resolve listeners 0 996   
2025-07-19 11:25:09.317 [debug] ignore file modify evnet 学习库/学习库.components   
2025-07-19 11:25:09.571 [debug] ignore file modify evnet 学习库/Deep learning/概念库/交叉验证.md   
2025-07-19 11:25:09.609 [info] trigger 学习库/Deep learning/概念库/交叉验证.md resolve  [object Object] 
2025-07-19 11:25:09.612 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:09.613 [info] refresh page data from resolve listeners 0 996   
2025-07-19 11:25:10.038 [info] indexing created file components/logs/2025-07-17.components.log  [object Object] 
2025-07-19 11:25:10.062 [info] refresh page data from created listeners 0 997   
2025-07-19 11:25:10.177 [info] indexing created file 日记库/fleeting_notes/2025-07-18.md  [object Object] 
2025-07-19 11:25:10.178 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-18.md   
2025-07-19 11:25:10.219 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-07-19 11:25:10.220 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:10.221 [info] refresh page data from resolve listeners 0 998   
2025-07-19 11:25:10.318 [info] indexing created file components/logs/2025-07-18.components.log  [object Object] 
2025-07-19 11:25:10.341 [info] refresh page data from created listeners 0 999   
2025-07-19 11:25:11.279 [info] indexing created file Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md  [object Object] 
2025-07-19 11:25:11.279 [info] indexing created ignore file Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md   
2025-07-19 11:25:11.367 [info] trigger Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md resolve  [object Object] 
2025-07-19 11:25:11.370 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:11.371 [info] refresh page data from resolve listeners 0 1000   
2025-07-19 11:25:11.586 [debug] ignore file modify evnet 工作库/项目/舌诊/tensorRT.md   
2025-07-19 11:25:11.635 [info] trigger 工作库/项目/舌诊/tensorRT.md resolve  [object Object] 
2025-07-19 11:25:11.636 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:11.637 [info] refresh page data from resolve listeners 0 1000   
2025-07-19 11:25:11.737 [info] indexing created file Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md  [object Object] 
2025-07-19 11:25:11.737 [info] indexing created ignore file Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md   
2025-07-19 11:25:11.774 [info] trigger Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md resolve  [object Object] 
2025-07-19 11:25:11.775 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:11.776 [info] refresh page data from resolve listeners 0 1001   
2025-07-19 11:25:12.598 [info] indexing created file 日记库/fleeting_notes/2025-07-19.md  [object Object] 
2025-07-19 11:25:12.598 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-19.md   
2025-07-19 11:25:12.651 [info] trigger 日记库/fleeting_notes/2025-07-19.md resolve  [object Object] 
2025-07-19 11:25:12.653 [info] index finished after resolve  [object Object] 
2025-07-19 11:25:12.655 [info] refresh page data from resolve listeners 0 1002   
2025-07-19 11:26:06.856 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-19.md   
2025-07-19 11:26:06.895 [info] trigger 日记库/fleeting_notes/2025-07-19.md resolve  [object Object] 
2025-07-19 11:26:06.897 [info] index finished after resolve  [object Object] 
2025-07-19 11:26:06.898 [info] refresh page data from resolve listeners 0 1002   
2025-07-19 11:27:06.650 [debug] ignore file modify evnet 日记库/fleeting_notes/2025-07-19.md   
2025-07-19 11:27:06.692 [info] trigger 日记库/fleeting_notes/2025-07-19.md resolve  [object Object] 
2025-07-19 11:27:06.695 [info] index finished after resolve  [object Object] 
2025-07-19 11:27:06.696 [info] refresh page data from resolve listeners 0 1002   
