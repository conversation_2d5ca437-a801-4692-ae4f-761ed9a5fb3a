2025-03-12 00:46:08 [info] auth: net::ERR_NETWORK_IO_SUSPENDED   
2025-03-12 00:46:08 [info] indexing created file components/logs/2025-03-12.components.log  [object Object] 
2025-03-12 00:46:08 [info] refresh page data from created listeners 18 516   
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:09 [info] query  [object Object] 
2025-03-12 00:46:10 [info] query  [object Object] 
2025-03-12 00:46:10 [info] query  [object Object] 
2025-03-12 00:46:10 [info] query  [object Object] 
2025-03-12 00:46:10 [info] query  [object Object] 
2025-03-12 11:25:51 [info] auth: net::ERR_NETWORK_IO_SUSPENDED   
2025-03-12 11:30:14 [info] ignore file modify evnet Home/Home.md   
2025-03-12 11:30:14 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-12 11:30:14 [info] index finished after resolve  [object Object] 
2025-03-12 11:30:14 [info] refresh page data from resolve listeners 18 516   
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:15 [info] query  [object Object] 
2025-03-12 11:30:16 [info] query  [object Object] 
2025-03-12 11:30:16 [info] query  [object Object] 
2025-03-12 11:30:16 [info] query  [object Object] 
2025-03-12 11:30:16 [info] query  [object Object] 
2025-03-12 11:30:51 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:35:52 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:35:53 [info] indexing created file 工作库/template/比赛模板.md  [object Object] 
2025-03-12 11:35:53 [info] indexing created ignore file 工作库/template/比赛模板.md   
2025-03-12 11:35:53 [info] trigger 工作库/template/比赛模板.md resolve  [object Object] 
2025-03-12 11:35:53 [info] index finished after resolve  [object Object] 
2025-03-12 11:35:53 [info] refresh page data from resolve listeners 18 517   
2025-03-12 11:35:53 [info] refresh page data from delete listeners 18 516   
2025-03-12 11:35:53 [info] refresh page data from delete listeners 18 515   
2025-03-12 11:35:54 [info] refresh page data from delete listeners 18 514   
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query  [object Object] 
2025-03-12 11:35:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:35:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:35:55 [info] query  [object Object] 
2025-03-12 11:35:55 [info] query  [object Object] 
2025-03-12 11:35:55 [info] query  [object Object] 
2025-03-12 11:35:55 [info] query  [object Object] 
2025-03-12 11:35:56 [info] indexing created file 日记库/template/日记模板.md  [object Object] 
2025-03-12 11:35:56 [info] indexing created ignore file 日记库/template/日记模板.md   
2025-03-12 11:35:56 [info] trigger 日记库/template/日记模板.md resolve  [object Object] 
2025-03-12 11:35:56 [info] index finished after resolve  [object Object] 
2025-03-12 11:35:56 [info] refresh page data from resolve listeners 18 515   
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query  [object Object] 
2025-03-12 11:35:57 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:35:57 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:35:57 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:35:57 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:35:57 [info] indexing created file 日记库/template/日记月复盘模板.md  [object Object] 
2025-03-12 11:35:57 [info] indexing created ignore file 日记库/template/日记月复盘模板.md   
2025-03-12 11:35:57 [info] trigger 日记库/template/日记月复盘模板.md resolve  [object Object] 
2025-03-12 11:35:57 [info] index finished after resolve  [object Object] 
2025-03-12 11:35:57 [info] refresh page data from resolve listeners 18 516   
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query  [object Object] 
2025-03-12 11:35:58 [info] query changed, compare cost 0ms, data length diff 3/4   
2025-03-12 11:35:58 [info] query changed, compare cost 0ms, data length diff 3/4   
2025-03-12 11:35:58 [info] query changed, compare cost 0ms, data length diff 3/4   
2025-03-12 11:35:58 [info] query changed, compare cost 0ms, data length diff 3/4   
2025-03-12 11:35:58 [info] indexing created file 日记库/template/fleeting_note.md  [object Object] 
2025-03-12 11:35:58 [info] indexing created ignore file 日记库/template/fleeting_note.md   
2025-03-12 11:35:58 [info] trigger 日记库/template/fleeting_note.md resolve  [object Object] 
2025-03-12 11:35:58 [info] index finished after resolve  [object Object] 
2025-03-12 11:35:58 [info] refresh page data from resolve listeners 18 517   
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:35:59 [info] query  [object Object] 
2025-03-12 11:36:00 [info] query  [object Object] 
2025-03-12 11:36:00 [info] query  [object Object] 
2025-03-12 11:36:00 [info] query  [object Object] 
2025-03-12 11:36:00 [info] query  [object Object] 
2025-03-12 11:36:13 [info] refresh page data from delete listeners 18 516   
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] query  [object Object] 
2025-03-12 11:36:14 [info] indexing created file 工作库/项目/舌诊/毕设/开题.md  [object Object] 
2025-03-12 11:36:14 [info] indexing created ignore file 工作库/项目/舌诊/毕设/开题.md   
2025-03-12 11:36:14 [info] trigger 工作库/项目/舌诊/毕设/开题.md resolve  [object Object] 
2025-03-12 11:36:14 [info] index finished after resolve  [object Object] 
2025-03-12 11:36:14 [info] refresh page data from resolve listeners 18 517   
2025-03-12 11:36:15 [info] indexing created file 工作库/template/项目模板.md  [object Object] 
2025-03-12 11:36:15 [info] indexing created ignore file 工作库/template/项目模板.md   
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] query  [object Object] 
2025-03-12 11:36:15 [info] trigger 工作库/template/项目模板.md resolve  [object Object] 
2025-03-12 11:36:15 [info] index finished after resolve  [object Object] 
2025-03-12 11:36:15 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:16 [info] query  [object Object] 
2025-03-12 11:36:17 [info] query  [object Object] 
2025-03-12 11:36:17 [info] query  [object Object] 
2025-03-12 11:36:17 [info] query  [object Object] 
2025-03-12 11:36:17 [info] query  [object Object] 
2025-03-12 11:39:12 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-12 11:39:12 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-12 11:39:12 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:12 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] query  [object Object] 
2025-03-12 11:39:13 [info] ignore file modify evnet 学习库/Anki/python/语句.md   
2025-03-12 11:39:13 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-12 11:39:13 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:13 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] query  [object Object] 
2025-03-12 11:39:14 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-12 11:39:15 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-12 11:39:15 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:15 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] query  [object Object] 
2025-03-12 11:39:15 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-03-12 11:39:15 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-12 11:39:15 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:15 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:16 [info] query  [object Object] 
2025-03-12 11:39:17 [info] query  [object Object] 
2025-03-12 11:39:17 [info] query  [object Object] 
2025-03-12 11:39:17 [info] query  [object Object] 
2025-03-12 11:39:17 [info] query  [object Object] 
2025-03-12 11:39:17 [info] ignore file modify evnet Home/components/view/快速导航.md   
2025-03-12 11:39:17 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-12 11:39:17 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:17 [info] refresh page data from resolve listeners 18 518   
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:18 [info] query  [object Object] 
2025-03-12 11:39:19 [info] indexing created file 学习库/template/通用学习模板.md  [object Object] 
2025-03-12 11:39:19 [info] indexing created ignore file 学习库/template/通用学习模板.md   
2025-03-12 11:39:19 [info] trigger 学习库/template/通用学习模板.md resolve  [object Object] 
2025-03-12 11:39:19 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:19 [info] refresh page data from resolve listeners 18 519   
2025-03-12 11:39:19 [info] indexing created file 学习库/二级/通用知识.md  [object Object] 
2025-03-12 11:39:19 [info] indexing created ignore file 学习库/二级/通用知识.md   
2025-03-12 11:39:19 [info] trigger 学习库/二级/通用知识.md resolve  [object Object] 
2025-03-12 11:39:19 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:19 [info] refresh page data from resolve listeners 18 520   
2025-03-12 11:39:19 [info] refresh page data from delete listeners 18 519   
2025-03-12 11:39:20 [info] refresh page data from delete listeners 18 518   
2025-03-12 11:39:20 [info] refresh page data from delete listeners 18 517   
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] query  [object Object] 
2025-03-12 11:39:20 [info] indexing created file att/scripts/getweather.js  [object Object] 
2025-03-12 11:39:20 [info] refresh page data from created listeners 18 518   
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] query  [object Object] 
2025-03-12 11:39:21 [info] indexing created file att/scripts/daily-stats.json  [object Object] 
2025-03-12 11:39:21 [info] refresh page data from created listeners 18 519   
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] query  [object Object] 
2025-03-12 11:39:22 [info] indexing created file att/scripts/dv_weatherSvg.js  [object Object] 
2025-03-12 11:39:22 [info] refresh page data from created listeners 18 520   
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:23 [info] query  [object Object] 
2025-03-12 11:39:25 [info] ignore file modify evnet Home/components/view/weather.md   
2025-03-12 11:39:25 [info] trigger Home/components/view/weather.md resolve  [object Object] 
2025-03-12 11:39:25 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:25 [info] refresh page data from resolve listeners 18 520   
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:26 [info] query  [object Object] 
2025-03-12 11:39:33 [info] indexing created file Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md  [object Object] 
2025-03-12 11:39:33 [info] indexing created ignore file Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md   
2025-03-12 11:39:33 [info] trigger Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md resolve  [object Object] 
2025-03-12 11:39:33 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:33 [info] refresh page data from resolve listeners 18 521   
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] query  [object Object] 
2025-03-12 11:39:34 [info] indexing created file att/scripts/switchLightDark.js  [object Object] 
2025-03-12 11:39:34 [info] refresh page data from created listeners 18 522   
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:35 [info] query  [object Object] 
2025-03-12 11:39:42 [info] indexing created file 未命名.md  [object Object] 
2025-03-12 11:39:42 [info] indexing created ignore file 未命名.md   
2025-03-12 11:39:42 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-12 11:39:42 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-12 11:39:42 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-12 11:39:42 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-12 11:39:42 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-12 11:39:42 [info] trigger 未命名.md resolve  [object Object] 
2025-03-12 11:39:42 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:42 [info] refresh page data from resolve listeners 18 523   
2025-03-12 11:39:43 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-12 11:39:43 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-12 11:39:43 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:43 [info] refresh page data from resolve listeners 18 523   
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:43 [info] query  [object Object] 
2025-03-12 11:39:44 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-12 11:39:44 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-12 11:39:44 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:44 [info] refresh page data from resolve listeners 18 523   
2025-03-12 11:39:44 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:46 [info] query  [object Object] 
2025-03-12 11:39:47 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-12 11:39:47 [info] ignore file modify evnet 学习库/python笔记/leetcode/两数相加.md   
2025-03-12 11:39:47 [info] trigger 学习库/python笔记/leetcode/两数相加.md resolve  [object Object] 
2025-03-12 11:39:47 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:47 [info] refresh page data from resolve listeners 18 523   
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:48 [info] query  [object Object] 
2025-03-12 11:39:51 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-12 11:39:53 [info] indexing created file 日记库/未命名 1.md  [object Object] 
2025-03-12 11:39:53 [info] indexing created ignore file 日记库/未命名 1.md   
2025-03-12 11:39:53 [info] trigger 日记库/未命名 1.md resolve  [object Object] 
2025-03-12 11:39:53 [info] index finished after resolve  [object Object] 
2025-03-12 11:39:53 [info] refresh page data from resolve listeners 18 524   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 523   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 522   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 521   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 520   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 519   
2025-03-12 11:39:53 [info] refresh page data from delete listeners 18 518   
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query  [object Object] 
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 4/2   
2025-03-12 11:39:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-12 11:40:12 [info] query  [object Object] 
2025-03-12 11:40:12 [info] query  [object Object] 
2025-03-12 11:40:12 [info] query  [object Object] 
2025-03-12 11:40:12 [info] query  [object Object] 
2025-03-12 11:40:12 [info] indexing created file 日记库/day/2025-03-11.md  [object Object] 
2025-03-12 11:40:12 [info] indexing created ignore file 日记库/day/2025-03-11.md   
2025-03-12 11:40:12 [info] trigger 日记库/day/2025-03-11.md resolve  [object Object] 
2025-03-12 11:40:12 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:12 [info] refresh page data from resolve listeners 18 519   
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query  [object Object] 
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 2/3   
2025-03-12 11:40:13 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-12 11:40:14 [info] ignore file modify evnet 日记库/日记库.components   
2025-03-12 11:40:14 [info] query  [object Object] 
2025-03-12 11:40:14 [info] query  [object Object] 
2025-03-12 11:40:14 [info] query  [object Object] 
2025-03-12 11:40:14 [info] query  [object Object] 
2025-03-12 11:40:14 [info] indexing created file 工作库/项目/舌诊/test.md  [object Object] 
2025-03-12 11:40:14 [info] indexing created ignore file 工作库/项目/舌诊/test.md   
2025-03-12 11:40:14 [info] trigger 工作库/项目/舌诊/test.md resolve  [object Object] 
2025-03-12 11:40:14 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:14 [info] refresh page data from resolve listeners 18 520   
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] query  [object Object] 
2025-03-12 11:40:15 [info] indexing created file 工作库/项目/声音识别/test 1.md  [object Object] 
2025-03-12 11:40:15 [info] indexing created ignore file 工作库/项目/声音识别/test 1.md   
2025-03-12 11:40:15 [info] trigger 工作库/项目/声音识别/test 1.md resolve  [object Object] 
2025-03-12 11:40:15 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:15 [info] refresh page data from resolve listeners 18 521   
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] query  [object Object] 
2025-03-12 11:40:16 [info] indexing created file 工作库/比赛/测试/测试.md  [object Object] 
2025-03-12 11:40:16 [info] indexing created ignore file 工作库/比赛/测试/测试.md   
2025-03-12 11:40:16 [info] trigger 工作库/比赛/测试/测试.md resolve  [object Object] 
2025-03-12 11:40:16 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:16 [info] refresh page data from resolve listeners 18 522   
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] query  [object Object] 
2025-03-12 11:40:17 [info] indexing created file 工作库/比赛/测试1/测试1.md  [object Object] 
2025-03-12 11:40:17 [info] indexing created ignore file 工作库/比赛/测试1/测试1.md   
2025-03-12 11:40:17 [info] trigger 工作库/比赛/测试1/测试1.md resolve  [object Object] 
2025-03-12 11:40:17 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:17 [info] refresh page data from resolve listeners 18 523   
2025-03-12 11:40:18 [info] ignore file modify evnet 工作库/工作库.components   
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] query  [object Object] 
2025-03-12 11:40:18 [info] indexing created file 学习库/ROS/未命名.md  [object Object] 
2025-03-12 11:40:18 [info] indexing created ignore file 学习库/ROS/未命名.md   
2025-03-12 11:40:18 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-12 11:40:18 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-12 11:40:18 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-12 11:40:18 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-12 11:40:18 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-12 11:40:18 [info] trigger 学习库/ROS/未命名.md resolve  [object Object] 
2025-03-12 11:40:18 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:18 [info] refresh page data from resolve listeners 18 524   
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:19 [info] query  [object Object] 
2025-03-12 11:40:20 [info] indexing created file 学习库/学习库.components  [object Object] 
2025-03-12 11:40:20 [info] refresh page data from created listeners 18 525   
2025-03-12 11:40:20 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:20 [info] query  [object Object] 
2025-03-12 11:40:21 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-12 11:40:21 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-12 11:40:21 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:21 [info] refresh page data from resolve listeners 18 525   
2025-03-12 11:40:22 [info] indexing created file 学习库/Anki/python/attachments/文件操作-2025-03-12.png  [object Object] 
2025-03-12 11:40:22 [info] refresh page data from created listeners 18 526   
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] query  [object Object] 
2025-03-12 11:40:22 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-12 11:40:22 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-12 11:40:22 [info] index finished after resolve  [object Object] 
2025-03-12 11:40:22 [info] refresh page data from resolve listeners 18 526   
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:23 [info] query  [object Object] 
2025-03-12 11:40:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:41:12 [info] query  [object Object] 
2025-03-12 11:41:12 [info] query  [object Object] 
2025-03-12 11:41:12 [info] query  [object Object] 
2025-03-12 11:41:12 [info] query  [object Object] 
2025-03-12 11:42:43 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:45:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:47:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:50:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:52:45 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 11:55:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-12 12:02:49 [info] auth: net::ERR_NAME_NOT_RESOLVED   
2025-03-12 14:09:59 [info] auth: net::ERR_NETWORK_IO_SUSPENDED   
2025-03-12 15:14:14 [info] auth: net::ERR_NETWORK_IO_SUSPENDED   
2025-03-12 15:25:09 [info] auth: net::ERR_NAME_NOT_RESOLVED   
2025-03-12 17:42:40 [info] auth: net::ERR_NETWORK_IO_SUSPENDED   
