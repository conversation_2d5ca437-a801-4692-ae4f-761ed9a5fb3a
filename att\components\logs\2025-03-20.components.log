2025-03-20 10:42:17 [info] refresh page data from delete listeners 7 613   
2025-03-20 10:42:17 [info] indexing created file components/logs/2025-03-20.components.log  [object Object] 
2025-03-20 10:42:17 [info] refresh page data from created listeners 7 614   
2025-03-20 10:44:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 10:49:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 10:54:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 10:59:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:04:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:09:40 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:14:41 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:19:42 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:24:43 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:29:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:34:45 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:39:46 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:44:47 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:49:48 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:54:49 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 11:59:50 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:04:51 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:09:52 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:14:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:19:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:24:55 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:29:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:34:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:39:58 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:44:59 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:50:00 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 12:55:01 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:00:02 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:05:03 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:10:04 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:15:05 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:20:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:25:07 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:30:08 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:35:09 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:40:10 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:45:11 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:50:12 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 13:55:13 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:00:14 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:05:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:10:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:15:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:20:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:25:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:30:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:35:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:40:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:45:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:50:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 14:55:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:00:25 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:05:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:10:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:15:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:20:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:25:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:30:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:41:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:42:12 [info] components database created cost 1 ms   
2025-03-20 15:42:12 [info] components index initializing...   
2025-03-20 15:42:12 [info] components index initialized, 613 files cost 312 ms   
2025-03-20 15:42:12 [info] refresh page data from init listeners 0 613   
2025-03-20 15:42:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 15:42:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 15:42:17 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:42:17 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:42:17 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:42:17 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 15:42:33 [info] components database created cost 4 ms   
2025-03-20 15:42:33 [info] components index initializing...   
2025-03-20 15:42:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:42:33 [info] start to batch put pages: 4   
2025-03-20 15:42:33 [info] batch persist cost 4  22 
2025-03-20 15:42:33 [info] components index initialized, 618 files cost 251 ms   
2025-03-20 15:42:33 [info] refresh page data from init listeners 0 618   
2025-03-20 15:43:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 15:43:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 15:43:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:47 [info] components database created cost 4 ms   
2025-03-20 15:43:47 [info] components index initializing...   
2025-03-20 15:43:47 [info] start to batch put pages: 1   
2025-03-20 15:43:47 [info] batch persist cost 1  10 
2025-03-20 15:43:47 [info] components index initialized, 618 files cost 214 ms   
2025-03-20 15:43:47 [info] refresh page data from init listeners 0 618   
2025-03-20 15:43:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:43:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:43:48 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:43:48 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 15:43:49 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:43:49 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:43:49 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 15:43:49 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:43:50 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 15:43:50 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:43:50 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:43:50 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 15:43:50 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 15:43:51 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:43:51 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 15:43:51 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 15:43:51 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 15:43:51 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 16:47:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 16:47:56 [info] components database created cost 1 ms   
2025-03-20 16:47:56 [info] components index initializing...   
2025-03-20 16:47:56 [info] start to batch put pages: 5   
2025-03-20 16:47:56 [info] batch persist cost 5  23 
2025-03-20 16:47:56 [info] components index initialized, 618 files cost 241 ms   
2025-03-20 16:47:56 [info] refresh page data from init listeners 0 618   
2025-03-20 16:47:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 16:47:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 16:47:56 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 16:47:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 16:47:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-20 16:47:56 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 16:47:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 16:47:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 16:47:57 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 16:47:57 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 16:47:57 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 16:47:57 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 16:47:58 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 16:47:58 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 16:47:58 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-20 16:47:59 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-20 16:48:00 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-20 16:48:00 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-20 16:48:00 [info] index finished after resolve  [object Object] 
2025-03-20 16:48:00 [info] refresh page data from resolve listeners 0 618   
2025-03-20 16:52:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 16:57:55 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:02:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:07:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:12:58 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:17:59 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:23:00 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:28:01 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:33:02 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:38:03 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:43:04 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:48:05 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:53:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 17:58:07 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:03:08 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:08:09 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:13:10 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:18:11 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:23:12 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:28:13 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:33:14 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:38:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:43:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:48:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:53:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 18:58:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:03:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:08:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:13:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:18:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:20:08 [info] indexing created file 学习库/嵌入式.md  [object Object] 
2025-03-20 19:20:08 [info] indexing created ignore file 学习库/嵌入式.md   
2025-03-20 19:20:08 [info] trigger 学习库/嵌入式.md resolve  [object Object] 
2025-03-20 19:20:08 [info] index finished after resolve  [object Object] 
2025-03-20 19:20:08 [info] refresh page data from resolve listeners 0 619   
2025-03-20 19:23:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:26:44 [info] indexing created file 学习库/嵌入式秋招经验贴.md  [object Object] 
2025-03-20 19:26:44 [info] indexing created ignore file 学习库/嵌入式秋招经验贴.md   
2025-03-20 19:26:44 [info] refresh page data from delete listeners 0 618   
2025-03-20 19:26:44 [info] trigger 学习库/嵌入式秋招经验贴.md resolve  [object Object] 
2025-03-20 19:26:44 [info] index finished after resolve  [object Object] 
2025-03-20 19:26:44 [info] refresh page data from resolve listeners 0 619   
2025-03-20 19:28:25 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:33:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:38:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:43:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:48:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:53:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 19:58:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:03:32 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:08:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:13:34 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:18:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:23:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:28:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:33:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:38:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-20 20:43:50 [info] auth: net::ERR_CONNECTION_CLOSED   
2025-03-20 20:49:01 [info] auth: net::ERR_CONNECTION_CLOSED   
2025-03-20 21:00:47 [info] ignore file modify evnet 学习库/嵌入式秋招经验贴.md   
2025-03-20 21:00:47 [info] trigger 学习库/嵌入式秋招经验贴.md resolve  [object Object] 
2025-03-20 21:00:47 [info] index finished after resolve  [object Object] 
2025-03-20 21:00:47 [info] refresh page data from resolve listeners 0 619   
2025-03-20 21:04:24 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:24 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:24 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:24 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query  [object Object] 
2025-03-20 21:04:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:26 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:26 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:26 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:26 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query  [object Object] 
2025-03-20 21:04:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:26 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:29 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:29 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:29 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:29 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query  [object Object] 
2025-03-20 21:04:29 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:29 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:32 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:32 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:32 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:32 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query  [object Object] 
2025-03-20 21:04:32 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:32 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:35 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:35 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:35 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:35 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query  [object Object] 
2025-03-20 21:04:35 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:35 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:38 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:38 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:38 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:38 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query  [object Object] 
2025-03-20 21:04:39 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:39 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query  [object Object] 
2025-03-20 21:04:41 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:48 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:48 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:48 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:48 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query  [object Object] 
2025-03-20 21:04:48 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:04:48 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:55 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:04:55 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:04:55 [info] index finished after resolve  [object Object] 
2025-03-20 21:04:55 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query  [object Object] 
2025-03-20 21:04:55 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:04:55 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:12 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:12 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:12 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:12 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query  [object Object] 
2025-03-20 21:05:12 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:12 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:14 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:14 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:14 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:14 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query  [object Object] 
2025-03-20 21:05:14 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:14 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:19 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:19 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:19 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:19 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query  [object Object] 
2025-03-20 21:05:19 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:19 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:21 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:21 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:21 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:21 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query  [object Object] 
2025-03-20 21:05:22 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:22 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:24 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:24 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:24 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:24 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query  [object Object] 
2025-03-20 21:05:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:26 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:26 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:26 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:26 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query  [object Object] 
2025-03-20 21:05:26 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:29 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:29 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:29 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:29 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query  [object Object] 
2025-03-20 21:05:30 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:30 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:33 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:33 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:33 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:33 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query  [object Object] 
2025-03-20 21:05:33 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:33 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:37 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:37 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:37 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:37 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query  [object Object] 
2025-03-20 21:05:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query  [object Object] 
2025-03-20 21:05:41 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:46 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:46 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:46 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:46 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query  [object Object] 
2025-03-20 21:05:46 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:46 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:49 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:50 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:50 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:50 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query  [object Object] 
2025-03-20 21:05:50 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:50 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:56 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:56 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:56 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:56 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query  [object Object] 
2025-03-20 21:05:56 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:56 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:05:59 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:05:59 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:05:59 [info] index finished after resolve  [object Object] 
2025-03-20 21:05:59 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query  [object Object] 
2025-03-20 21:05:59 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:05:59 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:46 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:46 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:46 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:46 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query  [object Object] 
2025-03-20 21:09:46 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:46 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:48 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:48 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:48 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:48 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query  [object Object] 
2025-03-20 21:09:48 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:09:48 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:51 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:51 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:51 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:51 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query  [object Object] 
2025-03-20 21:09:51 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:51 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:53 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:53 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:53 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:53 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query  [object Object] 
2025-03-20 21:09:53 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:09:53 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:55 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:55 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:55 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:55 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query  [object Object] 
2025-03-20 21:09:55 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:55 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:57 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:09:57 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:09:57 [info] index finished after resolve  [object Object] 
2025-03-20 21:09:57 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query  [object Object] 
2025-03-20 21:09:57 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:09:57 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:07 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:07 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:07 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:07 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query  [object Object] 
2025-03-20 21:10:07 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:07 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:10 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:10 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:10 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:10 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query  [object Object] 
2025-03-20 21:10:10 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:10 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:12 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:12 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:12 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:12 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query  [object Object] 
2025-03-20 21:10:12 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:12 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:14 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:14 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:14 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:14 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query  [object Object] 
2025-03-20 21:10:15 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:10:15 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:18 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:18 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:18 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:18 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query  [object Object] 
2025-03-20 21:10:18 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:18 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:21 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:21 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:21 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:21 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query  [object Object] 
2025-03-20 21:10:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:23 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:23 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:23 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:23 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query  [object Object] 
2025-03-20 21:10:23 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:10:23 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:26 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:26 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:26 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:26 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query  [object Object] 
2025-03-20 21:10:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:28 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:29 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:29 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:29 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query  [object Object] 
2025-03-20 21:10:29 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:10:29 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:33 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:33 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:33 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:33 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query  [object Object] 
2025-03-20 21:10:33 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:33 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query  [object Object] 
2025-03-20 21:10:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:47 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:47 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:47 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:47 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query  [object Object] 
2025-03-20 21:10:47 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:47 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:52 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:52 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:52 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:52 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query  [object Object] 
2025-03-20 21:10:52 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:10:52 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:54 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:54 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:54 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:54 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query  [object Object] 
2025-03-20 21:10:54 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:54 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:56 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:56 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:56 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:56 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query  [object Object] 
2025-03-20 21:10:56 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:56 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:10:59 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:10:59 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:10:59 [info] index finished after resolve  [object Object] 
2025-03-20 21:10:59 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query  [object Object] 
2025-03-20 21:10:59 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:10:59 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:11:04 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:11:04 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:11:04 [info] index finished after resolve  [object Object] 
2025-03-20 21:11:04 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query  [object Object] 
2025-03-20 21:11:04 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:11:04 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:11:06 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:11:06 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:11:06 [info] index finished after resolve  [object Object] 
2025-03-20 21:11:06 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query  [object Object] 
2025-03-20 21:11:06 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:11:06 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:11:11 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:11:11 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:11:11 [info] index finished after resolve  [object Object] 
2025-03-20 21:11:11 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query  [object Object] 
2025-03-20 21:11:11 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:11:11 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:15:19 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:15:19 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:15:19 [info] index finished after resolve  [object Object] 
2025-03-20 21:15:19 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query  [object Object] 
2025-03-20 21:15:19 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:15:19 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:15:25 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:15:25 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:15:25 [info] index finished after resolve  [object Object] 
2025-03-20 21:15:25 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query  [object Object] 
2025-03-20 21:15:25 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:15:25 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:15:28 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:15:28 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:15:28 [info] index finished after resolve  [object Object] 
2025-03-20 21:15:28 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query  [object Object] 
2025-03-20 21:15:28 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:15:28 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:15:33 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:15:33 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:15:33 [info] index finished after resolve  [object Object] 
2025-03-20 21:15:33 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query  [object Object] 
2025-03-20 21:15:33 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:15:33 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:19:55 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:19:55 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:19:55 [info] index finished after resolve  [object Object] 
2025-03-20 21:19:55 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query  [object Object] 
2025-03-20 21:19:55 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:19:55 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:19:57 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:19:57 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:19:57 [info] index finished after resolve  [object Object] 
2025-03-20 21:19:57 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query  [object Object] 
2025-03-20 21:19:57 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:19:57 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:19:59 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:19:59 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:19:59 [info] index finished after resolve  [object Object] 
2025-03-20 21:19:59 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query  [object Object] 
2025-03-20 21:19:59 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:19:59 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:20:01 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:20:01 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:20:01 [info] index finished after resolve  [object Object] 
2025-03-20 21:20:01 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query  [object Object] 
2025-03-20 21:20:01 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:20:01 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:35 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:35 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:35 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:35 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query  [object Object] 
2025-03-20 21:25:35 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:35 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:37 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:37 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:37 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:37 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query  [object Object] 
2025-03-20 21:25:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:39 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:39 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:39 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:39 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query  [object Object] 
2025-03-20 21:25:39 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:39 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query  [object Object] 
2025-03-20 21:25:42 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:42 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:44 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:44 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:44 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:44 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query  [object Object] 
2025-03-20 21:25:44 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:25:44 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:46 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:46 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:46 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:46 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query  [object Object] 
2025-03-20 21:25:46 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:25:46 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:48 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:25:48 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:25:48 [info] index finished after resolve  [object Object] 
2025-03-20 21:25:48 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query  [object Object] 
2025-03-20 21:25:49 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:25:49 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:26:14 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:26:14 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:26:14 [info] index finished after resolve  [object Object] 
2025-03-20 21:26:14 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query  [object Object] 
2025-03-20 21:26:14 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:26:14 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:26:16 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:26:16 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:26:16 [info] index finished after resolve  [object Object] 
2025-03-20 21:26:16 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query  [object Object] 
2025-03-20 21:26:16 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:26:16 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:09 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:09 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:09 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:09 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query  [object Object] 
2025-03-20 21:27:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:11 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:11 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:11 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:11 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query  [object Object] 
2025-03-20 21:27:11 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:11 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:14 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:14 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:14 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:14 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query  [object Object] 
2025-03-20 21:27:14 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:27:14 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:17 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:17 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:17 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:17 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query  [object Object] 
2025-03-20 21:27:17 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:17 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:19 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:19 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:19 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:19 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query  [object Object] 
2025-03-20 21:27:19 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:19 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:21 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:21 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:21 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:21 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query  [object Object] 
2025-03-20 21:27:21 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:27:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:24 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:24 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:24 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:24 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query  [object Object] 
2025-03-20 21:27:24 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:27:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:26 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:26 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:26 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:26 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query  [object Object] 
2025-03-20 21:27:26 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:27:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:28 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:28 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:28 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:28 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query  [object Object] 
2025-03-20 21:27:28 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:28 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:27:30 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:27:30 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:27:30 [info] index finished after resolve  [object Object] 
2025-03-20 21:27:30 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query  [object Object] 
2025-03-20 21:27:30 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:27:30 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:03 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:03 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:03 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:03 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query  [object Object] 
2025-03-20 21:28:03 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:03 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:05 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:05 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:05 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:05 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query  [object Object] 
2025-03-20 21:28:05 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:05 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:07 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:08 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:08 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:08 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query  [object Object] 
2025-03-20 21:28:08 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:08 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:10 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:10 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:10 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:10 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query  [object Object] 
2025-03-20 21:28:10 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:10 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:12 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:12 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:12 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:12 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query  [object Object] 
2025-03-20 21:28:12 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:12 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:14 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:14 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:14 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:14 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query  [object Object] 
2025-03-20 21:28:15 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:15 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:21 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:21 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:21 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:21 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query  [object Object] 
2025-03-20 21:28:21 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:23 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:23 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:23 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:23 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query  [object Object] 
2025-03-20 21:28:23 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:23 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:25 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:25 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:25 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:25 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query  [object Object] 
2025-03-20 21:28:25 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:25 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:27 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:27 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:27 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:27 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query  [object Object] 
2025-03-20 21:28:27 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:27 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:29 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:29 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:29 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:29 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query  [object Object] 
2025-03-20 21:28:30 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:30 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:32 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:32 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:32 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:32 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query  [object Object] 
2025-03-20 21:28:32 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:32 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:53 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:54 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:54 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:54 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query  [object Object] 
2025-03-20 21:28:54 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:54 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:56 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:56 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:56 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:56 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query  [object Object] 
2025-03-20 21:28:56 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:56 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:28:58 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:28:58 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:28:58 [info] index finished after resolve  [object Object] 
2025-03-20 21:28:58 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query  [object Object] 
2025-03-20 21:28:58 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:28:58 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:00 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:29:00 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:29:00 [info] index finished after resolve  [object Object] 
2025-03-20 21:29:00 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query  [object Object] 
2025-03-20 21:29:00 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:00 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:02 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:29:02 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:29:02 [info] index finished after resolve  [object Object] 
2025-03-20 21:29:02 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query  [object Object] 
2025-03-20 21:29:02 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:29:02 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:04 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:29:04 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:29:04 [info] index finished after resolve  [object Object] 
2025-03-20 21:29:04 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query  [object Object] 
2025-03-20 21:29:04 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:04 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:07 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:29:07 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:29:07 [info] index finished after resolve  [object Object] 
2025-03-20 21:29:07 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query  [object Object] 
2025-03-20 21:29:07 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:29:07 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:09 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:29:09 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:29:09 [info] index finished after resolve  [object Object] 
2025-03-20 21:29:09 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query  [object Object] 
2025-03-20 21:29:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:29:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:18 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:18 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:18 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:18 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query  [object Object] 
2025-03-20 21:32:19 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:32:19 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:32 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:32 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:32 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:32 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query  [object Object] 
2025-03-20 21:32:32 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:32:32 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:35 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:35 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:35 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:35 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query  [object Object] 
2025-03-20 21:32:35 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:35 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:37 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:37 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:37 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:37 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query  [object Object] 
2025-03-20 21:32:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:37 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:39 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:39 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:39 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:39 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query  [object Object] 
2025-03-20 21:32:39 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:32:39 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query  [object Object] 
2025-03-20 21:32:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:43 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:43 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:43 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:43 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query  [object Object] 
2025-03-20 21:32:43 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:43 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:45 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:32:45 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:32:45 [info] index finished after resolve  [object Object] 
2025-03-20 21:32:45 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query  [object Object] 
2025-03-20 21:32:45 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:32:45 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:15 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:15 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:15 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:15 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query  [object Object] 
2025-03-20 21:45:15 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:15 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:18 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:18 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:18 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:18 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query  [object Object] 
2025-03-20 21:45:18 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:45:18 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:21 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:21 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:21 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:21 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query  [object Object] 
2025-03-20 21:45:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:21 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:23 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:23 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:23 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:23 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query  [object Object] 
2025-03-20 21:45:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:24 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:26 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:26 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:26 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:26 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query  [object Object] 
2025-03-20 21:45:26 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:45:26 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:28 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:28 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:28 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:28 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query  [object Object] 
2025-03-20 21:45:28 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:28 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:30 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:30 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:30 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:30 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query  [object Object] 
2025-03-20 21:45:30 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:45:30 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:33 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:33 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:33 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:33 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query  [object Object] 
2025-03-20 21:45:33 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:45:33 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:36 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:36 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:36 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:36 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query  [object Object] 
2025-03-20 21:45:36 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:36 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:41 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:45:41 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:45:41 [info] index finished after resolve  [object Object] 
2025-03-20 21:45:41 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query  [object Object] 
2025-03-20 21:45:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:45:41 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:51:09 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:51:09 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:51:09 [info] index finished after resolve  [object Object] 
2025-03-20 21:51:09 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query  [object Object] 
2025-03-20 21:51:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:51:09 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:51:23 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:51:23 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:51:23 [info] index finished after resolve  [object Object] 
2025-03-20 21:51:23 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query  [object Object] 
2025-03-20 21:51:23 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:51:23 [info] query changed, compare cost 0ms, data length diff 615/615   
2025-03-20 21:51:31 [info] ignore file modify evnet 学习库/c/核心语法.md   
2025-03-20 21:51:31 [info] trigger 学习库/c/核心语法.md resolve  [object Object] 
2025-03-20 21:51:31 [info] index finished after resolve  [object Object] 
2025-03-20 21:51:31 [info] refresh page data from resolve listeners 7 615   
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query  [object Object] 
2025-03-20 21:51:31 [info] query changed, compare cost 1ms, data length diff 615/615   
2025-03-20 21:51:31 [info] query changed, compare cost 0ms, data length diff 615/615   
