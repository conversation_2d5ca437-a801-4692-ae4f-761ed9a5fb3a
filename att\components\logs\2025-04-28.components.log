2025-04-28 08:50:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:50:00 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:50:00 [info] index finished after resolve  [object Object] 
2025-04-28 08:50:00 [info] refresh page data from resolve listeners 0 847   
2025-04-28 08:50:00 [info] indexing created file components/logs/2025-04-28.components.log  [object Object] 
2025-04-28 08:50:00 [info] refresh page data from created listeners 0 848   
2025-04-28 08:51:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:51:49 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:51:49 [info] index finished after resolve  [object Object] 
2025-04-28 08:51:49 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:52:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-28 08:52:07 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-28 08:52:07 [info] index finished after resolve  [object Object] 
2025-04-28 08:52:07 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:53:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-28 08:53:17 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-28 08:53:17 [info] index finished after resolve  [object Object] 
2025-04-28 08:53:17 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:54:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:54:01 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:54:01 [info] index finished after resolve  [object Object] 
2025-04-28 08:54:01 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:55:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:55:31 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:55:31 [info] index finished after resolve  [object Object] 
2025-04-28 08:55:31 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:55:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:55:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:55:36 [info] index finished after resolve  [object Object] 
2025-04-28 08:55:36 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:55:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:55:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:55:40 [info] index finished after resolve  [object Object] 
2025-04-28 08:55:40 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-28 08:57:18 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-28 08:57:18 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:18 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-28 08:57:20 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-28 08:57:20 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:20 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:57:24 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:57:24 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:24 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:57:27 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:57:27 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:27 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:57:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:57:32 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:32 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:57:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:57:36 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:36 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:57:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:57:56 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:57:56 [info] index finished after resolve  [object Object] 
2025-04-28 08:57:56 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:12 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:12 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:12 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:18 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:18 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:18 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:20 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:20 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:23 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:23 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:23 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:27 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:27 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:27 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:32 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:32 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:41 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:41 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:41 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:43 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:43 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:45 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:45 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:45 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:58:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:58:48 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:58:48 [info] index finished after resolve  [object Object] 
2025-04-28 08:58:48 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:59:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:59:24 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:59:24 [info] index finished after resolve  [object Object] 
2025-04-28 08:59:24 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:59:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:59:31 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:59:31 [info] index finished after resolve  [object Object] 
2025-04-28 08:59:31 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:59:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:59:34 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:59:34 [info] index finished after resolve  [object Object] 
2025-04-28 08:59:34 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:59:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:59:38 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:59:38 [info] index finished after resolve  [object Object] 
2025-04-28 08:59:38 [info] refresh page data from resolve listeners 0 848   
2025-04-28 08:59:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 08:59:42 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 08:59:42 [info] index finished after resolve  [object Object] 
2025-04-28 08:59:42 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:00:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:00:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:00:43 [info] index finished after resolve  [object Object] 
2025-04-28 09:00:43 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:00:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:00:48 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:00:48 [info] index finished after resolve  [object Object] 
2025-04-28 09:00:48 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:00:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:00:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:00:51 [info] index finished after resolve  [object Object] 
2025-04-28 09:00:51 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:08:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:08:57 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:08:57 [info] index finished after resolve  [object Object] 
2025-04-28 09:08:57 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:09:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:09:47 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:09:47 [info] index finished after resolve  [object Object] 
2025-04-28 09:09:47 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:11:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:11:34 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:11:34 [info] index finished after resolve  [object Object] 
2025-04-28 09:11:34 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:11:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:11:44 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:11:44 [info] index finished after resolve  [object Object] 
2025-04-28 09:11:44 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:12:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:12:44 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:12:44 [info] index finished after resolve  [object Object] 
2025-04-28 09:12:44 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:13:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-28 09:13:04 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:13:04 [info] index finished after resolve  [object Object] 
2025-04-28 09:13:04 [info] refresh page data from resolve listeners 0 848   
2025-04-28 09:16:22 [info] indexing created file 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md  [object Object] 
2025-04-28 09:16:22 [info] indexing created ignore file 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-04-28 09:16:22 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-28 09:16:22 [info] index finished after resolve  [object Object] 
2025-04-28 09:16:22 [info] refresh page data from resolve listeners 0 849   
2025-04-28 09:16:23 [info] refresh page data from delete listeners 0 848   
2025-04-28 09:16:46 [info] indexing created file 学习库/Deep learning/pytorch/3. 反向传播.md  [object Object] 
2025-04-28 09:16:46 [info] indexing created ignore file 学习库/Deep learning/pytorch/3. 反向传播.md   
2025-04-28 09:16:46 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播.md resolve  [object Object] 
2025-04-28 09:16:46 [info] index finished after resolve  [object Object] 
2025-04-28 09:16:46 [info] refresh page data from resolve listeners 0 849   
2025-04-28 09:18:45 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf.crdownload  [object Object] 
2025-04-28 09:18:45 [info] refresh page data from created listeners 0 850   
2025-04-28 09:18:45 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf  [object Object] 
2025-04-28 09:18:45 [info] refresh page data from created listeners 0 851   
2025-04-28 09:18:45 [info] trigger 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf resolve  [object Object] 
2025-04-28 09:18:45 [info] index finished after resolve  [object Object] 
2025-04-28 09:18:45 [info] refresh page data from modify listeners 0 851   
2025-04-28 09:18:46 [info] refresh page data from delete listeners 0 850   
2025-04-28 09:19:29 [info] refresh page data from rename listeners 0 850   
2025-04-28 09:19:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 09:19:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 09:19:47 [info] index finished after resolve  [object Object] 
2025-04-28 09:19:47 [info] refresh page data from resolve listeners 0 850   
2025-04-28 09:19:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 09:19:52 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 09:19:52 [info] index finished after resolve  [object Object] 
2025-04-28 09:19:52 [info] refresh page data from resolve listeners 0 850   
2025-04-28 09:22:59 [info] components database created cost 13 ms   
2025-04-28 09:22:59 [info] components index initializing...   
2025-04-28 09:23:00 [info] start to batch put pages: 6   
2025-04-28 09:23:00 [info] batch persist cost 6  231 
2025-04-28 09:23:00 [info] components index initialized, 850 files cost 1511 ms   
2025-04-28 09:23:00 [info] refresh page data from init listeners 0 850   
2025-04-28 09:23:02 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-28 09:23:02 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-28 09:23:03 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-28 09:23:03 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-28 09:23:03 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-28 09:23:03 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-28 09:28:50 [info] trigger 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf resolve  [object Object] 
2025-04-28 09:28:50 [info] index finished after resolve  [object Object] 
2025-04-28 09:28:50 [info] refresh page data from modify listeners 0 850   
2025-04-28 16:01:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:01:54 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:01:54 [info] index finished after resolve  [object Object] 
2025-04-28 16:01:54 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:02:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:02:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:02:21 [info] index finished after resolve  [object Object] 
2025-04-28 16:02:21 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:02:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:02:23 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:02:23 [info] index finished after resolve  [object Object] 
2025-04-28 16:02:23 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:02:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:02:28 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:02:28 [info] index finished after resolve  [object Object] 
2025-04-28 16:02:28 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:03:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:03:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:03:29 [info] index finished after resolve  [object Object] 
2025-04-28 16:03:29 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:03:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:03:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:03:32 [info] index finished after resolve  [object Object] 
2025-04-28 16:03:32 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:03:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:03:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:03:35 [info] index finished after resolve  [object Object] 
2025-04-28 16:03:35 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:03:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:03:57 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:03:57 [info] index finished after resolve  [object Object] 
2025-04-28 16:03:57 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:04:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:04:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:04:14 [info] index finished after resolve  [object Object] 
2025-04-28 16:04:14 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:04:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:04:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:04:16 [info] index finished after resolve  [object Object] 
2025-04-28 16:04:16 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:04:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:04:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:04:18 [info] index finished after resolve  [object Object] 
2025-04-28 16:04:18 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:04:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:04:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:04:20 [info] index finished after resolve  [object Object] 
2025-04-28 16:04:20 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:05:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:05:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:05:10 [info] index finished after resolve  [object Object] 
2025-04-28 16:05:10 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:05:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:05:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:05:32 [info] index finished after resolve  [object Object] 
2025-04-28 16:05:32 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:06:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:06:26 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:06:26 [info] index finished after resolve  [object Object] 
2025-04-28 16:06:26 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:07:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:07:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:07:27 [info] index finished after resolve  [object Object] 
2025-04-28 16:07:27 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:08:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:08:01 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:08:01 [info] index finished after resolve  [object Object] 
2025-04-28 16:08:01 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:10:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:10:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:10:18 [info] index finished after resolve  [object Object] 
2025-04-28 16:10:18 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:11:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:11:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:11:10 [info] index finished after resolve  [object Object] 
2025-04-28 16:11:10 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:11:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:11:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:11:17 [info] index finished after resolve  [object Object] 
2025-04-28 16:11:17 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:11:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:11:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:11:40 [info] index finished after resolve  [object Object] 
2025-04-28 16:11:40 [info] refresh page data from resolve listeners 0 850   
2025-04-28 16:12:33 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-04-28-16-12-33.png  [object Object] 
2025-04-28 16:12:33 [info] refresh page data from created listeners 0 851   
2025-04-28 16:12:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:12:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:12:35 [info] index finished after resolve  [object Object] 
2025-04-28 16:12:35 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:12:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:12:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:12:37 [info] index finished after resolve  [object Object] 
2025-04-28 16:12:37 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:13:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:13:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:13:11 [info] index finished after resolve  [object Object] 
2025-04-28 16:13:11 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:13:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-28 16:13:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-28 16:13:14 [info] index finished after resolve  [object Object] 
2025-04-28 16:13:14 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:07 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:07 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:07 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:07 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:25 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:25 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:25 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:25 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:30 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:30 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:30 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:30 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:48 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:48 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:48 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:49 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:51 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:51 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:51 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:51 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:35:56 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:35:56 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:35:56 [info] index finished after resolve  [object Object] 
2025-04-28 16:35:56 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:00 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:00 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:00 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:00 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:04 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:04 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:04 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:04 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:06 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:06 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:06 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:06 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:16 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:16 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:16 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:16 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:22 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:22 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:22 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:22 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:24 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:24 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:24 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:24 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:26 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:26 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:26 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:26 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:32 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:32 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:32 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:32 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:34 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:34 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:34 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:34 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:37 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:37 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:37 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:37 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:39 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:39 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:39 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:39 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:41 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:41 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:41 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:41 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:52 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:52 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:52 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:52 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:36:55 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:36:55 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:36:55 [info] index finished after resolve  [object Object] 
2025-04-28 16:36:55 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:37:07 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:37:07 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:37:07 [info] index finished after resolve  [object Object] 
2025-04-28 16:37:07 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:37:09 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:37:09 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:37:09 [info] index finished after resolve  [object Object] 
2025-04-28 16:37:09 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:37:17 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:37:17 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:37:17 [info] index finished after resolve  [object Object] 
2025-04-28 16:37:17 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:38:03 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:38:03 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:38:03 [info] index finished after resolve  [object Object] 
2025-04-28 16:38:03 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:38:06 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:38:06 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:38:06 [info] index finished after resolve  [object Object] 
2025-04-28 16:38:06 [info] refresh page data from resolve listeners 0 851   
2025-04-28 16:38:12 [info] ignore file modify evnet 学习库/linux/wsl2.md   
2025-04-28 16:38:12 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-28 16:38:12 [info] index finished after resolve  [object Object] 
2025-04-28 16:38:12 [info] refresh page data from resolve listeners 0 851   
