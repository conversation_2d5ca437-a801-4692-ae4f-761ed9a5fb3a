2025-07-06 07:17:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-06 07:17:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-06 07:17:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-06 07:17:16 [info] components database created cost 6 ms   
2025-07-06 07:17:16 [info] components index initializing...   
2025-07-06 07:17:19 [info] indexing created file components/logs/2025-07-06.components.log  [object Object] 
2025-07-06 07:17:19 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-06 07:17:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-06 07:17:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-06 07:17:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-06 07:17:21 [info] start to batch put pages: 8   
2025-07-06 07:17:21 [info] batch persist cost 8  10 
2025-07-06 07:17:21 [info] components index initialized, 727 files cost 4439 ms   
2025-07-06 07:17:21 [info] refresh page data from init listeners 0 727   
2025-07-06 07:17:22 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-07-10-45-57.png  [object Object] 
2025-07-06 07:17:22 [info] refresh page data from created listeners 0 728   
2025-07-06 07:17:24 [info] indexing created file components/logs/2025-04-07.components.log  [object Object] 
2025-07-06 07:17:24 [info] refresh page data from created listeners 0 729   
2025-07-06 07:17:25 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211848.png  [object Object] 
2025-07-06 07:17:25 [info] refresh page data from created listeners 0 730   
2025-07-06 07:17:26 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-25-46.png  [object Object] 
2025-07-06 07:17:26 [info] refresh page data from created listeners 0 731   
2025-07-06 07:17:26 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-07-06 07:17:28 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-17-10.png  [object Object] 
2025-07-06 07:17:28 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-07-06 07:17:28 [info] refresh page data from created listeners 0 732   
2025-07-06 07:17:30 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-16-02.png  [object Object] 
2025-07-06 07:17:30 [info] refresh page data from created listeners 0 733   
2025-07-06 07:17:30 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-07-06 07:17:31 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211103.png  [object Object] 
2025-07-06 07:17:31 [info] refresh page data from created listeners 0 734   
2025-07-06 07:17:31 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015213508.png  [object Object] 
2025-07-06 07:17:31 [info] refresh page data from created listeners 0 735   
2025-07-06 07:17:33 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241014093021.png  [object Object] 
2025-07-06 07:17:33 [info] refresh page data from created listeners 0 736   
2025-07-06 07:17:35 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015210604.png  [object Object] 
2025-07-06 07:17:35 [info] refresh page data from created listeners 0 737   
2025-07-06 07:17:36 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-07-06 07:17:36 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-07-06 07:17:36 [info] index finished after resolve  [object Object] 
2025-07-06 07:17:36 [info] refresh page data from resolve listeners 0 737   
2025-07-06 07:17:56 [info] indexing created file 学习库/Anki/stm32/I2C.md  [object Object] 
2025-07-06 07:17:56 [info] indexing created ignore file 学习库/Anki/stm32/I2C.md   
2025-07-06 07:17:56 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-07-06 07:17:56 [info] index finished after resolve  [object Object] 
2025-07-06 07:17:56 [info] refresh page data from resolve listeners 0 738   
2025-07-06 07:17:58 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-08-14-36-45.png  [object Object] 
2025-07-06 07:17:58 [info] refresh page data from created listeners 0 739   
2025-07-06 07:17:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-07-06 07:17:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-07-06 07:17:59 [info] index finished after resolve  [object Object] 
2025-07-06 07:17:59 [info] refresh page data from resolve listeners 0 739   
2025-07-06 07:18:15 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-07-06 07:18:15 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-07-06 07:18:15 [info] index finished after resolve  [object Object] 
2025-07-06 07:18:15 [info] refresh page data from resolve listeners 0 739   
2025-07-06 07:18:16 [info] indexing created file components/logs/2025-04-08.components.log  [object Object] 
2025-07-06 07:18:16 [info] refresh page data from created listeners 0 740   
2025-07-06 07:18:17 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-07-06 07:18:17 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-07-06 07:18:17 [info] index finished after resolve  [object Object] 
2025-07-06 07:18:17 [info] refresh page data from resolve listeners 0 740   
2025-07-06 07:18:17 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-07-06 07:18:17 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-07-06 07:18:17 [info] index finished after resolve  [object Object] 
2025-07-06 07:18:17 [info] refresh page data from resolve listeners 0 740   
2025-07-06 07:18:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-07-06 07:18:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-07-06 07:18:18 [info] index finished after resolve  [object Object] 
2025-07-06 07:18:18 [info] refresh page data from resolve listeners 0 740   
2025-07-06 07:18:20 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-09-50-40.png  [object Object] 
2025-07-06 07:18:20 [info] refresh page data from created listeners 0 741   
2025-07-06 07:18:21 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-13-55.png  [object Object] 
2025-07-06 07:18:21 [info] refresh page data from created listeners 0 742   
2025-07-06 07:18:23 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-18-24.png  [object Object] 
2025-07-06 07:18:23 [info] refresh page data from created listeners 0 743   
2025-07-06 07:18:24 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-27-20.png  [object Object] 
2025-07-06 07:18:24 [info] refresh page data from created listeners 0 744   
2025-07-06 07:18:27 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-30-23.png  [object Object] 
2025-07-06 07:18:27 [info] refresh page data from created listeners 0 745   
2025-07-06 07:18:31 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-14-15-25.png  [object Object] 
2025-07-06 07:18:31 [info] refresh page data from created listeners 0 746   
2025-07-06 07:18:32 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-24-16.png  [object Object] 
2025-07-06 07:18:32 [info] refresh page data from created listeners 0 747   
2025-07-06 07:18:36 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-32-10.png  [object Object] 
2025-07-06 07:18:36 [info] refresh page data from created listeners 0 748   
2025-07-06 07:18:41 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-35-20.png  [object Object] 
2025-07-06 07:18:41 [info] refresh page data from created listeners 0 749   
2025-07-06 07:18:43 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-21-45-18.png  [object Object] 
2025-07-06 07:18:44 [info] refresh page data from created listeners 0 750   
2025-07-06 07:18:47 [info] indexing created file components/logs/2025-04-09.components.log  [object Object] 
2025-07-06 07:18:47 [info] refresh page data from created listeners 0 751   
2025-07-06 07:18:52 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-09-39-05.png  [object Object] 
2025-07-06 07:18:52 [info] refresh page data from created listeners 0 752   
2025-07-06 07:18:53 [info] indexing created file 学习库/Anki/stm32/SPI.md  [object Object] 
2025-07-06 07:18:53 [info] indexing created ignore file 学习库/Anki/stm32/SPI.md   
2025-07-06 07:18:53 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-07-06 07:18:53 [info] index finished after resolve  [object Object] 
2025-07-06 07:18:53 [info] refresh page data from resolve listeners 0 753   
2025-07-06 07:18:55 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-10-16-13.png  [object Object] 
2025-07-06 07:18:55 [info] refresh page data from created listeners 0 754   
2025-07-06 07:18:56 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-03-37.png  [object Object] 
2025-07-06 07:18:56 [info] refresh page data from created listeners 0 755   
2025-07-06 07:18:57 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-03-59.png  [object Object] 
2025-07-06 07:18:57 [info] refresh page data from created listeners 0 756   
2025-07-06 07:18:59 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-04-18.png  [object Object] 
2025-07-06 07:18:59 [info] refresh page data from created listeners 0 757   
2025-07-06 07:19:01 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-10-11-04-45.png  [object Object] 
2025-07-06 07:19:01 [info] refresh page data from created listeners 0 758   
2025-07-06 07:19:02 [info] indexing created file 学习库/stm32/5 SPI.md  [object Object] 
2025-07-06 07:19:02 [info] indexing created ignore file 学习库/stm32/5 SPI.md   
2025-07-06 07:19:02 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-07-06 07:19:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-07-06 07:19:02 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:02 [info] refresh page data from resolve listeners 0 759   
2025-07-06 07:19:03 [info] indexing created file 学习库/ROS/Attachments/6 中断-2025-04-10-15-34-24.png  [object Object] 
2025-07-06 07:19:03 [info] refresh page data from created listeners 0 760   
2025-07-06 07:19:05 [info] indexing created file 学习库/ROS/Attachments/6 中断-2025-04-10-15-51-01.png  [object Object] 
2025-07-06 07:19:05 [info] refresh page data from created listeners 0 761   
2025-07-06 07:19:06 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-07-21.png  [object Object] 
2025-07-06 07:19:06 [info] refresh page data from created listeners 0 762   
2025-07-06 07:19:08 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-23-35.png  [object Object] 
2025-07-06 07:19:08 [info] refresh page data from created listeners 0 763   
2025-07-06 07:19:09 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-16-49-57.png  [object Object] 
2025-07-06 07:19:09 [info] refresh page data from created listeners 0 764   
2025-07-06 07:19:11 [info] indexing created file 学习库/stm32/attachments/6 中断-2025-04-10-19-36-10.png  [object Object] 
2025-07-06 07:19:12 [info] refresh page data from created listeners 0 765   
2025-07-06 07:19:12 [info] indexing created file 学习库/stm32/6 中断.md  [object Object] 
2025-07-06 07:19:12 [info] indexing created ignore file 学习库/stm32/6 中断.md   
2025-07-06 07:19:12 [info] trigger 学习库/stm32/6 中断.md resolve  [object Object] 
2025-07-06 07:19:12 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:12 [info] refresh page data from resolve listeners 0 766   
2025-07-06 07:19:14 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-20-50-24.png  [object Object] 
2025-07-06 07:19:14 [info] refresh page data from created listeners 0 767   
2025-07-06 07:19:16 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-20-56-24.png  [object Object] 
2025-07-06 07:19:16 [info] refresh page data from created listeners 0 768   
2025-07-06 07:19:17 [info] indexing created file 学习库/stm32/attachments/7 EXTI-2025-04-10-21-01-00.png  [object Object] 
2025-07-06 07:19:17 [info] refresh page data from created listeners 0 769   
2025-07-06 07:19:18 [info] indexing created file 学习库/stm32/7 EXTI.md  [object Object] 
2025-07-06 07:19:18 [info] indexing created ignore file 学习库/stm32/7 EXTI.md   
2025-07-06 07:19:18 [info] trigger 学习库/stm32/7 EXTI.md resolve  [object Object] 
2025-07-06 07:19:18 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:18 [info] refresh page data from resolve listeners 0 770   
2025-07-06 07:19:20 [info] indexing created file components/logs/2025-04-10.components.log  [object Object] 
2025-07-06 07:19:20 [info] refresh page data from created listeners 0 771   
2025-07-06 07:19:21 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-07-06 07:19:21 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-07-06 07:19:21 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:21 [info] refresh page data from resolve listeners 0 771   
2025-07-06 07:19:24 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-04-41.png  [object Object] 
2025-07-06 07:19:24 [info] refresh page data from created listeners 0 772   
2025-07-06 07:19:26 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-10-15.png  [object Object] 
2025-07-06 07:19:26 [info] refresh page data from created listeners 0 773   
2025-07-06 07:19:27 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-23-14.png  [object Object] 
2025-07-06 07:19:28 [info] refresh page data from created listeners 0 774   
2025-07-06 07:19:29 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-16-26.png  [object Object] 
2025-07-06 07:19:29 [info] refresh page data from created listeners 0 775   
2025-07-06 07:19:32 [info] indexing created file 学习库/stm32/attachments/8 时钟-2025-04-11-11-36-40.png  [object Object] 
2025-07-06 07:19:32 [info] refresh page data from created listeners 0 776   
2025-07-06 07:19:33 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-14-57-08.png  [object Object] 
2025-07-06 07:19:34 [info] refresh page data from created listeners 0 777   
2025-07-06 07:19:35 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-14-59-00.png  [object Object] 
2025-07-06 07:19:35 [info] refresh page data from created listeners 0 778   
2025-07-06 07:19:35 [info] indexing created file 学习库/stm32/attachments/1 启动-2025-04-11-15-03-04.png  [object Object] 
2025-07-06 07:19:36 [info] refresh page data from created listeners 0 779   
2025-07-06 07:19:37 [info] ignore file modify evnet 学习库/stm32/1 启动.md   
2025-07-06 07:19:37 [info] trigger 学习库/stm32/1 启动.md resolve  [object Object] 
2025-07-06 07:19:37 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:37 [info] refresh page data from resolve listeners 0 779   
2025-07-06 07:19:38 [info] indexing created file 学习库/stm32/8 时钟.md  [object Object] 
2025-07-06 07:19:38 [info] indexing created ignore file 学习库/stm32/8 时钟.md   
2025-07-06 07:19:38 [info] trigger 学习库/stm32/8 时钟.md resolve  [object Object] 
2025-07-06 07:19:38 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:38 [info] refresh page data from resolve listeners 0 780   
2025-07-06 07:19:39 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-15-53-32.png  [object Object] 
2025-07-06 07:19:39 [info] refresh page data from created listeners 0 781   
2025-07-06 07:19:40 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-02-37.png  [object Object] 
2025-07-06 07:19:40 [info] refresh page data from created listeners 0 782   
2025-07-06 07:19:41 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-14.png  [object Object] 
2025-07-06 07:19:41 [info] refresh page data from created listeners 0 783   
2025-07-06 07:19:41 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-28.png  [object Object] 
2025-07-06 07:19:42 [info] refresh page data from created listeners 0 784   
2025-07-06 07:19:42 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-16-09-42.png  [object Object] 
2025-07-06 07:19:42 [info] refresh page data from created listeners 0 785   
2025-07-06 07:19:43 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-11-19-44-54.png  [object Object] 
2025-07-06 07:19:43 [info] refresh page data from created listeners 0 786   
2025-07-06 07:19:44 [info] indexing created file components/logs/2025-04-11.components.log  [object Object] 
2025-07-06 07:19:45 [info] refresh page data from created listeners 0 787   
2025-07-06 07:19:46 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-01-26.png  [object Object] 
2025-07-06 07:19:46 [info] refresh page data from created listeners 0 788   
2025-07-06 07:19:46 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-06-33.png  [object Object] 
2025-07-06 07:19:46 [info] refresh page data from created listeners 0 789   
2025-07-06 07:19:48 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-09-52.png  [object Object] 
2025-07-06 07:19:48 [info] refresh page data from created listeners 0 790   
2025-07-06 07:19:50 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-17-31.png  [object Object] 
2025-07-06 07:19:50 [info] refresh page data from created listeners 0 791   
2025-07-06 07:19:51 [info] indexing created file components/logs/2025-04-12.components.log  [object Object] 
2025-07-06 07:19:51 [info] refresh page data from created listeners 0 792   
2025-07-06 07:19:53 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-32-24.png  [object Object] 
2025-07-06 07:19:53 [info] refresh page data from created listeners 0 793   
2025-07-06 07:19:54 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-13-08-51-10.png  [object Object] 
2025-07-06 07:19:54 [info] refresh page data from created listeners 0 794   
2025-07-06 07:19:55 [info] indexing created file 学习库/stm32/9 定时器.md  [object Object] 
2025-07-06 07:19:55 [info] indexing created ignore file 学习库/stm32/9 定时器.md   
2025-07-06 07:19:55 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-07-06 07:19:55 [info] index finished after resolve  [object Object] 
2025-07-06 07:19:55 [info] refresh page data from resolve listeners 0 795   
2025-07-06 07:19:56 [info] indexing created file components/logs/2025-04-13.components.log  [object Object] 
2025-07-06 07:19:56 [info] refresh page data from created listeners 0 796   
2025-07-06 07:19:57 [info] indexing created file components/logs/2025-04-15.components.log  [object Object] 
2025-07-06 07:19:57 [info] refresh page data from created listeners 0 797   
2025-07-06 07:19:57 [info] refresh page data from delete listeners 0 796   
2025-07-06 07:19:58 [info] indexing created file components/logs/2025-04-16.components.log  [object Object] 
2025-07-06 07:19:58 [info] refresh page data from created listeners 0 797   
2025-07-06 07:19:58 [info] indexing created file components/logs/2025-04-17.components.log  [object Object] 
2025-07-06 07:19:58 [info] refresh page data from created listeners 0 798   
2025-07-06 07:20:01 [info] indexing created file 工作库/项目/舌诊/attachments/裂纹舌-2025-04-22-19-52-40.png  [object Object] 
2025-07-06 07:20:01 [info] refresh page data from created listeners 0 799   
2025-07-06 07:20:02 [info] indexing created file components/logs/2025-04-22.components.log  [object Object] 
2025-07-06 07:20:02 [info] refresh page data from created listeners 0 800   
2025-07-06 07:20:03 [info] ignore file modify evnet 工作库/项目/舌诊/分割.md   
2025-07-06 07:20:03 [info] trigger 工作库/项目/舌诊/分割.md resolve  [object Object] 
2025-07-06 07:20:03 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:03 [info] refresh page data from resolve listeners 0 800   
2025-07-06 07:20:04 [info] ignore file modify evnet 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md   
2025-07-06 07:20:05 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-07-06 07:20:05 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:05 [info] refresh page data from resolve listeners 0 800   
2025-07-06 07:20:07 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/QRHRD785.png  [object Object] 
2025-07-06 07:20:07 [info] refresh page data from created listeners 0 801   
2025-07-06 07:20:08 [info] indexing created file 文献库/文献库/舌诊/舌象分割/RSA-PT- A point transformer-based semantic segmentation network for uninterrupted operation in a distribution network sc.md  [object Object] 
2025-07-06 07:20:08 [info] indexing created ignore file 文献库/文献库/舌诊/舌象分割/RSA-PT- A point transformer-based semantic segmentation network for uninterrupted operation in a distribution network sc.md   
2025-07-06 07:20:08 [info] trigger 文献库/文献库/舌诊/舌象分割/RSA-PT- A point transformer-based semantic segmentation network for uninterrupted operation in a distribution network sc.md resolve  [object Object] 
2025-07-06 07:20:08 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:08 [info] refresh page data from resolve listeners 0 802   
2025-07-06 07:20:09 [info] ignore file modify evnet 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md   
2025-07-06 07:20:09 [info] trigger 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md resolve  [object Object] 
2025-07-06 07:20:09 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:09 [info] refresh page data from resolve listeners 0 802   
2025-07-06 07:20:10 [info] indexing created file components/logs/2025-04-23.components.log  [object Object] 
2025-07-06 07:20:10 [info] refresh page data from created listeners 0 803   
2025-07-06 07:20:11 [info] indexing created file Excalidraw/Drawing 2025-04-24 11.04.53.excalidraw.md  [object Object] 
2025-07-06 07:20:11 [info] indexing created ignore file Excalidraw/Drawing 2025-04-24 11.04.53.excalidraw.md   
2025-07-06 07:20:11 [info] trigger Excalidraw/Drawing 2025-04-24 11.04.53.excalidraw.md resolve  [object Object] 
2025-07-06 07:20:11 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:11 [info] refresh page data from resolve listeners 0 804   
2025-07-06 07:20:12 [info] indexing created file 学习库/Deep learning/attachments/激活函数-2025-04-24-15-09-01.png  [object Object] 
2025-07-06 07:20:12 [info] refresh page data from created listeners 0 805   
2025-07-06 07:20:14.808 [info] components database created cost 1 ms   
2025-07-06 07:20:14.810 [info] components index initializing...   
2025-07-06 07:20:15.052 [info] indexing created file 学习库/Deep learning/attachments/激活函数-2025-04-24-15-18-54.png  [object Object] 
2025-07-06 07:20:15.610 [info] start to batch put pages: 6   
2025-07-06 07:20:15.616 [info] batch persist cost 6  6 
2025-07-06 07:20:15.634 [info] components index initialized, 806 files cost 827 ms   
2025-07-06 07:20:15.634 [info] refresh page data from init listeners 0 806   
2025-07-06 07:20:15.952 [info] indexing created file 学习库/Deep learning/attachments/激活函数-2025-04-24-15-21-53.png  [object Object] 
2025-07-06 07:20:16.038 [info] refresh page data from created listeners 0 807   
2025-07-06 07:20:16.828 [info] indexing created file 学习库/Deep learning/attachments/激活函数-2025-04-24-15-27-02.png  [object Object] 
2025-07-06 07:20:16.917 [info] refresh page data from created listeners 0 808   
2025-07-06 07:20:16.972 [info] refresh page data from delete listeners 0 807   
2025-07-06 07:20:17.065 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-06 07:20:17.071 [info] refresh page data from delete listeners 0 806   
2025-07-06 07:20:17.124 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-07-06 07:20:17.173 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-06 07:20:17.176 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-07-06 07:20:17.185 [info] refresh page data from delete listeners 0 805   
2025-07-06 07:20:17.278 [info] refresh page data from delete listeners 0 804   
2025-07-06 07:20:17.372 [info] refresh page data from delete listeners 0 803   
2025-07-06 07:20:19.052 [info] indexing created file 学习库/Deep learning/概念库/Transformer.md  [object Object] 
2025-07-06 07:20:19.052 [info] indexing created ignore file 学习库/Deep learning/概念库/Transformer.md   
2025-07-06 07:20:19.160 [info] trigger 学习库/Deep learning/概念库/Transformer.md resolve  [object Object] 
2025-07-06 07:20:19.162 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:19.163 [info] refresh page data from resolve listeners 0 804   
2025-07-06 07:20:19.833 [info] indexing created file 学习库/Deep learning/概念库/评价指标.md  [object Object] 
2025-07-06 07:20:19.834 [info] indexing created ignore file 学习库/Deep learning/概念库/评价指标.md   
2025-07-06 07:20:19.934 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-07-06 07:20:19.972 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:19.974 [info] refresh page data from resolve listeners 0 805   
2025-07-06 07:20:20.630 [info] indexing created file 学习库/Deep learning/概念库/神经网络.md  [object Object] 
2025-07-06 07:20:20.630 [info] indexing created ignore file 学习库/Deep learning/概念库/神经网络.md   
2025-07-06 07:20:20.728 [info] trigger 学习库/Deep learning/概念库/神经网络.md resolve  [object Object] 
2025-07-06 07:20:20.730 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:20.732 [info] refresh page data from resolve listeners 0 806   
2025-07-06 07:20:22.814 [info] indexing created file 学习库/Deep learning/概念库/attachments/正则化-2025-04-24-15-42-07.png  [object Object] 
2025-07-06 07:20:22.911 [info] refresh page data from created listeners 0 807   
2025-07-06 07:20:26.174 [info] indexing created file 学习库/Deep learning/概念库/attachments/正则化-2025-04-24-15-48-52.png  [object Object] 
2025-07-06 07:20:26.266 [info] refresh page data from created listeners 0 808   
2025-07-06 07:20:27.273 [info] indexing created file 学习库/Deep learning/概念库/attachments/正则化-2025-04-24-16-31-40.png  [object Object] 
2025-07-06 07:20:27.385 [info] refresh page data from created listeners 0 809   
2025-07-06 07:20:27.920 [info] indexing created file 学习库/Deep learning/概念库/正则化.md  [object Object] 
2025-07-06 07:20:27.920 [info] indexing created ignore file 学习库/Deep learning/概念库/正则化.md   
2025-07-06 07:20:28.097 [info] trigger 学习库/Deep learning/概念库/正则化.md resolve  [object Object] 
2025-07-06 07:20:28.099 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:28.100 [info] refresh page data from resolve listeners 0 810   
2025-07-06 07:20:29.105 [info] indexing created file components/logs/2025-04-24.components.log  [object Object] 
2025-07-06 07:20:29.216 [info] refresh page data from created listeners 0 811   
2025-07-06 07:20:31.077 [debug] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-07-06 07:20:31.244 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-07-06 07:20:31.249 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:31.250 [info] refresh page data from resolve listeners 0 811   
2025-07-06 07:20:31.868 [info] indexing created file 学习库/python笔记/Numpy/1. 数组基础.md  [object Object] 
2025-07-06 07:20:31.868 [info] indexing created ignore file 学习库/python笔记/Numpy/1. 数组基础.md   
2025-07-06 07:20:32.018 [info] trigger 学习库/python笔记/Numpy/1. 数组基础.md resolve  [object Object] 
2025-07-06 07:20:32.020 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:32.021 [info] refresh page data from resolve listeners 0 812   
2025-07-06 07:20:32.567 [info] indexing created file Dual residual attention network for image denoising-2025-04-23-20-55-08.png.md  [object Object] 
2025-07-06 07:20:32.568 [info] indexing created ignore file Dual residual attention network for image denoising-2025-04-23-20-55-08.png.md   
2025-07-06 07:20:32.735 [info] trigger Dual residual attention network for image denoising-2025-04-23-20-55-08.png.md resolve  [object Object] 
2025-07-06 07:20:32.736 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:32.738 [info] refresh page data from resolve listeners 0 813   
2025-07-06 07:20:35.407 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Dual residual attention network for image denoising-2025-04-23-20-55-08.png  [object Object] 
2025-07-06 07:20:35.518 [info] refresh page data from created listeners 0 814   
2025-07-06 07:20:40.135 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Dual residual attention network for image denoising-2025-04-23-20-52-48.png  [object Object] 
2025-07-06 07:20:40.232 [info] refresh page data from created listeners 0 815   
2025-07-06 07:20:40.233 [info] trigger 文献库/文献库/舌诊/舌象分割/RSA-PT- A point transformer-based semantic segmentation network for uninterrupted operation in a distribution network sc.md resolve  [object Object] 
2025-07-06 07:20:40.708 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/一些基础.md   
2025-07-06 07:20:40.849 [info] trigger 学习库/Deep learning/训练实践/一些基础.md resolve  [object Object] 
2025-07-06 07:20:40.853 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:40.854 [info] refresh page data from resolve listeners 0 815   
2025-07-06 07:20:42.369 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Dual residual attention network for image denoising.md  [object Object] 
2025-07-06 07:20:42.369 [info] indexing created ignore file 文献库/文献库/舌诊/舌象分割/Dual residual attention network for image denoising.md   
2025-07-06 07:20:42.540 [info] trigger 文献库/文献库/舌诊/舌象分割/Dual residual attention network for image denoising.md resolve  [object Object] 
2025-07-06 07:20:42.544 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:42.545 [info] refresh page data from resolve listeners 0 816   
2025-07-06 07:20:44.279 [info] indexing created file Excalidraw/Drawing 2025-04-24 11.06.41.excalidraw.md  [object Object] 
2025-07-06 07:20:44.279 [info] indexing created ignore file Excalidraw/Drawing 2025-04-24 11.06.41.excalidraw.md   
2025-07-06 07:20:44.429 [info] trigger Excalidraw/Drawing 2025-04-24 11.06.41.excalidraw.md resolve  [object Object] 
2025-07-06 07:20:44.471 [info] index finished after resolve  [object Object] 
2025-07-06 07:20:44.472 [info] refresh page data from resolve listeners 0 817   
2025-07-06 07:20:46.519 [info] indexing created file components/logs/2025-04-25.components.log  [object Object] 
2025-07-06 07:20:46.629 [info] refresh page data from created listeners 0 818   
2025-07-06 07:21:33.411 [info] components database created cost 4 ms   
2025-07-06 07:21:33.412 [info] components index initializing...   
2025-07-06 07:21:34.002 [info] start to batch put pages: 1   
2025-07-06 07:21:34.003 [info] batch persist cost 1  1 
2025-07-06 07:21:34.021 [info] components index initialized, 818 files cost 614 ms   
2025-07-06 07:21:34.021 [info] refresh page data from init listeners 0 818   
2025-07-06 07:26:57.864 [info] indexing created file 学习库/心得/大队长手把手带你发论文_V2_工房版.pdf  [object Object] 
2025-07-06 07:26:57.869 [info] refresh page data from created listeners 0 819   
2025-07-06 07:26:58.666 [info] indexing created file 学习库/心得/缝合笔记.md  [object Object] 
2025-07-06 07:26:58.666 [info] indexing created ignore file 学习库/心得/缝合笔记.md   
2025-07-06 07:26:58.693 [info] trigger 学习库/心得/缝合笔记.md resolve  [object Object] 
2025-07-06 07:26:58.696 [info] index finished after resolve  [object Object] 
2025-07-06 07:26:58.699 [info] refresh page data from resolve listeners 0 820   
2025-07-06 07:26:59.202 [info] indexing created file 学习库/python笔记/Numpy/3. 数组的索引.md  [object Object] 
2025-07-06 07:26:59.203 [info] indexing created ignore file 学习库/python笔记/Numpy/3. 数组的索引.md   
2025-07-06 07:26:59.250 [info] trigger 学习库/python笔记/Numpy/3. 数组的索引.md resolve  [object Object] 
2025-07-06 07:26:59.253 [info] index finished after resolve  [object Object] 
2025-07-06 07:26:59.255 [info] refresh page data from resolve listeners 0 821   
2025-07-06 07:26:59.725 [info] indexing created file 学习库/python笔记/Numpy/4. 数组的变形.md  [object Object] 
2025-07-06 07:26:59.726 [info] indexing created ignore file 学习库/python笔记/Numpy/4. 数组的变形.md   
2025-07-06 07:26:59.754 [info] trigger 学习库/python笔记/Numpy/4. 数组的变形.md resolve  [object Object] 
2025-07-06 07:26:59.755 [info] index finished after resolve  [object Object] 
2025-07-06 07:26:59.757 [info] refresh page data from resolve listeners 0 822   
2025-07-06 07:27:00.236 [info] indexing created file 学习库/python笔记/Numpy/5. 数组的运算.md  [object Object] 
2025-07-06 07:27:00.236 [info] indexing created ignore file 学习库/python笔记/Numpy/5. 数组的运算.md   
2025-07-06 07:27:00.262 [info] trigger 学习库/python笔记/Numpy/5. 数组的运算.md resolve  [object Object] 
2025-07-06 07:27:00.264 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:00.266 [info] refresh page data from resolve listeners 0 823   
2025-07-06 07:27:01.361 [info] indexing created file 学习库/python笔记/Numpy/6. 数组的函数.md  [object Object] 
2025-07-06 07:27:01.362 [info] indexing created ignore file 学习库/python笔记/Numpy/6. 数组的函数.md   
2025-07-06 07:27:01.389 [info] trigger 学习库/python笔记/Numpy/6. 数组的函数.md resolve  [object Object] 
2025-07-06 07:27:01.391 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:01.393 [info] refresh page data from resolve listeners 0 824   
2025-07-06 07:27:01.855 [info] indexing created file 学习库/python笔记/Numpy/7. 布尔型数组.md  [object Object] 
2025-07-06 07:27:01.855 [info] indexing created ignore file 学习库/python笔记/Numpy/7. 布尔型数组.md   
2025-07-06 07:27:01.875 [info] trigger 学习库/python笔记/Numpy/7. 布尔型数组.md resolve  [object Object] 
2025-07-06 07:27:01.877 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:01.879 [info] refresh page data from resolve listeners 0 825   
2025-07-06 07:27:02.311 [info] indexing created file 学习库/python笔记/Numpy/8. 从数组到张量.md  [object Object] 
2025-07-06 07:27:02.312 [info] indexing created ignore file 学习库/python笔记/Numpy/8. 从数组到张量.md   
2025-07-06 07:27:02.326 [info] refresh page data from delete listeners 0 824   
2025-07-06 07:27:02.333 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-06 07:27:02.336 [info] trigger 学习库/python笔记/Numpy/8. 从数组到张量.md resolve  [object Object] 
2025-07-06 07:27:02.338 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:02.339 [info] refresh page data from resolve listeners 0 825   
2025-07-06 07:27:03.031 [info] indexing created file 学习库/Deep learning/Unet.md  [object Object] 
2025-07-06 07:27:03.031 [info] indexing created ignore file 学习库/Deep learning/Unet.md   
2025-07-06 07:27:03.044 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-06 07:27:03.060 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-06 07:27:03.063 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:03.064 [info] refresh page data from resolve listeners 0 826   
2025-07-06 07:27:03.085 [info] refresh page data from delete listeners 0 825   
2025-07-06 07:27:03.088 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:03.094 [info] refresh page data from delete listeners 0 824   
2025-07-06 07:27:03.096 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-07-06 07:27:03.102 [info] refresh page data from delete listeners 0 823   
2025-07-06 07:27:03.110 [info] refresh page data from delete listeners 0 822   
2025-07-06 07:27:03.115 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:03.122 [info] refresh page data from delete listeners 0 821   
2025-07-06 07:27:03.136 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:03.139 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-07-06 07:27:09.854 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123205552.png  [object Object] 
2025-07-06 07:27:09.858 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-07-06 07:27:09.861 [info] refresh page data from created listeners 0 822   
2025-07-06 07:27:14.306 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123221549.png  [object Object] 
2025-07-06 07:27:14.308 [info] refresh page data from created listeners 0 823   
2025-07-06 07:27:17.020 [info] indexing created file 学习库/Deep learning/attachments/目标检测的尺度.png  [object Object] 
2025-07-06 07:27:17.025 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:17.027 [info] refresh page data from created listeners 0 824   
2025-07-06 07:27:17.031 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-07-06 07:27:19.718 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240123163728.png  [object Object] 
2025-07-06 07:27:19.722 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:19.725 [info] refresh page data from created listeners 0 825   
2025-07-06 07:27:25.333 [info] indexing created file 学习库/Deep learning/attachments/157381276-6e8429f3-c759-4aef-aea8-034438919457.png  [object Object] 
2025-07-06 07:27:25.336 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-07-06 07:27:25.338 [info] refresh page data from created listeners 0 826   
2025-07-06 07:27:25.353 [info] refresh page data from delete listeners 0 825   
2025-07-06 07:27:25.852 [info] indexing created file 学习库/Deep learning/YOLOv5.md  [object Object] 
2025-07-06 07:27:25.852 [info] indexing created ignore file 学习库/Deep learning/YOLOv5.md   
2025-07-06 07:27:25.873 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-06 07:27:25.876 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:25.878 [info] refresh page data from resolve listeners 0 826   
2025-07-06 07:27:26.325 [info] indexing created file 学习库/Deep learning/self-attention.md  [object Object] 
2025-07-06 07:27:26.325 [info] indexing created ignore file 学习库/Deep learning/self-attention.md   
2025-07-06 07:27:26.341 [info] trigger 学习库/Deep learning/self-attention.md resolve  [object Object] 
2025-07-06 07:27:26.344 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:26.346 [info] refresh page data from resolve listeners 0 827   
2025-07-06 07:27:26.824 [info] indexing created file 学习库/Deep learning/概念库/Attention.md  [object Object] 
2025-07-06 07:27:26.824 [info] indexing created ignore file 学习库/Deep learning/概念库/Attention.md   
2025-07-06 07:27:26.860 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-07-06 07:27:26.862 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:26.864 [info] refresh page data from resolve listeners 0 828   
2025-07-06 07:27:28.030 [info] indexing created file components/logs/2025-04-26.components.log  [object Object] 
2025-07-06 07:27:28.034 [info] refresh page data from created listeners 0 829   
2025-07-06 07:27:30.105 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型-2025-04-27-15-04-26.png  [object Object] 
2025-07-06 07:27:30.108 [info] refresh page data from created listeners 0 830   
2025-07-06 07:27:39.931 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_02_Linear_Model.pdf  [object Object] 
2025-07-06 07:27:39.935 [info] refresh page data from created listeners 0 831   
2025-07-06 07:27:53.424 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_03_Gradient_Descent.pdf  [object Object] 
2025-07-06 07:27:53.433 [info] refresh page data from created listeners 0 832   
2025-07-06 07:27:54.545 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-16-59-13.png  [object Object] 
2025-07-06 07:27:54.553 [info] refresh page data from created listeners 0 833   
2025-07-06 07:27:56.162 [info] indexing created file 学习库/python笔记/Numpy/2. 数组的创建.md  [object Object] 
2025-07-06 07:27:56.163 [info] indexing created ignore file 学习库/python笔记/Numpy/2. 数组的创建.md   
2025-07-06 07:27:56.218 [info] trigger 学习库/python笔记/Numpy/2. 数组的创建.md resolve  [object Object] 
2025-07-06 07:27:56.220 [info] index finished after resolve  [object Object] 
2025-07-06 07:27:56.222 [info] refresh page data from resolve listeners 0 834   
2025-07-06 07:27:57.458 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-01-56.png  [object Object] 
2025-07-06 07:27:57.464 [info] refresh page data from created listeners 0 835   
2025-07-06 07:27:59.420 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-02-56.png  [object Object] 
2025-07-06 07:27:59.424 [info] refresh page data from created listeners 0 836   
2025-07-06 07:28:01.073 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-17-12.png  [object Object] 
2025-07-06 07:28:01.080 [info] refresh page data from created listeners 0 837   
2025-07-06 07:28:03.266 [info] indexing created file components/logs/2025-04-27.components.log  [object Object] 
2025-07-06 07:28:03.275 [info] refresh page data from created listeners 0 838   
2025-07-06 07:28:06.587 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-04-28-16-12-33.png  [object Object] 
2025-07-06 07:28:06.596 [info] refresh page data from created listeners 0 839   
2025-07-06 07:28:07.880 [info] indexing created file components/logs/2025-04-28.components.log  [object Object] 
2025-07-06 07:28:07.888 [info] refresh page data from created listeners 0 840   
2025-07-06 07:28:24.041 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf  [object Object] 
2025-07-06 07:28:24.047 [info] refresh page data from created listeners 0 841   
2025-07-06 07:28:29.646 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md  [object Object] 
2025-07-06 07:28:29.646 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-07-06 07:28:29.898 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-07-06 07:28:29.914 [info] index finished after resolve  [object Object] 
2025-07-06 07:28:29.916 [info] refresh page data from resolve listeners 0 842   
2025-07-06 07:28:30.375 [info] indexing created file 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md  [object Object] 
2025-07-06 07:28:30.376 [info] indexing created ignore file 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-07-06 07:28:30.418 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-07-06 07:28:30.421 [info] index finished after resolve  [object Object] 
2025-07-06 07:28:30.422 [info] refresh page data from resolve listeners 0 843   
2025-07-06 07:28:38.028 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-46-41.png  [object Object] 
2025-07-06 07:28:38.037 [info] refresh page data from created listeners 0 844   
2025-07-06 07:28:42.297 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-52-04.png  [object Object] 
2025-07-06 07:28:42.305 [info] refresh page data from created listeners 0 845   
2025-07-06 07:28:43.292 [debug] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-07-06 07:28:43.343 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-07-06 07:28:43.345 [info] index finished after resolve  [object Object] 
2025-07-06 07:28:43.347 [info] refresh page data from resolve listeners 0 845   
2025-07-06 07:28:48.126 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-21-37-16.png  [object Object] 
2025-07-06 07:28:48.134 [info] refresh page data from created listeners 0 846   
2025-07-06 07:28:49.845 [info] indexing created file 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md  [object Object] 
2025-07-06 07:28:49.845 [info] indexing created ignore file 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-07-06 07:28:49.874 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-07-06 07:28:49.876 [info] index finished after resolve  [object Object] 
2025-07-06 07:28:49.877 [info] refresh page data from resolve listeners 0 847   
2025-07-06 07:28:56.774 [info] indexing created file components/logs/2025-04-29.components.log  [object Object] 
2025-07-06 07:28:56.786 [info] refresh page data from created listeners 0 848   
2025-07-06 07:29:04.247 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-08-58-20.png  [object Object] 
2025-07-06 07:29:04.254 [info] refresh page data from created listeners 0 849   
2025-07-06 07:29:04.753 [info] indexing created file 学习库/Anki/Deep learning/pytorch.md  [object Object] 
2025-07-06 07:29:04.754 [info] indexing created ignore file 学习库/Anki/Deep learning/pytorch.md   
2025-07-06 07:29:04.780 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-07-06 07:29:04.784 [info] index finished after resolve  [object Object] 
2025-07-06 07:29:04.785 [info] refresh page data from resolve listeners 0 850   
2025-07-06 07:29:13.906 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-10-54-27.png  [object Object] 
2025-07-06 07:29:13.911 [info] refresh page data from created listeners 0 851   
2025-07-06 07:29:14.361 [info] indexing created file 学习库/Deep learning/pytorch/PDF/test.py  [object Object] 
2025-07-06 07:29:14.372 [info] refresh page data from created listeners 0 852   
2025-07-06 07:29:15.720 [info] indexing created file components/logs/2025-04-30.components.log  [object Object] 
2025-07-06 07:29:15.728 [info] refresh page data from created listeners 0 853   
2025-07-06 07:29:15.754 [info] refresh page data from delete listeners 0 852   
2025-07-06 07:29:15.764 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-06 07:29:15.768 [info] refresh page data from delete listeners 0 851   
2025-07-06 07:29:15.782 [info] refresh page data from delete listeners 0 850   
2025-07-06 07:29:15.791 [info] refresh page data from delete listeners 0 849   
2025-07-06 07:29:15.801 [info] refresh page data from delete listeners 0 848   
2025-07-06 07:29:15.809 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:15.812 [info] refresh page data from delete listeners 0 847   
2025-07-06 07:29:15.822 [info] refresh page data from delete listeners 0 846   
2025-07-06 07:29:15.827 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:15.838 [info] refresh page data from delete listeners 0 845   
2025-07-06 07:29:15.842 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:15.851 [info] refresh page data from delete listeners 0 844   
2025-07-06 07:29:15.856 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.869 [info] refresh page data from delete listeners 0 843   
2025-07-06 07:29:15.873 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.883 [info] refresh page data from delete listeners 0 842   
2025-07-06 07:29:15.890 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.900 [info] refresh page data from delete listeners 0 841   
2025-07-06 07:29:15.906 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.918 [info] refresh page data from delete listeners 0 840   
2025-07-06 07:29:15.926 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.934 [info] refresh page data from delete listeners 0 839   
2025-07-06 07:29:15.940 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:15.949 [info] refresh page data from delete listeners 0 838   
2025-07-06 07:29:15.963 [info] refresh page data from delete listeners 0 837   
2025-07-06 07:29:15.970 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:15.973 [info] refresh page data from delete listeners 0 836   
2025-07-06 07:29:15.980 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:15.984 [info] refresh page data from delete listeners 0 835   
2025-07-06 07:29:15.990 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:16.001 [info] refresh page data from delete listeners 0 834   
2025-07-06 07:29:16.019 [info] refresh page data from delete listeners 0 833   
2025-07-06 07:29:16.030 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-07-06 07:29:16.034 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-06 07:29:16.039 [info] refresh page data from delete listeners 0 832   
2025-07-06 07:29:16.040 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:16.043 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-06 07:29:16.047 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-06 07:29:16.531 [info] indexing created file 学习库/Deep learning/概念库/concatenate和add.md  [object Object] 
2025-07-06 07:29:16.531 [info] indexing created ignore file 学习库/Deep learning/概念库/concatenate和add.md   
2025-07-06 07:29:16.547 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-07-06 07:29:16.558 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-07-06 07:29:16.560 [info] trigger 学习库/Deep learning/概念库/concatenate和add.md resolve  [object Object] 
2025-07-06 07:29:16.563 [info] index finished after resolve  [object Object] 
2025-07-06 07:29:16.565 [info] refresh page data from resolve listeners 0 833   
2025-07-06 07:29:16.983 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样.md  [object Object] 
2025-07-06 07:29:16.984 [info] indexing created ignore file 学习库/Deep learning/概念库/上采样；下采样.md   
2025-07-06 07:29:16.994 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-07-06 07:29:17.000 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:17.002 [info] index finished after resolve  [object Object] 
2025-07-06 07:29:17.004 [info] refresh page data from resolve listeners 0 834   
2025-07-06 07:29:17.475 [info] indexing created file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md  [object Object] 
2025-07-06 07:29:17.476 [info] indexing created ignore file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-07-06 07:29:17.511 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-07-06 07:29:17.513 [info] index finished after resolve  [object Object] 
2025-07-06 07:29:17.514 [info] refresh page data from resolve listeners 0 835   
2025-07-06 07:29:18.408 [info] indexing created file 学习库/Deep learning/概念库/卷积和转置卷积.md  [object Object] 
2025-07-06 07:29:18.409 [info] indexing created ignore file 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-07-06 07:29:18.426 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-06 07:29:18.433 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:18.449 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:18.451 [info] index finished after resolve  [object Object] 
2025-07-06 07:29:18.453 [info] refresh page data from resolve listeners 0 836   
2025-07-06 07:29:21.612 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730111724.png  [object Object] 
2025-07-06 07:29:21.623 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:21.627 [info] refresh page data from created listeners 0 837   
2025-07-06 07:29:24.007 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730155551.png  [object Object] 
2025-07-06 07:29:24.014 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:24.018 [info] refresh page data from created listeners 0 838   
2025-07-06 07:29:27.971 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730110119.png  [object Object] 
2025-07-06 07:29:27.979 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:27.981 [info] refresh page data from created listeners 0 839   
2025-07-06 07:29:31.578 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730171955.png  [object Object] 
2025-07-06 07:29:31.585 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:31.587 [info] refresh page data from created listeners 0 840   
2025-07-06 07:29:34.690 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120114804.png  [object Object] 
2025-07-06 07:29:34.697 [info] refresh page data from created listeners 0 841   
2025-07-06 07:29:37.284 [info] indexing created file 学习库/Deep learning/概念库/attachments/tmp6067.png  [object Object] 
2025-07-06 07:29:37.293 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:37.302 [info] refresh page data from created listeners 0 842   
2025-07-06 07:29:40.096 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120113636.png  [object Object] 
2025-07-06 07:29:40.104 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:40.107 [info] refresh page data from created listeners 0 843   
2025-07-06 07:29:42.336 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730172720.png  [object Object] 
2025-07-06 07:29:42.346 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:42.350 [info] refresh page data from created listeners 0 844   
2025-07-06 07:29:44.096 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730110336.png  [object Object] 
2025-07-06 07:29:44.104 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:44.108 [info] refresh page data from created listeners 0 845   
2025-07-06 07:29:45.618 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240730145412.png  [object Object] 
2025-07-06 07:29:45.627 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-06 07:29:45.631 [info] refresh page data from created listeners 0 846   
2025-07-06 07:29:47.396 [info] indexing created file 学习库/Deep learning/概念库/attachments/Pasted image 20240120121420.png  [object Object] 
2025-07-06 07:29:47.405 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:29:47.407 [info] refresh page data from created listeners 0 847   
2025-07-06 07:29:49.459 [info] indexing created file 学习库/Deep learning/概念库/attachments/bda132ec94ebe94bd598dd9ae9fa04d5.gif  [object Object] 
2025-07-06 07:29:49.466 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-06 07:29:49.470 [info] refresh page data from created listeners 0 848   
2025-07-06 07:29:51.290 [info] indexing created file 学习库/Deep learning/概念库/attachments/3d8813449101f99a106a74cee1754184.gif  [object Object] 
2025-07-06 07:29:51.300 [info] refresh page data from created listeners 0 849   
2025-07-06 07:29:53.357 [info] indexing created file 学习库/Deep learning/概念库/attachments/3bb7fc0d90165becc7ed5033534fdac4.gif  [object Object] 
2025-07-06 07:29:53.365 [info] refresh page data from created listeners 0 850   
2025-07-06 07:29:56.810 [info] indexing created file 学习库/Deep learning/概念库/attachments/b6ca1dfbf6eacd96531ac2fd26a5eec9.gif  [object Object] 
2025-07-06 07:29:56.817 [info] refresh page data from created listeners 0 851   
2025-07-06 07:30:01.190 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101220640484.gif  [object Object] 
2025-07-06 07:30:01.200 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:30:01.204 [info] refresh page data from created listeners 0 852   
2025-07-06 07:30:03.513 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101220640484 - 副本.jpeg  [object Object] 
2025-07-06 07:30:03.523 [info] refresh page data from created listeners 0 853   
2025-07-06 07:30:07.861 [info] indexing created file 学习库/Deep learning/概念库/attachments/20190101212350912.gif  [object Object] 
2025-07-06 07:30:07.870 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-06 07:30:07.871 [info] refresh page data from created listeners 0 854   
2025-07-06 07:30:08.402 [info] indexing created file components/logs/2025-05-01.components.log  [object Object] 
2025-07-06 07:30:08.410 [info] refresh page data from created listeners 0 855   
2025-07-06 07:30:09.121 [info] indexing created file components/logs/2025-05-02.components.log  [object Object] 
2025-07-06 07:30:09.128 [info] refresh page data from created listeners 0 856   
2025-07-06 07:30:09.617 [info] indexing created file components/logs/2025-05-03.components.log  [object Object] 
2025-07-06 07:30:09.627 [info] refresh page data from created listeners 0 857   
2025-07-06 07:30:10.315 [info] indexing created file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md  [object Object] 
2025-07-06 07:30:10.315 [info] indexing created ignore file Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md   
2025-07-06 07:30:10.423 [info] trigger Excalidraw/Drawing 2025-05-02 08.16.21.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:10.425 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:10.426 [info] refresh page data from resolve listeners 0 858   
2025-07-06 07:30:10.866 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md  [object Object] 
2025-07-06 07:30:10.866 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md   
2025-07-06 07:30:10.897 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:10.899 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:10.901 [info] refresh page data from resolve listeners 0 859   
2025-07-06 07:30:11.415 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md  [object Object] 
2025-07-06 07:30:11.415 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md   
2025-07-06 07:30:11.455 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:11.457 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:11.459 [info] refresh page data from resolve listeners 0 860   
2025-07-06 07:30:12.052 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md  [object Object] 
2025-07-06 07:30:12.053 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md   
2025-07-06 07:30:12.128 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:12.130 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:12.132 [info] refresh page data from resolve listeners 0 861   
2025-07-06 07:30:12.645 [info] indexing created file 学习库/Deep learning/训练实践/attachments/Netron 可视化-2025-05-04-09-22-21.png  [object Object] 
2025-07-06 07:30:12.664 [info] refresh page data from created listeners 0 862   
2025-07-06 07:30:13.160 [info] indexing created file 学习库/Deep learning/训练实践/Netron 可视化.md  [object Object] 
2025-07-06 07:30:13.161 [info] indexing created ignore file 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-07-06 07:30:13.190 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-07-06 07:30:13.193 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:13.195 [info] refresh page data from resolve listeners 0 863   
2025-07-06 07:30:13.627 [info] indexing created file components/logs/2025-05-05.components.log  [object Object] 
2025-07-06 07:30:13.635 [info] refresh page data from created listeners 0 864   
2025-07-06 07:30:14.172 [info] indexing created file 学习库/Deep learning/概念库/激活函数.md  [object Object] 
2025-07-06 07:30:14.173 [info] indexing created ignore file 学习库/Deep learning/概念库/激活函数.md   
2025-07-06 07:30:14.187 [info] trigger 学习库/Deep learning/概念库/Attention.md resolve  [object Object] 
2025-07-06 07:30:14.206 [info] trigger 学习库/Deep learning/概念库/激活函数.md resolve  [object Object] 
2025-07-06 07:30:14.208 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:14.209 [info] refresh page data from resolve listeners 0 865   
2025-07-06 07:30:15.108 [info] indexing created file components/logs/2025-05-04.components.log  [object Object] 
2025-07-06 07:30:15.117 [info] refresh page data from created listeners 0 866   
2025-07-06 07:30:15.961 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md  [object Object] 
2025-07-06 07:30:15.961 [info] indexing created ignore file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md   
2025-07-06 07:30:16.037 [info] trigger 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:16.051 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:16.053 [info] refresh page data from resolve listeners 0 867   
2025-07-06 07:30:16.888 [info] indexing created file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md  [object Object] 
2025-07-06 07:30:16.889 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md   
2025-07-06 07:30:16.934 [info] trigger Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md resolve  [object Object] 
2025-07-06 07:30:16.937 [info] index finished after resolve  [object Object] 
2025-07-06 07:30:16.939 [info] refresh page data from resolve listeners 0 868   
2025-07-06 19:22:23.210 [info] indexing created file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md  [object Object] 
2025-07-06 19:22:23.212 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md   
2025-07-06 19:22:24.775 [info] trigger Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md resolve  [object Object] 
2025-07-06 19:22:25.141 [info] index finished after resolve  [object Object] 
2025-07-06 19:22:25.146 [info] refresh page data from resolve listeners 0 869   
