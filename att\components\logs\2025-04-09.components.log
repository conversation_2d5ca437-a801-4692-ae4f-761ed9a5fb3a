2025-04-09 08:22:31 [info] components database created cost 17 ms   
2025-04-09 08:22:31 [info] components index initializing...   
2025-04-09 08:22:33 [info] start to batch put pages: 7   
2025-04-09 08:22:33 [info] batch persist cost 7  206 
2025-04-09 08:22:33 [info] components index initialized, 736 files cost 1749 ms   
2025-04-09 08:22:33 [info] refresh page data from init listeners 0 736   
2025-04-09 08:22:34 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-09 08:22:34 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-09 08:22:35 [info] indexing created file components/logs/2025-04-09.components.log  [object Object] 
2025-04-09 08:22:35 [info] refresh page data from created listeners 0 737   
2025-04-09 08:22:35 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-09 08:22:35 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-09 08:22:35 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-09 08:22:35 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-09 08:23:01 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211848.png  [object Object] 
2025-04-09 08:23:03 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-25-46.png  [object Object] 
2025-04-09 08:23:03 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-09 08:23:05 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-17-10.png  [object Object] 
2025-04-09 08:23:05 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-09 08:23:06 [info] indexing created file 工作库/项目/舌诊/attachments/机械臂-2025-03-24-21-16-02.png  [object Object] 
2025-04-09 08:23:06 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-09 08:23:07 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015211103.png  [object Object] 
2025-04-09 08:23:08 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015213508.png  [object Object] 
2025-04-09 08:23:08 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241014093021.png  [object Object] 
2025-04-09 08:23:09 [info] indexing created file 工作库/项目/舌诊/attachments/Pasted image 20241015210604.png  [object Object] 
2025-04-09 08:23:09 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-09 08:23:09 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-09 08:23:09 [info] index finished after resolve  [object Object] 
2025-04-09 08:23:09 [info] refresh page data from resolve listeners 0 737   
2025-04-09 08:23:10 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-04-09 08:23:10 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-09 08:23:11 [info] index finished after resolve  [object Object] 
2025-04-09 08:23:11 [info] refresh page data from resolve listeners 0 737   
2025-04-09 08:23:14 [info] indexing created file 学习库/Anki/stm32/I2C.md  [object Object] 
2025-04-09 08:23:14 [info] indexing created ignore file 学习库/Anki/stm32/I2C.md   
2025-04-09 08:23:14 [info] trigger 学习库/Anki/stm32/I2C.md resolve  [object Object] 
2025-04-09 08:23:14 [info] index finished after resolve  [object Object] 
2025-04-09 08:23:14 [info] refresh page data from resolve listeners 0 738   
2025-04-09 08:23:15 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-08-14-36-45.png  [object Object] 
2025-04-09 08:23:15 [info] refresh page data from created listeners 0 739   
2025-04-09 08:23:16 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-09 08:23:16 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-09 08:23:16 [info] index finished after resolve  [object Object] 
2025-04-09 08:23:16 [info] refresh page data from resolve listeners 0 739   
2025-04-09 08:23:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-09 08:23:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-09 08:23:23 [info] index finished after resolve  [object Object] 
2025-04-09 08:23:23 [info] refresh page data from resolve listeners 0 739   
2025-04-09 08:23:24 [info] indexing created file components/logs/2025-04-08.components.log  [object Object] 
2025-04-09 08:23:24 [info] refresh page data from created listeners 0 740   
2025-04-09 08:24:21 [info] indexing created file components/logs/2025-04-09.components.log  [object Object] 
2025-04-09 08:24:21 [info] refresh page data from created listeners 0 744   
2025-04-09 08:27:05 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:27:05 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:27:05 [info] index finished after resolve  [object Object] 
2025-04-09 08:27:05 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:27:10 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:27:10 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:27:10 [info] index finished after resolve  [object Object] 
2025-04-09 08:27:10 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:27:12 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:27:12 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:27:12 [info] index finished after resolve  [object Object] 
2025-04-09 08:27:12 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:27:15 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:27:16 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:27:16 [info] index finished after resolve  [object Object] 
2025-04-09 08:27:16 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:27:22 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:27:22 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:27:22 [info] index finished after resolve  [object Object] 
2025-04-09 08:27:22 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:30:02 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:30:03 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:30:03 [info] index finished after resolve  [object Object] 
2025-04-09 08:30:03 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:30:52 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:30:52 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:30:52 [info] index finished after resolve  [object Object] 
2025-04-09 08:30:52 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:30:55 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:30:55 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:30:55 [info] index finished after resolve  [object Object] 
2025-04-09 08:30:55 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:04 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:05 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:05 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:05 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:07 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:07 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:07 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:07 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:12 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:12 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:12 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:12 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:15 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:16 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:16 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:16 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:32 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:32 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:32 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:32 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:31:35 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:31:35 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:31:35 [info] index finished after resolve  [object Object] 
2025-04-09 08:31:35 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:32:05 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:32:06 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:32:06 [info] index finished after resolve  [object Object] 
2025-04-09 08:32:06 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:32:08 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:32:08 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:32:08 [info] index finished after resolve  [object Object] 
2025-04-09 08:32:08 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:32:11 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:32:11 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:32:11 [info] index finished after resolve  [object Object] 
2025-04-09 08:32:11 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:34:04 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:34:05 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:34:05 [info] index finished after resolve  [object Object] 
2025-04-09 08:34:05 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:34:08 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:34:09 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:34:09 [info] index finished after resolve  [object Object] 
2025-04-09 08:34:09 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:34:44 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:34:45 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:34:45 [info] index finished after resolve  [object Object] 
2025-04-09 08:34:45 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:35:59 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:00 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:00 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:00 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:02 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:03 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:03 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:03 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:07 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:08 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:08 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:08 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:26 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:26 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:26 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:26 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:28 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:29 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:29 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:29 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:30 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:31 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:31 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:31 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:35 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:35 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:35 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:35 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:41 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:41 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:41 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:41 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:49 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:49 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:49 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:49 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:52 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:53 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:53 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:53 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:36:54 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-04-09 08:36:55 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-09 08:36:55 [info] index finished after resolve  [object Object] 
2025-04-09 08:36:55 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:38:10 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:38:10 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:38:10 [info] index finished after resolve  [object Object] 
2025-04-09 08:38:10 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:38:17 [info] components database created cost 7 ms   
2025-04-09 08:38:17 [info] components index initializing...   
2025-04-09 08:38:17 [info] start to batch put pages: 6   
2025-04-09 08:38:19 [info] batch persist cost 6  2485 
2025-04-09 08:38:19 [info] components index initialized, 744 files cost 2687 ms   
2025-04-09 08:38:19 [info] refresh page data from init listeners 0 744   
2025-04-09 08:38:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-09 08:38:22 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-09 08:38:22 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-09 08:38:22 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-09 08:38:22 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-09 08:38:22 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-09 08:38:26 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-09 08:38:26 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-09 08:38:26 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-09 08:38:55 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:38:55 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:38:55 [info] index finished after resolve  [object Object] 
2025-04-09 08:38:55 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:38:57 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:38:57 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:38:57 [info] index finished after resolve  [object Object] 
2025-04-09 08:38:57 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:39:06 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:39:06 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:39:06 [info] index finished after resolve  [object Object] 
2025-04-09 08:39:06 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:39:17 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:39:17 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:39:17 [info] index finished after resolve  [object Object] 
2025-04-09 08:39:17 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:39:19 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:39:19 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:39:19 [info] index finished after resolve  [object Object] 
2025-04-09 08:39:19 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:39:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:39:23 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:39:23 [info] index finished after resolve  [object Object] 
2025-04-09 08:39:23 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:39:24 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:39:24 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:39:24 [info] index finished after resolve  [object Object] 
2025-04-09 08:39:24 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:47:34 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:47:34 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:47:34 [info] index finished after resolve  [object Object] 
2025-04-09 08:47:34 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:47:36 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:47:36 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:47:36 [info] index finished after resolve  [object Object] 
2025-04-09 08:47:36 [info] refresh page data from resolve listeners 0 744   
2025-04-09 08:47:38 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-09 08:47:38 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-09 08:47:38 [info] index finished after resolve  [object Object] 
2025-04-09 08:47:38 [info] refresh page data from resolve listeners 0 744   
2025-04-09 09:25:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-04-09 09:25:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-04-09 09:25:54 [info] index finished after resolve  [object Object] 
2025-04-09 09:25:54 [info] refresh page data from resolve listeners 0 744   
2025-04-09 09:45:11 [info] indexing created file 学习库/stm32/未命名.md  [object Object] 
2025-04-09 09:45:11 [info] indexing created ignore file 学习库/stm32/未命名.md   
2025-04-09 09:45:11 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-09 09:45:11 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-09 09:45:11 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-09 09:45:11 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-09 09:45:11 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-09 09:45:11 [info] trigger 学习库/stm32/未命名.md resolve  [object Object] 
2025-04-09 09:45:11 [info] index finished after resolve  [object Object] 
2025-04-09 09:45:11 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:45:21 [info] refresh page data from rename listeners 0 745   
2025-04-09 09:45:21 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-09 09:45:21 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-09 09:45:21 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-09 09:45:21 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-09 09:45:21 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-09 09:45:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:45:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:45:24 [info] index finished after resolve  [object Object] 
2025-04-09 09:45:24 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:45:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:45:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:45:29 [info] index finished after resolve  [object Object] 
2025-04-09 09:45:29 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:45:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:45:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:45:31 [info] index finished after resolve  [object Object] 
2025-04-09 09:45:31 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:48:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:48:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:48:58 [info] index finished after resolve  [object Object] 
2025-04-09 09:48:58 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:01 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:01 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:03 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:03 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:17 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:17 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:17 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:17 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:19 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:20 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:20 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:20 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:23 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:23 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:23 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:23 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:25 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:25 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:25 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:25 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:27 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:27 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:27 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:27 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:30 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:30 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:30 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:30 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:33 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:33 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:33 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:35 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:35 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:37 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:37 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:37 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:37 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:39 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:39 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:42 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:42 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:46 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:46 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:48 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:48 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:50 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:50 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:50 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:50 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:52 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:52 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:52 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:52 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:49:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:49:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:49:57 [info] index finished after resolve  [object Object] 
2025-04-09 09:49:57 [info] refresh page data from resolve listeners 0 745   
2025-04-09 09:50:40 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-09-50-40.png  [object Object] 
2025-04-09 09:50:40 [info] refresh page data from created listeners 0 746   
2025-04-09 09:50:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:50:43 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:50:43 [info] index finished after resolve  [object Object] 
2025-04-09 09:50:43 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:50:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:50:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:50:48 [info] index finished after resolve  [object Object] 
2025-04-09 09:50:48 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:50:59 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:50:59 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:50:59 [info] index finished after resolve  [object Object] 
2025-04-09 09:50:59 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:02 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:02 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:09 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:09 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:09 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:09 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:24 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:24 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:31 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:31 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:39 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:39 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:46 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:46 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:51:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:51:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:51:51 [info] index finished after resolve  [object Object] 
2025-04-09 09:51:51 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:52:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:52:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:52:01 [info] index finished after resolve  [object Object] 
2025-04-09 09:52:01 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:52:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:52:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:52:03 [info] index finished after resolve  [object Object] 
2025-04-09 09:52:03 [info] refresh page data from resolve listeners 0 746   
2025-04-09 09:52:07 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 09:52:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 09:52:07 [info] index finished after resolve  [object Object] 
2025-04-09 09:52:07 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:02:17 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:02:17 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:02:17 [info] index finished after resolve  [object Object] 
2025-04-09 10:02:17 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:02:45 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:02:45 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:02:45 [info] index finished after resolve  [object Object] 
2025-04-09 10:02:45 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:02:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:02:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:02:48 [info] index finished after resolve  [object Object] 
2025-04-09 10:02:48 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:05:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:05:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:05:02 [info] index finished after resolve  [object Object] 
2025-04-09 10:05:02 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:05:05 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:05:05 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:05:05 [info] index finished after resolve  [object Object] 
2025-04-09 10:05:05 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:06:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:06:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:06:51 [info] index finished after resolve  [object Object] 
2025-04-09 10:06:51 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:06:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:06:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:06:54 [info] index finished after resolve  [object Object] 
2025-04-09 10:06:54 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:07:10 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:07:10 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:07:10 [info] index finished after resolve  [object Object] 
2025-04-09 10:07:10 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:11:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:11:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:11:54 [info] index finished after resolve  [object Object] 
2025-04-09 10:11:54 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:11:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:11:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:11:57 [info] index finished after resolve  [object Object] 
2025-04-09 10:11:57 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:11:59 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:11:59 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:11:59 [info] index finished after resolve  [object Object] 
2025-04-09 10:11:59 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:01 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:01 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:05 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:05 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:05 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:05 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:08 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:08 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:10 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:10 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:10 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:10 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:12 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:12 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:12 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:12 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:15 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:15 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:15 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:15 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:19 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:19 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:19 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:19 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:21 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:39 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:39 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:42 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:42 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:44 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:44 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:46 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:46 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:48 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:48 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:54 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:54 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:12:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:12:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:12:57 [info] index finished after resolve  [object Object] 
2025-04-09 10:12:57 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:13:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:13:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:13:01 [info] index finished after resolve  [object Object] 
2025-04-09 10:13:01 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:13:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:13:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:13:24 [info] index finished after resolve  [object Object] 
2025-04-09 10:13:24 [info] refresh page data from resolve listeners 0 746   
2025-04-09 10:13:55 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-13-55.png  [object Object] 
2025-04-09 10:13:55 [info] refresh page data from created listeners 0 747   
2025-04-09 10:13:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:13:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:13:57 [info] index finished after resolve  [object Object] 
2025-04-09 10:13:57 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:15:32 [info] trigger 学习库/stm32/attachments/5 SPI-2025-04-09-10-13-55.png resolve  [object Object] 
2025-04-09 10:15:32 [info] index finished after resolve  [object Object] 
2025-04-09 10:15:32 [info] refresh page data from modify listeners 0 747   
2025-04-09 10:16:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:16:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:16:54 [info] index finished after resolve  [object Object] 
2025-04-09 10:16:54 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:16:56 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:16:56 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:16:56 [info] index finished after resolve  [object Object] 
2025-04-09 10:16:56 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:16:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:16:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:16:58 [info] index finished after resolve  [object Object] 
2025-04-09 10:16:58 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:01 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:01 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:03 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:03 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:08 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:08 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:28 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:28 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:28 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:34 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:34 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:34 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:34 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:38 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:38 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:38 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:41 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:41 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:41 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:41 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:46 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:46 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:51 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:51 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:53 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:53 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:17:56 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:17:56 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:17:56 [info] index finished after resolve  [object Object] 
2025-04-09 10:17:56 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:18:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:13 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:13 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:18:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:16 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:16 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:18:19 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:19 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:19 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:19 [info] refresh page data from resolve listeners 0 747   
2025-04-09 10:18:24 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-18-24.png  [object Object] 
2025-04-09 10:18:24 [info] refresh page data from created listeners 0 748   
2025-04-09 10:18:25 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:26 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:26 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:18:45 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:45 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:45 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:45 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:18:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:53 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:53 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:18:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:18:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:18:55 [info] index finished after resolve  [object Object] 
2025-04-09 10:18:55 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:20:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:20:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:20:53 [info] index finished after resolve  [object Object] 
2025-04-09 10:20:53 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:20:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:20:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:20:58 [info] index finished after resolve  [object Object] 
2025-04-09 10:20:58 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:04 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:04 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:06 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:06 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:06 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:21 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:24 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:24 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:45 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:45 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:45 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:45 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:51 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:51 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:21:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:21:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:21:55 [info] index finished after resolve  [object Object] 
2025-04-09 10:21:55 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:02 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:02 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:08 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:08 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:12 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:12 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:12 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:12 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:21 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:25 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:25 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:25 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:25 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:28 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:28 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:28 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:33 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:33 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:33 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:38 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:38 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:38 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:41 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:41 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:41 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:41 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:44 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:44 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:47 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:47 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:47 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:22:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:22:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:22:51 [info] index finished after resolve  [object Object] 
2025-04-09 10:22:51 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:24:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:24:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:24:29 [info] index finished after resolve  [object Object] 
2025-04-09 10:24:29 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:25:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:25:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:25:31 [info] index finished after resolve  [object Object] 
2025-04-09 10:25:31 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:25:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:25:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:25:51 [info] index finished after resolve  [object Object] 
2025-04-09 10:25:51 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:25:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:25:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:25:55 [info] index finished after resolve  [object Object] 
2025-04-09 10:25:55 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:01 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:01 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:03 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:03 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:06 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:06 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:06 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:08 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:08 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:18 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:18 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:18 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:18 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:21 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:24 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:24 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:29 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:29 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:34 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:35 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:35 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:40 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:40 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:40 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:40 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:26:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:26:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:26:42 [info] index finished after resolve  [object Object] 
2025-04-09 10:26:42 [info] refresh page data from resolve listeners 0 748   
2025-04-09 10:27:20 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-27-20.png  [object Object] 
2025-04-09 10:27:20 [info] refresh page data from created listeners 0 749   
2025-04-09 10:27:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:27:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:27:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:27:21 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:11 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:11 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:11 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:11 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:14 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:14 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:14 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:14 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:16 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:16 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:18 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:18 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:18 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:18 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:20 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:20 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:20 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:20 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:30 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:30 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:30 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:30 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:32 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:32 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:32 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:32 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:38 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:38 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:38 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:41 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:41 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:41 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:41 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:43 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:43 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:43 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:43 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:54 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:54 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:29:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:29:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:29:57 [info] index finished after resolve  [object Object] 
2025-04-09 10:29:57 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:30:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:30:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:30:07 [info] index finished after resolve  [object Object] 
2025-04-09 10:30:07 [info] refresh page data from resolve listeners 0 749   
2025-04-09 10:30:23 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-10-30-23.png  [object Object] 
2025-04-09 10:30:23 [info] refresh page data from created listeners 0 750   
2025-04-09 10:30:25 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:30:25 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:30:25 [info] index finished after resolve  [object Object] 
2025-04-09 10:30:25 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:30:32 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:30:32 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:30:32 [info] index finished after resolve  [object Object] 
2025-04-09 10:30:32 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:31:47 [info] trigger 学习库/stm32/attachments/5 SPI-2025-04-09-10-30-23.png resolve  [object Object] 
2025-04-09 10:31:47 [info] index finished after resolve  [object Object] 
2025-04-09 10:31:47 [info] refresh page data from modify listeners 0 750   
2025-04-09 10:32:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:35 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:35 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:37 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:37 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:37 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:37 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:39 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:39 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:47 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:47 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:47 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:50 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:50 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:50 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:50 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:53 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:53 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:55 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:55 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:32:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:32:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:32:57 [info] index finished after resolve  [object Object] 
2025-04-09 10:32:57 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:00 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:00 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:00 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:00 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:04 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:04 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:07 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:07 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:07 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:16 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:16 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:18 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:18 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:18 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:18 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:21 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:21 [info] refresh page data from resolve listeners 0 750   
2025-04-09 10:33:26 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 10:33:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 10:33:26 [info] index finished after resolve  [object Object] 
2025-04-09 10:33:26 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:25 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:25 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:25 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:25 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:31 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:31 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:33 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:34 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:34 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:36 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:36 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:36 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:36 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:39 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:39 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:14:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:14:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:14:42 [info] index finished after resolve  [object Object] 
2025-04-09 14:14:42 [info] refresh page data from resolve listeners 0 750   
2025-04-09 14:15:25 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-14-15-25.png  [object Object] 
2025-04-09 14:15:25 [info] refresh page data from created listeners 0 751   
2025-04-09 14:15:27 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:15:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:15:28 [info] index finished after resolve  [object Object] 
2025-04-09 14:15:28 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:39 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:39 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:42 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:42 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:44 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:44 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:46 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:46 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:48 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:48 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:16:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:16:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:16:58 [info] index finished after resolve  [object Object] 
2025-04-09 14:16:58 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:17:00 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:17:00 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:17:00 [info] index finished after resolve  [object Object] 
2025-04-09 14:17:00 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:17:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:17:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:17:02 [info] index finished after resolve  [object Object] 
2025-04-09 14:17:02 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:17:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:17:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:17:04 [info] index finished after resolve  [object Object] 
2025-04-09 14:17:04 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:17:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:17:06 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:17:06 [info] index finished after resolve  [object Object] 
2025-04-09 14:17:06 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:17:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:17:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:17:08 [info] index finished after resolve  [object Object] 
2025-04-09 14:17:08 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:21 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:21 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:24 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:24 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:26 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:26 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:26 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:31 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:31 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:32 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:33 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:33 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:35 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:35 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:37 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:37 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:37 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:37 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:39 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:39 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:41 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:41 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:41 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:41 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:44 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:44 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:46 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:46 [info] refresh page data from resolve listeners 0 751   
2025-04-09 14:18:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 14:18:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 14:18:48 [info] index finished after resolve  [object Object] 
2025-04-09 14:18:48 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:08:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:08:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:08:53 [info] index finished after resolve  [object Object] 
2025-04-09 20:08:53 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:10:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:10:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:10:48 [info] index finished after resolve  [object Object] 
2025-04-09 20:10:48 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:10:50 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:10:50 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:10:50 [info] index finished after resolve  [object Object] 
2025-04-09 20:10:50 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:10:52 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:10:52 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:10:53 [info] index finished after resolve  [object Object] 
2025-04-09 20:10:53 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:10:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:10:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:10:55 [info] index finished after resolve  [object Object] 
2025-04-09 20:10:55 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:28 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:28 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:28 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:30 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:30 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:30 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:30 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:34 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:34 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:34 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:34 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:37 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:37 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:37 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:37 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:39 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:39 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:43 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:43 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:43 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:43 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:45 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:45 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:46 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:46 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:18:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:18:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:18:48 [info] index finished after resolve  [object Object] 
2025-04-09 20:18:48 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:19:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:19:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:19:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:19:13 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:19:20 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:19:20 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:19:20 [info] index finished after resolve  [object Object] 
2025-04-09 20:19:20 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:20:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:20:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:20:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:20:13 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:20:17 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:20:17 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:20:17 [info] index finished after resolve  [object Object] 
2025-04-09 20:20:17 [info] refresh page data from resolve listeners 0 751   
2025-04-09 20:24:16 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-24-16.png  [object Object] 
2025-04-09 20:24:16 [info] refresh page data from created listeners 0 752   
2025-04-09 20:24:17 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:17 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:17 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:17 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:27 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:27 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:27 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:27 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:29 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:29 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:31 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:31 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:33 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:33 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:33 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:35 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:35 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:38 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:38 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:38 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:42 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:42 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:44 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:44 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:47 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:47 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:47 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:47 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:49 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:49 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:49 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:49 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:24:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:24:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:24:55 [info] index finished after resolve  [object Object] 
2025-04-09 20:24:55 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:03 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:03 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:07 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:07 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:07 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:09 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:09 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:09 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:09 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:13 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:14 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:14 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:14 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:14 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:18 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:18 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:18 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:18 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:27 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:27 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:27 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:27 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:29 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:29 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:32 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:32 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:32 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:32 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:34 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:34 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:34 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:34 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:40 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:40 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:40 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:40 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:44 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:44 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:48 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:48 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:51 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:51 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:53 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:53 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:53 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:53 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:25:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:25:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:25:57 [info] index finished after resolve  [object Object] 
2025-04-09 20:25:57 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:01 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:01 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:04 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:04 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:06 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:06 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:06 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:08 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:08 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:10 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:10 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:10 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:10 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:13 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:24 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:24 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:27 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:27 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:27 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:27 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:29 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:29 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:32 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:32 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:32 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:32 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:38 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:38 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:38 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:40 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:40 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:40 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:40 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:42 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:42 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:45 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:45 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:45 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:45 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:48 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:48 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:48 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:48 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:50 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:50 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:50 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:50 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:52 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:52 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:52 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:52 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:55 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:55 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:57 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:57 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:26:59 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:26:59 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:26:59 [info] index finished after resolve  [object Object] 
2025-04-09 20:26:59 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:01 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:01 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:06 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:06 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:06 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:06 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:13 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:16 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:16 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:22 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:22 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:22 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:22 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:51 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:51 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:51 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:51 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:27:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:27:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:27:57 [info] index finished after resolve  [object Object] 
2025-04-09 20:27:57 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:02 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:02 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:11 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:11 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:11 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:11 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:33 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:33 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:33 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:33 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:40 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:40 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:40 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:40 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:43 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:43 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:43 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:43 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:52 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:52 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:52 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:52 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:28:57 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:28:57 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:28:57 [info] index finished after resolve  [object Object] 
2025-04-09 20:28:57 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:05 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:05 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:05 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:05 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:07 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:07 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:07 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:10 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:10 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:10 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:10 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:22 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:22 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:22 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:22 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:31 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:31 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:35 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:35 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:37 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:38 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:38 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:38 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:29:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:29:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:29:42 [info] index finished after resolve  [object Object] 
2025-04-09 20:29:42 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:31:11 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:31:11 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:31:11 [info] index finished after resolve  [object Object] 
2025-04-09 20:31:11 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:31:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:31:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:31:58 [info] index finished after resolve  [object Object] 
2025-04-09 20:31:58 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:32:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:02 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:02 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:32:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:04 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:04 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:32:07 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:07 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:07 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:07 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:32:09 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:09 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:09 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:09 [info] refresh page data from resolve listeners 0 752   
2025-04-09 20:32:10 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-32-10.png  [object Object] 
2025-04-09 20:32:11 [info] refresh page data from created listeners 0 753   
2025-04-09 20:32:11 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:11 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:11 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:11 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:32:14 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:32:14 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:32:14 [info] index finished after resolve  [object Object] 
2025-04-09 20:32:14 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:34:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:34:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:34:55 [info] index finished after resolve  [object Object] 
2025-04-09 20:34:55 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:34:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:34:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:34:58 [info] index finished after resolve  [object Object] 
2025-04-09 20:34:58 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:00 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:00 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:00 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:00 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:03 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:03 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:03 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:03 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:05 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:05 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:05 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:05 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:08 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:08 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:08 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:08 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:10 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:10 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:10 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:10 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:13 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:13 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:16 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:16 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:19 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:19 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:19 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:19 [info] refresh page data from resolve listeners 0 753   
2025-04-09 20:35:20 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-20-35-20.png  [object Object] 
2025-04-09 20:35:20 [info] refresh page data from created listeners 0 754   
2025-04-09 20:35:22 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:22 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:22 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:22 [info] refresh page data from resolve listeners 0 754   
2025-04-09 20:35:26 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:26 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:26 [info] refresh page data from resolve listeners 0 754   
2025-04-09 20:35:31 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 20:35:31 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 20:35:31 [info] index finished after resolve  [object Object] 
2025-04-09 20:35:31 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:02 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:02 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:13 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:13 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:13 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:13 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:16 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:16 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:19 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:19 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:19 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:19 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:21 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:21 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:24 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:24 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:24 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:24 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:26 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:26 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:26 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:35:29 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:35:29 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:35:30 [info] index finished after resolve  [object Object] 
2025-04-09 21:35:30 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:21 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:21 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:23 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:23 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:23 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:23 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:26 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:26 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:26 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:26 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:28 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:29 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:29 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:30 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:30 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:30 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:30 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:36 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:36 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:36 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:36 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:36 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:36 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:36 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:39 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:39 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:39 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:39 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:42 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:42 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:42 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:42 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:44 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:44 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:44 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:44 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:46 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:46 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:46 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:46 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:50 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:50 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:50 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:50 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:52 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:52 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:52 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:52 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:55 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:55 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:55 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:55 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:36:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:36:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:36:58 [info] index finished after resolve  [object Object] 
2025-04-09 21:36:58 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:37:02 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:37:02 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:37:02 [info] index finished after resolve  [object Object] 
2025-04-09 21:37:02 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:37:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:37:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:37:04 [info] index finished after resolve  [object Object] 
2025-04-09 21:37:04 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:37:35 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:37:35 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:37:35 [info] index finished after resolve  [object Object] 
2025-04-09 21:37:35 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:45:15 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:45:15 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:45:15 [info] index finished after resolve  [object Object] 
2025-04-09 21:45:15 [info] refresh page data from resolve listeners 0 754   
2025-04-09 21:45:18 [info] indexing created file 学习库/stm32/attachments/5 SPI-2025-04-09-21-45-18.png  [object Object] 
2025-04-09 21:45:18 [info] refresh page data from created listeners 0 755   
2025-04-09 21:45:18 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:45:18 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:45:18 [info] index finished after resolve  [object Object] 
2025-04-09 21:45:18 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:45:20 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:45:20 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:45:20 [info] index finished after resolve  [object Object] 
2025-04-09 21:45:20 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:45:28 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:45:28 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:45:28 [info] index finished after resolve  [object Object] 
2025-04-09 21:45:28 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:46:16 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:46:16 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:46:16 [info] index finished after resolve  [object Object] 
2025-04-09 21:46:16 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:46:21 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:46:21 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:46:21 [info] index finished after resolve  [object Object] 
2025-04-09 21:46:21 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:46:23 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:46:23 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:46:23 [info] index finished after resolve  [object Object] 
2025-04-09 21:46:23 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:47:54 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:47:54 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:47:54 [info] index finished after resolve  [object Object] 
2025-04-09 21:47:54 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:47:56 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:47:56 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:47:56 [info] index finished after resolve  [object Object] 
2025-04-09 21:47:56 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:47:58 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:47:58 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:47:58 [info] index finished after resolve  [object Object] 
2025-04-09 21:47:58 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:48:01 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:48:01 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:48:01 [info] index finished after resolve  [object Object] 
2025-04-09 21:48:01 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:48:04 [info] ignore file modify evnet 学习库/stm32/5 SPI.md   
2025-04-09 21:48:04 [info] trigger 学习库/stm32/5 SPI.md resolve  [object Object] 
2025-04-09 21:48:04 [info] index finished after resolve  [object Object] 
2025-04-09 21:48:04 [info] refresh page data from resolve listeners 0 755   
2025-04-09 21:55:08 [info] indexing created file 学习库/Anki/stm32/未命名.md  [object Object] 
2025-04-09 21:55:08 [info] indexing created ignore file 学习库/Anki/stm32/未命名.md   
2025-04-09 21:55:08 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-09 21:55:08 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-09 21:55:08 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-09 21:55:08 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-09 21:55:08 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-09 21:55:09 [info] trigger 学习库/Anki/stm32/未命名.md resolve  [object Object] 
2025-04-09 21:55:09 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:09 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:15 [info] refresh page data from rename listeners 0 756   
2025-04-09 21:55:15 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-09 21:55:15 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-09 21:55:15 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-09 21:55:15 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-09 21:55:15 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-09 21:55:17 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:17 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:17 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:17 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:36 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:36 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:36 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:36 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:43 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:43 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:43 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:43 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:45 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:45 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:45 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:45 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:48 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:48 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:48 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:48 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:50 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:50 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:50 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:50 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:52 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:52 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:52 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:52 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:54 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:54 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:54 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:54 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:55:57 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:55:57 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:55:57 [info] index finished after resolve  [object Object] 
2025-04-09 21:55:57 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:56:02 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:56:02 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:56:02 [info] index finished after resolve  [object Object] 
2025-04-09 21:56:02 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:56:32 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:56:32 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:56:33 [info] index finished after resolve  [object Object] 
2025-04-09 21:56:33 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:56:35 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:56:35 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:56:35 [info] index finished after resolve  [object Object] 
2025-04-09 21:56:35 [info] refresh page data from resolve listeners 0 756   
2025-04-09 21:56:37 [info] ignore file modify evnet 学习库/Anki/stm32/SPI.md   
2025-04-09 21:56:37 [info] trigger 学习库/Anki/stm32/SPI.md resolve  [object Object] 
2025-04-09 21:56:37 [info] index finished after resolve  [object Object] 
2025-04-09 21:56:37 [info] refresh page data from resolve listeners 0 756   
