2025-03-15 09:27:01 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-15 09:27:03 [info] components database created cost 1 ms   
2025-03-15 09:27:03 [info] components index initializing...   
2025-03-15 09:27:03 [info] indexing created file components/logs/2025-03-15.components.log  [object Object] 
2025-03-15 09:27:03 [info] components index initialized, 536 files cost 186 ms   
2025-03-15 09:27:03 [info] refresh page data from init listeners 0 536   
2025-03-15 09:27:03 [info] refresh page data from created listeners 0 537   
2025-03-15 09:27:03 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-15 09:27:04 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-15 09:27:04 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:04 [info] refresh page data from resolve listeners 0 537   
2025-03-15 09:27:04 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-15 09:27:04 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-15 09:27:05 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-15 09:27:05 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-15 09:27:05 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-15 09:27:05 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-15 09:27:11 [info] indexing created file 日记库/day/2025-03-15.md  [object Object] 
2025-03-15 09:27:11 [info] indexing created ignore file 日记库/day/2025-03-15.md   
2025-03-15 09:27:11 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:11 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:11 [info] refresh page data from resolve listeners 0 538   
2025-03-15 09:27:11 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:11 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:11 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:11 [info] refresh page data from resolve listeners 0 538   
2025-03-15 09:27:13 [info] indexing created file 学习库/Deep learning/attachments/GIF 2024-10-8 14-54-29.gif  [object Object] 
2025-03-15 09:27:13 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-15 09:27:13 [info] refresh page data from created listeners 0 539   
2025-03-15 09:27:18 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:18 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:18 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:18 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:20 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:20 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:20 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:20 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:24 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:24 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:24 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:24 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:26 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:26 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:26 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:26 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:28 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:28 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:28 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:28 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:32 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:32 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:32 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:32 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:34 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:34 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:34 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:34 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:37 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:37 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:37 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:37 [info] refresh page data from resolve listeners 0 539   
2025-03-15 09:27:44 [info] indexing created file 学习库/Deep learning/attachments/GIF 2024-10-8 10-21-33.gif  [object Object] 
2025-03-15 09:27:44 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-15 09:27:44 [info] refresh page data from created listeners 0 540   
2025-03-15 09:27:44 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:44 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:44 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:44 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:27:50 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:50 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:50 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:50 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:27:54 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:27:54 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:27:54 [info] index finished after resolve  [object Object] 
2025-03-15 09:27:54 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:01 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:01 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:01 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:01 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:06 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:07 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:07 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:07 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:11 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:11 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:11 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:11 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:17 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:17 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:17 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:17 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:22 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:22 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:22 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:22 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:24 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:24 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:24 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:24 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:29 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:29 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:29 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:29 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:32 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:32 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:32 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:32 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:34 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:34 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:34 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:34 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:39 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:39 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:39 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:39 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:42 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:42 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:42 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:42 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:45 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:45 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:45 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:45 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:48 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:48 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:48 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:48 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:51 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:51 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:51 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:51 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:53 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:53 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:53 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:53 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:28:56 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:28:56 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:28:56 [info] index finished after resolve  [object Object] 
2025-03-15 09:28:56 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:01 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:01 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:01 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:01 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:04 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:04 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:04 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:04 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:09 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:09 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:09 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:09 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:15 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:15 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:15 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:15 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:17 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:17 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:17 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:17 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:21 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:21 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:21 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:21 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:24 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:24 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:24 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:24 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:27 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:27 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:27 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:27 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:33 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:33 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:33 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:33 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:36 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:36 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:36 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:36 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:38 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:38 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:38 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:38 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:41 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:41 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:41 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:41 [info] refresh page data from resolve listeners 0 540   
2025-03-15 09:29:43 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-15 09:29:43 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-15 09:29:43 [info] index finished after resolve  [object Object] 
2025-03-15 09:29:43 [info] refresh page data from resolve listeners 0 540   
