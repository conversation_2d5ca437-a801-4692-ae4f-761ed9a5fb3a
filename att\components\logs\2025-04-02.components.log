2025-04-02 22:58:00 [error] Load Component Error: {"id":"988560c2-3b05-473a-b563-50945fd33a14","type":"chart","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2024-12-27T05:57:54.921Z","updateAt":"2024-12-27T05:57:54.921Z","chartType":"heatmap","filter":{"id":"e6f7d84a-499d-48a6-b779-351efdb7bfd8","type":"group","operator":"and","conditions":[{"id":"d1ef6824-5eda-4d93-999a-611680655bae","type":"filter","operator":"contains","property":"${file.parent}","value":"area/journal/day","conditions":[]}]},"labelProperty":"${file.basename}","labelFormat":"$none","valueProperty":"${file.words}","valuePrecision":0,"sortField":"xAxisValue","sortOrder":"asc","maxHeight":300,"chartColorSet":"default","chartLabelPosition":"top","showDataValue":false,"valueScaleStartFromZero":true,"smoothLine":false,"fillArea":false,"options":{"minValue":1,"maxValue":3000,"cellSize":12,"numberOfSegments":4,"showSplitLine":false,"firstDayOfWeek":1,"dateRange":{"type":"currentYear","latestValue":180,"latestUnit":"day"}},"title":"今年日记字词数热力图"}, error detail:   {"message":"document[Kth(0x3aec)]['computedStyleMap'] is not a function. (In 'document[Kth(0x3aec)]['computedStyleMap']()', 'document[Kth(0x3aec)]['computedStyleMap']' is undefined)","stack":"@\nuseMemo@\nRBt@\nHBt@\nh1Z@\nh4z@\nh45@\nh43@\nh42@\nh3B@\nh3w@\nh3w@[native code]\nx@\nR@"},
HBt
Zio
div
W@
xv
nao
Suspense
div
W@
W@
W@
div
W@
W@
div
div
uao
Bro
div
So
zdr
Zio
div
W@
xv
W@
noe 
2025-04-02 22:58:00 [error] Load Component Error: {"id":"cef44a38-95e6-431c-a6ca-f0fffedff3e9","type":"chart","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2024-12-27T05:57:54.921Z","updateAt":"2024-12-27T05:57:54.921Z","chartType":"bar","filter":{"id":"e6f7d84a-499d-48a6-b779-351efdb7bfd8","type":"group","operator":"and","conditions":[{"id":"d1ef6824-5eda-4d93-999a-611680655bae","type":"filter","operator":"contains","property":"${file.parent}","value":"journal/day","conditions":[]}]},"labelProperty":"${file.basename}","labelFormat":"$timeFormat","valueProperty":"$file_count","valuePrecision":0,"sortField":"xAxisValue","sortOrder":"asc","maxHeight":300,"chartColorSet":"default","chartLabelPosition":"top","showDataValue":true,"valueScaleStartFromZero":true,"smoothLine":false,"fillArea":false,"options":{"minValue":1,"maxValue":3000,"cellSize":12,"numberOfSegments":4,"showSplitLine":false,"firstDayOfWeek":1,"dateRange":{"type":"currentYear","latestValue":180,"latestUnit":"day"}},"title":"季度日记数","valueFormatPattern":"yyyy-'Q'q","labelRotation":0}, error detail:   {"message":"document[Kth(0x3aec)]['computedStyleMap'] is not a function. (In 'document[Kth(0x3aec)]['computedStyleMap']()', 'document[Kth(0x3aec)]['computedStyleMap']' is undefined)","stack":"@\nuseMemo@\nRBt@\nHBt@\nh1Z@\nh4z@\nh45@\nh43@\nh42@\nh3B@\nh3w@\nh3w@[native code]\nx@\nR@"},
HBt
Zio
div
W@
xv
nao
Suspense
div
W@
W@
W@
div
W@
W@
div
div
uao
Bro
div
So
zdr
Zio
div
W@
xv
W@
noe 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:00 [info] query  [object Object] 
2025-04-02 22:58:01 [info] upgrade db  [object Object] 
2025-04-02 22:58:01 [info] clear all page data in upgrade   
2025-04-02 22:58:01 [info] components database created cost 42 ms   
2025-04-02 22:58:01 [info] components index initializing...   
2025-04-02 22:58:02 [info] components index initialized, 524 files cost 1440 ms   
2025-04-02 22:58:02 [info] refresh page data from init listeners 0 524   
2025-04-02 22:58:02 [info] indexing created file components/logs/2025-04-02.components.log  [object Object] 
2025-04-02 22:58:02 [info] refresh page data from created listeners 0 525   
2025-04-02 22:58:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-02 22:58:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-02 22:58:04 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-02 22:58:04 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-02 22:58:04 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-02 22:58:04 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-02 22:58:07 [info] ignore file modify evnet 学习库/template/通用学习模板.md   
2025-04-02 22:58:07 [info] trigger 学习库/template/通用学习模板.md resolve  [object Object] 
2025-04-02 22:58:07 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:07 [info] refresh page data from resolve listeners 0 525   
2025-04-02 22:58:07 [info] refresh page data from delete listeners 0 524   
2025-04-02 22:58:07 [info] refresh page data from delete listeners 0 523   
2025-04-02 22:58:07 [info] refresh page data from delete listeners 0 522   
2025-04-02 22:58:08 [info] indexing created file att/scripts/getweather.js  [object Object] 
2025-04-02 22:58:08 [info] refresh page data from created listeners 0 523   
2025-04-02 22:58:10 [info] indexing created file att/scripts/daily-stats.json  [object Object] 
2025-04-02 22:58:10 [info] refresh page data from created listeners 0 524   
2025-04-02 22:58:17 [info] indexing created file att/scripts/dv_weatherSvg.js  [object Object] 
2025-04-02 22:58:17 [info] refresh page data from created listeners 0 525   
2025-04-02 22:58:20 [info] indexing created file Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md  [object Object] 
2025-04-02 22:58:20 [info] indexing created ignore file Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md   
2025-04-02 22:58:20 [info] refresh page data from delete listeners 0 524   
2025-04-02 22:58:20 [info] trigger Excalidraw/Drawing 2025-03-11 14.53.35.excalidraw.md resolve  [object Object] 
2025-04-02 22:58:20 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:20 [info] refresh page data from resolve listeners 0 525   
2025-04-02 22:58:20 [info] indexing created file att/scripts/switchLightDark.js  [object Object] 
2025-04-02 22:58:20 [info] refresh page data from created listeners 0 526   
2025-04-02 22:58:25 [info] indexing created file 未命名.md  [object Object] 
2025-04-02 22:58:25 [info] indexing created ignore file 未命名.md   
2025-04-02 22:58:25 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-02 22:58:25 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-02 22:58:25 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-02 22:58:25 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-02 22:58:25 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-02 22:58:25 [info] trigger 未命名.md resolve  [object Object] 
2025-04-02 22:58:25 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:25 [info] refresh page data from resolve listeners 0 527   
2025-04-02 22:58:25 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 526   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 525   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 524   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 523   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 522   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 521   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 520   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 519   
2025-04-02 22:58:30 [info] refresh page data from delete listeners 0 518   
2025-04-02 22:58:30 [info] indexing created file 工作库/项目/舌诊/test.md  [object Object] 
2025-04-02 22:58:30 [info] indexing created ignore file 工作库/项目/舌诊/test.md   
2025-04-02 22:58:30 [info] trigger 工作库/项目/舌诊/test.md resolve  [object Object] 
2025-04-02 22:58:30 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:30 [info] refresh page data from resolve listeners 0 519   
2025-04-02 22:58:31 [info] indexing created file 工作库/项目/声音识别/test 1.md  [object Object] 
2025-04-02 22:58:31 [info] indexing created ignore file 工作库/项目/声音识别/test 1.md   
2025-04-02 22:58:31 [info] trigger 工作库/项目/声音识别/test 1.md resolve  [object Object] 
2025-04-02 22:58:31 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:31 [info] refresh page data from resolve listeners 0 520   
2025-04-02 22:58:32 [info] indexing created file 工作库/比赛/测试/测试.md  [object Object] 
2025-04-02 22:58:32 [info] indexing created ignore file 工作库/比赛/测试/测试.md   
2025-04-02 22:58:32 [info] trigger 工作库/比赛/测试/测试.md resolve  [object Object] 
2025-04-02 22:58:32 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:32 [info] refresh page data from resolve listeners 0 521   
2025-04-02 22:58:32 [info] indexing created file 工作库/比赛/测试1/测试1.md  [object Object] 
2025-04-02 22:58:32 [info] indexing created ignore file 工作库/比赛/测试1/测试1.md   
2025-04-02 22:58:32 [info] trigger 工作库/比赛/测试1/测试1.md resolve  [object Object] 
2025-04-02 22:58:32 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:32 [info] refresh page data from resolve listeners 0 522   
2025-04-02 22:58:34 [info] refresh page data from delete listeners 0 521   
2025-04-02 22:58:35 [info] ignore file modify evnet 日记库/template/日记模板.md   
2025-04-02 22:58:35 [info] trigger 日记库/template/日记模板.md resolve  [object Object] 
2025-04-02 22:58:35 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:35 [info] refresh page data from resolve listeners 0 521   
2025-04-02 22:58:36 [info] trigger att/picture/cat.gif resolve  [object Object] 
2025-04-02 22:58:36 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:36 [info] refresh page data from modify listeners 0 521   
2025-04-02 22:58:36 [info] refresh page data from delete listeners 0 520   
2025-04-02 22:58:36 [info] refresh page data from delete listeners 0 519   
2025-04-02 22:58:36 [info] trigger GIT食用指南/GIT 工作流和核心原理.md resolve  [object Object] 
2025-04-02 22:58:36 [info] refresh page data from delete listeners 0 518   
2025-04-02 22:58:36 [info] refresh page data from delete listeners 0 517   
2025-04-02 22:58:36 [info] refresh page data from delete listeners 0 516   
2025-04-02 22:58:39 [info] indexing created file 学习库/GIT食用指南/Excalidraw/Drawing 2024-03-19 20.45.53.excalidraw.md  [object Object] 
2025-04-02 22:58:39 [info] indexing created ignore file 学习库/GIT食用指南/Excalidraw/Drawing 2024-03-19 20.45.53.excalidraw.md   
2025-04-02 22:58:39 [info] trigger 学习库/GIT食用指南/Excalidraw/Drawing 2024-03-19 20.45.53.excalidraw.md resolve  [object Object] 
2025-04-02 22:58:39 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:39 [info] refresh page data from resolve listeners 0 517   
2025-04-02 22:58:45 [info] indexing created file 学习库/GIT食用指南/attachments/Pasted image 20240319205533.png  [object Object] 
2025-04-02 22:58:45 [info] refresh page data from created listeners 0 518   
2025-04-02 22:58:46 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-12.png  [object Object] 
2025-04-02 22:58:46 [info] refresh page data from created listeners 0 519   
2025-04-02 22:58:49 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-12-22-03.png  [object Object] 
2025-04-02 22:58:49 [info] refresh page data from created listeners 0 520   
2025-04-02 22:58:49 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-12-22-18-10.png  [object Object] 
2025-04-02 22:58:49 [info] refresh page data from created listeners 0 521   
2025-04-02 22:58:50 [info] indexing created file 日记库/day/学习/2025-03-11.md  [object Object] 
2025-04-02 22:58:50 [info] indexing created ignore file 日记库/day/学习/2025-03-11.md   
2025-04-02 22:58:50 [info] trigger 日记库/day/学习/2025-03-11.md resolve  [object Object] 
2025-04-02 22:58:50 [info] index finished after resolve  [object Object] 
2025-04-02 22:58:50 [info] refresh page data from resolve listeners 0 522   
2025-04-02 22:58:50 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-13-09-30-24.png  [object Object] 
2025-04-02 22:58:50 [info] refresh page data from created listeners 0 523   
2025-04-02 22:58:52 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-13-09-29-43.png  [object Object] 
2025-04-02 22:58:52 [info] refresh page data from created listeners 0 524   
2025-04-02 22:58:54 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-13-09-33-47.png  [object Object] 
2025-04-02 22:58:54 [info] refresh page data from created listeners 0 525   
2025-04-02 22:59:01 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-13-09-37-35.png  [object Object] 
2025-04-02 22:59:01 [info] refresh page data from created listeners 0 526   
2025-04-02 22:59:01 [info] indexing created file 学习库/GIT食用指南/使用经验.md  [object Object] 
2025-04-02 22:59:01 [info] indexing created ignore file 学习库/GIT食用指南/使用经验.md   
2025-04-02 22:59:01 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-04-02 22:59:01 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:01 [info] refresh page data from resolve listeners 0 527   
2025-04-02 22:59:14 [info] trigger 学习库/ROS/Attachments/Pasted image 20240805163511.png resolve  [object Object] 
2025-04-02 22:59:14 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:14 [info] refresh page data from modify listeners 0 527   
2025-04-02 22:59:16 [info] indexing created file 学习库/ROS/Attachments/ros入门-2025-03-13-11-22-39.png  [object Object] 
2025-04-02 22:59:16 [info] refresh page data from created listeners 0 528   
2025-04-02 22:59:17 [info] indexing created file 学习库/ROS/Attachments/ros入门-2025-03-13-11-24-05.png  [object Object] 
2025-04-02 22:59:17 [info] refresh page data from created listeners 0 529   
2025-04-02 22:59:23 [info] indexing created file 学习库/ROS/Attachments/ros入门-2025-03-13-11-25-32.png  [object Object] 
2025-04-02 22:59:23 [info] refresh page data from created listeners 0 530   
2025-04-02 22:59:24 [info] indexing created file Area 导航.md  [object Object] 
2025-04-02 22:59:24 [info] indexing created ignore file Area 导航.md   
2025-04-02 22:59:24 [info] trigger Area 导航.md resolve  [object Object] 
2025-04-02 22:59:24 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:24 [info] refresh page data from resolve listeners 0 531   
2025-04-02 22:59:30 [info] ignore file modify evnet 工作库/工作库.components   
2025-04-02 22:59:31 [info] ignore file modify evnet Home/components/view/快速导航.md   
2025-04-02 22:59:31 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-04-02 22:59:31 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:31 [info] refresh page data from resolve listeners 0 531   
2025-04-02 22:59:31 [info] indexing created file Home/components/view/时钟.components  [object Object] 
2025-04-02 22:59:31 [info] refresh page data from created listeners 0 532   
2025-04-02 22:59:32 [info] ignore file modify evnet 学习库/python笔记/变量/变量.md   
2025-04-02 22:59:32 [info] trigger 学习库/python笔记/变量/变量.md resolve  [object Object] 
2025-04-02 22:59:32 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:32 [info] refresh page data from resolve listeners 0 532   
2025-04-02 22:59:35 [info] ignore file modify evnet 学习库/python笔记/变量/变量 Excalidraw.md   
2025-04-02 22:59:35 [info] trigger 学习库/python笔记/变量/变量 Excalidraw.md resolve  [object Object] 
2025-04-02 22:59:35 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:35 [info] refresh page data from resolve listeners 0 532   
2025-04-02 22:59:36 [info] ignore file modify evnet 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.24.excalidraw.md   
2025-04-02 22:59:36 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.24.excalidraw.md resolve  [object Object] 
2025-04-02 22:59:36 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:36 [info] refresh page data from resolve listeners 0 532   
2025-04-02 22:59:37 [info] ignore file modify evnet 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.50.excalidraw.md   
2025-04-02 22:59:37 [info] refresh page data from delete listeners 0 531   
2025-04-02 22:59:37 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.50.excalidraw.md resolve  [object Object] 
2025-04-02 22:59:37 [info] index finished after resolve  [object Object] 
2025-04-02 22:59:37 [info] refresh page data from resolve listeners 0 531   
2025-04-02 22:59:37 [info] refresh page data from delete listeners 0 530   
2025-04-02 22:59:37 [info] refresh page data from delete listeners 0 529   
2025-04-02 22:59:37 [info] trigger 学习库/python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-04-02 22:59:37 [info] trigger 学习库/python笔记/异常，模块，包/模块.md resolve  [object Object] 
2025-04-02 23:02:44 [info] components database created cost 11 ms   
2025-04-02 23:02:44 [info] components index initializing...   
2025-04-02 23:02:45 [info] start to batch put pages: 6   
2025-04-02 23:02:45 [info] batch persist cost 6  4 
2025-04-02 23:02:45 [info] components index initialized, 529 files cost 684 ms   
2025-04-02 23:02:45 [info] refresh page data from init listeners 0 529   
2025-04-02 23:02:56 [info] refresh page data from delete listeners 0 528   
2025-04-02 23:02:56 [info] refresh page data from delete listeners 0 527   
2025-04-02 23:02:56 [info] refresh page data from delete listeners 0 526   
2025-04-02 23:02:56 [info] refresh page data from delete listeners 0 525   
2025-04-02 23:02:57 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227160400.png  [object Object] 
2025-04-02 23:02:57 [info] refresh page data from created listeners 0 526   
2025-04-02 23:02:57 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227160140.png  [object Object] 
2025-04-02 23:02:57 [info] refresh page data from created listeners 0 527   
2025-04-02 23:02:58 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250228152417.png  [object Object] 
2025-04-02 23:02:58 [info] refresh page data from created listeners 0 528   
2025-04-02 23:03:02 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227165524.png  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from created listeners 0 529   
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 528   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 527   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 526   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 525   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 524   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 523   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 522   
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 521   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 520   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 519   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 518   
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 517   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 516   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 515   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 514   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:02 [info] refresh page data from delete listeners 0 513   
2025-04-02 23:03:02 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:03 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706101124.png  [object Object] 
2025-04-02 23:03:03 [info] refresh page data from created listeners 0 514   
2025-04-02 23:03:03 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:03 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706095850.png  [object Object] 
2025-04-02 23:03:03 [info] refresh page data from created listeners 0 515   
2025-04-02 23:03:03 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:04 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706151929.png  [object Object] 
2025-04-02 23:03:04 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:04 [info] refresh page data from created listeners 0 516   
2025-04-02 23:03:05 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008151321.png  [object Object] 
2025-04-02 23:03:05 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:05 [info] refresh page data from created listeners 0 517   
2025-04-02 23:03:05 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008152233.png  [object Object] 
2025-04-02 23:03:05 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:05 [info] refresh page data from created listeners 0 518   
2025-04-02 23:03:06 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008150458.png  [object Object] 
2025-04-02 23:03:06 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:06 [info] refresh page data from created listeners 0 519   
2025-04-02 23:03:06 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240605152225.jpg  [object Object] 
2025-04-02 23:03:06 [info] refresh page data from created listeners 0 520   
2025-04-02 23:03:07 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008095639.png  [object Object] 
2025-04-02 23:03:07 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:07 [info] refresh page data from created listeners 0 521   
2025-04-02 23:03:09 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008153216.png  [object Object] 
2025-04-02 23:03:09 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:09 [info] refresh page data from created listeners 0 522   
2025-04-02 23:03:09 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008110538.png  [object Object] 
2025-04-02 23:03:09 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:09 [info] refresh page data from created listeners 0 523   
2025-04-02 23:03:10 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008145042.png  [object Object] 
2025-04-02 23:03:10 [info] refresh page data from created listeners 0 524   
2025-04-02 23:03:11 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008100523.png  [object Object] 
2025-04-02 23:03:11 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:11 [info] refresh page data from created listeners 0 525   
2025-04-02 23:03:12 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706093227.png  [object Object] 
2025-04-02 23:03:12 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:12 [info] refresh page data from created listeners 0 526   
2025-04-02 23:03:16 [info] indexing created file 学习库/Deep learning/attachments/GIF 2024-10-8 14-54-29.gif  [object Object] 
2025-04-02 23:03:16 [info] refresh page data from created listeners 0 527   
2025-04-02 23:03:16 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:21 [info] indexing created file 学习库/Deep learning/attachments/GIF 2024-10-8 10-21-33.gif  [object Object] 
2025-04-02 23:03:21 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-04-02 23:03:21 [info] refresh page data from created listeners 0 528   
2025-04-02 23:03:25 [info] indexing created file 学习库/Deep learning/attachments/GIF 2024-10-8 11-09-20.gif  [object Object] 
2025-04-02 23:03:25 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-04-02 23:03:25 [info] refresh page data from created listeners 0 529   
2025-04-02 23:03:26 [info] indexing created file 学习库/Deep learning/训练实践/attachments/一些基础-2025-03-14-15-30-11.png  [object Object] 
2025-04-02 23:03:26 [info] refresh page data from created listeners 0 530   
2025-04-02 23:03:26 [info] indexing created file 学习库/Deep learning/训练实践/一些基础.md  [object Object] 
2025-04-02 23:03:26 [info] indexing created ignore file 学习库/Deep learning/训练实践/一些基础.md   
2025-04-02 23:03:26 [info] trigger 学习库/Deep learning/训练实践/一些基础.md resolve  [object Object] 
2025-04-02 23:03:26 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:26 [info] refresh page data from resolve listeners 0 531   
2025-04-02 23:03:27 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-04-02 23:03:27 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-04-02 23:03:27 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:27 [info] refresh page data from resolve listeners 0 531   
2025-04-02 23:03:27 [info] indexing created file Home/attachments/426.png  [object Object] 
2025-04-02 23:03:27 [info] refresh page data from created listeners 0 532   
2025-04-02 23:03:28 [info] indexing created file 日记库/fleeting_notes/attachments/427.png  [object Object] 
2025-04-02 23:03:28 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-04-02 23:03:28 [info] refresh page data from created listeners 0 533   
2025-04-02 23:03:29 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/语义分割-2025-03-16-08-42-15.png  [object Object] 
2025-04-02 23:03:29 [info] refresh page data from created listeners 0 534   
2025-04-02 23:03:29 [info] ignore file modify evnet 学习库/Deep learning/概念库/语义分割/语义分割.md   
2025-04-02 23:03:29 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-04-02 23:03:29 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:29 [info] refresh page data from resolve listeners 0 534   
2025-04-02 23:03:30 [info] ignore file modify evnet 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md   
2025-04-02 23:03:30 [info] trigger 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md resolve  [object Object] 
2025-04-02 23:03:30 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:30 [info] refresh page data from resolve listeners 0 534   
2025-04-02 23:03:31 [info] indexing created file 学习库/Anki/二级/attachments/选择题-2025-03-16-16-07-52.png  [object Object] 
2025-04-02 23:03:31 [info] refresh page data from created listeners 0 535   
2025-04-02 23:03:32 [info] indexing created file 学习库/Anki/python/attachments/数据容器-2025-03-16-16-46-29.png  [object Object] 
2025-04-02 23:03:32 [info] refresh page data from created listeners 0 536   
2025-04-02 23:03:32 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-19-57-38.png  [object Object] 
2025-04-02 23:03:32 [info] refresh page data from created listeners 0 537   
2025-04-02 23:03:33 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-03-27.png  [object Object] 
2025-04-02 23:03:33 [info] refresh page data from created listeners 0 538   
2025-04-02 23:03:33 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-10-17.png  [object Object] 
2025-04-02 23:03:33 [info] refresh page data from created listeners 0 539   
2025-04-02 23:03:34 [info] indexing created file 日记库/day/attachments/2025-03-13-2025-03-16-20-11-35.png  [object Object] 
2025-04-02 23:03:34 [info] refresh page data from created listeners 0 540   
2025-04-02 23:03:37 [info] indexing created file 日记库/day/2025-03-15.md  [object Object] 
2025-04-02 23:03:37 [info] indexing created ignore file 日记库/day/2025-03-15.md   
2025-04-02 23:03:37 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-04-02 23:03:37 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:37 [info] refresh page data from resolve listeners 0 541   
2025-04-02 23:03:38 [info] refresh page data from delete listeners 0 540   
2025-04-02 23:03:38 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-44-07.png  [object Object] 
2025-04-02 23:03:38 [info] refresh page data from created listeners 0 541   
2025-04-02 23:03:39 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-21-07-35.png  [object Object] 
2025-04-02 23:03:39 [info] refresh page data from created listeners 0 542   
2025-04-02 23:03:40 [info] indexing created file 日记库/day/attachments/1207929781.png  [object Object] 
2025-04-02 23:03:40 [info] refresh page data from created listeners 0 543   
2025-04-02 23:03:40 [info] indexing created file 日记库/day/2025-03-12.md  [object Object] 
2025-04-02 23:03:40 [info] indexing created ignore file 日记库/day/2025-03-12.md   
2025-04-02 23:03:40 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-04-02 23:03:40 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:40 [info] refresh page data from resolve listeners 0 544   
2025-04-02 23:03:41 [info] indexing created file 日记库/day/attachments/S50316-23514890_com.xingin.xhs.png  [object Object] 
2025-04-02 23:03:41 [info] refresh page data from created listeners 0 545   
2025-04-02 23:03:42 [info] indexing created file 日记库/day/attachments/S50316-23561981_com.xingin.xhs.png  [object Object] 
2025-04-02 23:03:42 [info] refresh page data from created listeners 0 546   
2025-04-02 23:03:42 [info] indexing created file 日记库/day/attachments/S50316-23582502_com.xingin.xhs.png  [object Object] 
2025-04-02 23:03:42 [info] refresh page data from created listeners 0 547   
2025-04-02 23:03:43 [info] indexing created file 学习库/linux/Attachments/Pasted image 20250317012600.jpg  [object Object] 
2025-04-02 23:03:43 [info] refresh page data from created listeners 0 548   
2025-04-02 23:03:43 [info] ignore file modify evnet 学习库/学习库.components   
2025-04-02 23:03:44 [info] indexing created file 日记库/读博.md  [object Object] 
2025-04-02 23:03:44 [info] indexing created ignore file 日记库/读博.md   
2025-04-02 23:03:44 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-04-02 23:03:44 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:44 [info] refresh page data from resolve listeners 0 549   
2025-04-02 23:03:45 [info] indexing created file 日记库/教师编.md  [object Object] 
2025-04-02 23:03:45 [info] indexing created ignore file 日记库/教师编.md   
2025-04-02 23:03:45 [info] trigger 日记库/教师编.md resolve  [object Object] 
2025-04-02 23:03:45 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:45 [info] refresh page data from resolve listeners 0 550   
2025-04-02 23:03:45 [info] indexing created file 日记库/day/2025-03-16.md  [object Object] 
2025-04-02 23:03:45 [info] indexing created ignore file 日记库/day/2025-03-16.md   
2025-04-02 23:03:45 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-04-02 23:03:45 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:45 [info] refresh page data from resolve listeners 0 551   
2025-04-02 23:03:46 [info] ignore file modify evnet 日记库/日记库.components   
2025-04-02 23:03:46 [info] indexing created file 日记库/day/attachments/Pasted image 20250318001638.png  [object Object] 
2025-04-02 23:03:46 [info] refresh page data from created listeners 0 552   
2025-04-02 23:03:47 [info] indexing created file 日记库/day/2025-03-18.md  [object Object] 
2025-04-02 23:03:47 [info] indexing created ignore file 日记库/day/2025-03-18.md   
2025-04-02 23:03:47 [info] trigger 日记库/day/2025-03-18.md resolve  [object Object] 
2025-04-02 23:03:47 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:47 [info] refresh page data from resolve listeners 0 553   
2025-04-02 23:03:48 [info] indexing created file 学习库/c/attachments/从这开始吧-2025-03-18-09-45-15.png  [object Object] 
2025-04-02 23:03:48 [info] refresh page data from created listeners 0 554   
2025-04-02 23:03:48 [info] indexing created file 学习库/二级/attachments/公共基础-2025-03-18-14-21-31.png  [object Object] 
2025-04-02 23:03:48 [info] refresh page data from created listeners 0 555   
2025-04-02 23:03:49 [info] indexing created file 学习库/二级/attachments/公共基础-2025-03-18-14-32-33.png  [object Object] 
2025-04-02 23:03:49 [info] refresh page data from created listeners 0 556   
2025-04-02 23:03:49 [info] indexing created file 学习库/二级/计算机的系统构成.md  [object Object] 
2025-04-02 23:03:49 [info] indexing created ignore file 学习库/二级/计算机的系统构成.md   
2025-04-02 23:03:49 [info] trigger 学习库/二级/计算机的系统构成.md resolve  [object Object] 
2025-04-02 23:03:49 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:49 [info] refresh page data from resolve listeners 0 557   
2025-04-02 23:03:50 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-14-53-53.png  [object Object] 
2025-04-02 23:03:50 [info] refresh page data from created listeners 0 558   
2025-04-02 23:03:50 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-14-57-40.png  [object Object] 
2025-04-02 23:03:50 [info] refresh page data from created listeners 0 559   
2025-04-02 23:03:52 [info] indexing created file 学习库/二级/Drawing 2025-03-18 15.06.26.excalidraw.md  [object Object] 
2025-04-02 23:03:52 [info] indexing created ignore file 学习库/二级/Drawing 2025-03-18 15.06.26.excalidraw.md   
2025-04-02 23:03:52 [info] trigger 学习库/二级/Drawing 2025-03-18 15.06.26.excalidraw.md resolve  [object Object] 
2025-04-02 23:03:52 [info] index finished after resolve  [object Object] 
2025-04-02 23:03:52 [info] refresh page data from resolve listeners 0 560   
2025-04-02 23:03:52 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-15-29-49.png  [object Object] 
2025-04-02 23:03:52 [info] refresh page data from created listeners 0 561   
2025-04-02 23:03:53 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-15-45-47.gif  [object Object] 
2025-04-02 23:03:53 [info] refresh page data from created listeners 0 562   
2025-04-02 23:03:53 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-15-48-24.gif  [object Object] 
2025-04-02 23:03:53 [info] refresh page data from created listeners 0 563   
2025-04-02 23:03:55 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-15-54-23.gif  [object Object] 
2025-04-02 23:03:55 [info] refresh page data from created listeners 0 564   
2025-04-02 23:04:03 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-15-54-30.gif  [object Object] 
2025-04-02 23:04:03 [info] refresh page data from created listeners 0 565   
2025-04-02 23:04:04 [info] indexing created file 学习库/二级/Drawing 2025-03-18 15.23.48.excalidraw.md  [object Object] 
2025-04-02 23:04:04 [info] indexing created ignore file 学习库/二级/Drawing 2025-03-18 15.23.48.excalidraw.md   
2025-04-02 23:04:04 [info] trigger 学习库/二级/Drawing 2025-03-18 15.23.48.excalidraw.md resolve  [object Object] 
2025-04-02 23:04:04 [info] index finished after resolve  [object Object] 
2025-04-02 23:04:04 [info] refresh page data from resolve listeners 0 566   
2025-04-02 23:04:06 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-16-06-34.png  [object Object] 
2025-04-02 23:04:06 [info] refresh page data from created listeners 0 567   
2025-04-02 23:04:11 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-16-25-26.png  [object Object] 
2025-04-02 23:04:11 [info] refresh page data from created listeners 0 568   
2025-04-02 23:04:13 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-16-39-53.png  [object Object] 
2025-04-02 23:04:13 [info] refresh page data from created listeners 0 569   
2025-04-02 23:04:14 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-16-58-37.png  [object Object] 
2025-04-02 23:04:14 [info] refresh page data from created listeners 0 570   
2025-04-02 23:04:16 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-17-03-04.png  [object Object] 
2025-04-02 23:04:16 [info] refresh page data from created listeners 0 571   
2025-04-02 23:04:19 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-17-04-25.png  [object Object] 
2025-04-02 23:04:19 [info] refresh page data from created listeners 0 572   
2025-04-02 23:04:24 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-19-35-15.gif  [object Object] 
2025-04-02 23:04:24 [info] refresh page data from created listeners 0 573   
2025-04-02 23:04:25 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-19-50-08.png  [object Object] 
2025-04-02 23:04:25 [info] refresh page data from created listeners 0 574   
2025-04-02 23:04:26 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-19-53-50.png  [object Object] 
2025-04-02 23:04:26 [info] refresh page data from created listeners 0 575   
2025-04-02 23:04:27 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-19-56-56.png  [object Object] 
2025-04-02 23:04:27 [info] refresh page data from created listeners 0 576   
2025-04-02 23:04:28 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-02-12.png  [object Object] 
2025-04-02 23:04:28 [info] refresh page data from created listeners 0 577   
2025-04-02 23:04:29 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-04-55.png  [object Object] 
2025-04-02 23:04:29 [info] refresh page data from created listeners 0 578   
2025-04-02 23:04:32 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-18-34.png  [object Object] 
2025-04-02 23:04:32 [info] refresh page data from created listeners 0 579   
2025-04-02 23:04:35 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-20-27.png  [object Object] 
2025-04-02 23:04:35 [info] refresh page data from created listeners 0 580   
2025-04-02 23:04:36 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-22-26.png  [object Object] 
2025-04-02 23:04:36 [info] refresh page data from created listeners 0 581   
2025-04-02 23:04:37 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-29-39.png  [object Object] 
2025-04-02 23:04:37 [info] refresh page data from created listeners 0 582   
2025-04-02 23:04:38 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-33-36.png  [object Object] 
2025-04-02 23:04:38 [info] refresh page data from created listeners 0 583   
2025-04-02 23:04:39 [info] indexing created file 学习库/二级/attachments/Pasted Image 20250318203512_650.png  [object Object] 
2025-04-02 23:04:39 [info] refresh page data from created listeners 0 584   
2025-04-02 23:04:44 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-35-17.png  [object Object] 
2025-04-02 23:04:44 [info] refresh page data from created listeners 0 585   
2025-04-02 23:04:46 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-42-35.png  [object Object] 
2025-04-02 23:04:46 [info] refresh page data from created listeners 0 586   
2025-04-02 23:04:47 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-45-39.png  [object Object] 
2025-04-02 23:04:47 [info] refresh page data from created listeners 0 587   
2025-04-02 23:04:48 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-20-46-43.png  [object Object] 
2025-04-02 23:04:48 [info] refresh page data from created listeners 0 588   
2025-04-02 23:04:50 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-05-15.png  [object Object] 
2025-04-02 23:04:50 [info] refresh page data from created listeners 0 589   
2025-04-02 23:04:51 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-09-22.png  [object Object] 
2025-04-02 23:04:51 [info] refresh page data from created listeners 0 590   
2025-04-02 23:04:53 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-18-06.png  [object Object] 
2025-04-02 23:04:53 [info] refresh page data from created listeners 0 591   
2025-04-02 23:04:54 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-24-58.png  [object Object] 
2025-04-02 23:04:54 [info] refresh page data from created listeners 0 592   
2025-04-02 23:04:55 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-26-48.png  [object Object] 
2025-04-02 23:04:55 [info] refresh page data from created listeners 0 593   
2025-04-02 23:04:56 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-28-11.png  [object Object] 
2025-04-02 23:04:56 [info] refresh page data from created listeners 0 594   
2025-04-02 23:04:57 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-35-23.png  [object Object] 
2025-04-02 23:04:57 [info] refresh page data from created listeners 0 595   
2025-04-02 23:04:59 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-18-21-36-02.png  [object Object] 
2025-04-02 23:04:59 [info] refresh page data from created listeners 0 596   
2025-04-02 23:05:00 [info] indexing created file 学习库/二级/Drawing 2025-03-18 16.01.06.excalidraw.md  [object Object] 
2025-04-02 23:05:00 [info] indexing created ignore file 学习库/二级/Drawing 2025-03-18 16.01.06.excalidraw.md   
2025-04-02 23:05:00 [info] trigger 学习库/二级/Drawing 2025-03-18 16.01.06.excalidraw.md resolve  [object Object] 
2025-04-02 23:05:00 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:00 [info] refresh page data from resolve listeners 0 597   
2025-04-02 23:05:01 [info] indexing created file 学习库/二级/attachments/数据库-2025-03-19-14-33-19.png  [object Object] 
2025-04-02 23:05:01 [info] refresh page data from created listeners 0 598   
2025-04-02 23:05:02 [info] indexing created file 学习库/二级/attachments/数据库-2025-03-19-14-38-20.png  [object Object] 
2025-04-02 23:05:02 [info] refresh page data from created listeners 0 599   
2025-04-02 23:05:03 [info] indexing created file 学习库/二级/attachments/数据库-2025-03-19-14-49-19.png  [object Object] 
2025-04-02 23:05:03 [info] refresh page data from created listeners 0 600   
2025-04-02 23:05:04 [info] indexing created file 学习库/二级/attachments/数据库-2025-03-19-14-52-12.png  [object Object] 
2025-04-02 23:05:04 [info] refresh page data from created listeners 0 601   
2025-04-02 23:05:05 [info] indexing created file 学习库/二级/attachments/公共基础-2025-03-19-15-13-17.png  [object Object] 
2025-04-02 23:05:05 [info] refresh page data from created listeners 0 602   
2025-04-02 23:05:06 [info] indexing created file 学习库/二级/attachments/公共基础-2025-03-19-15-21-10.png  [object Object] 
2025-04-02 23:05:06 [info] refresh page data from created listeners 0 603   
2025-04-02 23:05:06 [info] indexing created file 学习库/二级/attachments/数据库-2025-03-19-15-31-04.png  [object Object] 
2025-04-02 23:05:06 [info] refresh page data from created listeners 0 604   
2025-04-02 23:05:08 [info] indexing created file 学习库/二级/attachments/数据结构-2025-03-19-15-39-04.gif  [object Object] 
2025-04-02 23:05:08 [info] refresh page data from created listeners 0 605   
2025-04-02 23:05:09 [info] ignore file modify evnet 学习库/python笔记/变量/运算符.md   
2025-04-02 23:05:09 [info] trigger 学习库/python笔记/变量/运算符.md resolve  [object Object] 
2025-04-02 23:05:09 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:09 [info] refresh page data from resolve listeners 0 605   
2025-04-02 23:05:10 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-19-16-36-42.png  [object Object] 
2025-04-02 23:05:10 [info] refresh page data from created listeners 0 606   
2025-04-02 23:05:10 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-19-16-38-12.png  [object Object] 
2025-04-02 23:05:10 [info] refresh page data from created listeners 0 607   
2025-04-02 23:05:11 [info] indexing created file 日记库/day/2025-03-19.md  [object Object] 
2025-04-02 23:05:11 [info] indexing created ignore file 日记库/day/2025-03-19.md   
2025-04-02 23:05:11 [info] trigger 日记库/day/2025-03-19.md resolve  [object Object] 
2025-04-02 23:05:11 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:11 [info] refresh page data from resolve listeners 0 608   
2025-04-02 23:05:11 [info] indexing created file 日记库/工作.md  [object Object] 
2025-04-02 23:05:11 [info] indexing created ignore file 日记库/工作.md   
2025-04-02 23:05:11 [info] trigger 日记库/工作.md resolve  [object Object] 
2025-04-02 23:05:11 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:11 [info] refresh page data from resolve listeners 0 609   
2025-04-02 23:05:12 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-04-02 23:05:12 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-04-02 23:05:12 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:12 [info] refresh page data from resolve listeners 0 609   
2025-04-02 23:05:13 [info] indexing created file components/logs/2025-03-20.components.log  [object Object] 
2025-04-02 23:05:13 [info] refresh page data from created listeners 0 610   
2025-04-02 23:05:14 [info] ignore file modify evnet 学习库/Anki/python/变量.md   
2025-04-02 23:05:14 [info] trigger 学习库/Anki/python/变量.md resolve  [object Object] 
2025-04-02 23:05:14 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:14 [info] refresh page data from resolve listeners 0 610   
2025-04-02 23:05:16 [info] ignore file modify evnet 学习库/python笔记/数据容器/数据容器.md   
2025-04-02 23:05:16 [info] trigger 学习库/python笔记/数据容器/数据容器.md resolve  [object Object] 
2025-04-02 23:05:16 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:16 [info] refresh page data from resolve listeners 0 610   
2025-04-02 23:05:18 [info] ignore file modify evnet 学习库/python笔记/数据容器/通用操作.md   
2025-04-02 23:05:18 [info] trigger 学习库/python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-04-02 23:05:18 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:18 [info] refresh page data from resolve listeners 0 610   
2025-04-02 23:05:19 [info] indexing created file 学习库/Anki/python/attachments/函数-2025-03-21-14-39-19.png  [object Object] 
2025-04-02 23:05:19 [info] refresh page data from created listeners 0 611   
2025-04-02 23:05:20 [info] indexing created file 学习库/Anki/python/attachments/函数-2025-03-21-14-40-33.png  [object Object] 
2025-04-02 23:05:20 [info] refresh page data from created listeners 0 612   
2025-04-02 23:05:20 [info] indexing created file components/logs/2025-03-13.components.log  [object Object] 
2025-04-02 23:05:20 [info] refresh page data from created listeners 0 613   
2025-04-02 23:05:21 [info] indexing created file components/logs/2025-03-17.components.log  [object Object] 
2025-04-02 23:05:21 [info] refresh page data from created listeners 0 614   
2025-04-02 23:05:21 [info] indexing created file components/logs/2025-03-18.components.log  [object Object] 
2025-04-02 23:05:21 [info] refresh page data from created listeners 0 615   
2025-04-02 23:05:22 [info] indexing created file components/logs/2025-03-15.components.log  [object Object] 
2025-04-02 23:05:22 [info] refresh page data from created listeners 0 616   
2025-04-02 23:05:22 [info] indexing created file components/logs/2025-03-19.components.log  [object Object] 
2025-04-02 23:05:22 [info] refresh page data from created listeners 0 617   
2025-04-02 23:05:23 [info] indexing created file components/logs/2025-03-12.components.log  [object Object] 
2025-04-02 23:05:23 [info] refresh page data from created listeners 0 618   
2025-04-02 23:05:24 [info] indexing created file components/logs/2025-03-14.components.log  [object Object] 
2025-04-02 23:05:24 [info] refresh page data from created listeners 0 619   
2025-04-02 23:05:24 [info] indexing created file components/logs/2025-03-16.components.log  [object Object] 
2025-04-02 23:05:24 [info] refresh page data from created listeners 0 620   
2025-04-02 23:05:27 [info] indexing created file 学习库/Anki/python/attachments/文件操作-2025-03-12.png  [object Object] 
2025-04-02 23:05:27 [info] refresh page data from created listeners 0 621   
2025-04-02 23:05:28 [info] ignore file modify evnet 学习库/python笔记/文件操作/文件操作.md   
2025-04-02 23:05:28 [info] trigger 学习库/python笔记/文件操作/文件操作.md resolve  [object Object] 
2025-04-02 23:05:28 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:28 [info] refresh page data from resolve listeners 0 621   
2025-04-02 23:05:31 [info] refresh page data from delete listeners 0 620   
2025-04-02 23:05:31 [info] refresh page data from delete listeners 0 619   
2025-04-02 23:05:31 [info] refresh page data from delete listeners 0 618   
2025-04-02 23:05:31 [info] indexing created file 学习库/leetcode/attachments/Pasted image 20240710213400.png  [object Object] 
2025-04-02 23:05:31 [info] refresh page data from created listeners 0 619   
2025-04-02 23:05:32 [info] indexing created file 学习库/leetcode/49. 字母异维词分组.md  [object Object] 
2025-04-02 23:05:32 [info] indexing created ignore file 学习库/leetcode/49. 字母异维词分组.md   
2025-04-02 23:05:32 [info] trigger 学习库/leetcode/49. 字母异维词分组.md resolve  [object Object] 
2025-04-02 23:05:32 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:32 [info] refresh page data from resolve listeners 0 620   
2025-04-02 23:05:32 [info] indexing created file 学习库/leetcode/1. 两数之和.md  [object Object] 
2025-04-02 23:05:32 [info] indexing created ignore file 学习库/leetcode/1. 两数之和.md   
2025-04-02 23:05:32 [info] trigger 学习库/leetcode/1. 两数之和.md resolve  [object Object] 
2025-04-02 23:05:32 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:32 [info] refresh page data from resolve listeners 0 621   
2025-04-02 23:05:33 [info] indexing created file 学习库/leetcode/2. 两数相加.md  [object Object] 
2025-04-02 23:05:33 [info] indexing created ignore file 学习库/leetcode/2. 两数相加.md   
2025-04-02 23:05:33 [info] trigger 学习库/leetcode/2. 两数相加.md resolve  [object Object] 
2025-04-02 23:05:33 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:33 [info] refresh page data from resolve listeners 0 622   
2025-04-02 23:05:34 [info] indexing created file components/logs/2025-03-21.components.log  [object Object] 
2025-04-02 23:05:34 [info] refresh page data from created listeners 0 623   
2025-04-02 23:05:36 [info] ignore file modify evnet 学习库/python笔记/数据容器/列表.md   
2025-04-02 23:05:36 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-04-02 23:05:36 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:36 [info] refresh page data from resolve listeners 0 623   
2025-04-02 23:05:37 [info] ignore file modify evnet 学习库/python笔记/python函数/函数进阶.md   
2025-04-02 23:05:37 [info] trigger 学习库/python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-04-02 23:05:37 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:37 [info] refresh page data from resolve listeners 0 623   
2025-04-02 23:05:57 [info] trigger 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009201823.png resolve  [object Object] 
2025-04-02 23:05:57 [info] index finished after resolve  [object Object] 
2025-04-02 23:05:57 [info] refresh page data from modify listeners 0 623   
2025-04-02 23:06:00 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-02 23:06:00 [info] ignore file modify evnet Home/Home.md   
2025-04-02 23:06:00 [info] trigger Home/Home.md resolve  [object Object] 
2025-04-02 23:06:00 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:00 [info] refresh page data from resolve listeners 0 623   
2025-04-02 23:06:01 [info] ignore file modify evnet Home/components/view/weather.md   
2025-04-02 23:06:01 [info] trigger Home/components/view/weather.md resolve  [object Object] 
2025-04-02 23:06:01 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:01 [info] refresh page data from resolve listeners 0 623   
2025-04-02 23:06:02 [info] ignore file modify evnet 学习库/python笔记/面向对象/封装，继承，和多态.md   
2025-04-02 23:06:02 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-04-02 23:06:02 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:02 [info] refresh page data from resolve listeners 0 623   
2025-04-02 23:06:04 [info] indexing created file 学习库/GIT食用指南/常用指令.md  [object Object] 
2025-04-02 23:06:04 [info] indexing created ignore file 学习库/GIT食用指南/常用指令.md   
2025-04-02 23:06:04 [info] trigger 学习库/GIT食用指南/常用指令.md resolve  [object Object] 
2025-04-02 23:06:04 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:04 [info] refresh page data from resolve listeners 0 624   
2025-04-02 23:06:05 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-22-22-31-03.png  [object Object] 
2025-04-02 23:06:05 [info] refresh page data from created listeners 0 625   
2025-04-02 23:06:05 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-22-22-32-21.png  [object Object] 
2025-04-02 23:06:05 [info] refresh page data from created listeners 0 626   
2025-04-02 23:06:06 [info] indexing created file 学习库/GIT食用指南/GIT 工作流.md  [object Object] 
2025-04-02 23:06:06 [info] indexing created ignore file 学习库/GIT食用指南/GIT 工作流.md   
2025-04-02 23:06:06 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-04-02 23:06:06 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:06 [info] refresh page data from resolve listeners 0 627   
2025-04-02 23:06:06 [info] indexing created file 学习库/嵌入式秋招经验贴.md  [object Object] 
2025-04-02 23:06:06 [info] indexing created ignore file 学习库/嵌入式秋招经验贴.md   
2025-04-02 23:06:06 [info] trigger 学习库/嵌入式秋招经验贴.md resolve  [object Object] 
2025-04-02 23:06:06 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:06 [info] refresh page data from resolve listeners 0 628   
2025-04-02 23:06:08 [info] indexing created file components/logs/2025-03-22.components.log  [object Object] 
2025-04-02 23:06:08 [info] refresh page data from created listeners 0 629   
2025-04-02 23:06:09 [info] indexing created file 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md  [object Object] 
2025-04-02 23:06:09 [info] indexing created ignore file 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-04-02 23:06:09 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-04-02 23:06:09 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:09 [info] refresh page data from resolve listeners 0 630   
2025-04-02 23:06:10 [info] indexing created file 学习库/c/attachments/运算符-2025-03-23-10-19-25.png  [object Object] 
2025-04-02 23:06:10 [info] refresh page data from created listeners 0 631   
2025-04-02 23:06:14 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-25-00.png  [object Object] 
2025-04-02 23:06:14 [info] refresh page data from created listeners 0 632   
2025-04-02 23:06:17 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-36-24.png  [object Object] 
2025-04-02 23:06:17 [info] refresh page data from created listeners 0 633   
2025-04-02 23:06:21 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-41-28.png  [object Object] 
2025-04-02 23:06:21 [info] refresh page data from created listeners 0 634   
2025-04-02 23:06:23 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-51-13.png  [object Object] 
2025-04-02 23:06:23 [info] refresh page data from created listeners 0 635   
2025-04-02 23:06:25 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-52-01.png  [object Object] 
2025-04-02 23:06:25 [info] refresh page data from created listeners 0 636   
2025-04-02 23:06:27 [info] indexing created file components/logs/2025-03-23.components.log  [object Object] 
2025-04-02 23:06:27 [info] refresh page data from created listeners 0 637   
2025-04-02 23:06:27 [info] indexing created file 学习库/c/1 从这开始吧.md  [object Object] 
2025-04-02 23:06:27 [info] indexing created ignore file 学习库/c/1 从这开始吧.md   
2025-04-02 23:06:27 [info] trigger 学习库/c/1 从这开始吧.md resolve  [object Object] 
2025-04-02 23:06:27 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:27 [info] refresh page data from resolve listeners 0 638   
2025-04-02 23:06:29 [info] indexing created file components/logs/2025-03-24.components.log  [object Object] 
2025-04-02 23:06:29 [info] refresh page data from created listeners 0 639   
2025-04-02 23:06:29 [info] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-04-02 23:06:29 [info] refresh page data from delete listeners 0 638   
2025-04-02 23:06:29 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-02 23:06:29 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:29 [info] refresh page data from resolve listeners 0 638   
2025-04-02 23:06:29 [info] refresh page data from delete listeners 0 637   
2025-04-02 23:06:29 [info] refresh page data from delete listeners 0 636   
2025-04-02 23:06:29 [info] refresh page data from delete listeners 0 635   
2025-04-02 23:06:29 [info] refresh page data from delete listeners 0 634   
2025-04-02 23:06:30 [info] indexing created file 学习库/二级/公共基础.md  [object Object] 
2025-04-02 23:06:30 [info] indexing created ignore file 学习库/二级/公共基础.md   
2025-04-02 23:06:30 [info] trigger 学习库/二级/公共基础.md resolve  [object Object] 
2025-04-02 23:06:30 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:30 [info] refresh page data from resolve listeners 0 635   
2025-04-02 23:06:31 [info] indexing created file 学习库/c/attachments/4 流程控制语句-2025-03-25-09-44-49.png  [object Object] 
2025-04-02 23:06:31 [info] refresh page data from created listeners 0 636   
2025-04-02 23:06:32 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-25-14-10-18.png  [object Object] 
2025-04-02 23:06:32 [info] refresh page data from created listeners 0 637   
2025-04-02 23:06:32 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-25-14-11-22.png  [object Object] 
2025-04-02 23:06:32 [info] refresh page data from created listeners 0 638   
2025-04-02 23:06:33 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-25-15-30-22.png  [object Object] 
2025-04-02 23:06:33 [info] refresh page data from created listeners 0 639   
2025-04-02 23:06:33 [info] indexing created file 学习库/Anki/二级/attachments/公共基础-2025-03-25-15-44-44.png  [object Object] 
2025-04-02 23:06:33 [info] refresh page data from created listeners 0 640   
2025-04-02 23:06:34 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-25-21-44-02.png  [object Object] 
2025-04-02 23:06:34 [info] refresh page data from created listeners 0 641   
2025-04-02 23:06:35 [info] indexing created file components/logs/2025-03-25.components.log  [object Object] 
2025-04-02 23:06:35 [info] refresh page data from created listeners 0 642   
2025-04-02 23:06:36 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-04-02 23:06:36 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-04-02 23:06:36 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:36 [info] refresh page data from resolve listeners 0 642   
2025-04-02 23:06:37 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-04-42.png  [object Object] 
2025-04-02 23:06:37 [info] refresh page data from created listeners 0 643   
2025-04-02 23:06:38 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-08-31.png  [object Object] 
2025-04-02 23:06:38 [info] refresh page data from created listeners 0 644   
2025-04-02 23:06:38 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-15-31.png  [object Object] 
2025-04-02 23:06:38 [info] refresh page data from created listeners 0 645   
2025-04-02 23:06:39 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-24-12.png  [object Object] 
2025-04-02 23:06:39 [info] refresh page data from created listeners 0 646   
2025-04-02 23:06:40 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-49-12.png  [object Object] 
2025-04-02 23:06:40 [info] refresh page data from created listeners 0 647   
2025-04-02 23:06:41 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-50-34.png  [object Object] 
2025-04-02 23:06:41 [info] refresh page data from created listeners 0 648   
2025-04-02 23:06:42 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-08-56.png  [object Object] 
2025-04-02 23:06:42 [info] refresh page data from created listeners 0 649   
2025-04-02 23:06:42 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-19-05.png  [object Object] 
2025-04-02 23:06:42 [info] refresh page data from created listeners 0 650   
2025-04-02 23:06:43 [info] indexing created file components/logs/2025-03-26.components.log  [object Object] 
2025-04-02 23:06:43 [info] refresh page data from created listeners 0 651   
2025-04-02 23:06:44 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-04-02 23:06:44 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-04-02 23:06:44 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:44 [info] refresh page data from resolve listeners 0 651   
2025-04-02 23:06:45 [info] ignore file modify evnet 学习库/Anki/python/语句.md   
2025-04-02 23:06:45 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-04-02 23:06:45 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:45 [info] refresh page data from resolve listeners 0 651   
2025-04-02 23:06:45 [info] indexing created file 日记库/day/attachments/2025-03-26-2025-03-27-08-33-46.png  [object Object] 
2025-04-02 23:06:45 [info] refresh page data from created listeners 0 652   
2025-04-02 23:06:46 [info] indexing created file 日记库/day/2025-03-26.md  [object Object] 
2025-04-02 23:06:46 [info] indexing created ignore file 日记库/day/2025-03-26.md   
2025-04-02 23:06:46 [info] trigger 日记库/day/2025-03-26.md resolve  [object Object] 
2025-04-02 23:06:46 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:46 [info] refresh page data from resolve listeners 0 653   
2025-04-02 23:06:48 [info] indexing created file 学习库/c/attachments/4 流程控制语句-2025-03-27-09-19-14.png  [object Object] 
2025-04-02 23:06:48 [info] refresh page data from created listeners 0 654   
2025-04-02 23:06:48 [info] indexing created file 学习库/c/2 核心语法.md  [object Object] 
2025-04-02 23:06:48 [info] indexing created ignore file 学习库/c/2 核心语法.md   
2025-04-02 23:06:48 [info] trigger 学习库/c/2 核心语法.md resolve  [object Object] 
2025-04-02 23:06:48 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:48 [info] refresh page data from resolve listeners 0 655   
2025-04-02 23:06:49 [info] indexing created file 学习库/c/0 一些操作.md  [object Object] 
2025-04-02 23:06:49 [info] indexing created ignore file 学习库/c/0 一些操作.md   
2025-04-02 23:06:49 [info] trigger 学习库/c/0 一些操作.md resolve  [object Object] 
2025-04-02 23:06:49 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:49 [info] refresh page data from resolve listeners 0 656   
2025-04-02 23:06:49 [info] indexing created file 学习库/c/4 流程控制语句.md  [object Object] 
2025-04-02 23:06:49 [info] indexing created ignore file 学习库/c/4 流程控制语句.md   
2025-04-02 23:06:49 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-04-02 23:06:49 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:49 [info] refresh page data from resolve listeners 0 657   
2025-04-02 23:06:50 [info] indexing created file 学习库/linux/常用Linux命令.md  [object Object] 
2025-04-02 23:06:50 [info] indexing created ignore file 学习库/linux/常用Linux命令.md   
2025-04-02 23:06:50 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-02 23:06:50 [info] index finished after resolve  [object Object] 
2025-04-02 23:06:50 [info] refresh page data from resolve listeners 0 658   
2025-04-02 23:06:51 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-27-16-44-04.png  [object Object] 
2025-04-02 23:06:51 [info] refresh page data from created listeners 0 659   
2025-04-02 23:06:52 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-20-39-47.png  [object Object] 
2025-04-02 23:06:52 [info] refresh page data from created listeners 0 660   
2025-04-02 23:06:55 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-20-48-10.png  [object Object] 
2025-04-02 23:06:55 [info] refresh page data from created listeners 0 661   
2025-04-02 23:06:57 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-18-58.png  [object Object] 
2025-04-02 23:06:57 [info] refresh page data from created listeners 0 662   
2025-04-02 23:06:58 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-25-03.png  [object Object] 
2025-04-02 23:06:58 [info] refresh page data from created listeners 0 663   
2025-04-02 23:07:00 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-29-06.png  [object Object] 
2025-04-02 23:07:00 [info] refresh page data from created listeners 0 664   
2025-04-02 23:07:01 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-27-21-34-52.png  [object Object] 
2025-04-02 23:07:01 [info] refresh page data from created listeners 0 665   
2025-04-02 23:07:02 [info] indexing created file components/logs/2025-03-27.components.log  [object Object] 
2025-04-02 23:07:02 [info] refresh page data from created listeners 0 666   
2025-04-02 23:07:02 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-02 23:07:02 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-04-02 23:07:02 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-04-02 23:07:03 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:03 [info] refresh page data from resolve listeners 0 666   
2025-04-02 23:07:03 [info] indexing created file 学习库/二级/数据库.md  [object Object] 
2025-04-02 23:07:03 [info] indexing created ignore file 学习库/二级/数据库.md   
2025-04-02 23:07:03 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-04-02 23:07:03 [info] trigger 学习库/二级/数据库.md resolve  [object Object] 
2025-04-02 23:07:03 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:03 [info] refresh page data from resolve listeners 0 667   
2025-04-02 23:07:04 [info] indexing created file 学习库/二级/数据结构.md  [object Object] 
2025-04-02 23:07:04 [info] indexing created ignore file 学习库/二级/数据结构.md   
2025-04-02 23:07:04 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-04-02 23:07:04 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:04 [info] refresh page data from resolve listeners 0 668   
2025-04-02 23:07:04 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-04-02 23:07:04 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-04-02 23:07:04 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:04 [info] refresh page data from resolve listeners 0 668   
2025-04-02 23:07:05 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-04-02 23:07:05 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-04-02 23:07:05 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:05 [info] refresh page data from resolve listeners 0 668   
2025-04-02 23:07:05 [info] indexing created file 学习库/c/5 函数.md  [object Object] 
2025-04-02 23:07:05 [info] indexing created ignore file 学习库/c/5 函数.md   
2025-04-02 23:07:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-04-02 23:07:05 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:05 [info] refresh page data from resolve listeners 0 669   
2025-04-02 23:07:06 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-04-02 23:07:06 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-04-02 23:07:06 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:06 [info] refresh page data from resolve listeners 0 669   
2025-04-02 23:07:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-04-02 23:07:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-04-02 23:07:07 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:07 [info] refresh page data from resolve listeners 0 669   
2025-04-02 23:07:09 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-28-14-46-56.png  [object Object] 
2025-04-02 23:07:09 [info] refresh page data from created listeners 0 670   
2025-04-02 23:07:09 [info] refresh page data from delete listeners 0 669   
2025-04-02 23:07:09 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-04-02 23:07:12 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-28-14-55-20.png  [object Object] 
2025-04-02 23:07:12 [info] refresh page data from created listeners 0 670   
2025-04-02 23:07:14 [info] indexing created file 学习库/linux/shell.md  [object Object] 
2025-04-02 23:07:14 [info] indexing created ignore file 学习库/linux/shell.md   
2025-04-02 23:07:14 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-04-02 23:07:14 [info] trigger 学习库/linux/shell.md resolve  [object Object] 
2025-04-02 23:07:14 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:14 [info] refresh page data from resolve listeners 0 671   
2025-04-02 23:07:15 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-28-20-50-04.png  [object Object] 
2025-04-02 23:07:15 [info] refresh page data from created listeners 0 672   
2025-04-02 23:07:15 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-28-20-50-48.png  [object Object] 
2025-04-02 23:07:15 [info] refresh page data from created listeners 0 673   
2025-04-02 23:07:16 [info] indexing created file components/logs/2025-03-28.components.log  [object Object] 
2025-04-02 23:07:16 [info] refresh page data from created listeners 0 674   
2025-04-02 23:07:17 [info] indexing created file 学习库/linux/orbslam.md  [object Object] 
2025-04-02 23:07:17 [info] indexing created ignore file 学习库/linux/orbslam.md   
2025-04-02 23:07:17 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-04-02 23:07:17 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:17 [info] refresh page data from resolve listeners 0 675   
2025-04-02 23:07:17 [info] indexing created file components/logs/2025-03-29.components.log  [object Object] 
2025-04-02 23:07:17 [info] refresh page data from created listeners 0 676   
2025-04-02 23:07:19 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-04-02 23:07:19 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-04-02 23:07:19 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:19 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:19 [info] ignore file modify evnet 学习库/python笔记/数据容器/字典.md   
2025-04-02 23:07:19 [info] trigger 学习库/python笔记/数据容器/字典.md resolve  [object Object] 
2025-04-02 23:07:19 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:19 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:19 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-04-02 23:07:19 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-04-02 23:07:19 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:19 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:20 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-04-02 23:07:20 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-04-02 23:07:20 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:20 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:20 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-04-02 23:07:20 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-04-02 23:07:20 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:20 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:21 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-04-02 23:07:21 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-04-02 23:07:21 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:21 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:21 [info] ignore file modify evnet 学习库/ROS/机器人学/机器人运动学/导论.md   
2025-04-02 23:07:21 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-04-02 23:07:21 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:21 [info] refresh page data from resolve listeners 0 676   
2025-04-02 23:07:22 [info] indexing created file 学习库/c/3 运算符.md  [object Object] 
2025-04-02 23:07:22 [info] indexing created ignore file 学习库/c/3 运算符.md   
2025-04-02 23:07:22 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-04-02 23:07:22 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:22 [info] refresh page data from resolve listeners 0 677   
2025-04-02 23:07:22 [info] indexing created file components/logs/2025-03-30.components.log  [object Object] 
2025-04-02 23:07:22 [info] refresh page data from created listeners 0 678   
2025-04-02 23:07:23 [info] indexing created file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md  [object Object] 
2025-04-02 23:07:23 [info] indexing created ignore file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-04-02 23:07:23 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-04-02 23:07:23 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:23 [info] refresh page data from resolve listeners 0 679   
2025-04-02 23:07:24 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-31-14-22-07.png  [object Object] 
2025-04-02 23:07:24 [info] refresh page data from created listeners 0 680   
2025-04-02 23:07:24 [info] indexing created file 学习库/stm32/1 启动.md  [object Object] 
2025-04-02 23:07:24 [info] indexing created ignore file 学习库/stm32/1 启动.md   
2025-04-02 23:07:24 [info] trigger 学习库/stm32/1 启动.md resolve  [object Object] 
2025-04-02 23:07:24 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:24 [info] refresh page data from resolve listeners 0 681   
2025-04-02 23:07:25 [info] indexing created file 学习库/stm32/2 GPIO.md  [object Object] 
2025-04-02 23:07:25 [info] indexing created ignore file 学习库/stm32/2 GPIO.md   
2025-04-02 23:07:25 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-04-02 23:07:25 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:25 [info] refresh page data from resolve listeners 0 682   
2025-04-02 23:07:25 [info] indexing created file 学习库/Anki/stm32/GPIO.md  [object Object] 
2025-04-02 23:07:25 [info] indexing created ignore file 学习库/Anki/stm32/GPIO.md   
2025-04-02 23:07:25 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-02 23:07:25 [info] index finished after resolve  [object Object] 
2025-04-02 23:07:25 [info] refresh page data from resolve listeners 0 683   
2025-04-02 23:07:26 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-15-26.png  [object Object] 
2025-04-02 23:07:26 [info] refresh page data from created listeners 0 684   
2025-04-02 23:07:27 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-22-21.png  [object Object] 
2025-04-02 23:07:27 [info] refresh page data from created listeners 0 685   
2025-04-02 23:07:27 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-34-09.png  [object Object] 
2025-04-02 23:07:27 [info] refresh page data from created listeners 0 686   
2025-04-02 23:07:28 [info] indexing created file components/logs/2025-03-31.components.log  [object Object] 
2025-04-02 23:07:28 [info] refresh page data from created listeners 0 687   
2025-04-02 23:07:30 [info] indexing created file 学习库/c/attachments/6 数组-2025-04-01-09-19-48.gif  [object Object] 
2025-04-02 23:07:30 [info] refresh page data from created listeners 0 688   
2025-04-02 23:08:08 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-01-15-47-55.png  [object Object] 
2025-04-02 23:08:08 [info] refresh page data from created listeners 0 689   
2025-04-02 23:08:09 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-04-01-16-19-05.png  [object Object] 
2025-04-02 23:08:09 [info] refresh page data from created listeners 0 690   
2025-04-02 23:08:10 [info] indexing created file components/logs/2025-04-01.components.log  [object Object] 
2025-04-02 23:08:10 [info] refresh page data from created listeners 0 691   
2025-04-02 23:08:11 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-09-31-23.png  [object Object] 
2025-04-02 23:08:11 [info] refresh page data from created listeners 0 692   
2025-04-02 23:08:12 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-09-37-01.png  [object Object] 
2025-04-02 23:08:12 [info] refresh page data from created listeners 0 693   
2025-04-02 23:08:13 [info] indexing created file 学习库/Anki/c/指针.md  [object Object] 
2025-04-02 23:08:13 [info] indexing created ignore file 学习库/Anki/c/指针.md   
2025-04-02 23:08:13 [info] trigger 学习库/Anki/c/指针.md resolve  [object Object] 
2025-04-02 23:08:13 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:13 [info] refresh page data from resolve listeners 0 694   
2025-04-02 23:08:14 [info] indexing created file 学习库/c/6 数组.md  [object Object] 
2025-04-02 23:08:14 [info] indexing created ignore file 学习库/c/6 数组.md   
2025-04-02 23:08:14 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-04-02 23:08:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-04-02 23:08:14 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:14 [info] refresh page data from resolve listeners 0 695   
2025-04-02 23:08:14 [info] indexing created file 学习库/Anki/c/数组.md  [object Object] 
2025-04-02 23:08:14 [info] indexing created ignore file 学习库/Anki/c/数组.md   
2025-04-02 23:08:14 [info] trigger 学习库/Anki/c/数组.md resolve  [object Object] 
2025-04-02 23:08:14 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:14 [info] refresh page data from resolve listeners 0 696   
2025-04-02 23:08:15 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-10-17-26.png  [object Object] 
2025-04-02 23:08:15 [info] refresh page data from created listeners 0 697   
2025-04-02 23:08:16 [info] indexing created file 学习库/c/7 指针.md  [object Object] 
2025-04-02 23:08:16 [info] indexing created ignore file 学习库/c/7 指针.md   
2025-04-02 23:08:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-02 23:08:16 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:16 [info] refresh page data from resolve listeners 0 698   
2025-04-02 23:08:17 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-04-02-15-55-30.png  [object Object] 
2025-04-02 23:08:17 [info] refresh page data from created listeners 0 699   
2025-04-02 23:08:17 [info] indexing created file 学习库/stm32/3 串口.md  [object Object] 
2025-04-02 23:08:17 [info] indexing created ignore file 学习库/stm32/3 串口.md   
2025-04-02 23:08:17 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-04-02 23:08:17 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:17 [info] refresh page data from resolve listeners 0 700   
2025-04-02 23:08:18 [info] indexing created file 学习库/stm32/attachments/I2C-2025-04-02-18-28-06.png  [object Object] 
2025-04-02 23:08:18 [info] refresh page data from created listeners 0 701   
2025-04-02 23:08:19 [info] indexing created file 学习库/stm32/attachments/I2C-2025-04-02-18-30-50.png  [object Object] 
2025-04-02 23:08:19 [info] refresh page data from created listeners 0 702   
2025-04-02 23:08:20 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-19-34-34.png  [object Object] 
2025-04-02 23:08:20 [info] refresh page data from created listeners 0 703   
2025-04-02 23:08:21 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-10-09.png  [object Object] 
2025-04-02 23:08:21 [info] refresh page data from created listeners 0 704   
2025-04-02 23:08:21 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-23-20.png  [object Object] 
2025-04-02 23:08:21 [info] refresh page data from created listeners 0 705   
2025-04-02 23:08:22 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-28-13.png  [object Object] 
2025-04-02 23:08:22 [info] refresh page data from created listeners 0 706   
2025-04-02 23:08:23 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-41-01.png  [object Object] 
2025-04-02 23:08:23 [info] refresh page data from created listeners 0 707   
2025-04-02 23:08:24 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-44-58.png  [object Object] 
2025-04-02 23:08:24 [info] refresh page data from created listeners 0 708   
2025-04-02 23:08:25 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-49-26.png  [object Object] 
2025-04-02 23:08:25 [info] refresh page data from created listeners 0 709   
2025-04-02 23:08:26 [info] indexing created file 学习库/stm32/attachments/Pasted Image 20250402205837_778.png  [object Object] 
2025-04-02 23:08:26 [info] refresh page data from created listeners 0 710   
2025-04-02 23:08:27 [info] indexing created file 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md  [object Object] 
2025-04-02 23:08:27 [info] indexing created ignore file 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md   
2025-04-02 23:08:27 [info] trigger 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md resolve  [object Object] 
2025-04-02 23:08:27 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:27 [info] refresh page data from resolve listeners 0 711   
2025-04-02 23:08:27 [info] indexing created file 学习库/stm32/4 I2C.md  [object Object] 
2025-04-02 23:08:27 [info] indexing created ignore file 学习库/stm32/4 I2C.md   
2025-04-02 23:08:27 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-02 23:08:27 [info] index finished after resolve  [object Object] 
2025-04-02 23:08:27 [info] refresh page data from resolve listeners 0 712   
2025-04-02 23:10:06 [info] indexing created file 日记库/day/2025-04-02.md  [object Object] 
2025-04-02 23:10:06 [info] indexing created ignore file 日记库/day/2025-04-02.md   
2025-04-02 23:10:07 [info] trigger 日记库/day/2025-04-02.md resolve  [object Object] 
2025-04-02 23:10:07 [info] index finished after resolve  [object Object] 
2025-04-02 23:10:07 [info] refresh page data from resolve listeners 0 713   
2025-04-02 23:10:07 [info] ignore file modify evnet 日记库/day/2025-04-02.md   
2025-04-02 23:10:07 [info] trigger 日记库/day/2025-04-02.md resolve  [object Object] 
2025-04-02 23:10:07 [info] index finished after resolve  [object Object] 
2025-04-02 23:10:07 [info] refresh page data from resolve listeners 0 713   
2025-04-02 23:12:06 [info] components database created cost 33 ms   
2025-04-02 23:12:06 [info] components index initializing...   
2025-04-02 23:12:07 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-02 23:12:07 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-02 23:12:07 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-02 23:12:07 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-02 23:12:07 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-02 23:12:08 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-02 23:12:09 [info] start to batch put pages: 7   
2025-04-02 23:12:09 [info] batch persist cost 7  5 
2025-04-02 23:12:09 [info] components index initialized, 713 files cost 3462 ms   
2025-04-02 23:12:09 [info] refresh page data from init listeners 0 713   
