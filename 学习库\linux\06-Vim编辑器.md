---
tags:
  - 学习
  - linux
  - vim
  - 编辑器
---

# Vim编辑器完全指南

> [!info] 说明
> Vim是Linux系统中最强大的文本编辑器之一。本文档从基础到高级，全面介绍Vim的使用方法。

## 🎯 Vim模式概述

> [!info] Vim的三种主要模式
> - **普通模式 (Normal Mode)**: 默认模式，用于导航和命令执行
> - **插入模式 (Insert Mode)**: 用于编辑文本内容
> - **可视模式 (Visual Mode)**: 用于选择文本块
> - **命令行模式 (Command Mode)**: 用于执行复杂命令

### 模式切换
| 按键 | 功能 | 说明 |
|------|------|------|
| `ESC` | 返回普通模式 | 从任何模式返回 |
| `i` | 进入插入模式 | 在光标前插入 |
| `a` | 进入插入模式 | 在光标后插入 |
| `v` | 进入可视模式 | 字符选择 |
| `V` | 进入可视行模式 | 行选择 |
| `Ctrl+v` | 进入可视块模式 | 块选择 |
| `:` | 进入命令行模式 | 执行命令 |

## 🚀 基本操作

### 启动和退出
| 命令 | 功能 | 说明 |
|------|------|------|
| `vim file.txt` | 打开文件 | 不存在则创建 |
| `vim +10 file.txt` | 打开并跳到第10行 | |
| `vim +/pattern file.txt` | 打开并搜索模式 | |
| `:w` | 保存文件 | |
| `:w filename` | 另存为 | |
| `:q` | 退出 | 未修改时 |
| `:q!` | 强制退出 | 不保存修改 |
| `:wq` 或 `ZZ` | 保存并退出 | |
| `:x` | 保存并退出 | 仅在有修改时保存 |

### 插入文本
| 命令 | 功能 | 说明 |
|------|------|------|
| `i` | 在光标前插入 | insert |
| `I` | 在行首插入 | |
| `a` | 在光标后插入 | append |
| `A` | 在行尾插入 | |
| `o` | 在下方新建行并插入 | open line below |
| `O` | 在上方新建行并插入 | open line above |
| `s` | 删除字符并插入 | substitute |
| `S` | 删除整行并插入 | |

## 🧭 光标移动

### 基本移动
| 命令 | 功能 | 说明 |
|------|------|------|
| `h` `j` `k` `l` | 左下上右 | 基本方向键 |
| `w` | 下一个单词开头 | word |
| `W` | 下一个空格分隔的单词 | |
| `b` | 上一个单词开头 | back |
| `B` | 上一个空格分隔的单词 | |
| `e` | 下一个单词结尾 | end |
| `E` | 下一个空格分隔单词结尾 | |

### 行内移动
| 命令 | 功能 | 说明 |
|------|------|------|
| `0` | 行首 | |
| `^` | 行首非空字符 | |
| `$` | 行尾 | |
| `g_` | 行尾非空字符 | |
| `f{char}` | 跳到字符 | find |
| `F{char}` | 反向跳到字符 | |
| `t{char}` | 跳到字符前 | till |
| `T{char}` | 反向跳到字符前 | |
| `;` | 重复上次f/t命令 | |
| `,` | 反向重复上次f/t命令 | |

### 文档移动
| 命令 | 功能 | 说明 |
|------|------|------|
| `gg` | 文档开头 | |
| `G` | 文档结尾 | |
| `{number}G` | 跳到第n行 | 如 `10G` |
| `:n` | 跳到第n行 | 如 `:10` |
| `H` | 屏幕顶部 | High |
| `M` | 屏幕中间 | Middle |
| `L` | 屏幕底部 | Low |

### 屏幕滚动
| 命令 | 功能 | 说明 |
|------|------|------|
| `Ctrl+f` | 向下翻页 | forward |
| `Ctrl+b` | 向上翻页 | backward |
| `Ctrl+d` | 向下半页 | down |
| `Ctrl+u` | 向上半页 | up |
| `zz` | 当前行居中 | |
| `zt` | 当前行置顶 | top |
| `zb` | 当前行置底 | bottom |

## ✂️ 编辑操作

### 删除操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `x` | 删除当前字符 | |
| `X` | 删除前一个字符 | |
| `dw` | 删除单词 | delete word |
| `dW` | 删除空格分隔的单词 | |
| `dd` | 删除整行 | |
| `D` 或 `d$` | 删除到行尾 | |
| `d0` | 删除到行首 | |
| `dG` | 删除到文档末尾 | |
| `dgg` | 删除到文档开头 | |

### 复制和粘贴
| 命令 | 功能 | 说明 |
|------|------|------|
| `yy` 或 `Y` | 复制整行 | yank |
| `yw` | 复制单词 | |
| `y$` | 复制到行尾 | |
| `y0` | 复制到行首 | |
| `p` | 在光标后粘贴 | put |
| `P` | 在光标前粘贴 | |
| `"{register}y` | 复制到指定寄存器 | 如 `"ay` |
| `"{register}p` | 从指定寄存器粘贴 | 如 `"ap` |

### 修改操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `r{char}` | 替换单个字符 | replace |
| `R` | 进入替换模式 | |
| `cw` | 修改单词 | change word |
| `cc` 或 `S` | 修改整行 | |
| `C` 或 `c$` | 修改到行尾 | |
| `c0` | 修改到行首 | |
| `~` | 切换大小写 | |
| `u` | 撤销 | undo |
| `Ctrl+r` | 重做 | redo |
| `.` | 重复上次操作 | |

## 🔍 搜索和替换

### 搜索
| 命令 | 功能 | 说明 |
|------|------|------|
| `/pattern` | 向前搜索 | |
| `?pattern` | 向后搜索 | |
| `n` | 下一个匹配 | next |
| `N` | 上一个匹配 | |
| `*` | 搜索当前单词 | 向前 |
| `#` | 搜索当前单词 | 向后 |
| `:noh` | 清除高亮 | no highlight |

### 替换
| 命令 | 功能 | 说明 |
|------|------|------|
| `:s/old/new/` | 替换当前行第一个 | substitute |
| `:s/old/new/g` | 替换当前行所有 | global |
| `:%s/old/new/g` | 替换全文所有 | |
| `:%s/old/new/gc` | 替换全文所有(确认) | confirm |
| `:1,10s/old/new/g` | 替换1-10行 | |
| `:'<,'>s/old/new/g` | 替换选中区域 | |

> [!tip] 正则表达式在搜索中的使用
> - `.` 匹配任意字符
> - `*` 匹配前一个字符0次或多次
> - `^` 行首
> - `$` 行尾
> - `\<` 单词开始
> - `\>` 单词结束

## 👁️ 可视模式

### 可视模式类型
| 命令 | 功能 | 说明 |
|------|------|------|
| `v` | 字符可视模式 | 选择字符 |
| `V` | 行可视模式 | 选择整行 |
| `Ctrl+v` | 块可视模式 | 选择矩形块 |
| `gv` | 重新选择上次选择 | |

### 可视模式操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `d` | 删除选中内容 | |
| `y` | 复制选中内容 | |
| `c` | 修改选中内容 | |
| `>` | 右缩进 | |
| `<` | 左缩进 | |
| `=` | 自动缩进 | |
| `u` | 转小写 | |
| `U` | 转大写 | |

### 块操作示例
```bash
# 在多行前插入相同内容
1. Ctrl+v 进入块可视模式
2. 选择多行的开头
3. I 进入插入模式
4. 输入要插入的内容
5. ESC 应用到所有行

# 在多行后追加相同内容
1. Ctrl+v 进入块可视模式
2. 选择多行的结尾
3. A 进入追加模式
4. 输入要追加的内容
5. ESC 应用到所有行
```

## 🪟 窗口和标签页

### 窗口操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `:sp` 或 `:split` | 水平分割 | |
| `:vs` 或 `:vsplit` | 垂直分割 | |
| `:sp filename` | 分割并打开文件 | |
| `Ctrl+w h/j/k/l` | 切换窗口 | |
| `Ctrl+w w` | 循环切换窗口 | |
| `Ctrl+w q` | 关闭当前窗口 | |
| `Ctrl+w o` | 只保留当前窗口 | only |
| `Ctrl+w =` | 平均分配窗口大小 | |
| `Ctrl+w +/-` | 调整窗口高度 | |
| `Ctrl+w </>` | 调整窗口宽度 | |

### 标签页操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `:tabnew` | 新建标签页 | |
| `:tabnew filename` | 新建标签页并打开文件 | |
| `gt` | 下一个标签页 | |
| `gT` | 上一个标签页 | |
| `:tabclose` | 关闭当前标签页 | |
| `:tabonly` | 只保留当前标签页 | |
| `{number}gt` | 跳到第n个标签页 | |

## 🔧 高级功能

### 宏录制
| 命令 | 功能 | 说明 |
|------|------|------|
| `q{register}` | 开始录制宏 | 如 `qa` |
| `q` | 停止录制 | |
| `@{register}` | 执行宏 | 如 `@a` |
| `@@` | 重复上次宏 | |
| `{number}@{register}` | 执行宏n次 | 如 `10@a` |

### 标记和跳转
| 命令 | 功能 | 说明 |
|------|------|------|
| `m{letter}` | 设置标记 | 如 `ma` |
| `'{letter}` | 跳到标记行首 | 如 `'a` |
| `` `{letter}`` | 跳到标记精确位置 | 如 `` `a`` |
| `''` | 跳到上次位置行首 | |
| ``` `` ``` | 跳到上次位置 | |
| `:marks` | 显示所有标记 | |

### 折叠
| 命令 | 功能 | 说明 |
|------|------|------|
| `zf` | 创建折叠 | fold |
| `zo` | 打开折叠 | open |
| `zc` | 关闭折叠 | close |
| `za` | 切换折叠状态 | |
| `zR` | 打开所有折叠 | |
| `zM` | 关闭所有折叠 | |

## ⚙️ 配置和定制

### 基本配置 (~/.vimrc)
```vim
" 基本设置
set number              " 显示行号
set relativenumber      " 相对行号
set tabstop=4          " Tab宽度
set shiftwidth=4       " 缩进宽度
set expandtab          " 用空格替代Tab
set autoindent         " 自动缩进
set smartindent        " 智能缩进
set hlsearch           " 高亮搜索结果
set incsearch          " 增量搜索
set ignorecase         " 忽略大小写
set smartcase          " 智能大小写
set wrap               " 自动换行
set ruler              " 显示光标位置
set showcmd            " 显示命令
set wildmenu           " 命令行补全
set laststatus=2       " 总是显示状态栏

" 颜色主题
syntax enable          " 语法高亮
colorscheme desert     " 颜色方案

" 键位映射
let mapleader = ","    " 设置leader键
nnoremap <leader>w :w<CR>              " 快速保存
nnoremap <leader>q :q<CR>              " 快速退出
nnoremap <C-h> <C-w>h                  " 窗口切换
nnoremap <C-j> <C-w>j
nnoremap <C-k> <C-w>k
nnoremap <C-l> <C-w>l
```

### 实用插件推荐
```vim
" 使用vim-plug插件管理器
call plug#begin('~/.vim/plugged')

Plug 'preservim/nerdtree'              " 文件树
Plug 'vim-airline/vim-airline'         " 状态栏
Plug 'tpope/vim-fugitive'              " Git集成
Plug 'junegunn/fzf.vim'                " 模糊搜索
Plug 'dense-analysis/ale'              " 语法检查
Plug 'tpope/vim-surround'              " 括号操作
Plug 'jiangmiao/auto-pairs'            " 自动配对

call plug#end()
```

## 🎯 实用技巧

### 快速编辑技巧
```bash
# 快速交换两行
ddp

# 快速复制行并粘贴
yyp

# 快速删除括号内容
di(    # 删除()内容
di"    # 删除""内容
di{    # 删除{}内容

# 快速选择括号内容
vi(    # 选择()内容
va(    # 选择()及括号

# 快速跳转到匹配的括号
%

# 快速注释/取消注释（需要配置）
gcc    # 注释当前行
gc     # 注释选中内容
```

### 文本对象
| 命令 | 功能 | 说明 |
|------|------|------|
| `iw` | 单词内部 | inner word |
| `aw` | 整个单词 | a word |
| `is` | 句子内部 | inner sentence |
| `as` | 整个句子 | a sentence |
| `ip` | 段落内部 | inner paragraph |
| `ap` | 整个段落 | a paragraph |
| `i(` `i)` | 括号内部 | |
| `a(` `a)` | 包含括号 | |
| `i"` | 引号内部 | |
| `a"` | 包含引号 | |

### 命令行技巧
```bash
# 执行shell命令
:!ls -la

# 读取命令输出到当前位置
:r !date

# 对选中内容执行shell命令
:'<,'>!sort

# 保存为root权限文件
:w !sudo tee %

# 比较文件
:diffsplit other_file.txt
```

> [!success] Vim学习建议
> 1. **循序渐进**: 先掌握基本操作，再学习高级功能
> 2. **多加练习**: 在实际编辑中反复使用命令
> 3. **配置定制**: 根据个人习惯配置.vimrc
> 4. **插件扩展**: 使用插件增强Vim功能
> 5. **参考资源**: 使用`:help`命令查看帮助文档

---

**相关笔记链接:**
- [[02-文件内容查看与处理]]
- [[shell]]
- [[07-软件包管理]]
