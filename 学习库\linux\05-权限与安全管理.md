---
tags:
  - 学习
  - linux
  - 权限管理
  - 安全
---

# Linux权限与安全管理

> [!info] 说明
> 本文档整理了Linux系统中文件权限、用户管理和系统安全相关的命令和概念。

## 🔐 文件权限管理

### 权限查看与理解
```bash
# 查看文件权限
ls -l file.txt
# 输出示例: -rw-r--r-- 1 <USER> <GROUP> 1024 Jan 01 12:00 file.txt
```

> [!info] 权限位解释
> ```
> -rw-r--r--
> |||||||||| 
> |||||||++- 其他用户权限 (r--)
> ||||+++--- 组权限 (r--)  
> |+++------ 所有者权限 (rw-)
> +--------- 文件类型 (- 普通文件, d 目录, l 链接)
> ```

### 权限数字表示
| 数字 | 二进制 | 权限 | 说明 |
|------|--------|------|------|
| 0 | 000 | --- | 无权限 |
| 1 | 001 | --x | 执行 |
| 2 | 010 | -w- | 写入 |
| 3 | 011 | -wx | 写入+执行 |
| 4 | 100 | r-- | 读取 |
| 5 | 101 | r-x | 读取+执行 |
| 6 | 110 | rw- | 读取+写入 |
| 7 | 111 | rwx | 读取+写入+执行 |

### 常用权限组合
| 权限，以下为二进制数转的十进制数  | 数字        | 适用场景              |
| ----------------- | --------- | ----------------- |
| 644 (110 100 100) | rw-r--r-- | 普通文件（所有者可读写，其他只读） |
| 755 (111 101 101) | rwxr-xr-x | 可执行文件、目录          |
| 600 (110 000 000) | rw------- | 私人文件（只有所有者可访问）    |
| 700 (111 000 000) | rwx------ | 私人目录              |
| 666 (110 110 110) | rw-rw-rw- | 所有人可读写（不推荐）       |
| 777 (111 111 111) | rwxrwxrwx | 所有人完全权限（危险，不推荐）   |

## 🛠️ 权限修改命令

### chmod - 修改文件权限
| 用法 | 说明 | 示例 |
|------|------|------|
| 数字模式 | 直接设置权限 | `chmod 755 script.sh`<br>`chmod 644 document.txt` |
| 符号模式 | 增减权限 | `chmod +x script.sh`<br>`chmod u+w,g-w file.txt`<br>`chmod a-x file.txt` |
| 递归模式 | 递归修改目录 | `chmod -R 755 directory/`<br>`chmod -R u+w,g-w folder/` |

### 符号模式详解
| 符号  | 含义        | 示例                            |
| --- | --------- | ----------------------------- |
| u   | 用户(所有者)   | `chmod u+x file`，增加所有者用户的执行权限 |
| g   | 组         | `chmod g-w file`, 去除组用户的写权限   |
| o   | 其他        | `chmod o+r file`              |
| a   | 所有(u+g+o) | `chmod a+x file`              |
| +   | 添加权限      | `chmod +x file`               |
| -   | 移除权限      | `chmod -w file`               |
| =   | 设置权限      | `chmod u=rw,g=r,o= file`      |

### chown - 修改所有者
| 命令 | 功能 | 示例 |
|------|------|------|
| `chown user file` | 修改所有者 | `chown alice file.txt` |
| `chown user:group file` | 修改所有者和组 | `chown alice:developers file.txt` |
| `chown :group file` | 只修改组 | `chown :developers file.txt` |
| `chown -R user:group dir/` | 递归修改 | `chown -R www-data:www-data /var/www/` |

### chgrp - 修改组
| 命令 | 功能 | 示例 |
|------|------|------|
| `chgrp group file` | 修改文件组 | `chgrp developers file.txt` |
| `chgrp -R group dir/` | 递归修改组 | `chgrp -R staff project/` |

## 👥 用户管理

### 用户信息查看
| 命令 | 功能 | 示例 |
|------|------|------|
| `whoami` | 当前用户 | `whoami` |
| `id` | 用户ID和组信息 | `id`<br>`id username` |
| `groups` | 用户所属组 | `groups`<br>`groups username` |
| `finger` | 用户详细信息 | `finger username` |
| `w` | 当前登录用户 | `w` |
| `who` | 登录用户列表 | `who` |
| `last` | 登录历史 | `last`<br>`last username` |

### 用户账户管理
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `useradd` | 添加用户 | `-m` 创建家目录<br>`-s` 指定shell<br>`-G` 附加组 | `sudo useradd -m -s /bin/bash newuser`<br>`sudo useradd -m -G sudo,developers alice` |
| `userdel` | 删除用户 | `-r` 删除家目录 | `sudo userdel username`<br>`sudo userdel -r username` |
| `usermod` | 修改用户 | `-G` 设置组<br>`-a -G` 添加到组<br>`-s` 修改shell | `sudo usermod -a -G sudo username`<br>`sudo usermod -s /bin/zsh username` |
| `passwd` | 修改密码 | | `passwd`<br>`sudo passwd username` |

### 组管理
| 命令 | 功能 | 示例 |
|------|------|------|
| `groupadd` | 添加组 | `sudo groupadd developers` |
| `groupdel` | 删除组 | `sudo groupdel oldgroup` |
| `groupmod` | 修改组 | `sudo groupmod -n newname oldname` |
| `gpasswd` | 组密码管理 | `sudo gpasswd -a user group`<br>`sudo gpasswd -d user group` |

## 🔑 sudo权限管理

### sudo基本使用
| 命令 | 功能 | 示例 |
|------|------|------|
| `sudo command` | 以root权限执行 | `sudo apt update` |
| `sudo -u user command` | 以指定用户执行 | `sudo -u www-data ls /var/www/` |
| `sudo -i` | 切换到root shell | `sudo -i` |
| `sudo su -` | 切换到root用户 | `sudo su -` |
| `sudo -l` | 查看sudo权限 | `sudo -l` |

### sudoers配置
```bash
# 编辑sudoers文件（安全方式）
sudo visudo

# 常用配置示例
# 允许用户无密码执行所有命令
username ALL=(ALL) NOPASSWD:ALL

# 允许组成员执行特定命令
%admin ALL=(ALL) /usr/bin/systemctl, /usr/bin/service

# 允许用户以特定用户身份执行命令
username ALL=(www-data) /usr/bin/php
```

> [!warning] sudoers编辑注意事项
> - 始终使用 `visudo` 命令编辑sudoers文件
> - 语法错误可能导致无法使用sudo
> - 测试新配置前保持一个root会话

## 🔒 特殊权限

### SetUID, SetGID, Sticky Bit
| 权限 | 数字 | 符号 | 作用 | 示例 |
|------|------|------|------|------|
| SetUID | 4 | s (用户位) | 以文件所有者身份执行 | `chmod 4755 /usr/bin/passwd` |
| SetGID | 2 | s (组位) | 以文件所属组身份执行 | `chmod 2755 /usr/bin/wall` |
| Sticky Bit | 1 | t (其他位) | 只有所有者可删除 | `chmod 1777 /tmp` |

### 特殊权限示例
```bash
# 设置SetUID
chmod u+s /path/to/program
chmod 4755 /path/to/program

# 设置SetGID
chmod g+s /path/to/directory
chmod 2755 /path/to/directory

# 设置Sticky Bit
chmod +t /shared/directory
chmod 1777 /shared/directory

# 查看特殊权限
ls -l /usr/bin/passwd    # -rwsr-xr-x (SetUID)
ls -ld /tmp              # drwxrwxrwt (Sticky Bit)
```

## 🛡️ 系统安全

### 文件完整性检查
| 命令 | 功能 | 示例 |
|------|------|------|
| `md5sum` | MD5校验 | `md5sum file.txt`<br>`md5sum -c checksums.md5` |
| `sha256sum` | SHA256校验 | `sha256sum file.txt`<br>`sha256sum -c checksums.sha256` |
| `find` | 查找可疑文件 | `find / -perm -4000 -type f 2>/dev/null`<br>`find /home -name "*.sh" -perm 777` |

### 系统日志监控
```bash
# 查看认证日志
sudo tail -f /var/log/auth.log        # Debian/Ubuntu
sudo tail -f /var/log/secure          # CentOS/RHEL

# 查看系统日志
sudo tail -f /var/log/syslog          # Debian/Ubuntu
sudo tail -f /var/log/messages        # CentOS/RHEL

# 查看失败的登录尝试
sudo grep "Failed password" /var/log/auth.log

# 查看sudo使用记录
sudo grep "sudo:" /var/log/auth.log
```

### 进程权限检查
```bash
# 查看进程的用户
ps aux | grep process_name

# 查看进程打开的文件
lsof -p PID

# 查看以root运行的进程
ps aux | grep "^root"

# 查看网络连接的进程
netstat -tulnp | grep :80
```

## 🔐 SSH安全配置

### SSH密钥管理
```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
ssh-keygen -t ed25519 -C "<EMAIL>"

# 复制公钥到远程服务器
ssh-copy-id user@server
ssh-copy-id -i ~/.ssh/id_rsa.pub user@server

# 查看SSH代理中的密钥
ssh-add -l

# 添加密钥到SSH代理
ssh-add ~/.ssh/id_rsa
```

### SSH配置文件
```bash
# 编辑SSH客户端配置
nano ~/.ssh/config

# 示例配置
Host myserver
    HostName *************
    User myuser
    Port 2222
    IdentityFile ~/.ssh/id_rsa
    IdentitiesOnly yes

# 编辑SSH服务器配置
sudo nano /etc/ssh/sshd_config

# 安全配置建议
Port 2222                          # 更改默认端口
PermitRootLogin no                 # 禁止root登录
PasswordAuthentication no          # 禁用密码认证
PubkeyAuthentication yes           # 启用密钥认证
AllowUsers user1 user2             # 限制允许的用户
```

## 🚨 安全检查脚本

### 系统安全检查
```bash
#!/bin/bash
# 系统安全检查脚本

echo "系统安全检查报告 - $(date)"
echo "================================"

# 检查SetUID文件
echo "SetUID文件:"
find / -perm -4000 -type f 2>/dev/null | head -10

# 检查可写目录
echo -e "\n全局可写目录:"
find / -type d -perm -002 2>/dev/null | grep -v proc | head -10

# 检查空密码账户
echo -e "\n空密码账户:"
awk -F: '($2 == "") {print $1}' /etc/shadow 2>/dev/null

# 检查root权限用户
echo -e "\nUID为0的用户:"
awk -F: '($3 == 0) {print $1}' /etc/passwd

# 检查最近登录
echo -e "\n最近登录:"
last | head -5
```

### 权限审计脚本
```bash
#!/bin/bash
# 文件权限审计脚本

TARGET_DIR=${1:-"/home"}

echo "权限审计报告 - $TARGET_DIR"
echo "========================"

# 查找权限过于宽松的文件
echo "权限777的文件:"
find "$TARGET_DIR" -type f -perm 777 2>/dev/null

echo -e "\n权限777的目录:"
find "$TARGET_DIR" -type d -perm 777 2>/dev/null

# 查找其他用户可写的文件
echo -e "\n其他用户可写的文件:"
find "$TARGET_DIR" -type f -perm -002 2>/dev/null

# 查找没有所有者的文件
echo -e "\n没有所有者的文件:"
find "$TARGET_DIR" -nouser -o -nogroup 2>/dev/null
```

## 📋 权限管理最佳实践

### 安全原则
1. **最小权限原则**: 只给予完成任务所需的最小权限
2. **定期审计**: 定期检查用户权限和文件权限
3. **密钥管理**: 使用SSH密钥而非密码认证
4. **日志监控**: 监控系统日志发现异常活动

### 常用安全配置
```bash
# 设置合理的umask
echo "umask 022" >> ~/.bashrc

# 设置文件默认权限
# 新建文件: 644 (rw-r--r--)
# 新建目录: 755 (rwxr-xr-x)

# 保护重要配置文件
sudo chmod 600 /etc/ssh/sshd_config
sudo chmod 600 /etc/shadow
sudo chmod 644 /etc/passwd

# 设置临时目录权限
sudo chmod 1777 /tmp
sudo chmod 1777 /var/tmp
```

> [!success] 权限管理要点
> 1. **理解权限**: 深入理解Linux权限模型
> 2. **谨慎操作**: 修改权限前仔细考虑影响
> 3. **定期检查**: 建立权限审计机制
> 4. **文档记录**: 记录权限变更和原因

---

**相关笔记链接:**
- [[03-系统信息与进程管理]]
- [[04-网络命令]]
- [[07-软件包管理]]
