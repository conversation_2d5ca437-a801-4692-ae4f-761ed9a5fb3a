---
tags:
  - 学习
  - deep_learning
---
## 读取 `.yaml` 文件

**第一步**：安装 PyYAML 库
如果你的环境中还没有安装 PyYAML，你需要先安装它。打开你的终端（Terminal）并运行以下命令：
```bash
pip install pyyaml
```

**第二步**：使用 Python 读取和解析 YAML 文件定义你的YAML文件路径
```python
import yaml  # 导入PyYAML库
file_path = '/home/<USER>/YOLO/yolov8/data/face_keypoints.yaml'  # 定义你的YAML文件路径
with open(file_path, 'r', encoding='utf-8') as file:
#  使用 with open() 打开文件
       data = yaml.safe_load(file)  # 使用 yaml.safe_load() 来解析文件，它会将YAML内容转换成一个Python对象（通常是字典或列表）
```


