2025-06-30 10:34:13.126 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:13.134 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:13.136 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:13.137 [info] refresh page data from resolve listeners 0 932   
2025-06-30 10:34:13.138 [info] indexing created file components/logs/2025-06-30.components.log  [object Object] 
2025-06-30 10:34:13.157 [info] refresh page data from created listeners 0 933   
2025-06-30 10:34:16.143 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:16.183 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:16.186 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:16.188 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:18.422 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:18.440 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:18.442 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:18.443 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:20.625 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:20.647 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:20.648 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:20.649 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:23.715 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:23.765 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:23.766 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:23.767 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:25.765 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:25.794 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:25.796 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:25.796 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:27.833 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:27.865 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:27.866 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:27.867 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:30.463 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:30.499 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:30.500 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:30.501 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:34.919 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:34.950 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:34.951 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:34.952 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:36.970 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:36.987 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:36.988 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:36.989 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:39.180 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:39.213 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:39.213 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:39.214 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:41.290 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:41.328 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:41.328 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:41.329 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:43.577 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:43.613 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:43.614 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:43.615 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:47.074 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:47.105 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:47.107 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:47.108 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:34:53.308 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:34:53.347 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:34:53.348 [info] index finished after resolve  [object Object] 
2025-06-30 10:34:53.350 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:01.327 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:01.362 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:01.363 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:01.364 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:06.219 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:06.258 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:06.259 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:06.260 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:08.214 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:08.219 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:08.220 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:08.221 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:12.944 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:12.948 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:12.950 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:12.951 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:16.187 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:16.207 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:16.224 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:16.225 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:19.113 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:19.153 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:19.155 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:19.156 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:21.223 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:21.275 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:21.276 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:21.278 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:23.810 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:23.860 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:23.861 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:23.862 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:26.610 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:26.615 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:26.615 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:26.616 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:39.850 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:39.854 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:39.855 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:39.855 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:41.911 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:41.915 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:41.915 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:41.916 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:46.919 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:46.923 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:46.924 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:46.925 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:49.421 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:49.425 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:49.426 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:49.427 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:53.731 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:53.735 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:53.736 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:53.736 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:57.051 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:57.056 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:57.056 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:57.057 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:35:59.137 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:35:59.140 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:35:59.141 [info] index finished after resolve  [object Object] 
2025-06-30 10:35:59.142 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:01.546 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:01.550 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:01.550 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:01.551 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:04.001 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:04.006 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:04.015 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:04.016 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:06.017 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:06.022 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:06.023 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:06.024 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:08.603 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:08.606 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:08.607 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:08.608 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:17.654 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:17.657 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:17.658 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:17.659 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:26.888 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:26.891 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:26.892 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:26.893 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:36.883 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:36.887 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:36.887 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:36.888 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:39.957 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:39.962 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:39.963 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:39.964 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:46.141 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:46.145 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:46.146 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:46.147 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:49.331 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:49.336 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:49.337 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:49.338 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:36:59.881 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:36:59.885 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:36:59.886 [info] index finished after resolve  [object Object] 
2025-06-30 10:36:59.887 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:02.090 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:02.094 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:02.095 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:02.095 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:09.734 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:09.737 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:09.738 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:09.739 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:16.064 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:16.068 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:16.069 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:16.070 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:28.267 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:28.271 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:28.272 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:28.273 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:30.665 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:30.669 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:30.670 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:30.671 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:33.973 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:33.977 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:33.978 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:33.979 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:36.272 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:36.277 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:36.278 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:36.279 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:39.592 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:39.598 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:39.599 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:39.600 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:41.623 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:41.628 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:41.629 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:41.630 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:43.657 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:43.661 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:43.662 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:43.663 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:45.705 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:45.709 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:45.710 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:45.711 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:47.755 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:47.759 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:47.760 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:47.761 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:50.445 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:50.450 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:50.450 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:50.451 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:53.240 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:53.245 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:53.246 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:53.247 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:55.582 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:55.585 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:55.586 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:55.587 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:37:58.420 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:37:58.424 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:37:58.425 [info] index finished after resolve  [object Object] 
2025-06-30 10:37:58.425 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:01.206 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:01.232 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:01.242 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:01.243 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:03.227 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:03.230 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:03.231 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:03.232 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:05.658 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:05.663 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:05.664 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:05.665 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:08.856 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:08.861 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:08.862 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:08.863 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:10.952 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:10.956 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:10.957 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:10.958 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:16.232 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:16.236 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:16.238 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:16.243 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:18.938 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:18.942 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:18.944 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:18.945 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:22.036 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:22.040 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:22.041 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:22.042 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:29.230 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:29.234 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:29.234 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:29.235 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:31.373 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:31.378 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:31.380 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:31.381 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:38:53.027 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:38:53.030 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:38:53.031 [info] index finished after resolve  [object Object] 
2025-06-30 10:38:53.032 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:01.151 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:01.156 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:01.156 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:01.157 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:30.209 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:30.269 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:30.272 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:30.273 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:35.751 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:35.831 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:35.833 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:35.834 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:40.074 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:40.162 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:40.163 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:40.164 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:42.324 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:42.425 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:42.433 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:42.437 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:44.416 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:44.489 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:44.490 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:44.491 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:47.173 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:47.266 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:47.270 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:47.271 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:49.258 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:49.337 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:49.339 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:49.341 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:52.190 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:52.265 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:52.267 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:52.268 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:54.318 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:54.397 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:54.399 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:54.399 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:56.432 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:56.515 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:56.516 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:56.517 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:39:59.668 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:39:59.734 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:39:59.735 [info] index finished after resolve  [object Object] 
2025-06-30 10:39:59.736 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:40:34.305 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:40:34.309 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:40:34.311 [info] index finished after resolve  [object Object] 
2025-06-30 10:40:34.311 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:40:36.343 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:40:36.410 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:40:36.412 [info] index finished after resolve  [object Object] 
2025-06-30 10:40:36.413 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:40:43.005 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:40:43.010 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:40:43.011 [info] index finished after resolve  [object Object] 
2025-06-30 10:40:43.011 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:40:59.616 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:40:59.685 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:40:59.687 [info] index finished after resolve  [object Object] 
2025-06-30 10:40:59.691 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:02.724 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:02.858 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:02.860 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:02.862 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:05.990 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:06.070 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:06.072 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:06.074 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:16.542 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:16.597 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:16.598 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:16.599 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:18.740 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:18.803 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:18.806 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:18.807 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:24.092 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:24.097 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:24.099 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:24.100 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:29.062 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:29.121 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:29.128 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:29.130 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:31.486 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:31.544 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:31.545 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:31.546 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:39.992 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:39.996 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:39.997 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:39.998 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:45.284 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:45.342 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:45.344 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:45.345 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:50.203 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:50.297 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:50.304 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:50.305 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:52.289 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:52.373 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:52.373 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:52.374 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:54.426 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:54.513 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:54.514 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:54.515 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:41:57.714 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:41:57.813 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:41:57.814 [info] index finished after resolve  [object Object] 
2025-06-30 10:41:57.815 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:00.311 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:00.412 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:00.413 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:00.414 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:03.982 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:03.987 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:03.988 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:03.989 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:06.768 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:06.772 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:06.773 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:06.774 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:11.874 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:11.880 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:11.881 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:11.882 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:27.254 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:27.259 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:27.260 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:27.260 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:30.121 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:30.129 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:30.131 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:30.131 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:40.630 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:40.636 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:40.637 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:40.638 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:42.865 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:42.869 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:42.871 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:42.871 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:44.962 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:44.969 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:44.970 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:44.971 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:42:49.255 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:42:49.260 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:42:49.262 [info] index finished after resolve  [object Object] 
2025-06-30 10:42:49.263 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:01.231 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:01.293 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:01.294 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:01.295 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:10.394 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:10.473 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:10.475 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:10.476 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:12.466 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:12.564 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:12.565 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:12.566 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:14.556 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:14.644 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:14.647 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:14.647 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:18.881 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:18.997 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:19.006 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:19.009 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:20.978 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:21.069 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:21.070 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:21.071 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:23.537 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:23.630 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:23.631 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:23.632 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:35.255 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:35.259 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:35.260 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:35.261 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:43.093 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:43.166 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:43.173 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:43.174 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:45.621 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:45.702 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:45.703 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:45.704 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:47.843 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:47.923 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:47.924 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:47.925 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:43:52.778 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:43:52.783 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:43:52.785 [info] index finished after resolve  [object Object] 
2025-06-30 10:43:52.786 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:14.708 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:14.713 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:14.714 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:14.715 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:17.130 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:17.135 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:17.136 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:17.137 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:19.165 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:19.169 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:19.171 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:19.171 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:21.220 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:21.226 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:21.227 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:21.228 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:23.495 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:23.502 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:23.504 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:23.505 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:25.812 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:25.816 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:25.817 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:25.818 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:28.014 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:28.019 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:28.022 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:28.023 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:29.998 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:30.088 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:30.089 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:30.090 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:55.893 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:55.964 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:55.965 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:55.966 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:44:58.409 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:44:58.483 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:44:58.485 [info] index finished after resolve  [object Object] 
2025-06-30 10:44:58.486 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:06.491 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:06.559 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:06.560 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:06.561 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:08.595 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:08.698 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:08.699 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:08.700 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:10.921 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:11.015 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:11.016 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:11.017 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:14.530 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:14.645 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:14.646 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:14.647 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:16.639 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:16.756 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:16.756 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:16.757 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:18.748 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:18.844 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:18.844 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:18.845 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:22.065 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:22.191 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:22.198 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:22.199 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:24.171 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:24.255 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:24.255 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:24.256 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:26.774 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:26.853 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:26.855 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:26.856 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:50.015 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:50.104 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:50.107 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:50.109 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:54.128 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:54.221 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:54.225 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:54.227 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:45:56.610 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:45:56.728 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:45:56.728 [info] index finished after resolve  [object Object] 
2025-06-30 10:45:56.729 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:46:17.004 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:46:17.009 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:46:17.010 [info] index finished after resolve  [object Object] 
2025-06-30 10:46:17.011 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:47:21.178 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:47:21.259 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:47:21.268 [info] index finished after resolve  [object Object] 
2025-06-30 10:47:21.271 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:47:24.632 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:47:24.637 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:47:24.638 [info] index finished after resolve  [object Object] 
2025-06-30 10:47:24.638 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:47:26.992 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:47:27.067 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:47:27.068 [info] index finished after resolve  [object Object] 
2025-06-30 10:47:27.069 [info] refresh page data from resolve listeners 0 933   
2025-06-30 10:47:29.744 [debug] ignore file modify evnet 学习库/GIT食用指南/使用经验.md   
2025-06-30 10:47:29.750 [info] trigger 学习库/GIT食用指南/使用经验.md resolve  [object Object] 
2025-06-30 10:47:29.751 [info] index finished after resolve  [object Object] 
2025-06-30 10:47:29.751 [info] refresh page data from resolve listeners 0 933   
2025-06-30 21:43:00.761 [info] query  [object Object] 
2025-06-30 21:43:00.761 [info] query  [object Object] 
2025-06-30 21:43:00.761 [info] query  [object Object] 
2025-06-30 21:43:00.761 [info] query  [object Object] 
2025-06-30 21:43:00.762 [info] query  [object Object] 
2025-06-30 21:43:00.762 [info] query  [object Object] 
2025-06-30 21:43:00.764 [info] query  [object Object] 
2025-06-30 21:43:00.845 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-06-30 21:43:01.007 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-06-30 21:43:01.010 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-06-30 21:43:01.022 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-06-30 21:43:01.036 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-06-30 21:43:05.101 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-30 21:43:05.608 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-30 21:43:05.943 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-30 21:43:06.113 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-30 21:43:06.123 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-30 21:43:06.126 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-30 21:43:06.305 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-30 21:43:06.308 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-30 21:43:06.311 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-30 21:43:06.314 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-30 21:43:17.250 [debug] ignore file modify evnet 日记库/读博.md   
2025-06-30 21:43:17.268 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-06-30 21:43:17.275 [info] index finished after resolve  [object Object] 
2025-06-30 21:43:17.276 [info] refresh page data from resolve listeners 0 933   
2025-06-30 21:43:29.297 [debug] ignore file modify evnet 日记库/读博.md   
2025-06-30 21:43:29.301 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-06-30 21:43:29.302 [info] index finished after resolve  [object Object] 
2025-06-30 21:43:29.304 [info] refresh page data from resolve listeners 0 933   
2025-06-30 21:43:32.527 [debug] ignore file modify evnet 日记库/读博.md   
2025-06-30 21:43:32.536 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-06-30 21:43:32.538 [info] index finished after resolve  [object Object] 
2025-06-30 21:43:32.540 [info] refresh page data from resolve listeners 0 933   
2025-06-30 21:43:35.291 [debug] ignore file modify evnet 日记库/读博.md   
2025-06-30 21:43:35.296 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-06-30 21:43:35.296 [info] index finished after resolve  [object Object] 
2025-06-30 21:43:35.298 [info] refresh page data from resolve listeners 0 933   
2025-06-30 21:43:37.347 [debug] ignore file modify evnet 日记库/读博.md   
2025-06-30 21:43:37.351 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-06-30 21:43:37.352 [info] index finished after resolve  [object Object] 
2025-06-30 21:43:37.353 [info] refresh page data from resolve listeners 0 933   
