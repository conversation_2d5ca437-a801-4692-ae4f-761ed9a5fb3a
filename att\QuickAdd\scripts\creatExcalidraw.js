module.exports = {
  entry: async (params) => {
    // --- 1. 定义空白Excalidraw文件的模板 (这是核心) ---
    const templateContent = `---
excalidraw-plugin: parsed
tags:
  - excalidraw
---

==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


## Drawing
\`\`\`compressed-json
N4IgLgngDgpiBcIYA8DGBDANgSwCYCd0B3EAGhADcZ8BnbAewDsEAmcm+gV31TkQAswYKDXgB6MQHNsYfpwBGAOlT0AtmIBeNCtlQbs6RmPry6uA4wC0KDDgLFLUTJ2lH8MTDHQ0YNMWHRJMRZFAEZQxQAGMiRPVRhGMBoEAG0AXXJ0KCgAZQCwPlBJfDwc7A0+Rk5MTHIdGCIAIXRUAGtirkZcAGF6THp8BBAAYgAzcYmQAF8poA===
\`\`\`
%%
`;

    // --- 2. 获取当前笔记信息 ---
    const activeFile = params.app.workspace.getActiveFile();
    if (!activeFile) {
      new Notice("❌ 没有活动的笔记文件。");
      return;
    }

    const currentFileName = activeFile.basename;
    const excalidrawFolderName = "excalidraw";
    const excalidrawFolderPath = `${activeFile.parent.path}/${excalidrawFolderName}`;
    const finalExcalidrawPath = `${excalidrawFolderPath}/${currentFileName}.excalidraw.md`;

    // --- 3. 检查文件是否存在 ---
    const finalFileExists = await params.app.vault.adapter.exists(finalExcalidrawPath);

    if (finalFileExists) {
      // 如果已存在，直接在新页面打开
      new Notice(`️ 绘图文件已存在，将在新页面中打开。`);
      await params.app.workspace.openLinkText(finalExcalidrawPath, '', true);
    } else {
      // 如果不存在，使用模板创建新文件
      new Notice(`🚀 正在创建新的绘图文件...`);
      
      // 确保文件夹存在
      try {
        await params.app.vault.createFolder(excalidrawFolderPath);
      } catch (e) { /* 忽略文件夹已存在的错误 */ }

      // 使用模板内容创建最终的Excalidraw文件
      await params.app.vault.create(finalExcalidrawPath, templateContent);
      
      // 在新页面中打开这个刚创建好的完美文件
      await params.app.workspace.openLinkText(finalExcalidrawPath, '', true);
      
      new Notice(`✅ 绘图创建成功！`);
    }
  },
};