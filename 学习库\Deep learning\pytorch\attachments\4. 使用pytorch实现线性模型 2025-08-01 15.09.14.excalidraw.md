---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
非线性模型 神经网络 ^qd78cqTn

输入 X ^NuLg5DMH

线性组合<br>Z = WX + b ^loR4achu

非线性激活函数<br>A = g Z ^twa0lB06

输出 Y_hat ^9ic1scjy

线性模型 ^Jbp8rAJ7

输入 X ^BsJyPMOb

线性组合<br>Z = WX + b ^j4K6F5Ec

输出  ^eoQ8Zqdd

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGeO0ABho6IIR9BA4oZm4AbXAwUDBSiBJuCHjfTQAxADM4ACEAZQBVAE1kgDkKAGZCAEUAdUxSIzTSyFhESsDsKI5lYMmy

zG5neIB2ZP4ymA2ANgAWY+0eQ54AVnieHmSdvtu9yAoSdW4+492iyEkEQjKaTcLZbACc50uNzuD2STz4vwg1mW4lQPymEGYUFIbAA1ggAMJsfBsUiVADE8QQVKpq0gmlw2FxyhxQg4xCJJLJEmx1mYcFwgRydIg9UI+HwLVgKwkgg8IqxOPxw3ekm4CIxirxCClMBl6DlFReEFZQI44TyaHixrYAuwagOVuS6LKLOEcAAksRLahClNIPQAOLEACO

xxDAAk+gAZboABR4g2YTVqABViPQ/BBfgBdY31chZL3cDhCCXGwjsrCVXCpY2s9nm5g+0vlxFhBDET7HMFXPuHY2MFjsLhoY6DpisTjdThibi3PrJa5g+J9DVlQjMAAiGSgne49QIYWNmmE7IAosEsjkfX6piV/dB4KjoFgoHSN12JCHiFsABzYCGqZcL8YAAL65saQhwMQuB7l+qDbFc3xrlcf5/oc4LGkQHC4iWZb4NhbBMvuaCHvgYRFGBewP

pAFTfr+AFAVwxozC+e6YO+xrrGgzhguOiKOqgziHH0EIXNctz3I8zyIm8xAfFa6HGv8gLAmg1zGsi+ougI2LapypIUjS1JICejLMg2HLEkZPLkBw/KCtkXGImKEq6vqmLEka7b6cqqrqsaWr4h5L6Gl29bCGaFrzjadoOvOzrGm60FereoEMMGYaRjG8aJsmaYZlmkGuYWCDFmgraEYilbENWEi4PEIpWU2LYEUFCCkYhxxbGuxwSX+E7Dpw3ADo

iQ5ThwM4cHOVrfGhf7xMkYLrnR267l15HHoip5ssQl6ZM5t55oi0GwfB85bMhcLXOhhx/rpEA4XhlXtYiJIkQhW0IKxb6VIAeumAP1+gDkBoAhFaANHqqCAHqegDzfoAiv6ADt+zWUKmf0SEDYOQ7DiMivUnBQC0hBGKi9z5vjtS4Po4pCVcv2cQAgkQyijugYg5EwIpDlA5gEIzgIs9AtoinoOS4JWTAVeg1RaA0zTtF0vQDCMYwTDapCApWBCo

5xAMgxD0Pw0jWlCFAbAAErhETqLYkIP3veLEYAkC75WucVxUTRNUIRAxBXJgBJ/gA8mwWxsDwFAcIHyj1MM+i4pIf4tCKbFzAgCzaWZiI8cJxx9Oc1zOnc6GLX+fRjRiQkifE4lQlJsLwsa8mKagGHaMcVzOsk7egqJmGrVITvqagPXaLTiIZ9wj3BYSNncuglKmbS5lMil7KGXP0D2Y5QouRibmStKYXeRFvlKggKoKWqGlBX5OqH5U4XNVFkit

bFiK2oyCVOo9KWet6BQZSDKGcMUZYwJiTCmdMmZlDZimCdPeZVJZVQrFWbOSIeBPz2q/V6bZNSdQQvcP8ucMKHEehNEco0hqTWmrNVAhw+y53iMcIhFZ1rBAumRI8dsMS7QvFeI6AD/S0TKCnHkf0XifkqN0IQ0ZlBXC3AAWQjLAqY1FQLCLot7NobB6hbjYEBLYgxlBCHiB6XEEYhDJCuMMOAeEJHTGfDWUgOIqCgQgnAqCME4JdSQihW6GEHrY

UrC9VAyD3rEXxF9LhHsii0XKN7aRsj5FKOTo4sROtuIbEXOccEYIwR/lBCtME90rrGkrqQv8KRbjQmknCWSGIm5XzockUeVw8lgiumhNpfRSmIlUs7dUY8MQTzQFPW+69jKL0zjwiyq9rJckqLyByAod643FAfPUR95Q3zPhfZu/dp6hQfsfTB0VmxvwxB/e0sBEo/1ZH/dKj4gHZVAXlCBhVoEqLAPAsoBZKblQQmEjEtV6roFwH0TBjYYo4Oqn

gnxYlerJEOJcQa41JwULQOXMo5DpyzlRPEUh3xDjxD/DwVFwK2EIA4agb6J4zz7X4TeAoPzIBnW8QhXxN0Fr3Ues9fCuCygfUiQeLhdMXboEAMnxgBTRVQAADWRhQbW4qIDSrlbjfGhNibqkenjHIFMqb4BpmKvmzNKhsz3GSIa3N3AmoFlTYgxAVjGhFlEcWpBJY+z9gHYOodw6R2jrHeOicRSkg1hwLWaNJUyvlcbU2FtWBarQDbbhgqHaD3FU

ka4MTShxPougFo9MIwACs4AdGUMoNgUB8kACkIxxiMOeZQ8RcAADVUmzAkI4SmnAT4YmzpsB4CR4RXCLuhElZcykbBHUkLYUJkilxWn0L4jcAoaSYZCHgPZDj8VJf1fu/Sh48HXRJLdO7N13C0ksHSOyDKz0mSZEUDIV5WQmXZPkKznJrPcvfWUJyb3+UvoFU+2ojm/u2YiU0L9oWITip/G539kr3LSoIjEzyQG5XAQVKBxUPGlX+Ugt6wLUE1mO

JC4g2DQmEbKB2Dl9CwRfCPf3HFLMthUJHDQkmTC+w7C2Mw1hO52GbVFTtelB1ry5BQzm0CDiO3oA4rvR8eanrm2OIyDQXzwISI0fEyo2jdH6NTIY4xpjzGWOsbYj8MmXyChcRptRQjpM6YkG0GAxAeCYEkB0KACi4DKGcC2sEZtmBtBbbK2VABpSzT5ZNImcWwVx/p3GlBZRANl1LOWoTuoE+2uF+WwsFREoTFEEDZuKF7SoJIzaqewOp1iaS5Pi

Kzhsb4KR6EoqsUhf81pBLNZHQkYp6FjjxD7DwXqpwV2AbQNljEB7xW9OGVe1EYyz6vvniZJeO1ZkvrvW+5ZTlhT5nWaBg0f7gMAf2f+u+mzjngYxJBij3XLnxXg4hJKiJf7IbQHebFWV0NgPypAoqMCSoIPw4CqjdFiMNSuGRijQLqP4JBFsJh86wRvdQ+ikamK2O4pmqifqzpikCQpQJqlRXto8NE4yiTX2UtpZ8VdPx3LpuptyzCoin0RXFbFZ

UEGgARv0ABAqAAdDgAAeTQpAAB8AAtVAABeEXwxZWoAANSoE0AqpVvPgaC/F1L2XcvUBK9V+r9VORNUkx1eTSm1NuBDJEW+W1ZrnIcytTzfATuJD2sddMsoLqxbmndd7AtxbS3lsrTWutDam2tpDerfwEadYSH5wLvXMv5dG+V2rjXsbzaW0TagZNQTzSOzUhmt2pXc3e30MoBA0EtjMBaJIYYxBkhGH0DwBR9Qi3S4UWbdtL4u36B7SKftq57eQ

ErtcLYo9O7Dduv+NHWLXirt4DsFS6b1Tr/Hotyel3VsQAXg+5ell6UH6WdvT9h3v3XbAz5TUt89lNIObfY7XlbtlHu9Bx7ZQrlf1e3cu6J9r6IAr9jlP9u8thsDrhqDkWODgKpDnVGgrgIcLDtBvDgIIjnNH0NOj1JujjixgQRxtwCtLcI8GuPxhtFEtziJntGJgIl9uoo5qIg1hko5kplABQLWPgE0EinZlpuwVoq5u5p5t5r5v5oFsFqFhFlFi

wbFrZm4gIf6JopUIHLgMcNWhoTwDAFAFsE0N0ABFYOeFuIHGCIMLIfVvIfFnZiDmUPThyozlyllrysEnlhzsKpwsVpXuVjyFwckDwXwXVjFvJqPkcI9JXASi0p3MkPPmhIvkihNs3LcI9LNiCBPkiLvqMvvjtmtlMo+ltmfjkZvO+vtgppAPvG/o/Jdk/kBg/mfJUadnds/A9rBtckJEtIAalP/IwU8mAa8phoDp8rYeUYgvAflogaCkiFsGgecu

zu2FgXQjcMcPQj1MvgwJjizGscxsQWgMhGOk8MpDVJStSrSrQXwodEyrTp4udAztdJlgEi4WzpRggcppzp4RTg7knugBjIAAP2gA3LaAC/iiLoAA6mae9MGeygIu0umukaEAvxgJoJEuku4JhuygqA0JZM5uVs2qmJUA+qtuuxxqTMAs5qruaKpA1qvMxJlQ3uTqiI/ubqHqNedecADeTeLebeHeXePefeceYaieyq8JAJiJUuKJqAaJGJ48Jsee

Ca1spAtsxeCApeAyrsWapQ9mZWwK3sFAW4YIkgAA+gSNWvQH0JoFoOeMkHGIQMkKQOFh6OeAPqnOnLvpkrxPdNoIuLXCOnEX+GCJOpinED2HkkQrxrcEtPxIkU0quIcNoMSgXIuDcFdP+BvmXtwCPOkSMmiNkQshIEfqZPkc+oUTmXJlvB+gdq5Edj+idh/npLsqvi/vUVWe/vfp/s0d/q0f/h0YhkAd0SAb0cAuAW8lhkDl8iln8nAW4TVFDmCn

+DMW1C8TRvOF8LOshKNlsRsXbkQXiolKcL6cNizmtKTiccJpTnQdTo8veMwZYSEfYk5ugGCOYPEMwNgEWgcIoUwcoXeRAGoRoVoToXoQYfaLgMYaYeYbeXITZtYW4sMall4ulo4fcZhH6TliEhga8R4TStEuqZ7FqZUA+dgE+S+QcEEexI1n2lOnnDwO0gUuCFRSUukUJL1NoLxsGT1ENkestMTmUI0olDGW0nkp0n+N0vNmUKkRpBmZkVmWdjPM

WYfutr7vSAUXtOfqWaUV+hsp5FUdJTUdfNJQ0TWSaG2bMTBu/M9u0ejq6Ehr2d9gGH0RhgDh8jhslvmKMZOURkgTWGCHOW5QjvCjEbOl3AQZQuSdQtuWOHUgSjCJQYJtQR8fSFThcTTr6HTnBbcUzs4cXqhRDuheTimtMLCRKoAF+KqAHQ+pkgcEMJXxKqxVpV5VZRooGq2JGkVueqNuhqm5iI8mnurMLulq5JlJHu1JXuJAPuws+MAeEs2pupBp

RpJpZpmgFpVpNpdpDpas/J+AWuEgRVJVZVFVue8aTVhe8peVT0aaqZqp7s2FsSPh6AemeiBiRiJiZiFiViNidinVlhkFVArpaIo8/pwkVwyOG6NS9c9S3Fq+LShK0R0NMRy6fSm+Y4Zw9wy07SqN7Sl6KIe+0lB+eZG2MyhZylRRF+ZZ9VFRTZWldR2oOlvAl2+lLZkAX+xlP+kAf+L2XZ72VlF5P2A5/RDlUBo5LlYOPlExyB9M3laAwi0WJMvw

GpmBPibSmEdS86QVaArGIV7GYVw8vUWwHFK00VZOsVJ1vCDKiVx01x7Kl0dx/iPKmVwtOVhtxocAbAlYSVNlNlYAukpQyQoELKYA7tzgLSE+pQm6o8PtEi/tkNXcMN0Rq4XFUwpw5whOaNaNPtMF+AoQUARI+gVMMgnYcYztwocxD+goUATQtUlYMCEtGUGQ4mHqvs/sQcIcYcEcUcMcccCcScEiDV2AQgPontEAyguAcA84Mtxo2QxAZd7IFd3A

ktNdzkHqIeJaZaFaVaf4ta9ajazabaXdeMPdfdKQm62wnSZKRCq4aOfYXdg9w9Y42gpKdwa4SFw27cK4o9vkJd9McWFA/wuAYxY97IH9Li393sX1IoQQp4FAuV3huFzmwhHmXmPmfmAWQWIWYWkWJFTitmP1zNEAlcnpbceSINMkfQkZ84CQUd0dzosdKZKpw8ecOwHRFD/cmZy2t6sluNClEAT6p+hNslxNal1+GlWy9NmIj+9ZtN5NjRrZfgUG

TNHZbNFlkAH21loBPN9lkBI5MF45AKdtIKyBTQ4tqAktLBPAr9cKCEyx1caO426tWOqAatGOw0U0mtaEPYzCS45KG4xxuVdKZ5ptzK5t8FVtzOjxWVLxQq3jiITtLtXNft0mntHtYdoE/tzNwdec7SFDhcxwiT/oyTZD3wGTVD0muczFMRGTS4qdMBgqGdWdOd8E+dLtdtWIJdk9jgSwM91d1OC9haS94eq9690eW9H43dvdGwURkkyOfYBKyxEz

S0q0ijQ9I9qif9E95dbTVdj4c9OQTJte9ejezere7ene3eve/eO9xEIzoyB9a4a4y0VwfQ6EHSK4vUl9CzN9pcvciQZK1cPA8Ipj1GUQFJAD8WQDdt49QLX9IQwDn9oD+A4DkDV1Um0D6AP5mh1a2huh+hhhwFJhZhjpDU0LP1gd/1q4+DO6hSdFGEEZckENf18N51tDidZTXcElmNWR2NRR7DBZ3Da8RNqlqyAjdNva/zdZk2NNelEjBljNPo2D

rN5lnRDykmtlqjEBw5QxlTIxQtRdG405SIBIBjRj9WJjSz8xDOvYucyKs6KtdjW5eOJBnz/YLCRxR5ETp55x4mZtp0qVDhQTGVKFdt4TDtkTBdrtSTcT9i3t/ovtEdtLyhm62TUw0bQdYAxTyNZT7cFTzl701TBgtTedwbjTALpdqzldhjHTiV2zLJbJ+znJRzPJpzGUu9FzwkYzQ2BSPSs6XwucDwPzLz19iEfzkA49LT096zGImzUAHqOpephp

xppp5plp1ptp9pQzjbfdvbduHpzop6zC561cf4A7Ij79n9ILWrg7/9x7kLGD1hY9sL8W8Lmm11SLEAPAhAzgHQmAqYcAlpqYyQsqxwZsBIuAf4cAIYCiYFH1MW8wiwmNP1mw/UQ61SxKcINwVwlwxLQlJDGkRCwNdcMkmkdLNDz9OHMIeHLL167LbD8lXLcyKlJR/LFZN+mlkjtZVNYj4rt+1ZwjUrFyv+Zlty3ZXRMTEAgY4W0YRgPAbAMAPACA

HQcY0YgYEY9AhwcYZsgwqY1aAteGE5p75QOruAW4BjaFi5zV861w56ZCG5hJNjTjtraATw/YcIB55QXjgbrrJt7rir2mchN5ghlQ1amgcAf4pA9M1a0x0F6rsFNx3r6VDxttOnAbXOlECLmpkiEg/ngXwXoXeLrB9V/a3wxLaOmHLcKRCNvAZHS22ZtkuRx+m2BNPLvDfLV+DHgjN2wj081NDZIGErXHRl0rcjcrAnCrPRGIInYnEnUnMncnCnSn

KnanGnmjrlOnujNYq1EG9KcO2VxniElwKE9GCj6xjjwVDjoVtnqATwOBNFBK+tx5NBbn9BlxyVATaVThsXfr8XhWrnnxyqes4MlV33mMZuBMh1pMrk1uBqRqnVjuQ1PV7MfVDjA13Vgsw9zq41jJ3sL7b7H7X7cYP7f7AHQHIHYHfJCeG1sJP3IouA0pB1BeReOWSppXmal1D7iLqX6ARgYIuIRgAX3QiY9QHAHoa8zgMA8QUAPAcY2XJoac0HdJ

5FvEKbBcKNJKjGvp/1HFRXrc7cnc3cHbfc1DQ86ZGN5HlN+IONVHJ+NHvLdHTXe8lZHHzZQrLH52z+4jdvFNUjZyfXplcGA3HNPZQno34nkn0nsn8ninynqn6nmnsB2jS3entQhnm3CxXWDwpKlr1nR32KGxOxiEOt/4BS9G/cm4zrn38VvjHnw3LPGI3nZFim3sTQzA1aMAcYCigcGu75Dmn5Sm4W9ACi54f4CA9AWw2AxAxw+pxAygYIFA0ECi

4W8f4Fn10L4XmbGI9hltMXNtb3zx4x9tiXJWyXVelQ9fjfzfrfEvPnsvwk2SZKncJw8626KOaxQkdwjPRXpCrS7Sglwl6RYlZXhvFXFHVXOSnkXN7bYGuVvcsjb0Y5CMHeIjEVhdnY5MdJWvXHjizT44IZfegnRVsJ1E6B8JuIfabuHzm5R9fki3Lfigg8oNRAwCfBcgsVJT5Iu4UzK1ukW2Ka1iU2wR5nxidZUFd+PjN1gwUe6esoua/F7khTi5

kDwkbxTCrdy+6VBVUMaCDCjAKrRpAeFuHEqD1arg8OqVfKHvzGdyw9OYTABHtDwgC0kOGDJQPB6nZ6c9uevPfnoL2F6i9xea1EnptSjRqp9q+eOUgqTp7Kkh4jPKBqz2/LVpSAygfUoMH0DVoW0FAEMG0EkC4glo2AM2EICMCqwIOg+KwMPnZChFeIS0GfKuGqR5IletwFXj1js6DoO4nce5kwm3S58iuR6JGjt2KRno90evcVPUI3Tbtd0F6HfK

yykrG8ZKgAzliAKLKAC+G9HSAS1zvwwD2ubHfoYK1OQyNPeT2b3vxwwFDc+yI3HAeN2D5Tcw+s3SPgt01biD3KkxXAMokihYJ0CifLqPcBuBLR+opCK1vY0z6ONs+AVYpMtH25F9uB7xI2glXL4bDK+Iia8jXyfZFpjg4WQ4LUCuDnhsA/BD8rX0qDd9e+/fQfsP1H7j9J+0/WfhYRiwgN2+l5Tvt7AUT0BMAqmY4Cpw6DOA+wBICgFAFTCDBnAZ

sZIHCPn54jF+iWGCqvytAIVraTnPlO90kHfQAhKhCQOCMhHQjYRZ/UERADy5RE2sOBe6IkBHSdt/q7bVrKh2v6JlAaHjFfKKyc4/8RKkAZhpVw3hDDau3LeZKMMa4QDfktvRAW11EaitOuIUbrjAO45Wh+uqwjEEo395bCg+k3UPjNwj7zcIuWjAjC8WW4NQPQ1A7flt1XC8ZiU26E4Fa3XKvDNa2wUuCfQ7i6jnOxfHgWcXc78D8gKVIQTyJ9av

cMQAo44QViFEnkZByeHXMLjFxIkDciuLPKbnrCKCqqKeNPAbkzwm4c86goHgXhB57wweBJVAOkS6rGDSScPF4UYN0HDUHUMvP3KjwsHexA4wQ0IeEMiHRDYh8QlkUkJSHE9NYpPHsU2L7EZ5jc2eCnlT08HcBaeVYs6jQ38H78bqEAQYC0DTjPlZURgHRDwCaAhhJAhwYYG0DNjnhRsEvKDhPFg7ulPSy4YbCOjGxx0cGduIGrOk3RNCd2LQ6lqK

2jKxlqkcIDrEmVzE/9HhPQo3sK1YaDCzeFoi3mAL2zjC7RUA1rtMKdHwC5hbohYS0S95tFvRllP3lgID7bDAxBA/YaGOX4kCjhaFKMWCg04XCoUxlIzrQM7bMITgqE5jCCBta0J0q2wQvi5wLF3dzynnK8sEVBGBCEAbAQYH+Glw/guwBIoEaKPQAkiyR6hSkdSKuC0j6RjI5kayLMnWYORqiLkV62EEP17gR6NYtWLQoJdfhIor8lZJsl2SHU0o

tghf2cAh1oamE+jPxDub/U0INcLCduhwndCGkq+TgTNlK74cFsvQlhibw5Z0T8alo2jkxOt4sTJhnHdiXAOd4IDoBPE9snxM7L7dfRwk/0XgN2HBiiBhw7TjWJFo1g0Ga3S4cpOuEIQ8kBcLdJpMs5ncmMWfTWv1FXC7oKJJOH4VILiqcN/hxY0sRbXLF+ITgHcI9GIJikfcjJDYyVMVT+6yD3puJVQc1VxL4l2qVnbQQzFnG9UDBFJd3Ij1MFjV

RYaPSoF+J/HYA/xAEoCSBLAkQSoJzgs8a4OqroAPBspR8cdUVK+Dy8apZnil2ckQBkRffAfkPxH5j8J+U/OADPzn5pCr231JrJcyDK8Z7mROIhD0hHSq80IRXSOvkwoaFNKp9LBOvcAYbR0mGklOqQMLNGNSygXDBidaPAGk17RfU6orMOomujXezHQytI14nLD+J6An0ZzVGljcAx+AvYSGOIEasZpskvTtGH1YZRjGB7Lbg9H6i9RhsaYyaNpO

s7Z97m9wIpBQS4ExUXppfPgQ9xLFPdouIg/ka4UFEYVTiGIKJrHNDbKF4mEbdVrE2UIB0Y2/oEOkzykmlBo2UNApnDWUJSzSmGTI1nAjTrZts6agOpvmx05NMKSw7NZqWw2adNvYVgrnnAB56DA+eAvDkELxF5i8V25zfetDUBrMJ7mg2B6PxCPQpMB6rzftsazHbshu5JbWev3NULbiwhEQqITELiEJDjxqQx8Ku0ngH0hsV0H5jgSGxdxt0pKd

dm8zuDT4u4yxYlDrUOCezC24LE9rNIwDntAGl7fFpg0RBgM72hteKUplcnkiPJNIukQyKZEsiJeIDQlvg3oY3BKWHcHpE8GeGT47cdwD0sUnoaLgOkv84WbgtGy3ArGdGDga0LTJnAkUI6fJNuiXB3Mekf/LGv0NN7AD6JoA9Wa1NtHlEtZbEhUBxJ6lcSDZSA42QNNNlDT5WwBGytgOtnjSgxhAg4WGNIHOyKBYKBRG7MfAezt5vlBwuCEBr7lc

xWkp0DpNRClxchg2VCd8Mjm/DeBRY2OVdMCa3TNeOtR6dlVimnSTqGckNjkzDZxN425c6TIXJ7C59GFr8/iuCDjrB0zgZcThZSx4VLoLgMS/Of6HiUdIGFVFZJfkjT41z2F9CKitkuuBLotgGbb5NhGbm5tiA9TQuqAs7lFsp6Pcg+eW03HHzdxZ8g8ZfOSHXzxxe9UZgfRuDbofmPzOEJcGqk+jN58QA9kO2LbtM+5/SuGd+OwC/j/x9QQCcBNA

ngTIJ0xM5pMt4hRFoiZcHpCcGcX0ZUO6EBIhlCvobtFwXcbCV0L3aAKj2ECn+qC3AXAtIFYKAljAtvYQN4F6pcAPAiRBwA4AUobxJsumD/AsgFWVMnsAYCEAEAFAJoEpXq6DD6gRK4lasAgA91nE89PcPoClArYGpwiv3CIB3gegqVeKurlaI3hjC2pZKxlZSsyC1ApFUw0leSqZVUqaVrHZ0ZiuFW8rqVr+biZKp5VbMqVZsZAZ6KKDcqKViqzI

IHDQEAF5VGqidlStqATiAZU4vVSKr5WNVRxLodVeav0BKpEewQeoAphtXSrEVgLC9gCq1YurNV+gc8ECohaerQV0ChlfquZWZBwWqYSwlZFJXPkcQEoeVHZ3yQpBnl+STdHdGYTPDMQ2AONfgA6DcBKkpSiofQwJx39MV/4gwMioDAEBbYd8sEKVm9UGrMgyqpaT6CNlZDMVLIEgD9N4DWrO1xAKUAgD7b24B66sYgAojYB1Q/VuATQMEE+4jqSA

q2OJE0GJDewQhDIAABRHpWMvAbYNQB3XbrA6AAShFAWxjE6dDeGutwCbrFwe6+ZbepvW/Urgx6+tVKvNy3xtV3MTgPOXyyigyoFsKsOrF6UwLw0M6rqE+L9xEA+24GyAOGjRVJpCZ78E2DhC8F78yZEAOwEWil6N5w0TMidQgCnWga51jIbmIwFTBsBiQlaqWg/AyALAKEzqXuqbDtX1YnpdY6QSMQMDfjggtG2xmnKqZYh6YtG0jeRvwB5YqI4A

ezKKHFDhAZ6EEMCEAA==
```
%%