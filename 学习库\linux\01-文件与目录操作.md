---
tags:
  - 学习
  - linux
  - 文件操作
---

# Linux文件与目录操作

> [!info] 说明
> 本文档整理了Linux系统中文件和目录操作的常用命令，是Linux学习的基础内容。

## 基本概念

### 目录结构

| 目录 (Directory) | 英文全称/缩写 (Full Name/Abbreviation) | 主要用途说明 (Primary Purpose)                                                                    |
| :------------- | :------------------------------- | :------------------------------------------------------------------------------------------ |
| **`/`**        | Root                             | 根目录，所有文件和目录的起点。                                                                             |
| **`/bin`**     | Binaries                         | 存放所有用户（包括普通用户）都可以使用的**基本命令**，如 `ls`, `cp`, `cat`。这些命令在单用户模式下也需要用到。                          |
| **`/sbin`**    | System Binaries                  | 存放只有系统管理员 (root) 才能使用的**系统管理命令**，如 `fdisk`, `ifconfig`, `reboot`。                           |
| **`/etc`**     | Etcetera                         | 存放系统级别的**配置文件**。几乎所有系统和应用程序的配置都在这里，如 `/etc/passwd` (用户账户), `/etc/fstab` (文件系统表)。            |
| **`/dev`**     | Devices                          | 存放**设备文件**。在 Linux 中，所有设备（如硬盘、光驱、鼠标）都被视为文件，通过访问这些文件来与设备交互。                                  |
| **`/proc`**    | Processes                        | 这是一个**虚拟文件系统**，不占用磁盘空间。它存放着当前内核运行状态的特殊文件，可以查看系统和进程信息。                                       |
| **`/var`**     | Variable                         | 存放经常**变化的文件**，如日志 (`/var/log`)、邮件队列 (`/var/spool/mail`)、缓存等。                                |
| **`/tmp`**     | Temporary                        | 存放**临时文件**。所有用户都可读写，系统重启后该目录下的内容通常会被清空。                                                     |
| **`/usr`**     | Unix System Resources            | "Unix 系统资源"的缩写。这是系统中**最大的目录之一**，存放用户安装的应用程序、库文件、文档和源代码。它有自己的子目录结构，如 `/usr/bin`, `/usr/lib`。 |
| **`/home`**    | Home                             | **普通用户的家目录**。每个用户都有一个以自己用户名命名的子目录（如 `/home/<USER>
| **`/root`**    | Root                             | **超级用户 (root) 的家目录**。与普通用户的家目录分开，以保证系统在 `/home` 分区出问题时 root 仍能登录。                           |
| **`/boot`**    | Boot                             | 存放启动 Linux 系统所必需的文件，包括**内核文件 (vmlinuz)** 和**引导加载程序 (GRUB)**。                                |
| **`/lib`**     | Libraries                        | 存放系统启动和运行根文件系统中的程序（即 `/bin` 和 `/sbin` 中的命令）所必需的**共享库文件**和内核模块。                              |
| **`/opt`**     | Optional                         | 用于存放**可选的、第三方软件包**。有些商业软件或大型应用会安装在这里，如 Google Chrome。                                       |
| **`/mnt`**     | Mount                            | **临时挂载点**。早期用于让系统管理员临时手动挂载文件系统（如另一块硬盘）。                                                     |
| **`/media`**   | Media                            | **可移动设备的挂载点**。当插入 U 盘、CD-ROM 等设备时，系统通常会自动将它们挂载到此目录下的一个子目录中。                                 |
| **`/srv`**     | Service                          | 存放系统提供的某些**服务所需的数据**。例如，web 服务器的网站文件可以放在 `/srv/www` 中。                                      |

### 相对路径和绝对路径

| 比较项 | 绝对路径 (Absolute Path) | 相对路径 (Relative Path) |
| :--- | :--- | :--- |
| **符号支持** | 不使用特殊符号（总是从 `/` 开始） | 支持 `.` (当前目录)、`..` (上级目录) |
| **起点** | 根目录 `/` | 当前工作目录（`pwd` 输出的位置） |
| **是否随目录变化** | 不会变化，始终指向同一位置 | 会变化，取决于你当前所在的位置 |
| **写法示例** | `/home/<USER>/documents/file.txt` | `documents/file.txt` |
| **稳定性** | ✅ 稳定，适合脚本使用 | ❌ 不稳定，位置不同可能导致路径失效 |
| **常见用途** | 脚本、系统配置文件、定期任务 (crontab) | 交互式操作、临时命令、项目内部文件访问 |


## 📁 基础操作

### 目录浏览与导航
| 命令     | 功能              | 常用参数                                                                | 示例                                             |
| ------ | --------------- | ------------------------------------------------------------------- | ---------------------------------------------- |
| `ls`   | 列出目录内容          | `-l` 详细信息<br>`-a` 显示隐藏文件<br>`-h` 人类可读格式<br>`-t` 按时间排序<br>`-S` 按大小排序 | `ls -la`<br>`ls -lht`<br>`ls -lS`              |
| `cd`   | 切换目录            | `..` 上级目录<br>`~` 家目录<br>`-` 上次目录                                    | `cd /home/<USER>
| `pwd`  | 显示当前路径          | `-P` 显示物理路径                                                         | `pwd`<br>`pwd -P`                              |
| `tree` | 以树的形式显示当前路径下的内容 |                                                                     | `tree`                                         |

> [!tip] ls命令技巧
> - `ls -la` 显示所有文件的详细信息
> - `ls -ltr` 按时间倒序显示，最新文件在底部
> - `ls -lSr` 按大小倒序显示，最大文件在底部

## 🔨 创建与删除

### 目录操作
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `mkdir` | 创建目录 | `-p` 创建多级目录<br>`-m` 设置权限 | `mkdir -p dir1/dir2/dir3`<br>`mkdir -m 755 newdir` |
| `rmdir` | 删除空目录 | `-p` 删除多级空目录 | `rmdir empty_dir`<br>`rmdir -p dir1/dir2/dir3` |

### 文件操作
| 命令      | 功能          | 常用参数                                             | 示例                                                      |
| ------- | ----------- | ------------------------------------------------ | ------------------------------------------------------- |
| `touch` | 创建空文件/更新时间戳 | `-t` 指定时间<br>`-c` 不创建新文件                         | `touch newfile.txt`<br>`touch -t 202301011200 file.txt` |
| `rm`    | 删除文件/目录     | `-r` 递归删除<br>`-f` 强制删除<br>`-i` 交互确认<br>`-v` 显示详情 | `rm -rf directory`<br>`rm -i file.txt`<br>`rm -v *.tmp` |

> [!warning] 删除操作注意事项
> - `rm -rf` 命令非常危险，删除前请仔细确认
> - 建议使用 `rm -i` 进行交互式删除
> - 重要文件删除前先备份

## 📋 复制与移动

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `cp` | 复制文件/目录 | `-r` 递归复制<br>`-i` 交互确认<br>`-v` 显示详情<br>`-p` 保持属性<br>`-u` 仅复制新文件 | `cp -r source/ dest/`<br>`cp -p file1.txt file2.txt`<br>`cp -u *.txt backup/` |
| `mv` | 移动/重命名 | `-i` 交互确认<br>`-v` 显示详情<br>`-u` 仅移动新文件 | `mv oldname.txt newname.txt`<br>`mv file.txt /path/to/`<br>`mv -i *.log logs/` |

> [!tip] 复制技巧
> - `cp -p` 保持文件的时间戳、权限等属性
> - `cp -u` 只复制源文件比目标文件新的文件
> - `cp -r` 复制目录时必须使用递归参数

## 🔍 查找与链接

### 文件查找
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `find` | 查找文件 | `-name` 按名称<br>`-type` 按类型<br>`-mtime` 按修改时间<br>`-size` 按大小<br>`-exec` 执行命令 | `find . -name "*.txt"`<br>`find /home -type d -name "docs"`<br>`find . -size +100M`<br>`find . -name "*.log" -exec rm {} \;` |
| `locate` | 快速查找文件 | `-i` 忽略大小写<br>`-r` 正则表达式 | `locate filename`<br>`locate -i "*.PDF"` |
| `which` | 查找命令路径 | | `which python`<br>`which -a gcc` |
| `whereis` | 查找命令、源码、手册 | `-b` 只查找二进制<br>`-m` 只查找手册 | `whereis gcc`<br>`whereis -b python` |

### 文件链接
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ln` | 创建链接 | `-s` 软链接<br>`-f` 强制创建<br>`-v` 显示详情 | `ln -s /path/to/file symlink`<br>`ln file.txt hardlink`<br>`ln -sf new_target existing_link` |

> [!info] 硬链接 vs 软链接
> - **硬链接**: 指向文件数据的直接引用，删除原文件不影响硬链接
> - **软链接**: 指向文件路径的符号引用，删除原文件会导致软链接失效
> - 软链接可以跨文件系统，硬链接不可以
> - 软链接可以链接目录，硬链接不可以

## 📊 文件信息查看

| 命令     | 功能       | 常用参数                                           | 示例                                               |
| ------ | -------- | ---------------------------------------------- | ------------------------------------------------ |
| `stat` | 显示文件详细信息 | `-c` 自定义格式                                     | `stat file.txt`<br>`stat -c "%n %s %y" file.txt` |
| `file` | 识别文件类型   | `-b` 简洁输出                                      | `file document.pdf`<br>`file -b *`               |
| `du`   | 显示目录大小   | `-sh` 总大小<br>`-h` 人类可读<br>`--max-depth=1` 限制深度 | `du -sh /home/<USER>
| `df`   | 显示磁盘使用情况 | `-h` 人类可读<br>`-T` 显示文件系统类型<br>`-i` 显示inode信息   | `df -h`<br>`df -T /home`                         |
|        |          |                                                |                                                  |

## 🎯 实用技巧

### 通配符使用
| 通配符 | 含义 | 示例 |
|--------|------|------|
| `*` | 匹配任意字符 | `*.txt` 所有txt文件 |
| `?` | 匹配单个字符 | `file?.txt` 匹配file1.txt等 |
| `[]` | 匹配括号内任一字符 | `file[123].txt` 匹配file1.txt等 |
| `{}` | 匹配大括号内任一模式 | `{*.txt,*.doc}` 匹配txt和doc文件 |

### 常用组合命令
```bash
# 查找大文件（大于100MB）
find . -type f -size +100M -exec ls -lh {} \;

# 查找并删除空目录
find . -type d -empty -delete

# 复制时显示进度（需要pv命令）
tar cf - source_dir | pv | tar xf - -C dest_dir

# 批量重命名文件
for file in *.txt; do mv "$file" "${file%.txt}.bak"; done
```

> [!success] 学习建议
> 1. **多练习**: 在安全环境中反复练习这些命令
> 2. **组合使用**: 学会将多个命令组合使用提高效率
> 3. **备份习惯**: 重要操作前养成备份的习惯
> 4. **查看帮助**: 使用 `man` 命令查看详细文档

---

**相关笔记链接:**
- [[02-文件内容查看与处理]]
- [[03-系统信息与进程管理]]
- [[04-网络命令]]
