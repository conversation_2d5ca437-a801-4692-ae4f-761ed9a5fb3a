2025-08-02 07:00:58.165 [info] indexing created file 学习库/Artificial Intelligence/excalidraw/mcp.excalidraw.md  [object Object] 
2025-08-02 07:00:58.165 [info] indexing created ignore file 学习库/Artificial Intelligence/excalidraw/mcp.excalidraw.md   
2025-08-02 07:00:58.205 [info] trigger 学习库/Artificial Intelligence/excalidraw/mcp.excalidraw.md resolve  [object Object] 
2025-08-02 07:00:58.209 [info] index finished after resolve  [object Object] 
2025-08-02 07:00:58.210 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 07:00:58.214 [info] indexing created file components/logs/2025-08-02.components.log  [object Object] 
2025-08-02 07:00:58.216 [info] refresh page data from created listeners 0 1031   
2025-08-02 07:00:58.798 [debug] ignore file modify evnet 学习库/Artificial Intelligence/mcp.md   
2025-08-02 07:00:58.819 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-08-02 07:00:58.823 [info] index finished after resolve  [object Object] 
2025-08-02 07:00:58.824 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:25:58.754 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:25:58.761 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:25:58.762 [info] index finished after resolve  [object Object] 
2025-08-02 07:25:58.763 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:00.531 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:00.588 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:00.588 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:00.589 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:08.414 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:08.484 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:08.488 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:08.489 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:10.438 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:10.475 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:10.477 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:10.478 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:12.705 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:12.741 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:12.742 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:12.742 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:15.433 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:15.466 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:15.469 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:15.469 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:17.539 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:17.578 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:17.580 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:17.580 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:23.393 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:23.397 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:23.398 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:23.399 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:26.942 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:26.948 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:26.949 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:26.949 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:30.548 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:30.553 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:30.555 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:30.555 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:33.059 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:33.064 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:33.064 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:33.065 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:35.886 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:35.891 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:35.892 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:35.892 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:37.924 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:37.928 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:37.929 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:37.930 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:44.640 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:44.645 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:44.646 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:44.646 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:47.991 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:47.997 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:47.997 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:47.998 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:50.161 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:50.165 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:50.167 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:50.167 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:56.294 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:56.298 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:56.299 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:56.300 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:26:59.895 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:26:59.899 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:26:59.901 [info] index finished after resolve  [object Object] 
2025-08-02 07:26:59.901 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:02.035 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:02.042 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:02.043 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:02.044 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:04.147 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:04.151 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:04.152 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:04.153 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:06.546 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:06.550 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:06.550 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:06.550 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:09.309 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:09.314 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:09.315 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:09.315 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:11.362 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:11.366 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:11.367 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:11.367 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:27.075 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:27.080 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:27.081 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:27.081 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:29.127 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:29.131 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:29.132 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:29.133 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:34.165 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:34.172 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:34.173 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:34.173 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:36.665 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:36.671 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:36.672 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:36.673 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:43.662 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:43.666 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:43.667 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:43.668 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:46.004 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:46.009 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:46.010 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:46.011 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:49.226 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:49.231 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:49.232 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:49.232 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:51.784 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:51.790 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:51.793 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:51.794 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:54.487 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:54.492 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:54.493 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:54.494 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:27:56.941 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:27:56.946 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:27:56.946 [info] index finished after resolve  [object Object] 
2025-08-02 07:27:56.947 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:01.006 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:01.034 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:01.035 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:01.035 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:04.084 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:04.121 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:04.123 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:04.124 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:06.255 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:06.279 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:06.280 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:06.281 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:08.999 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:09.004 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:09.006 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:09.006 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:16.778 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:16.784 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:16.784 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:16.785 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:19.014 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:19.019 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:19.019 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:19.020 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:21.042 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:21.047 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:21.048 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:21.048 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:23.337 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:23.342 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:23.344 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:23.344 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:25.367 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:25.373 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:25.374 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:25.374 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:27.538 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:27.543 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:27.544 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:27.544 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:29.790 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:29.796 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:29.797 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:29.797 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:34.634 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:34.665 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:34.666 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:34.667 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:38.313 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:38.317 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:38.318 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:38.319 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:28:39.703 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:28:39.711 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:28:39.712 [info] index finished after resolve  [object Object] 
2025-08-02 07:28:39.713 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:28.344 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:28.348 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:28.349 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:28.350 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:30.376 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:30.380 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:30.380 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:30.381 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:32.467 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:32.471 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:32.473 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:32.474 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:36.733 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:36.738 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:36.739 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:36.740 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:51.545 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:51.549 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:51.550 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:51.550 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:29:59.011 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:29:59.018 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:29:59.019 [info] index finished after resolve  [object Object] 
2025-08-02 07:29:59.019 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:01.202 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:01.208 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:01.209 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:01.210 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:04.730 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:04.736 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:04.736 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:04.737 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:09.061 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:09.066 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:09.067 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:09.067 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:15.739 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:15.745 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:15.745 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:15.746 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:17.804 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:17.808 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:17.810 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:17.810 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:20.017 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:20.021 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:20.022 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:20.023 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:22.385 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:22.389 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:22.391 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:22.391 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:25.002 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:25.007 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:25.008 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:25.008 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:27.948 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:27.952 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:27.953 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:27.953 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:30.535 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:30.543 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:30.544 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:30.545 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:32.963 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:32.967 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:32.968 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:32.968 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:30:57.411 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:30:57.416 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:30:57.417 [info] index finished after resolve  [object Object] 
2025-08-02 07:30:57.418 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:09.464 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:09.519 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:09.520 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:09.521 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:17.670 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:17.675 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:17.676 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:17.676 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:30.912 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:30.918 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:30.919 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:30.919 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:35.146 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:35.166 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:35.167 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:35.168 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:42.819 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:42.824 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:42.825 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:42.825 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:31:58.522 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:31:58.527 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:31:58.528 [info] index finished after resolve  [object Object] 
2025-08-02 07:31:58.529 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:32:00.851 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:32:00.856 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:32:00.857 [info] index finished after resolve  [object Object] 
2025-08-02 07:32:00.857 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:32:17.757 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:32:17.765 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:32:17.766 [info] index finished after resolve  [object Object] 
2025-08-02 07:32:17.767 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:32:18.883 [debug] ignore file modify evnet 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-08-02 07:32:18.911 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-08-02 07:32:18.913 [info] index finished after resolve  [object Object] 
2025-08-02 07:32:18.913 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:33:43.683 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 07:33:43.690 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 07:33:43.691 [info] index finished after resolve  [object Object] 
2025-08-02 07:33:43.692 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:34:11.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 07:34:11.182 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 07:34:11.183 [info] index finished after resolve  [object Object] 
2025-08-02 07:34:11.183 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:34:13.649 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 07:34:13.655 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 07:34:13.656 [info] index finished after resolve  [object Object] 
2025-08-02 07:34:13.656 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:34:15.863 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 07:34:15.871 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 07:34:15.871 [info] index finished after resolve  [object Object] 
2025-08-02 07:34:15.872 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:35:54.610 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-02 07:35:55.012 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-02 07:35:55.030 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-02 07:35:55.035 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-02 07:35:55.039 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-02 07:37:16.825 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:37:16.838 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:37:16.840 [info] index finished after resolve  [object Object] 
2025-08-02 07:37:16.841 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:37:27.392 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:37:27.398 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:37:27.404 [info] index finished after resolve  [object Object] 
2025-08-02 07:37:27.404 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:37:34.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:37:34.644 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:37:34.647 [info] index finished after resolve  [object Object] 
2025-08-02 07:37:34.648 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:39:10.190 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:39:10.197 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:39:10.200 [info] index finished after resolve  [object Object] 
2025-08-02 07:39:10.200 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:39:10.810 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:39:10.817 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:39:10.823 [info] index finished after resolve  [object Object] 
2025-08-02 07:39:10.823 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:39:26.689 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:39:26.696 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:39:26.698 [info] index finished after resolve  [object Object] 
2025-08-02 07:39:26.699 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:39:28.534 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:39:28.543 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:39:28.546 [info] index finished after resolve  [object Object] 
2025-08-02 07:39:28.546 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:39:44.384 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:39:44.392 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:39:44.394 [info] index finished after resolve  [object Object] 
2025-08-02 07:39:44.395 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:40:35.793 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:40:35.800 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:40:35.802 [info] index finished after resolve  [object Object] 
2025-08-02 07:40:35.802 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:40:43.977 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:40:43.984 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:40:43.986 [info] index finished after resolve  [object Object] 
2025-08-02 07:40:43.987 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:40:51.578 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:40:51.585 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:40:51.592 [info] index finished after resolve  [object Object] 
2025-08-02 07:40:51.593 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:41:02.866 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:41:02.874 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:41:02.876 [info] index finished after resolve  [object Object] 
2025-08-02 07:41:02.877 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:41:19.224 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:41:19.231 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:41:19.232 [info] index finished after resolve  [object Object] 
2025-08-02 07:41:19.233 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:41:19.360 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:41:19.366 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:41:19.368 [info] index finished after resolve  [object Object] 
2025-08-02 07:41:19.369 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:41:35.259 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:41:35.268 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:41:35.270 [info] index finished after resolve  [object Object] 
2025-08-02 07:41:35.271 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:41:43.448 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:41:43.459 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:41:43.462 [info] index finished after resolve  [object Object] 
2025-08-02 07:41:43.462 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:42:14.833 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:42:14.860 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:42:14.862 [info] index finished after resolve  [object Object] 
2025-08-02 07:42:14.863 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:42:26.190 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:42:26.197 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:42:26.199 [info] index finished after resolve  [object Object] 
2025-08-02 07:42:26.200 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:42:36.790 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:42:36.798 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:42:36.800 [info] index finished after resolve  [object Object] 
2025-08-02 07:42:36.801 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:42:54.322 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:42:54.331 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:42:54.333 [info] index finished after resolve  [object Object] 
2025-08-02 07:42:54.334 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:10.369 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:10.378 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:10.385 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:10.386 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:25.399 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:25.405 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:25.407 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:25.408 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:45.172 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:45.183 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:45.186 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:45.186 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:45.369 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:45.379 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:45.381 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:45.381 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:52.462 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:52.470 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:52.472 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:52.473 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:43:58.341 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:43:58.351 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:43:58.354 [info] index finished after resolve  [object Object] 
2025-08-02 07:43:58.355 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:44:46.540 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:44:46.551 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:44:46.554 [info] index finished after resolve  [object Object] 
2025-08-02 07:44:46.554 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:45:12.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:45:12.671 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:45:12.674 [info] index finished after resolve  [object Object] 
2025-08-02 07:45:12.674 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:45:28.788 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:45:28.796 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:45:28.800 [info] index finished after resolve  [object Object] 
2025-08-02 07:45:28.801 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:45:51.985 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:45:51.992 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:45:51.998 [info] index finished after resolve  [object Object] 
2025-08-02 07:45:51.999 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:46:06.858 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:46:06.868 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:46:06.871 [info] index finished after resolve  [object Object] 
2025-08-02 07:46:06.872 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:46:20.770 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:46:20.782 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:46:20.786 [info] index finished after resolve  [object Object] 
2025-08-02 07:46:20.787 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:46:30.043 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:46:30.344 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:46:30.364 [info] index finished after resolve  [object Object] 
2025-08-02 07:46:30.366 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:46:47.291 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:46:47.299 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:46:47.301 [info] index finished after resolve  [object Object] 
2025-08-02 07:46:47.302 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:46:55.590 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:46:55.599 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:46:55.602 [info] index finished after resolve  [object Object] 
2025-08-02 07:46:55.603 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:47:03.258 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:47:03.270 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:47:03.273 [info] index finished after resolve  [object Object] 
2025-08-02 07:47:03.274 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:47:59.427 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:47:59.437 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:47:59.440 [info] index finished after resolve  [object Object] 
2025-08-02 07:47:59.440 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:48:19.508 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:48:19.515 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:48:19.517 [info] index finished after resolve  [object Object] 
2025-08-02 07:48:19.518 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:48:36.690 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:48:36.701 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:48:36.703 [info] index finished after resolve  [object Object] 
2025-08-02 07:48:36.704 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:48:52.408 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:48:52.418 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:48:52.421 [info] index finished after resolve  [object Object] 
2025-08-02 07:48:52.421 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:48:58.059 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:48:58.069 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:48:58.072 [info] index finished after resolve  [object Object] 
2025-08-02 07:48:58.072 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:49:13.842 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:49:13.852 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:49:13.854 [info] index finished after resolve  [object Object] 
2025-08-02 07:49:13.855 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:50:51.830 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:50:51.841 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:50:51.843 [info] index finished after resolve  [object Object] 
2025-08-02 07:50:51.844 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:52:44.395 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:52:44.404 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:52:44.412 [info] index finished after resolve  [object Object] 
2025-08-02 07:52:44.412 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:52:48.934 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:52:48.944 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:52:48.946 [info] index finished after resolve  [object Object] 
2025-08-02 07:52:48.947 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:53:03.813 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:53:03.821 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:53:03.828 [info] index finished after resolve  [object Object] 
2025-08-02 07:53:03.829 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:53:37.739 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:53:37.750 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:53:37.753 [info] index finished after resolve  [object Object] 
2025-08-02 07:53:37.753 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:54:32.180 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:54:32.188 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:54:32.191 [info] index finished after resolve  [object Object] 
2025-08-02 07:54:32.191 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:54:41.578 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:54:41.585 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:54:41.588 [info] index finished after resolve  [object Object] 
2025-08-02 07:54:41.589 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:55:00.856 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:55:00.866 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:55:00.869 [info] index finished after resolve  [object Object] 
2025-08-02 07:55:00.869 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:55:21.752 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:55:21.766 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:55:21.769 [info] index finished after resolve  [object Object] 
2025-08-02 07:55:21.770 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:55:33.401 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:55:33.415 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:55:33.417 [info] index finished after resolve  [object Object] 
2025-08-02 07:55:33.418 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:55:40.788 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:55:40.797 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:55:40.801 [info] index finished after resolve  [object Object] 
2025-08-02 07:55:40.801 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:55:57.173 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:55:57.187 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:55:57.190 [info] index finished after resolve  [object Object] 
2025-08-02 07:55:57.190 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:56:21.371 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:56:21.386 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:56:21.389 [info] index finished after resolve  [object Object] 
2025-08-02 07:56:21.389 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:57:01.919 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:57:01.926 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:57:01.928 [info] index finished after resolve  [object Object] 
2025-08-02 07:57:01.929 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:57:27.152 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:57:27.158 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:57:27.161 [info] index finished after resolve  [object Object] 
2025-08-02 07:57:27.162 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:57:47.570 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:57:47.577 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:57:47.581 [info] index finished after resolve  [object Object] 
2025-08-02 07:57:47.583 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:57:59.938 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:57:59.948 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:57:59.952 [info] index finished after resolve  [object Object] 
2025-08-02 07:57:59.952 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:58:16.023 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:58:16.033 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:58:16.036 [info] index finished after resolve  [object Object] 
2025-08-02 07:58:16.036 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:58:35.360 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:58:35.368 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:58:35.370 [info] index finished after resolve  [object Object] 
2025-08-02 07:58:35.371 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:58:53.522 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:58:53.534 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:58:53.537 [info] index finished after resolve  [object Object] 
2025-08-02 07:58:53.538 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:59:06.089 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:59:06.100 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:59:06.103 [info] index finished after resolve  [object Object] 
2025-08-02 07:59:06.104 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:59:21.785 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:59:21.792 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:59:21.794 [info] index finished after resolve  [object Object] 
2025-08-02 07:59:21.794 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 07:59:28.891 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 07:59:28.906 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 07:59:28.909 [info] index finished after resolve  [object Object] 
2025-08-02 07:59:28.910 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:05.704 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:05.801 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:05.804 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:05.805 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:17.522 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:17.609 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:17.612 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:17.613 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:19.652 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:19.659 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:19.660 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:19.660 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:22.885 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:22.896 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:22.897 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:22.898 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:31.409 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:31.501 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:31.504 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:31.505 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:35.140 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:35.148 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:35.149 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:35.150 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:37.390 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:37.397 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:37.398 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:37.398 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:39.527 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:39.534 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:39.535 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:39.536 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:41.592 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:41.604 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:41.605 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:41.606 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:43.871 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:43.878 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:43.878 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:43.879 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:46.005 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:46.012 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:46.013 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:46.014 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:49.126 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:49.135 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:49.135 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:49.136 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:52.010 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:52.022 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:52.023 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:52.024 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:55.120 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:55.127 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:55.128 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:55.128 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:00:57.563 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:00:57.572 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:00:57.573 [info] index finished after resolve  [object Object] 
2025-08-02 08:00:57.574 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:01.647 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:01.656 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:01.657 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:01.657 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:03.887 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:03.895 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:03.896 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:03.896 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:06.325 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:06.338 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:06.341 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:06.342 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:08.579 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:08.587 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:08.588 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:08.589 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:10.855 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:10.863 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:10.864 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:10.865 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:13.132 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:13.141 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:13.142 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:13.143 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:15.106 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:15.112 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:15.113 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:15.113 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:22.474 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:22.482 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:22.482 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:22.483 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:24.862 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:24.871 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:24.873 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:24.873 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:27.521 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:27.529 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:27.529 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:27.530 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:29.872 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:29.880 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:29.881 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:29.881 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:32.709 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:32.716 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:32.721 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:32.721 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:39.430 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:39.437 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:39.438 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:39.439 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:41.494 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:41.502 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:41.503 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:41.503 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:43.598 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:43.608 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:43.608 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:43.609 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:48.443 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:48.449 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:48.450 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:48.451 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:50.793 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:50.800 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:50.806 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:50.807 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:01:53.002 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:01:53.010 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:01:53.011 [info] index finished after resolve  [object Object] 
2025-08-02 08:01:53.012 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:09.828 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:09.839 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:09.840 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:09.841 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:12.166 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:12.175 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:12.176 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:12.176 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:15.412 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:15.423 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:15.424 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:15.425 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:17.472 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:17.479 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:17.480 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:17.480 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:19.509 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:19.517 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:19.523 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:19.523 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:27.874 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:27.881 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:27.882 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:27.883 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:29.943 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:29.950 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:29.955 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:29.956 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:33.847 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:33.857 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:33.859 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:33.859 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:35.916 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:35.924 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:35.925 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:35.925 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:02:43.191 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:02:43.283 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:02:43.286 [info] index finished after resolve  [object Object] 
2025-08-02 08:02:43.286 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:09.583 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:09.590 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:09.591 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:09.591 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:12.378 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:12.537 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:12.671 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:12.672 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:25.596 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:25.609 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:25.610 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:25.610 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:29.716 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:29.724 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:29.725 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:29.725 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:33.955 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-02 08:03:33.988 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-02 08:03:33.993 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-02 08:03:33.996 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-02 08:03:34.000 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-02 08:03:34.307 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:34.312 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:34.313 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:34.314 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:40.148 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:40.157 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:40.157 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:40.158 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:45.086 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:45.095 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:45.096 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:45.097 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:03:58.822 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:03:58.830 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:03:58.830 [info] index finished after resolve  [object Object] 
2025-08-02 08:03:58.831 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:00.883 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:00.893 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:00.894 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:00.894 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:07.283 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:07.292 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:07.292 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:07.293 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:31.996 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:32.041 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:32.043 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:32.043 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:36.075 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:36.082 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:36.083 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:36.084 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:43.242 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:43.352 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:43.355 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:43.356 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:46.548 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:46.561 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:46.562 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:46.563 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:48.620 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:48.629 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:48.629 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:48.630 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:50.706 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:50.713 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:50.715 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:50.716 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:53.314 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:53.323 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:53.325 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:53.325 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:04:59.828 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:04:59.836 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:04:59.837 [info] index finished after resolve  [object Object] 
2025-08-02 08:04:59.838 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:05:02.874 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:05:02.882 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:05:02.882 [info] index finished after resolve  [object Object] 
2025-08-02 08:05:02.883 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:05:19.316 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:05:19.326 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:05:19.327 [info] index finished after resolve  [object Object] 
2025-08-02 08:05:19.328 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:05:26.136 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:05:26.146 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:05:26.147 [info] index finished after resolve  [object Object] 
2025-08-02 08:05:26.147 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:05:55.094 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:05:55.102 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:05:55.105 [info] index finished after resolve  [object Object] 
2025-08-02 08:05:55.106 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:06:10.133 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:06:10.144 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:06:10.146 [info] index finished after resolve  [object Object] 
2025-08-02 08:06:10.147 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:07:12.079 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:07:12.087 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:07:12.090 [info] index finished after resolve  [object Object] 
2025-08-02 08:07:12.090 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:07:27.532 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:07:27.539 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:07:27.542 [info] index finished after resolve  [object Object] 
2025-08-02 08:07:27.543 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:03.002 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:03.053 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:03.054 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:03.055 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:06.113 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:06.120 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:06.121 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:06.122 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:17.070 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:17.079 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:17.081 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:17.081 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:19.521 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:19.529 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:19.530 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:19.531 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:22.199 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:22.206 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:22.206 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:22.207 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:25.596 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:25.603 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:25.604 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:25.604 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:27.627 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:27.634 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:27.634 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:27.635 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:29.706 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:29.714 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:29.715 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:29.716 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:52.780 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:52.787 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:52.788 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:52.788 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:55.374 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:55.382 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:55.383 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:55.383 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:08:59.787 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:08:59.816 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:08:59.817 [info] index finished after resolve  [object Object] 
2025-08-02 08:08:59.818 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:10:25.197 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:10:25.354 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:10:25.356 [info] index finished after resolve  [object Object] 
2025-08-02 08:10:25.357 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:10:43.192 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:10:43.308 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:10:43.310 [info] index finished after resolve  [object Object] 
2025-08-02 08:10:43.311 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:10:45.905 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:10:45.922 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:10:45.924 [info] index finished after resolve  [object Object] 
2025-08-02 08:10:45.925 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:10:53.384 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:10:53.392 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:10:53.393 [info] index finished after resolve  [object Object] 
2025-08-02 08:10:53.393 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:03.818 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:03.858 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:03.858 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:03.859 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:08.919 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:09.018 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:09.020 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:09.020 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:26.025 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:26.034 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:26.035 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:26.035 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:28.384 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:28.390 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:28.391 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:28.392 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:53.923 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:53.938 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:53.939 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:53.940 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:11:56.029 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:11:56.137 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:11:56.138 [info] index finished after resolve  [object Object] 
2025-08-02 08:11:56.139 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:13:22.710 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:13:22.720 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:13:22.722 [info] index finished after resolve  [object Object] 
2025-08-02 08:13:22.723 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:13:41.415 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:13:41.423 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:13:41.426 [info] index finished after resolve  [object Object] 
2025-08-02 08:13:41.427 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:13:57.465 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:13:57.488 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:13:57.491 [info] index finished after resolve  [object Object] 
2025-08-02 08:13:57.491 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:14:26.271 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:14:26.326 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:14:26.328 [info] index finished after resolve  [object Object] 
2025-08-02 08:14:26.328 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:14:28.505 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:14:28.584 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:14:28.588 [info] index finished after resolve  [object Object] 
2025-08-02 08:14:28.589 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:14:31.264 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:14:31.331 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:14:31.332 [info] index finished after resolve  [object Object] 
2025-08-02 08:14:31.333 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:14:40.873 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:14:41.060 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:14:41.064 [info] index finished after resolve  [object Object] 
2025-08-02 08:14:41.064 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:17:36.529 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:17:36.540 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:17:36.543 [info] index finished after resolve  [object Object] 
2025-08-02 08:17:36.544 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:19:39.908 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:19:39.916 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:19:39.918 [info] index finished after resolve  [object Object] 
2025-08-02 08:19:39.919 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:19:48.120 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:19:48.129 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:19:48.132 [info] index finished after resolve  [object Object] 
2025-08-02 08:19:48.133 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:20:05.501 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:20:05.511 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:20:05.514 [info] index finished after resolve  [object Object] 
2025-08-02 08:20:05.515 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:20:32.201 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:20:32.211 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:20:32.214 [info] index finished after resolve  [object Object] 
2025-08-02 08:20:32.214 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:20:33.232 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:20:33.246 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:20:33.250 [info] index finished after resolve  [object Object] 
2025-08-02 08:20:33.250 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:20:49.204 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:20:49.213 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:20:49.216 [info] index finished after resolve  [object Object] 
2025-08-02 08:20:49.217 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:21:12.348 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:21:12.356 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:21:12.359 [info] index finished after resolve  [object Object] 
2025-08-02 08:21:12.360 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:21:17.707 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:21:17.716 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:21:17.719 [info] index finished after resolve  [object Object] 
2025-08-02 08:21:17.720 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:21:33.555 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:21:33.566 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:21:33.570 [info] index finished after resolve  [object Object] 
2025-08-02 08:21:33.570 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:21:43.343 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:21:43.351 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:21:43.356 [info] index finished after resolve  [object Object] 
2025-08-02 08:21:43.356 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:22:13.307 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:22:13.316 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:22:13.320 [info] index finished after resolve  [object Object] 
2025-08-02 08:22:13.320 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:22:29.040 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:22:29.049 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:22:29.053 [info] index finished after resolve  [object Object] 
2025-08-02 08:22:29.054 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:22:45.203 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:22:45.215 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:22:45.219 [info] index finished after resolve  [object Object] 
2025-08-02 08:22:45.220 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:22:49.234 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:22:49.242 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:22:49.252 [info] index finished after resolve  [object Object] 
2025-08-02 08:22:49.253 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:23:09.001 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:23:09.010 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:23:09.019 [info] index finished after resolve  [object Object] 
2025-08-02 08:23:09.019 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:23:13.010 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:23:13.100 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:23:14.624 [info] index finished after resolve  [object Object] 
2025-08-02 08:23:14.625 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:23:31.705 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:23:31.716 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:23:31.720 [info] index finished after resolve  [object Object] 
2025-08-02 08:23:31.721 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:23:45.138 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:23:45.150 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:23:45.153 [info] index finished after resolve  [object Object] 
2025-08-02 08:23:45.154 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:23:51.191 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:23:51.255 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:23:51.255 [info] index finished after resolve  [object Object] 
2025-08-02 08:23:51.256 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:01.723 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:01.832 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:01.834 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:01.834 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:05.693 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:05.703 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:05.704 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:05.705 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:34.136 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:34.144 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:34.145 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:34.146 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:37.439 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:37.456 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:37.458 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:37.459 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:39.499 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:39.506 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:39.507 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:39.508 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:41.764 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:41.773 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:41.775 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:41.776 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:43.894 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:43.902 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:43.903 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:43.903 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:46.142 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:46.151 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:46.152 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:46.152 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:24:50.795 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:24:50.803 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:24:50.804 [info] index finished after resolve  [object Object] 
2025-08-02 08:24:50.804 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:02.885 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:02.893 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:02.894 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:02.894 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:05.110 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:05.119 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:05.120 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:05.121 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:07.221 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:07.234 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:07.237 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:07.237 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:10.054 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:10.062 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:10.062 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:10.063 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:12.215 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:12.222 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:12.223 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:12.224 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:14.786 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:14.794 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:14.795 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:14.796 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:16.838 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:16.846 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:16.854 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:16.855 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:25.485 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:25.493 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:25.494 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:25.495 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:30.200 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:30.353 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:30.355 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:30.356 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:32.306 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:32.437 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:32.439 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:32.439 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:25:49.108 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:25:49.208 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:25:49.211 [info] index finished after resolve  [object Object] 
2025-08-02 08:25:49.211 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:12.120 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:12.276 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:12.279 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:12.280 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:14.257 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:14.324 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:14.329 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:14.330 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:25.646 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:25.700 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:25.702 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:25.703 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:38.155 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:38.267 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:38.281 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:38.281 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:41.088 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:41.156 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:41.158 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:41.159 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:43.681 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:44.103 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:44.146 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:44.147 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:51.374 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:51.502 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:51.505 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:51.505 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:26:53.603 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:26:53.734 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:26:53.736 [info] index finished after resolve  [object Object] 
2025-08-02 08:26:53.737 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:27:23.817 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:27:23.877 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:27:25.151 [info] index finished after resolve  [object Object] 
2025-08-02 08:27:25.152 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:27:43.258 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:27:43.309 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:27:43.376 [info] index finished after resolve  [object Object] 
2025-08-02 08:27:43.377 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:27:57.056 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:27:57.104 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:27:57.172 [info] index finished after resolve  [object Object] 
2025-08-02 08:27:57.173 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:28:56.349 [info] refresh page data from delete listeners 0 1030   
2025-08-02 08:28:56.382 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:28:58.057 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 08:28:58.352 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 08:28:58.354 [info] index finished after resolve  [object Object] 
2025-08-02 08:28:58.355 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:31:14.274 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:31:14.283 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:31:14.286 [info] index finished after resolve  [object Object] 
2025-08-02 08:31:14.287 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:31:31.538 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:31:31.546 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:31:31.549 [info] index finished after resolve  [object Object] 
2025-08-02 08:31:31.550 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:31:41.806 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:31:41.817 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:31:41.822 [info] index finished after resolve  [object Object] 
2025-08-02 08:31:41.822 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:32:04.538 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:32:04.546 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:32:04.549 [info] index finished after resolve  [object Object] 
2025-08-02 08:32:04.550 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:32:47.825 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:32:47.832 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:32:47.836 [info] index finished after resolve  [object Object] 
2025-08-02 08:32:47.837 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:33:04.973 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:33:04.981 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:33:04.985 [info] index finished after resolve  [object Object] 
2025-08-02 08:33:04.986 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:35:39.454 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:35:39.507 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:35:39.857 [info] index finished after resolve  [object Object] 
2025-08-02 08:35:39.858 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:38:34.681 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:38:34.725 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:38:35.343 [info] index finished after resolve  [object Object] 
2025-08-02 08:38:35.344 [info] refresh page data from resolve listeners 0 1030   
2025-08-02 08:46:55.498 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802084655_381.svg  [object Object] 
2025-08-02 08:46:55.508 [info] refresh page data from created listeners 0 1031   
2025-08-02 08:46:55.531 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:46:55.574 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:46:55.592 [info] index finished after resolve  [object Object] 
2025-08-02 08:46:55.593 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:47:10.601 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:47:10.612 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:47:10.615 [info] index finished after resolve  [object Object] 
2025-08-02 08:47:10.615 [info] refresh page data from resolve listeners 0 1031   
2025-08-02 08:50:36.886 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802085036_878.svg  [object Object] 
2025-08-02 08:50:36.894 [info] refresh page data from created listeners 0 1032   
2025-08-02 08:50:36.920 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:50:36.927 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:50:36.932 [info] index finished after resolve  [object Object] 
2025-08-02 08:50:36.932 [info] refresh page data from resolve listeners 0 1032   
2025-08-02 08:50:39.239 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:50:39.249 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:50:39.253 [info] index finished after resolve  [object Object] 
2025-08-02 08:50:39.254 [info] refresh page data from resolve listeners 0 1032   
2025-08-02 08:51:00.035 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:51:00.084 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:51:00.091 [info] index finished after resolve  [object Object] 
2025-08-02 08:51:00.092 [info] refresh page data from resolve listeners 0 1032   
2025-08-02 08:51:53.591 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:51:53.601 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:51:53.604 [info] index finished after resolve  [object Object] 
2025-08-02 08:51:53.605 [info] refresh page data from resolve listeners 0 1032   
2025-08-02 08:51:59.099 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:51:59.140 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:51:59.149 [info] index finished after resolve  [object Object] 
2025-08-02 08:51:59.150 [info] refresh page data from resolve listeners 0 1032   
2025-08-02 08:52:30.755 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802085230_748.svg  [object Object] 
2025-08-02 08:52:30.763 [info] refresh page data from created listeners 0 1033   
2025-08-02 08:52:30.784 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:52:30.792 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:52:30.796 [info] index finished after resolve  [object Object] 
2025-08-02 08:52:30.796 [info] refresh page data from resolve listeners 0 1033   
2025-08-02 08:52:45.844 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:52:45.855 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:52:45.858 [info] index finished after resolve  [object Object] 
2025-08-02 08:52:45.859 [info] refresh page data from resolve listeners 0 1033   
2025-08-02 08:54:21.695 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802085421_679.png  [object Object] 
2025-08-02 08:54:21.710 [info] refresh page data from created listeners 0 1034   
2025-08-02 08:54:21.731 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:54:21.740 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:54:21.744 [info] index finished after resolve  [object Object] 
2025-08-02 08:54:21.744 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:54:28.251 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:54:28.261 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:54:28.265 [info] index finished after resolve  [object Object] 
2025-08-02 08:54:28.265 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:54:42.253 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:54:42.264 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:54:42.269 [info] index finished after resolve  [object Object] 
2025-08-02 08:54:42.269 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:55:03.541 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:55:03.549 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:55:03.554 [info] index finished after resolve  [object Object] 
2025-08-02 08:55:03.555 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:55:04.892 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:55:04.901 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:55:04.906 [info] index finished after resolve  [object Object] 
2025-08-02 08:55:04.907 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:55:21.350 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:55:21.359 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:55:21.363 [info] index finished after resolve  [object Object] 
2025-08-02 08:55:21.363 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:55:36.616 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:55:36.625 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:55:36.629 [info] index finished after resolve  [object Object] 
2025-08-02 08:55:36.629 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:56:36.436 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:56:36.447 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:56:36.451 [info] index finished after resolve  [object Object] 
2025-08-02 08:56:36.452 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:56:52.710 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:56:52.717 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:56:52.721 [info] index finished after resolve  [object Object] 
2025-08-02 08:56:52.722 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:57:15.808 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:57:15.816 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:57:15.819 [info] index finished after resolve  [object Object] 
2025-08-02 08:57:15.820 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:57:31.004 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:57:31.013 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:57:31.017 [info] index finished after resolve  [object Object] 
2025-08-02 08:57:31.017 [info] refresh page data from resolve listeners 0 1034   
2025-08-02 08:59:52.110 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/attachments/Pasted Image 20250802085952_097.svg  [object Object] 
2025-08-02 08:59:52.121 [info] refresh page data from created listeners 0 1035   
2025-08-02 08:59:52.144 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:59:52.152 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:59:52.155 [info] index finished after resolve  [object Object] 
2025-08-02 08:59:52.156 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 08:59:56.378 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 08:59:56.386 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 08:59:56.390 [info] index finished after resolve  [object Object] 
2025-08-02 08:59:56.390 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:00:07.791 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:00:07.802 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:00:07.806 [info] index finished after resolve  [object Object] 
2025-08-02 09:00:07.807 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:00:22.859 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:00:22.868 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:00:22.872 [info] index finished after resolve  [object Object] 
2025-08-02 09:00:22.872 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:01:25.037 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:01:25.050 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:01:25.053 [info] index finished after resolve  [object Object] 
2025-08-02 09:01:25.053 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:01:29.515 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:01:29.521 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:01:29.522 [info] index finished after resolve  [object Object] 
2025-08-02 09:01:29.522 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:02:28.015 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:02:28.023 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:02:28.026 [info] index finished after resolve  [object Object] 
2025-08-02 09:02:28.027 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:02:59.343 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:02:59.351 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:02:59.355 [info] index finished after resolve  [object Object] 
2025-08-02 09:02:59.355 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:07:18.827 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:07:18.836 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:07:18.840 [info] index finished after resolve  [object Object] 
2025-08-02 09:07:18.840 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:07:41.928 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:07:41.935 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:07:41.937 [info] index finished after resolve  [object Object] 
2025-08-02 09:07:41.938 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:07:45.263 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:07:45.272 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:07:45.273 [info] index finished after resolve  [object Object] 
2025-08-02 09:07:45.273 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:00.531 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:00.614 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:00.623 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:00.623 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:03.592 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:03.667 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:03.672 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:03.672 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:07.494 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:07.562 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:07.599 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:07.599 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:11.046 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:11.058 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:11.060 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:11.061 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:13.162 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:13.219 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:13.222 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:13.222 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:08:15.392 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:08:15.406 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:08:15.407 [info] index finished after resolve  [object Object] 
2025-08-02 09:08:15.408 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:09:11.195 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:09:11.202 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:09:11.203 [info] index finished after resolve  [object Object] 
2025-08-02 09:09:11.203 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:09:40.886 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:09:41.123 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:09:41.128 [info] index finished after resolve  [object Object] 
2025-08-02 09:09:41.129 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:13:05.894 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:13:05.904 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:13:05.908 [info] index finished after resolve  [object Object] 
2025-08-02 09:13:05.909 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:13:22.481 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:13:22.490 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:13:22.498 [info] index finished after resolve  [object Object] 
2025-08-02 09:13:22.498 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:13:39.349 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:13:39.365 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:13:39.369 [info] index finished after resolve  [object Object] 
2025-08-02 09:13:39.370 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:13:54.413 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:13:54.422 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:13:54.426 [info] index finished after resolve  [object Object] 
2025-08-02 09:13:54.427 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:14:15.776 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:14:15.784 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:14:15.788 [info] index finished after resolve  [object Object] 
2025-08-02 09:14:15.789 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:14:20.992 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:14:21.003 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:14:21.007 [info] index finished after resolve  [object Object] 
2025-08-02 09:14:21.008 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:14:36.872 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:14:36.886 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:14:36.890 [info] index finished after resolve  [object Object] 
2025-08-02 09:14:36.891 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:14:52.013 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:14:52.021 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:14:52.025 [info] index finished after resolve  [object Object] 
2025-08-02 09:14:52.026 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:15:08.973 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:15:08.984 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:15:08.991 [info] index finished after resolve  [object Object] 
2025-08-02 09:15:08.992 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:15:15.237 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:15:15.247 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:15:15.252 [info] index finished after resolve  [object Object] 
2025-08-02 09:15:15.252 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:15:30.885 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:15:30.894 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:15:30.901 [info] index finished after resolve  [object Object] 
2025-08-02 09:15:30.902 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:15:46.663 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:15:46.671 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:15:46.675 [info] index finished after resolve  [object Object] 
2025-08-02 09:15:46.675 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:16:01.995 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:16:02.005 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:16:02.010 [info] index finished after resolve  [object Object] 
2025-08-02 09:16:02.010 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:17:09.341 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:17:09.443 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:17:09.444 [info] index finished after resolve  [object Object] 
2025-08-02 09:17:09.445 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:17:16.748 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:17:16.861 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:17:16.892 [info] index finished after resolve  [object Object] 
2025-08-02 09:17:16.894 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:16.853 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:16.860 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:16.860 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:16.861 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:24.001 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:24.009 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:24.010 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:24.010 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:29.319 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:29.326 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:29.327 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:29.327 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:32.543 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:32.553 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:32.553 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:32.554 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:34.535 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:34.541 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:34.542 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:34.542 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:36.652 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:36.658 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:36.659 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:36.659 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:40.061 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:40.069 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:40.069 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:40.070 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:43.049 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:43.056 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:43.057 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:43.057 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:45.559 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:45.568 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:45.569 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:45.569 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:47.913 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:47.921 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:47.922 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:47.923 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:18:50.126 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:18:50.133 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:18:50.135 [info] index finished after resolve  [object Object] 
2025-08-02 09:18:50.136 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:19:02.342 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:19:02.350 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:19:02.352 [info] index finished after resolve  [object Object] 
2025-08-02 09:19:02.352 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:19:39.622 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:19:39.634 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:19:39.638 [info] index finished after resolve  [object Object] 
2025-08-02 09:19:39.638 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:19:55.625 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:19:55.633 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:19:55.638 [info] index finished after resolve  [object Object] 
2025-08-02 09:19:55.638 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:20:12.773 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:20:12.784 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:20:12.790 [info] index finished after resolve  [object Object] 
2025-08-02 09:20:12.791 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:20:24.508 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:20:24.574 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:20:26.018 [info] index finished after resolve  [object Object] 
2025-08-02 09:20:26.019 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:20:26.615 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:20:26.669 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:20:27.786 [info] index finished after resolve  [object Object] 
2025-08-02 09:20:27.787 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:20:33.447 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:20:33.505 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:20:35.129 [info] index finished after resolve  [object Object] 
2025-08-02 09:20:35.130 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:20:45.202 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:20:45.251 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:20:47.340 [info] index finished after resolve  [object Object] 
2025-08-02 09:20:47.340 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:22:52.040 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:22:52.115 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:22:53.198 [info] index finished after resolve  [object Object] 
2025-08-02 09:22:53.199 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:07.763 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:07.772 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:07.777 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:07.777 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:14.677 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:14.685 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:14.689 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:14.690 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:18.359 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:18.368 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:18.373 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:18.374 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:34.128 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:34.137 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:34.142 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:34.143 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:34.536 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:34.548 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:34.552 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:34.552 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:50.232 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:50.241 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:50.245 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:50.246 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:23:52.983 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:23:53.036 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:23:54.212 [info] index finished after resolve  [object Object] 
2025-08-02 09:23:54.213 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:24:11.243 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:24:11.253 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:24:11.257 [info] index finished after resolve  [object Object] 
2025-08-02 09:24:11.258 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:24:26.352 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:24:26.362 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:24:26.366 [info] index finished after resolve  [object Object] 
2025-08-02 09:24:26.367 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:24:29.131 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:24:29.145 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:24:29.149 [info] index finished after resolve  [object Object] 
2025-08-02 09:24:29.150 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:24:44.911 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:24:44.921 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:24:44.925 [info] index finished after resolve  [object Object] 
2025-08-02 09:24:44.926 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:24:48.688 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:24:48.697 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:24:48.697 [info] index finished after resolve  [object Object] 
2025-08-02 09:24:48.698 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:02.176 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:02.187 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:02.191 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:02.192 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:03.953 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:03.968 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:03.973 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:03.974 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:18.817 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:18.863 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:19.350 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:19.350 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:39.422 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:39.481 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:40.430 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:40.430 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:44.231 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:44.278 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:45.148 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:45.149 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:25:52.347 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:25:52.356 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:25:52.362 [info] index finished after resolve  [object Object] 
2025-08-02 09:25:52.363 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:26:25.417 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:26:25.431 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:26:25.435 [info] index finished after resolve  [object Object] 
2025-08-02 09:26:25.436 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:29:30.504 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:29:30.513 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:29:30.518 [info] index finished after resolve  [object Object] 
2025-08-02 09:29:30.519 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:29:53.171 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:29:53.181 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:29:53.185 [info] index finished after resolve  [object Object] 
2025-08-02 09:29:53.186 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:30:15.566 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:30:15.574 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:30:15.578 [info] index finished after resolve  [object Object] 
2025-08-02 09:30:15.579 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:30:41.084 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:30:41.092 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:30:41.096 [info] index finished after resolve  [object Object] 
2025-08-02 09:30:41.097 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:31:04.453 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:31:04.504 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:31:05.380 [info] index finished after resolve  [object Object] 
2025-08-02 09:31:05.380 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:31:48.880 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:31:48.894 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:31:48.898 [info] index finished after resolve  [object Object] 
2025-08-02 09:31:48.900 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:32:10.568 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:32:10.576 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:32:10.580 [info] index finished after resolve  [object Object] 
2025-08-02 09:32:10.580 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:32:52.990 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:32:53.152 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:32:53.156 [info] index finished after resolve  [object Object] 
2025-08-02 09:32:53.157 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:32:57.681 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:32:57.690 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:32:57.690 [info] index finished after resolve  [object Object] 
2025-08-02 09:32:57.691 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:33:13.516 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:33:13.708 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:33:13.711 [info] index finished after resolve  [object Object] 
2025-08-02 09:33:13.712 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:35:13.993 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:35:14.209 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:35:14.212 [info] index finished after resolve  [object Object] 
2025-08-02 09:35:14.213 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:35:18.342 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:35:18.349 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:35:18.350 [info] index finished after resolve  [object Object] 
2025-08-02 09:35:18.351 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:35:40.875 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:35:41.037 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:35:41.039 [info] index finished after resolve  [object Object] 
2025-08-02 09:35:41.040 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:36:12.320 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:36:12.451 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:36:12.461 [info] index finished after resolve  [object Object] 
2025-08-02 09:36:12.461 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:36:40.345 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:36:40.479 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:36:40.486 [info] index finished after resolve  [object Object] 
2025-08-02 09:36:40.487 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:36:57.021 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:36:57.031 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:36:57.032 [info] index finished after resolve  [object Object] 
2025-08-02 09:36:57.033 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:37:53.010 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:37:53.019 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:37:53.023 [info] index finished after resolve  [object Object] 
2025-08-02 09:37:53.024 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:38:00.296 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:38:00.303 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:38:00.307 [info] index finished after resolve  [object Object] 
2025-08-02 09:38:00.308 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:38:29.450 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:38:29.460 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:38:29.465 [info] index finished after resolve  [object Object] 
2025-08-02 09:38:29.466 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:38:53.432 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:38:53.444 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:38:53.451 [info] index finished after resolve  [object Object] 
2025-08-02 09:38:53.452 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:39:07.236 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:39:07.244 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:39:07.248 [info] index finished after resolve  [object Object] 
2025-08-02 09:39:07.249 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:39:12.979 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:39:12.988 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:39:12.992 [info] index finished after resolve  [object Object] 
2025-08-02 09:39:12.993 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:39:24.340 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:39:24.353 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:39:24.358 [info] index finished after resolve  [object Object] 
2025-08-02 09:39:24.359 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:39:40.328 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:39:40.336 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:39:40.340 [info] index finished after resolve  [object Object] 
2025-08-02 09:39:40.341 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:41:09.452 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:41:09.464 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:41:09.468 [info] index finished after resolve  [object Object] 
2025-08-02 09:41:09.469 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:41:19.401 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:41:19.416 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:41:19.420 [info] index finished after resolve  [object Object] 
2025-08-02 09:41:19.420 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:41:32.490 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:41:32.500 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:41:32.505 [info] index finished after resolve  [object Object] 
2025-08-02 09:41:32.506 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:42:02.068 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:42:02.076 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:42:02.081 [info] index finished after resolve  [object Object] 
2025-08-02 09:42:02.081 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:42:13.975 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:42:13.983 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:42:13.987 [info] index finished after resolve  [object Object] 
2025-08-02 09:42:13.988 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:42:21.319 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:42:21.327 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:42:21.334 [info] index finished after resolve  [object Object] 
2025-08-02 09:42:21.334 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:42:50.803 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:42:50.861 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:42:51.943 [info] index finished after resolve  [object Object] 
2025-08-02 09:42:51.944 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:44:12.794 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:44:12.802 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:44:12.807 [info] index finished after resolve  [object Object] 
2025-08-02 09:44:12.807 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:44:20.692 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:44:20.734 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:44:20.748 [info] index finished after resolve  [object Object] 
2025-08-02 09:44:20.748 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:48:31.422 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:48:31.430 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:48:31.433 [info] index finished after resolve  [object Object] 
2025-08-02 09:48:31.433 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:49:07.046 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:49:07.057 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:49:07.062 [info] index finished after resolve  [object Object] 
2025-08-02 09:49:07.062 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:49:22.700 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:49:22.709 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:49:22.714 [info] index finished after resolve  [object Object] 
2025-08-02 09:49:22.715 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:49:39.544 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:49:39.554 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:49:39.561 [info] index finished after resolve  [object Object] 
2025-08-02 09:49:39.562 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:49:55.287 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:49:55.295 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:49:55.298 [info] index finished after resolve  [object Object] 
2025-08-02 09:49:55.299 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:50:12.395 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:50:12.406 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:50:12.410 [info] index finished after resolve  [object Object] 
2025-08-02 09:50:12.411 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:50:36.055 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:50:36.065 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:50:36.069 [info] index finished after resolve  [object Object] 
2025-08-02 09:50:36.070 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:50:44.633 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:50:44.642 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:50:44.646 [info] index finished after resolve  [object Object] 
2025-08-02 09:50:44.647 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:51:00.675 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:51:00.683 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:51:00.687 [info] index finished after resolve  [object Object] 
2025-08-02 09:51:00.687 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:51:05.042 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:51:05.087 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:51:06.069 [info] index finished after resolve  [object Object] 
2025-08-02 09:51:06.070 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:54:01.295 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:54:01.485 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:54:01.488 [info] index finished after resolve  [object Object] 
2025-08-02 09:54:01.490 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:54:13.803 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:54:13.939 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:54:13.941 [info] index finished after resolve  [object Object] 
2025-08-02 09:54:13.941 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:54:21.773 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:54:21.908 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:54:21.909 [info] index finished after resolve  [object Object] 
2025-08-02 09:54:21.910 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:54:25.193 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:54:25.201 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:54:25.204 [info] index finished after resolve  [object Object] 
2025-08-02 09:54:25.204 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:55:13.267 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:55:13.375 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:55:13.384 [info] index finished after resolve  [object Object] 
2025-08-02 09:55:13.385 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:55:18.202 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:55:18.332 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:55:18.336 [info] index finished after resolve  [object Object] 
2025-08-02 09:55:18.337 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:55:21.979 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:55:22.096 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:55:22.098 [info] index finished after resolve  [object Object] 
2025-08-02 09:55:22.099 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:55:24.090 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:55:24.099 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:55:24.100 [info] index finished after resolve  [object Object] 
2025-08-02 09:55:24.101 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:55:45.039 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:55:45.047 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:55:45.048 [info] index finished after resolve  [object Object] 
2025-08-02 09:55:45.049 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:57:02.853 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:57:02.860 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:57:02.863 [info] index finished after resolve  [object Object] 
2025-08-02 09:57:02.864 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:57:06.518 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:57:06.526 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:57:06.527 [info] index finished after resolve  [object Object] 
2025-08-02 09:57:06.527 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:57:14.010 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:57:14.219 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:57:14.221 [info] index finished after resolve  [object Object] 
2025-08-02 09:57:14.222 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:57:46.475 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:57:46.482 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:57:46.485 [info] index finished after resolve  [object Object] 
2025-08-02 09:57:46.485 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:58:04.709 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:58:04.717 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:58:04.719 [info] index finished after resolve  [object Object] 
2025-08-02 09:58:04.719 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:58:08.220 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:58:08.223 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:58:08.225 [info] index finished after resolve  [object Object] 
2025-08-02 09:58:08.225 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:58:15.290 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:58:15.298 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:58:15.298 [info] index finished after resolve  [object Object] 
2025-08-02 09:58:15.299 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:59:16.155 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:59:16.163 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:59:16.164 [info] index finished after resolve  [object Object] 
2025-08-02 09:59:16.165 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:59:18.262 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:59:18.269 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:59:18.271 [info] index finished after resolve  [object Object] 
2025-08-02 09:59:18.272 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:59:20.811 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 09:59:20.820 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 09:59:20.821 [info] index finished after resolve  [object Object] 
2025-08-02 09:59:20.821 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 09:59:56.351 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-02 09:59:56.386 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-02 09:59:56.398 [info] index finished after resolve  [object Object] 
2025-08-02 09:59:56.399 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:15.134 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:15.143 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:15.144 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:15.145 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:19.365 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:19.374 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:19.376 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:19.376 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:44.773 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:44.780 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:44.782 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:44.782 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:46.947 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:46.957 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:46.958 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:46.958 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:49.551 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:49.560 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:49.561 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:49.562 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:53.951 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:53.960 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:53.961 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:53.962 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:00:58.261 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:00:58.269 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:00:58.284 [info] index finished after resolve  [object Object] 
2025-08-02 10:00:58.285 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:00.286 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:00.294 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:00.295 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:00.296 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:02.933 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:02.941 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:02.942 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:02.943 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:05.026 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:05.033 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:05.051 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:05.052 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:07.049 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:07.057 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:07.058 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:07.058 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:09.900 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:09.908 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:09.910 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:09.910 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:12.274 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:12.281 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:12.283 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:12.283 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:14.402 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:14.409 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:14.410 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:14.411 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:16.557 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:16.563 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:16.564 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:16.565 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:19.058 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:19.065 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:19.066 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:19.066 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:21.304 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:21.312 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:21.312 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:21.313 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 10:01:23.908 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-02 10:01:23.915 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-02 10:01:23.916 [info] index finished after resolve  [object Object] 
2025-08-02 10:01:23.916 [info] refresh page data from resolve listeners 0 1035   
2025-08-02 17:03:31.665 [info] indexing created file 学习库/Deep learning/训练实践/未命名.md  [object Object] 
2025-08-02 17:03:31.665 [info] indexing created ignore file 学习库/Deep learning/训练实践/未命名.md   
2025-08-02 17:03:31.669 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 17:03:31.744 [info] trigger 学习库/Deep learning/训练实践/未命名.md resolve  [object Object] 
2025-08-02 17:03:31.847 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:31.848 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:03:37.580 [info] refresh page data from rename listeners 0 1036   
2025-08-02 17:03:37.614 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 17:03:41.262 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:03:41.276 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:03:41.277 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:41.278 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:03:46.379 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:03:46.384 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:03:46.386 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:46.386 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:03:49.249 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:03:49.252 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:03:49.254 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:49.255 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:03:55.656 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:03:55.662 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:03:55.662 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:55.663 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:03:58.976 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:03:58.983 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:03:58.984 [info] index finished after resolve  [object Object] 
2025-08-02 17:03:58.984 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:01.320 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:01.327 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:01.328 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:01.329 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:03.976 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:04.008 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:04.010 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:04.011 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:06.610 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:06.646 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:06.646 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:06.647 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:08.943 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:08.984 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:08.986 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:08.987 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:11.639 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:11.644 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:11.645 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:11.647 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:50.654 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:50.661 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:50.662 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:50.663 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:04:57.075 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:04:57.080 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:04:57.081 [info] index finished after resolve  [object Object] 
2025-08-02 17:04:57.082 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:00.082 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:00.086 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:00.087 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:00.088 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:02.347 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:02.352 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:02.353 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:02.353 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:04.458 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:04.465 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:04.466 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:04.467 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:08.101 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:08.108 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:08.109 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:08.109 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:12.091 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:12.098 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:12.099 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:12.101 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:28.578 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:28.616 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:28.617 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:28.618 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:31.180 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:31.213 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:31.214 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:31.214 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:05:34.227 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:05:34.233 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:05:34.234 [info] index finished after resolve  [object Object] 
2025-08-02 17:05:34.234 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:08.744 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:08.799 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:08.801 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:08.802 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:10.970 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:11.002 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:11.003 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:11.004 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:13.238 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:13.244 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:13.245 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:13.246 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:15.272 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:15.279 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:15.281 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:15.282 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:51.863 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:51.870 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:51.872 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:51.873 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:06:55.667 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:06:55.673 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:06:55.674 [info] index finished after resolve  [object Object] 
2025-08-02 17:06:55.674 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:01.012 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:01.017 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:01.018 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:01.020 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:07.198 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:07.203 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:07.205 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:07.205 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:16.624 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:16.629 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:16.630 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:16.631 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:21.012 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:21.016 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:21.017 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:21.018 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:23.751 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:23.757 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:23.759 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:23.760 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:26.607 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:26.613 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:26.614 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:26.615 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:29.879 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:29.884 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:29.885 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:29.885 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:32.596 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:32.601 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:32.602 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:32.602 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:40.941 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:40.946 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:40.947 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:40.948 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:54.135 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:54.141 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:54.141 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:54.142 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:07:56.260 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:07:56.264 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:07:56.265 [info] index finished after resolve  [object Object] 
2025-08-02 17:07:56.266 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:05.677 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:05.683 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:05.685 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:05.687 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:08.824 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:08.829 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:08.831 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:08.831 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:11.111 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:11.117 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:11.118 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:11.118 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:14.573 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:14.578 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:14.578 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:14.579 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:28.199 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:28.204 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:28.204 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:28.205 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:31.905 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:31.911 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:31.912 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:31.913 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:08:36.031 [debug] ignore file modify evnet 学习库/Deep learning/训练实践/读取文件.md   
2025-08-02 17:08:36.037 [info] trigger 学习库/Deep learning/训练实践/读取文件.md resolve  [object Object] 
2025-08-02 17:08:36.037 [info] index finished after resolve  [object Object] 
2025-08-02 17:08:36.038 [info] refresh page data from resolve listeners 0 1036   
2025-08-02 17:30:48.070 [info] indexing created file 工作库/项目/舌诊/attachments/人脸识别-{DATE}-{TIME}.webp  [object Object] 
2025-08-02 17:30:48.077 [info] refresh page data from created listeners 0 1037   
2025-08-02 17:30:50.112 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 17:30:50.120 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 17:30:50.122 [info] index finished after resolve  [object Object] 
2025-08-02 17:30:50.124 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 17:30:56.248 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 17:30:56.253 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 17:30:56.254 [info] index finished after resolve  [object Object] 
2025-08-02 17:30:56.254 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 17:30:58.422 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 17:30:58.426 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 17:30:58.427 [info] index finished after resolve  [object Object] 
2025-08-02 17:30:58.428 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:20:17.228 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:20:17.235 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:20:17.239 [info] index finished after resolve  [object Object] 
2025-08-02 21:20:17.240 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:26.863 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:26.871 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:26.872 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:26.873 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:37.783 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:37.789 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:37.791 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:37.792 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:39.829 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:39.834 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:39.835 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:39.836 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:51.922 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:51.929 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:51.930 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:51.931 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:53.975 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:53.982 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:53.984 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:53.984 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:26:58.358 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:26:58.366 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:26:58.367 [info] index finished after resolve  [object Object] 
2025-08-02 21:26:58.368 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:00.907 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:00.914 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:00.915 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:00.916 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:03.870 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:03.874 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:03.875 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:03.876 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:10.321 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:10.327 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:10.329 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:10.329 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:12.702 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:12.707 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:12.708 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:12.709 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:15.781 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:15.786 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:15.788 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:15.789 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:35.869 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:35.875 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:35.876 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:35.877 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:40.788 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:40.793 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:40.794 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:40.795 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:45.210 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:45.216 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:45.217 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:45.218 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:27:48.600 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:27:48.606 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:27:48.608 [info] index finished after resolve  [object Object] 
2025-08-02 21:27:48.608 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:28:05.004 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:28:05.011 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:28:05.012 [info] index finished after resolve  [object Object] 
2025-08-02 21:28:05.013 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:28:08.259 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:28:08.265 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:28:08.266 [info] index finished after resolve  [object Object] 
2025-08-02 21:28:08.267 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:32:16.374 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:32:16.381 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:32:16.383 [info] index finished after resolve  [object Object] 
2025-08-02 21:32:16.384 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:32:19.809 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:32:19.816 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:32:19.817 [info] index finished after resolve  [object Object] 
2025-08-02 21:32:19.818 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:32:22.303 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:32:22.309 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:32:22.311 [info] index finished after resolve  [object Object] 
2025-08-02 21:32:22.312 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:38:51.299 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:38:51.305 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:38:51.306 [info] index finished after resolve  [object Object] 
2025-08-02 21:38:51.307 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:34.317 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:34.324 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:34.326 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:34.326 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:44.072 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:44.079 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:44.080 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:44.080 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:52.372 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:52.378 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:52.379 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:52.379 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:54.417 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:54.422 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:54.423 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:54.424 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:56.479 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:56.483 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:56.484 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:56.484 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:39:58.520 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:39:58.526 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:39:58.527 [info] index finished after resolve  [object Object] 
2025-08-02 21:39:58.528 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:00.587 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:00.593 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:00.594 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:00.595 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:03.685 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:03.689 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:03.690 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:03.691 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:19.234 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:19.242 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:19.243 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:19.244 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:21.731 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:21.737 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:21.739 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:21.739 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:24.454 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:24.486 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:24.486 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:24.487 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:26.718 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:26.735 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:26.736 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:26.737 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:28.739 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:28.770 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:28.772 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:28.773 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:31.049 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:31.054 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:31.055 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:31.055 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:40:40.983 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:40:40.989 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:40:40.991 [info] index finished after resolve  [object Object] 
2025-08-02 21:40:40.992 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:54:55.042 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:54:55.072 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:54:55.074 [info] index finished after resolve  [object Object] 
2025-08-02 21:54:55.075 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:54:57.250 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:54:57.259 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:54:57.260 [info] index finished after resolve  [object Object] 
2025-08-02 21:54:57.260 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:00.001 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:00.049 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:00.051 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:00.051 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:02.438 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:02.445 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:02.446 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:02.447 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:16.126 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:16.151 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:16.153 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:16.154 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:18.894 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:18.900 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:18.901 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:18.902 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:20.960 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:20.984 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:20.987 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:20.989 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:25.020 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:25.029 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:25.030 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:25.031 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:27.461 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:27.467 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:27.468 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:27.469 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:35.552 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:35.557 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:35.558 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:35.559 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:55:39.968 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:55:39.974 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:55:39.975 [info] index finished after resolve  [object Object] 
2025-08-02 21:55:39.975 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:58:16.372 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:58:16.381 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:58:16.382 [info] index finished after resolve  [object Object] 
2025-08-02 21:58:16.383 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:58:38.791 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:58:38.798 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:58:38.799 [info] index finished after resolve  [object Object] 
2025-08-02 21:58:38.800 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:58:41.061 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:58:41.067 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:58:41.068 [info] index finished after resolve  [object Object] 
2025-08-02 21:58:41.068 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:58:50.117 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:58:50.126 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:58:50.127 [info] index finished after resolve  [object Object] 
2025-08-02 21:58:50.128 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:58:58.428 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:58:58.473 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:58:58.476 [info] index finished after resolve  [object Object] 
2025-08-02 21:58:58.477 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 21:59:02.034 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 21:59:02.040 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 21:59:02.041 [info] index finished after resolve  [object Object] 
2025-08-02 21:59:02.042 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 22:00:37.280 [info] indexing created file 工作库/项目/舌诊/attachments/人脸识别-{DATE}-{TIME}.png  [object Object] 
2025-08-02 22:00:37.283 [info] refresh page data from created listeners 0 1038   
2025-08-02 22:00:39.310 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:00:39.317 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:00:39.318 [info] index finished after resolve  [object Object] 
2025-08-02 22:00:39.319 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:02:20.737 [info] refresh page data from delete listeners 0 1037   
2025-08-02 22:02:20.747 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:02:22.565 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:02:22.571 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:02:22.572 [info] index finished after resolve  [object Object] 
2025-08-02 22:02:22.574 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 22:02:24.989 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:02:24.994 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:02:24.996 [info] index finished after resolve  [object Object] 
2025-08-02 22:02:24.997 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 22:02:30.110 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:02:30.117 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:02:30.118 [info] index finished after resolve  [object Object] 
2025-08-02 22:02:30.119 [info] refresh page data from resolve listeners 0 1037   
2025-08-02 22:02:44.541 [info] indexing created file 工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_44.png  [object Object] 
2025-08-02 22:02:44.612 [info] refresh page data from created listeners 0 1038   
2025-08-02 22:02:46.579 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:02:46.586 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:02:46.587 [info] index finished after resolve  [object Object] 
2025-08-02 22:02:46.588 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:03:30.090 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:03:30.098 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:03:30.102 [info] index finished after resolve  [object Object] 
2025-08-02 22:03:30.103 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:03:35.484 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:03:35.515 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:03:35.516 [info] index finished after resolve  [object Object] 
2025-08-02 22:03:35.517 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:03:37.820 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:03:37.829 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:03:37.831 [info] index finished after resolve  [object Object] 
2025-08-02 22:03:37.832 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:03:47.824 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:03:47.835 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:03:47.836 [info] index finished after resolve  [object Object] 
2025-08-02 22:03:47.837 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:00.499 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:00.506 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:00.507 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:00.508 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:03.809 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:03.816 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:03.817 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:03.818 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:08.017 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:08.061 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:08.064 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:08.065 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:24.850 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:24.885 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:24.887 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:24.887 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:27.137 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:27.166 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:27.171 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:27.172 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:32.547 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:32.559 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:32.587 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:32.588 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:35.688 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:35.700 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:35.701 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:35.702 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:38.170 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:38.181 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:38.182 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:38.182 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:44.344 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:44.351 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:44.352 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:44.353 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:04:55.413 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:04:55.421 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:04:55.422 [info] index finished after resolve  [object Object] 
2025-08-02 22:04:55.423 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:00.456 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:00.462 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:00.463 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:00.463 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:11.584 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:11.592 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:11.612 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:11.613 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:12.662 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:12.719 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:12.721 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:12.721 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:17.039 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:17.047 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:17.048 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:17.049 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:36.625 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:36.636 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:36.637 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:36.637 [info] refresh page data from resolve listeners 0 1038   
2025-08-02 22:05:51.989 [info] indexing created file 工作库/项目/舌诊/attachments/人脸识别-2025-08-02,22_08_51.png  [object Object] 
2025-08-02 22:05:51.991 [info] refresh page data from created listeners 0 1039   
2025-08-02 22:05:53.932 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:05:54.112 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:05:54.116 [info] index finished after resolve  [object Object] 
2025-08-02 22:05:54.117 [info] refresh page data from resolve listeners 0 1039   
2025-08-02 22:06:06.404 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:06:06.436 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:06:06.438 [info] index finished after resolve  [object Object] 
2025-08-02 22:06:06.439 [info] refresh page data from resolve listeners 0 1039   
2025-08-02 22:06:12.105 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-02 22:06:12.140 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-02 22:06:12.141 [info] index finished after resolve  [object Object] 
2025-08-02 22:06:12.142 [info] refresh page data from resolve listeners 0 1039   
