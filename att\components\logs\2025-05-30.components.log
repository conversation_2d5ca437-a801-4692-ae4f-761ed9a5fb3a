2025-05-30 11:02:25.923 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-05-30 11:02:25.924 [info] components database created cost 1 ms   
2025-05-30 11:02:25.924 [info] components index initializing...   
2025-05-30 11:02:26.212 [info] start to batch put pages: 5   
2025-05-30 11:02:26.243 [info] batch persist cost 5  31 
2025-05-30 11:02:26.279 [info] components index initialized, 899 files cost 356 ms   
2025-05-30 11:02:26.280 [info] refresh page data from init listeners 0 899   
2025-05-30 11:02:27.788 [info] indexing created file components/logs/2025-05-30.components.log  [object Object] 
2025-05-30 11:02:27.790 [info] refresh page data from created listeners 0 900   
2025-05-30 11:02:28.179 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-30 11:02:28.184 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-05-30 11:02:28.190 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-30 11:02:28.195 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-30 11:02:42.581 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-30 11:02:42.638 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-30 11:03:50.860 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:03:50.880 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:03:50.881 [info] index finished after resolve  [object Object] 
2025-05-30 11:03:50.882 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:03:52.983 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:03:52.986 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:03:52.987 [info] index finished after resolve  [object Object] 
2025-05-30 11:03:52.988 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:03:55.265 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:03:55.326 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:03:55.331 [info] index finished after resolve  [object Object] 
2025-05-30 11:03:55.332 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:03:57.837 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:03:57.898 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:03:57.900 [info] index finished after resolve  [object Object] 
2025-05-30 11:03:57.901 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:03:59.971 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:04:00.035 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:04:00.038 [info] index finished after resolve  [object Object] 
2025-05-30 11:04:00.039 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:04:02.116 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:04:02.297 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:04:02.300 [info] index finished after resolve  [object Object] 
2025-05-30 11:04:02.302 [info] refresh page data from resolve listeners 0 900   
2025-05-30 11:04:05.187 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-05-30 11:04:05.350 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-05-30 11:04:05.357 [info] index finished after resolve  [object Object] 
2025-05-30 11:04:05.358 [info] refresh page data from resolve listeners 0 900   
