2025-04-04 08:58:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 08:58:15 [info] indexing created file components/logs/2025-04-04.components.log  [object Object] 
2025-04-04 08:58:15 [info] refresh page data from created listeners 0 723   
2025-04-04 08:58:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 08:58:15 [info] index finished after resolve  [object Object] 
2025-04-04 08:58:15 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:42 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:42 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:42 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:42 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:44 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:44 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:44 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:44 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:50 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:50 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:51 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:52 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:52 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:54 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:54 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:54 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:54 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:56 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:56 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:56 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:56 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:33:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:33:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:33:59 [info] index finished after resolve  [object Object] 
2025-04-04 09:33:59 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:01 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:01 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:01 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:01 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:04 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:04 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:08 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:08 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:11 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:11 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:14 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:14 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:14 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:34:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-04 09:34:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-04 09:34:15 [info] index finished after resolve  [object Object] 
2025-04-04 09:34:15 [info] refresh page data from resolve listeners 0 723   
2025-04-04 09:44:19 [info] indexing created file 学习库/c/未命名.md  [object Object] 
2025-04-04 09:44:19 [info] indexing created ignore file 学习库/c/未命名.md   
2025-04-04 09:44:19 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-04 09:44:19 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-04 09:44:19 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-04 09:44:19 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-04 09:44:19 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-04 09:44:19 [info] trigger 学习库/c/未命名.md resolve  [object Object] 
2025-04-04 09:44:20 [info] index finished after resolve  [object Object] 
2025-04-04 09:44:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:44:24 [info] refresh page data from rename listeners 0 724   
2025-04-04 09:44:24 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-04 09:44:24 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-04 09:44:24 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-04 09:44:24 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-04 09:44:24 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-04 09:44:28 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:44:28 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:44:28 [info] index finished after resolve  [object Object] 
2025-04-04 09:44:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:44:31 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:44:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:44:31 [info] index finished after resolve  [object Object] 
2025-04-04 09:44:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:44:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:44:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:44:33 [info] index finished after resolve  [object Object] 
2025-04-04 09:44:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:03 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:03 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:03 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:03 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:05 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:05 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:08 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:08 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:08 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:08 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:10 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:10 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:10 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:13 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:13 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:13 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:15 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:15 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:15 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:15 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:17 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:17 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:17 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:17 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:19 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:19 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:19 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:22 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:24 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:24 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:24 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:27 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:27 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:27 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:27 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:29 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:29 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:29 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:31 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:31 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:33 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:40 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:40 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:40 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:40 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:50:43 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:50:44 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:50:44 [info] index finished after resolve  [object Object] 
2025-04-04 09:50:44 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:07 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:07 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:07 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:09 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:09 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:09 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:09 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:12 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:12 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:12 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:14 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:14 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:14 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:14 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:16 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:16 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:18 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:18 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:18 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:22 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:24 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:24 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:24 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:45 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:49 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:49 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:49 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:51 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:51 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:51 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:51 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:51:58 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:51:58 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:51:58 [info] index finished after resolve  [object Object] 
2025-04-04 09:51:58 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:00 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:00 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:00 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:00 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:05 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:05 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:13 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:13 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:13 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:16 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:16 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:19 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:19 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:19 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:23 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:23 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:23 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:26 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:26 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:26 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:26 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:28 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:29 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:29 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:31 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:31 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:33 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:35 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:35 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:35 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:35 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:37 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:38 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:38 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:38 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:40 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:40 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:40 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:40 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:42 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:45 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:52:47 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:52:47 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:52:47 [info] index finished after resolve  [object Object] 
2025-04-04 09:52:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:53:00 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:53:00 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:53:00 [info] index finished after resolve  [object Object] 
2025-04-04 09:53:00 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:53:27 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:53:27 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:53:27 [info] index finished after resolve  [object Object] 
2025-04-04 09:53:27 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:53:29 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:53:29 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:53:29 [info] index finished after resolve  [object Object] 
2025-04-04 09:53:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:53:32 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:53:32 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:53:32 [info] index finished after resolve  [object Object] 
2025-04-04 09:53:32 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:59:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:59:18 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:59:18 [info] index finished after resolve  [object Object] 
2025-04-04 09:59:18 [info] refresh page data from resolve listeners 0 724   
2025-04-04 09:59:55 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 09:59:55 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 09:59:55 [info] index finished after resolve  [object Object] 
2025-04-04 09:59:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:15 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:15 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:15 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:15 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:22 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:24 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:24 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:24 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:26 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:26 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:26 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:26 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:37 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:37 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:37 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:39 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:39 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:39 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:39 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:43 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:43 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:43 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:43 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:46 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:46 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:46 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:47 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:47 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:47 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:50 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:50 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:50 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:50 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:52 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:52 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:52 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:52 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:55 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:55 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:55 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:00:56 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:00:57 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:00:57 [info] index finished after resolve  [object Object] 
2025-04-04 10:00:57 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:01:01 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:01:01 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:01:01 [info] index finished after resolve  [object Object] 
2025-04-04 10:01:01 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:01:03 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:01:03 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:01:03 [info] index finished after resolve  [object Object] 
2025-04-04 10:01:03 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:01:05 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:01:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:01:05 [info] index finished after resolve  [object Object] 
2025-04-04 10:01:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:04:13 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:04:13 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:04:13 [info] index finished after resolve  [object Object] 
2025-04-04 10:04:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:04:16 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:04:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:04:16 [info] index finished after resolve  [object Object] 
2025-04-04 10:04:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:05:16 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-04-04 10:05:16 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-04-04 10:05:16 [info] index finished after resolve  [object Object] 
2025-04-04 10:05:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:27:38 [info] components database created cost 6 ms   
2025-04-04 10:27:38 [info] components index initializing...   
2025-04-04 10:27:38 [info] start to batch put pages: 6   
2025-04-04 10:27:40 [info] batch persist cost 6  2585 
2025-04-04 10:27:40 [info] components index initialized, 724 files cost 2926 ms   
2025-04-04 10:27:40 [info] refresh page data from init listeners 0 724   
2025-04-04 10:27:42 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 10:27:42 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 10:27:43 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-04 10:27:43 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-04 10:27:43 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-04 10:27:43 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-04 10:28:10 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:28:10 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:28:10 [info] index finished after resolve  [object Object] 
2025-04-04 10:28:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:28:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:28:12 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:28:12 [info] index finished after resolve  [object Object] 
2025-04-04 10:28:12 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:29:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:29:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:29:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:29:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:29:48 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:29:48 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:29:48 [info] index finished after resolve  [object Object] 
2025-04-04 10:29:48 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:29:50 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:29:50 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:29:50 [info] index finished after resolve  [object Object] 
2025-04-04 10:29:50 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:29:53 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:29:53 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:29:53 [info] index finished after resolve  [object Object] 
2025-04-04 10:29:53 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:14 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:14 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:14 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:14 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:18 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:18 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:18 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:22 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:25 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:25 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:25 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:28 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:28 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:28 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:31 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:31 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:33 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:38 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:38 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:38 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:38 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:40 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:40 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:40 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:40 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:42 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:47 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:47 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:47 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:49 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:49 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:49 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:52 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:52 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:52 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:52 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:30:56 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:30:56 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:30:56 [info] index finished after resolve  [object Object] 
2025-04-04 10:30:56 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:00 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:00 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:00 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:00 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:02 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:02 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:02 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:04 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:04 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:04 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:04 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:07 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:07 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:07 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:09 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:09 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:09 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:09 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:12 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:12 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:12 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:15 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:15 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:15 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:15 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:18 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:18 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:18 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:23 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:23 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:23 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:25 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:25 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:25 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:27 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:27 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:27 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:27 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:33 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:36 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:36 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:36 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:36 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:39 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:39 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:39 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:39 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:42 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:44 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:44 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:44 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:44 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:48 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:48 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:48 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:48 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:50 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:50 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:50 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:50 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:56 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:56 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:56 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:56 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:31:58 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:31:58 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:31:58 [info] index finished after resolve  [object Object] 
2025-04-04 10:31:58 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:32:00 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:32:00 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:32:01 [info] index finished after resolve  [object Object] 
2025-04-04 10:32:01 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:32:03 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:32:03 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:32:03 [info] index finished after resolve  [object Object] 
2025-04-04 10:32:03 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:41 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:41 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:41 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:41 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:49 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:49 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:49 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:51 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:51 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:51 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:51 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:53 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:54 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:54 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:54 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:56 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:56 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:56 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:56 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:35:58 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:35:58 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:35:58 [info] index finished after resolve  [object Object] 
2025-04-04 10:35:58 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:36:00 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:36:00 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:36:00 [info] index finished after resolve  [object Object] 
2025-04-04 10:36:00 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:36:02 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:36:02 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:36:02 [info] index finished after resolve  [object Object] 
2025-04-04 10:36:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:36:05 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:36:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:36:05 [info] index finished after resolve  [object Object] 
2025-04-04 10:36:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:04 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:04 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:04 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:04 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:07 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:07 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:07 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:12 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:12 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:12 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:14 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:14 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:14 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:14 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:17 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:17 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:17 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:17 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:28 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:28 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:28 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:30 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:31 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:33 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:35 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:35 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:35 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:35 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:37 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:37 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:37 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:38:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:38:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:38:42 [info] index finished after resolve  [object Object] 
2025-04-04 10:38:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:40:07 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:40:07 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:40:07 [info] index finished after resolve  [object Object] 
2025-04-04 10:40:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:06 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:06 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:06 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:06 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:13 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:13 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:13 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:16 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:16 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:18 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:18 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:18 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:23 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:23 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:23 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:25 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:25 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:25 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:29 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:29 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:29 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:31 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:31 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:33 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:33 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:33 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:33 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:35 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:35 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:35 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:35 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:37 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:37 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:37 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:41:39 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:41:39 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:41:39 [info] index finished after resolve  [object Object] 
2025-04-04 10:41:39 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:45:47 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:45:48 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:45:48 [info] index finished after resolve  [object Object] 
2025-04-04 10:45:48 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:47:15 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:47:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:47:16 [info] index finished after resolve  [object Object] 
2025-04-04 10:47:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:48:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:48:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:48:42 [info] index finished after resolve  [object Object] 
2025-04-04 10:48:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:49:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:49:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:49:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:49:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:06 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:06 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:06 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:06 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:10 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:10 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:10 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:12 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:12 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:12 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:14 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:15 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:15 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:15 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:26 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:26 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:26 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:26 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:29 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:29 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:29 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:32 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:32 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:32 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:32 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:53 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:53 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:53 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:53 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:55 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:55 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:55 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:50:58 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:50:58 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:50:58 [info] index finished after resolve  [object Object] 
2025-04-04 10:50:58 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:51:05 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:51:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:51:05 [info] index finished after resolve  [object Object] 
2025-04-04 10:51:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:51:07 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:51:08 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:51:08 [info] index finished after resolve  [object Object] 
2025-04-04 10:51:08 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:51:10 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:51:10 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:51:10 [info] index finished after resolve  [object Object] 
2025-04-04 10:51:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:51:20 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:51:20 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:51:20 [info] index finished after resolve  [object Object] 
2025-04-04 10:51:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:52:23 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:52:23 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:52:23 [info] index finished after resolve  [object Object] 
2025-04-04 10:52:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:52:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:52:42 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:52:42 [info] index finished after resolve  [object Object] 
2025-04-04 10:52:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:52:46 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:52:46 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:52:46 [info] index finished after resolve  [object Object] 
2025-04-04 10:52:46 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:53:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:53:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:53:22 [info] index finished after resolve  [object Object] 
2025-04-04 10:53:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:53:25 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:53:25 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:53:25 [info] index finished after resolve  [object Object] 
2025-04-04 10:53:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:53:28 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:53:28 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:53:28 [info] index finished after resolve  [object Object] 
2025-04-04 10:53:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:53:37 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:53:37 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:53:37 [info] index finished after resolve  [object Object] 
2025-04-04 10:53:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:54:30 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:54:31 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:54:31 [info] index finished after resolve  [object Object] 
2025-04-04 10:54:31 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:54:36 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:54:37 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:54:37 [info] index finished after resolve  [object Object] 
2025-04-04 10:54:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:54:40 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:54:41 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:54:41 [info] index finished after resolve  [object Object] 
2025-04-04 10:54:41 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:54:42 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:54:43 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:54:43 [info] index finished after resolve  [object Object] 
2025-04-04 10:54:43 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:15 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:16 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:16 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:18 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:19 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:19 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:22 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:22 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:22 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:27 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:28 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:28 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:45 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:55:48 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:55:49 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:55:49 [info] index finished after resolve  [object Object] 
2025-04-04 10:55:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:56:44 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:56:45 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:56:45 [info] index finished after resolve  [object Object] 
2025-04-04 10:56:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:56:55 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:56:55 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:56:55 [info] index finished after resolve  [object Object] 
2025-04-04 10:56:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:58:01 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:58:02 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:58:02 [info] index finished after resolve  [object Object] 
2025-04-04 10:58:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:58:04 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:58:05 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:58:05 [info] index finished after resolve  [object Object] 
2025-04-04 10:58:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:58:08 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:58:09 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:58:09 [info] index finished after resolve  [object Object] 
2025-04-04 10:58:09 [info] refresh page data from resolve listeners 0 724   
2025-04-04 10:58:12 [info] ignore file modify evnet 学习库/c/9 结构体.md   
2025-04-04 10:58:13 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-04 10:58:13 [info] index finished after resolve  [object Object] 
2025-04-04 10:58:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:12:38 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:12:38 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:12:38 [info] index finished after resolve  [object Object] 
2025-04-04 11:12:38 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:12:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:12:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:12:42 [info] index finished after resolve  [object Object] 
2025-04-04 11:12:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:03 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:03 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:03 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:05 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:07 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:07 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:07 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:16 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:19 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:13:31 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:13:32 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:13:32 [info] index finished after resolve  [object Object] 
2025-04-04 11:13:32 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:14:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:14:07 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:14:07 [info] index finished after resolve  [object Object] 
2025-04-04 11:14:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:14:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:14:11 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:14:11 [info] index finished after resolve  [object Object] 
2025-04-04 11:14:11 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:14:57 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:14:58 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:14:58 [info] index finished after resolve  [object Object] 
2025-04-04 11:14:58 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:15:37 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:15:37 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:15:37 [info] index finished after resolve  [object Object] 
2025-04-04 11:15:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:15:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:15:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:15:42 [info] index finished after resolve  [object Object] 
2025-04-04 11:15:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:15:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:15:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:15:45 [info] index finished after resolve  [object Object] 
2025-04-04 11:15:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:16:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:16:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:16:16 [info] index finished after resolve  [object Object] 
2025-04-04 11:16:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:16:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:16:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:16:20 [info] index finished after resolve  [object Object] 
2025-04-04 11:16:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:16:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:16:24 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:16:24 [info] index finished after resolve  [object Object] 
2025-04-04 11:16:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:16:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:16:30 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:16:30 [info] index finished after resolve  [object Object] 
2025-04-04 11:16:30 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:17:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:17:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:17:19 [info] index finished after resolve  [object Object] 
2025-04-04 11:17:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:17:29 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:17:32 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:17:32 [info] index finished after resolve  [object Object] 
2025-04-04 11:17:32 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:17:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:17:36 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:17:36 [info] index finished after resolve  [object Object] 
2025-04-04 11:17:36 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:18:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:18:11 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:18:11 [info] index finished after resolve  [object Object] 
2025-04-04 11:18:11 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:18:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:18:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:18:20 [info] index finished after resolve  [object Object] 
2025-04-04 11:18:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:18:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:18:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:18:23 [info] index finished after resolve  [object Object] 
2025-04-04 11:18:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:18:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:18:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:18:28 [info] index finished after resolve  [object Object] 
2025-04-04 11:18:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:19:41 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:19:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:19:42 [info] index finished after resolve  [object Object] 
2025-04-04 11:19:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:19:44 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:19:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:19:45 [info] index finished after resolve  [object Object] 
2025-04-04 11:19:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:19:47 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:19:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:19:48 [info] index finished after resolve  [object Object] 
2025-04-04 11:19:48 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:19:51 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:19:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:19:53 [info] index finished after resolve  [object Object] 
2025-04-04 11:19:53 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:22 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:24 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:24 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:24 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:26 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:26 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:26 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:26 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:34 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:34 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:40 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:40 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:40 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:43 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:43 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:43 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:20:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:20:47 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:20:47 [info] index finished after resolve  [object Object] 
2025-04-04 11:20:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:21:35 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:21:37 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:21:37 [info] index finished after resolve  [object Object] 
2025-04-04 11:21:37 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:21:38 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:21:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:21:40 [info] index finished after resolve  [object Object] 
2025-04-04 11:21:40 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:21:43 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:21:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:21:45 [info] index finished after resolve  [object Object] 
2025-04-04 11:21:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:21:47 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:21:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:21:49 [info] index finished after resolve  [object Object] 
2025-04-04 11:21:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:22:59 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:00 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:00 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:00 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:02 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:04 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:04 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:04 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:08 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:08 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:08 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:35 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:36 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:36 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:36 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:38 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:39 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:39 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:43 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:43 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:43 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:23:47 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:23:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:23:48 [info] index finished after resolve  [object Object] 
2025-04-04 11:23:48 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:24:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:24:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:24:46 [info] index finished after resolve  [object Object] 
2025-04-04 11:24:46 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:24:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:24:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:24:49 [info] index finished after resolve  [object Object] 
2025-04-04 11:24:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:24:52 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:24:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:24:53 [info] index finished after resolve  [object Object] 
2025-04-04 11:24:53 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:25:10 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:25:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:25:10 [info] index finished after resolve  [object Object] 
2025-04-04 11:25:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:25:13 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:25:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:25:13 [info] index finished after resolve  [object Object] 
2025-04-04 11:25:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:25:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:25:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:25:17 [info] index finished after resolve  [object Object] 
2025-04-04 11:25:17 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:25:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:25:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:25:19 [info] index finished after resolve  [object Object] 
2025-04-04 11:25:19 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:25:47 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:25:47 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:25:47 [info] index finished after resolve  [object Object] 
2025-04-04 11:25:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:26:59 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:26:59 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:26:59 [info] index finished after resolve  [object Object] 
2025-04-04 11:26:59 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:27:02 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:27:04 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:27:04 [info] index finished after resolve  [object Object] 
2025-04-04 11:27:04 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:28:21 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:28:21 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:28:21 [info] index finished after resolve  [object Object] 
2025-04-04 11:28:21 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:28:24 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:28:25 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:28:25 [info] index finished after resolve  [object Object] 
2025-04-04 11:28:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:28:27 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:28:27 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:28:27 [info] index finished after resolve  [object Object] 
2025-04-04 11:28:27 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:28:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:28:35 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:28:35 [info] index finished after resolve  [object Object] 
2025-04-04 11:28:35 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:02 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:02 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:07 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:07 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:07 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:11 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:11 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:11 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:17 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:17 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:22 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:22 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:26 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:28 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:34 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:34 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:38 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:38 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:38 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:38 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:41 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:41 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:41 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:44 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:45 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:45 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:29:54 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:29:54 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:29:54 [info] index finished after resolve  [object Object] 
2025-04-04 11:29:54 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:30:11 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:30:11 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:30:11 [info] index finished after resolve  [object Object] 
2025-04-04 11:30:11 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:30:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:30:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:30:17 [info] index finished after resolve  [object Object] 
2025-04-04 11:30:17 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:30:21 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:30:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:30:23 [info] index finished after resolve  [object Object] 
2025-04-04 11:30:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:30:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:30:29 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:30:29 [info] index finished after resolve  [object Object] 
2025-04-04 11:30:29 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:30:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:30:41 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:30:41 [info] index finished after resolve  [object Object] 
2025-04-04 11:30:41 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:02 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:02 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:04 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:06 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:06 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:06 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:08 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:10 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:12 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:13 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:13 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:14 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:15 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:15 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:15 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:20 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:24 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:24 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:24 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:25 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:26 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:26 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:26 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:41 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:41 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:41 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:47 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:47 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:47 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:31:49 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:31:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:31:49 [info] index finished after resolve  [object Object] 
2025-04-04 11:31:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:15 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:16 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:20 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:20 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:23 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:23 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:25 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:25 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:25 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:27 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:27 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:27 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:27 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:32:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:32:36 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:32:36 [info] index finished after resolve  [object Object] 
2025-04-04 11:32:36 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:02 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:02 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:02 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:04 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:05 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:05 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:10 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:10 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:15 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:16 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:16 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:34 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:34 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:37 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:39 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:39 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:44 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:44 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:44 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:47 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:49 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:49 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:51 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:53 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:53 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:33:54 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:33:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:33:55 [info] index finished after resolve  [object Object] 
2025-04-04 11:33:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:24 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:25 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:25 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:25 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:27 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:28 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:28 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:33 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:34 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:34 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:36 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:38 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:38 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:38 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:42 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:42 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:53 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:55 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:55 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:35:57 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:35:59 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:35:59 [info] index finished after resolve  [object Object] 
2025-04-04 11:35:59 [info] refresh page data from resolve listeners 0 724   
2025-04-04 11:36:08 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-04-11-36-08.png  [object Object] 
2025-04-04 11:36:09 [info] refresh page data from created listeners 0 725   
2025-04-04 11:36:10 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:10 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:10 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:17 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:19 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:19 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:28 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:28 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:31 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:31 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:31 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:31 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:34 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:34 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:41 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:43 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:43 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:43 [info] refresh page data from resolve listeners 0 725   
2025-04-04 11:36:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-04 11:36:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-04 11:36:46 [info] index finished after resolve  [object Object] 
2025-04-04 11:36:46 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:32:24 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-04 14:32:24 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-04 14:32:24 [info] index finished after resolve  [object Object] 
2025-04-04 14:32:24 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:32:26 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-04 14:32:28 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-04 14:32:28 [info] index finished after resolve  [object Object] 
2025-04-04 14:32:28 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:32:29 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-04 14:32:30 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-04 14:32:30 [info] index finished after resolve  [object Object] 
2025-04-04 14:32:30 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:32:36 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-04 14:32:36 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-04 14:32:36 [info] index finished after resolve  [object Object] 
2025-04-04 14:32:36 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:32:45 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-04 14:32:45 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-04 14:32:45 [info] index finished after resolve  [object Object] 
2025-04-04 14:32:45 [info] refresh page data from resolve listeners 0 725   
2025-04-04 14:44:33 [info] components database created cost 3 ms   
2025-04-04 14:44:33 [info] components index initializing...   
2025-04-04 14:44:33 [info] start to batch put pages: 5   
2025-04-04 14:44:36 [info] batch persist cost 5  2571 
2025-04-04 14:44:36 [info] components index initialized, 725 files cost 3141 ms   
2025-04-04 14:44:36 [info] refresh page data from init listeners 0 725   
2025-04-04 14:44:38 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 14:44:38 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 14:44:40 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-04 14:44:40 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-04 14:44:40 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-04 14:44:40 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-04 15:29:55 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:29:55 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:29:55 [info] index finished after resolve  [object Object] 
2025-04-04 15:29:55 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:29:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:29:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:29:59 [info] index finished after resolve  [object Object] 
2025-04-04 15:29:59 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:30:01 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:30:01 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:30:01 [info] index finished after resolve  [object Object] 
2025-04-04 15:30:01 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:44 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:44 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:44 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:46 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:46 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:46 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:46 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:49 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:49 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:49 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:49 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:52 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:52 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:52 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:52 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:54 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:55 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:55 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:55 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:41:58 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:41:58 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:41:58 [info] index finished after resolve  [object Object] 
2025-04-04 15:41:58 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:26 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:27 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:27 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:27 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:29 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:29 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:29 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:29 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:48 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:48 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:48 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:48 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:51 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:51 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:51 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:51 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:55 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:55 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:55 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:55 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:42:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:42:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:42:59 [info] index finished after resolve  [object Object] 
2025-04-04 15:42:59 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:03 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:03 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:03 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:03 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:08 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:08 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:08 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:08 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:10 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:10 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:10 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:10 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:12 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:12 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:12 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:12 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:17 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:17 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:17 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:17 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:19 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:19 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:19 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:19 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:22 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:22 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:22 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:22 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:24 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:24 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:24 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:24 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:26 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:26 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:26 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:26 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:43:29 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:43:29 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:43:29 [info] index finished after resolve  [object Object] 
2025-04-04 15:43:29 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:17 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:17 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:17 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:17 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:19 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:19 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:19 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:19 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:21 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:21 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:21 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:21 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:23 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:24 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:24 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:24 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:26 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:26 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:26 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:26 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:28 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:28 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:28 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:28 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:31 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:31 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:31 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:31 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:33 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:33 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:33 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:33 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:36 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:36 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:36 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:36 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:39 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:39 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:39 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:39 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:41 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:41 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:41 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:41 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:43 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:43 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:43 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:43 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:44:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:44:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:44:59 [info] index finished after resolve  [object Object] 
2025-04-04 15:44:59 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:03 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:03 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:03 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:03 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:37 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:37 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:37 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:37 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:39 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:39 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:39 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:39 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:42 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:42 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:44 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:44 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:44 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:46 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:46 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:46 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:46 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:48 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:48 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:48 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:48 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:51 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:51 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:51 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:51 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:53 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:53 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:53 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:53 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:45:55 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:45:55 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:45:55 [info] index finished after resolve  [object Object] 
2025-04-04 15:45:55 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:47:35 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:47:35 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:47:35 [info] index finished after resolve  [object Object] 
2025-04-04 15:47:35 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:25 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:25 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:25 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:25 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:27 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:27 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:27 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:27 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:33 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:33 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:33 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:33 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:35 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:35 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:35 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:35 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:42 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:42 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:45 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:45 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:45 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:45 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:48 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:48 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:48 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:48 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:51 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:51 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:51 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:51 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:49:58 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:49:58 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:49:58 [info] index finished after resolve  [object Object] 
2025-04-04 15:49:58 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:02 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:02 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:02 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:02 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:06 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:07 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:07 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:07 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:16 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:17 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:17 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:17 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:20 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:20 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:20 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:20 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:41 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:41 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:41 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:41 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:50:45 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:50:45 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:50:45 [info] index finished after resolve  [object Object] 
2025-04-04 15:50:45 [info] refresh page data from resolve listeners 0 725   
2025-04-04 15:52:13 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 15:52:13 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 15:52:13 [info] index finished after resolve  [object Object] 
2025-04-04 15:52:13 [info] refresh page data from resolve listeners 0 725   
2025-04-04 16:50:40 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:50:40 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:50:40 [info] index finished after resolve  [object Object] 
2025-04-04 16:50:40 [info] refresh page data from resolve listeners 0 725   
2025-04-04 16:50:43 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:50:43 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:50:43 [info] index finished after resolve  [object Object] 
2025-04-04 16:50:43 [info] refresh page data from resolve listeners 0 725   
2025-04-04 16:50:45 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:50:46 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:50:46 [info] index finished after resolve  [object Object] 
2025-04-04 16:50:46 [info] refresh page data from resolve listeners 0 725   
2025-04-04 16:51:01 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-04-16-51-01.png  [object Object] 
2025-04-04 16:51:02 [info] refresh page data from created listeners 0 726   
2025-04-04 16:51:03 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:51:04 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:51:04 [info] index finished after resolve  [object Object] 
2025-04-04 16:51:04 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:41 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:41 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:41 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:41 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:44 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:44 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:44 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:46 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:46 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:46 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:46 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:49 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:49 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:49 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:49 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:51 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:51 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:51 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:51 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:53 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:54 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:54 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:54 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:56 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:56 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:56 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:56 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:53:58 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:53:58 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:53:58 [info] index finished after resolve  [object Object] 
2025-04-04 16:53:58 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:02 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:02 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:02 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:02 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:07 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:07 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:07 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:07 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:09 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:09 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:09 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:09 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:12 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:12 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:12 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:12 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:14 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:14 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:14 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:14 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:16 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:16 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:16 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:16 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:18 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:18 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:18 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:18 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:22 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:22 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:22 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:22 [info] refresh page data from resolve listeners 0 726   
2025-04-04 16:54:25 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 16:54:25 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 16:54:25 [info] index finished after resolve  [object Object] 
2025-04-04 16:54:25 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:00 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:00 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:00 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:00 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:04 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:04 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:04 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:04 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:09 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:09 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:09 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:09 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:11 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:11 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:11 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:11 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:14 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:14 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:14 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:14 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:05:16 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-04 21:05:16 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-04 21:05:16 [info] index finished after resolve  [object Object] 
2025-04-04 21:05:16 [info] refresh page data from resolve listeners 0 726   
2025-04-04 21:49:19 [info] components database created cost 7 ms   
2025-04-04 21:49:19 [info] components index initializing...   
2025-04-04 21:49:21 [info] start to batch put pages: 5   
2025-04-04 21:49:22 [info] batch persist cost 5  1404 
2025-04-04 21:49:22 [info] components index initialized, 726 files cost 3325 ms   
2025-04-04 21:49:22 [info] refresh page data from init listeners 0 726   
2025-04-04 21:49:25 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 21:49:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-04 21:49:27 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-04 21:49:27 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-04 21:49:27 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-04 21:49:27 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-04 21:50:06 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-04 21:50:06 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-04 21:50:06 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-04 21:51:45 [info] ignore file modify evnet 学习库/Deep learning/训练实践/一些基础.md   
2025-04-04 21:51:46 [info] trigger 学习库/Deep learning/训练实践/一些基础.md resolve  [object Object] 
2025-04-04 21:51:46 [info] index finished after resolve  [object Object] 
2025-04-04 21:51:46 [info] refresh page data from resolve listeners 0 726   
