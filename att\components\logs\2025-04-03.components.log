2025-04-03 09:05:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:09 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:09 [info] refresh page data from resolve listeners 0 716   
2025-04-03 09:05:09 [info] indexing created file components/logs/2025-04-03.components.log  [object Object] 
2025-04-03 09:05:09 [info] refresh page data from created listeners 0 717   
2025-04-03 09:05:11 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:11 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:11 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:13 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:13 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:16 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:16 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:18 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:18 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:20 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:23 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:42 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:42 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:44 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:44 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:44 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:46 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:46 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:49 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:49 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:49 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:52 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:52 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:05:55 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:05:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:05:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:05:55 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:01 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:01 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:01 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:04 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:04 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:04 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:09 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:09 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:12 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:12 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:12 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:12 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:15 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:15 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:15 [info] refresh page data from resolve listeners 0 717   
2025-04-03 09:06:15 [info] indexing created file 日记库/day/2025-04-02.md  [object Object] 
2025-04-03 09:06:15 [info] indexing created ignore file 日记库/day/2025-04-02.md   
2025-04-03 09:06:15 [info] trigger 日记库/day/2025-04-02.md resolve  [object Object] 
2025-04-03 09:06:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:15 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:19 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:22 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:22 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:25 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:25 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:25 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:25 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:28 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:28 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:32 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:32 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:32 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:34 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:37 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:37 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:37 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:39 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:39 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:46 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:46 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:49 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:49 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:49 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:52 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:52 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:06:54 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:06:54 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:06:54 [info] index finished after resolve  [object Object] 
2025-04-03 09:06:54 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:44 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:44 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:44 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:48 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:50 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:50 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:50 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:52 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:52 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:55 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:55 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:08:58 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:08:58 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:08:58 [info] index finished after resolve  [object Object] 
2025-04-03 09:08:58 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:03 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:03 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:03 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:03 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:05 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:05 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:08 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:08 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:08 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:08 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:10 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:10 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:14 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:14 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:14 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:14 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:17 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:17 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:17 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:20 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:23 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:23 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:23 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:27 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:27 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:27 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:29 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:29 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:29 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:31 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:31 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:31 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:33 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:33 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:33 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:35 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:35 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:35 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:35 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:37 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:37 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:37 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:40 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:40 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:42 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:42 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:45 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:45 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:48 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:50 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:50 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:50 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:53 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:53 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:53 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:55 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:55 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:09:57 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:09:57 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:09:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:09:57 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:00 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:00 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:00 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:00 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:02 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:02 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:02 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:02 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:05 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:05 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:07 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:07 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:07 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:07 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:09 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:09 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:13 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:13 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:17 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:17 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:17 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:19 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:22 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:22 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:26 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:26 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:26 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:26 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:28 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:28 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:28 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:28 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:39 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:39 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:39 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:41 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:41 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:41 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:41 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:44 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:44 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:44 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:46 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:46 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:49 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:49 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:49 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:49 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:54 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:54 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:54 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:54 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:56 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:56 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:56 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:56 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:10:58 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:10:59 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:10:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:10:59 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:00 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:01 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:01 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:03 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:03 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:03 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:03 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:06 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:06 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:20 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:45 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:45 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:45 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:45 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:48 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:51 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:51 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:51 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:56 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:56 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:56 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:56 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:11:58 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:11:58 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:11:58 [info] index finished after resolve  [object Object] 
2025-04-03 09:11:58 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:06 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:06 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:10 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:10 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:13 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:13 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:13 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:15 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:15 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:15 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:17 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:17 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:17 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:17 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:19 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:22 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:22 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:22 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:22 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:24 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:24 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:24 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:24 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:30 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:30 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:30 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:32 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:32 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:32 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:32 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:35 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:35 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:35 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:35 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:37 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:37 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:37 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:40 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:40 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:40 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:42 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:42 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:42 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:46 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:46 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:48 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:51 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:51 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:51 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:53 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:53 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:53 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:12:55 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:12:55 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:12:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:12:55 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:04 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:04 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:04 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:06 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:06 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:06 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:09 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:09 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:09 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:12 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:12 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:12 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:12 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:18 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:18 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:18 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:20 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:20 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:20 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:31 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:31 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:31 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:34 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:34 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:34 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:43 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:43 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:43 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:13:46 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:13:46 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:13:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:13:46 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:27 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:27 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:27 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:29 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:29 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:29 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:31 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:31 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:31 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:33 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:33 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:33 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:36 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:36 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:36 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:41 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:42 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:42 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:42 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:43 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:44 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:44 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:48 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:48 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:48 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:51 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:51 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:51 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:53 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:53 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:53 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:53 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:14:56 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:14:56 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:14:56 [info] index finished after resolve  [object Object] 
2025-04-03 09:14:56 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:00 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:00 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:00 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:00 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:03 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:03 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:03 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:03 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:05 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:05 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:05 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:05 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:07 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:07 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:07 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:07 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:10 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:10 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:10 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:12 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:12 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:12 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:12 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:14 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:14 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:14 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:14 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:16 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:16 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:16 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:16 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:15:19 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:15:19 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:15:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:15:19 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:22:52 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:22:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:22:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:22:52 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:22:54 [info] ignore file modify evnet 学习库/c/7 指针.md   
2025-04-03 09:22:54 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-03 09:22:54 [info] index finished after resolve  [object Object] 
2025-04-03 09:22:54 [info] refresh page data from resolve listeners 0 718   
2025-04-03 09:33:31 [info] indexing created file 学习库/c/未命名.md  [object Object] 
2025-04-03 09:33:31 [info] indexing created ignore file 学习库/c/未命名.md   
2025-04-03 09:33:31 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-03 09:33:31 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-03 09:33:31 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-03 09:33:31 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-03 09:33:31 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-03 09:33:31 [info] trigger 学习库/c/未命名.md resolve  [object Object] 
2025-04-03 09:33:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:33:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:33:37 [info] refresh page data from rename listeners 0 719   
2025-04-03 09:33:37 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-03 09:33:37 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-03 09:33:37 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-03 09:33:37 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-03 09:33:37 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-03 09:33:40 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:33:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:33:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:33:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:33:47 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:33:47 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:33:47 [info] index finished after resolve  [object Object] 
2025-04-03 09:33:47 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:33:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:33:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:33:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:33:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:35 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:37 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:37 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:37 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:46 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:46 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:46 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:50 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:53 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:53 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:53 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:53 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:55 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:55 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:55 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:36:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:36:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:36:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:36:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:01 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:01 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:01 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:20 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:20 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:20 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:22 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:22 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:25 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:32 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:32 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:36 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:36 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:36 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:38 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:38 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:38 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:38 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:40 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:45 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:45 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:45 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:45 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:51 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:51 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:51 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:54 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:54 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:54 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:54 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:37:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:37:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:37:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:37:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:01 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:01 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:01 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:04 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:08 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:08 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:10 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:10 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:10 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:12 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:12 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:12 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:12 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:14 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:14 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:14 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:14 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:37 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:37 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:37 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:39 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:39 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:41 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:44 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:44 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:44 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:47 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:47 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:47 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:47 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:51 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:51 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:51 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:55 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:55 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:55 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:38:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:38:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:38:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:38:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:02 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:02 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:02 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:08 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:08 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:20 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:20 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:20 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:20 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:22 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:22 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:24 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:24 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:24 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:24 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:35 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:39 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:39 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:44 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:44 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:44 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:50 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:52 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:52 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:55 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:55 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:55 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:39:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:39:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:39:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:39:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:04 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:07 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:07 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:07 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:07 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:19 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:19 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:19 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:40:24 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:40:24 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:40:24 [info] index finished after resolve  [object Object] 
2025-04-03 09:40:24 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:19 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:19 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:19 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:24 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:24 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:24 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:24 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:26 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:26 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:26 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:26 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:28 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:28 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:28 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:41:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:41:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:41:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:41:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:44:49 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:44:49 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:44:49 [info] index finished after resolve  [object Object] 
2025-04-03 09:44:49 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:44:52 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:44:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:44:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:44:52 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:44:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:44:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:44:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:44:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:00 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:00 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:00 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:00 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:02 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:02 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:02 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:04 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:08 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:08 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:10 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:10 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:10 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:15 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:17 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:17 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:17 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:17 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:24 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:24 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:24 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:24 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:26 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:26 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:26 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:26 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:36 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:36 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:36 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:38 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:38 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:38 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:38 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:40 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:45 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:45 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:45 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:45 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:49 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:49 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:49 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:49 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:51 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:51 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:51 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:53 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:53 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:53 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:53 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:56 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:56 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:56 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:56 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:45:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:45:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:45:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:45:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:01 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:01 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:01 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:04 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:14 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:14 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:14 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:14 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:16 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:16 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:16 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:16 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:19 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:19 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:19 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:36 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:36 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:36 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:38 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:38 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:38 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:38 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:40 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:50 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:54 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:54 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:54 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:54 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:46:58 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:46:58 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:46:58 [info] index finished after resolve  [object Object] 
2025-04-03 09:46:58 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:00 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:00 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:00 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:00 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:02 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:02 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:02 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:04 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:04 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:04 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:04 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:11 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:13 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:16 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:16 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:16 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:16 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:18 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:29 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:32 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:36 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:36 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:36 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:38 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:38 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:38 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:38 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:41 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:56 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:56 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:56 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:56 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:47:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:47:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:47:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:47:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:05 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:05 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:05 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:05 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:31 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:33 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:36 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:36 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:36 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:36 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:39 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:39 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:41 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:51 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:51 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:51 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:51 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:55 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:55 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:55 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:55 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:51:58 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:51:58 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:51:58 [info] index finished after resolve  [object Object] 
2025-04-03 09:51:58 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:55:52 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:55:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:55:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:55:52 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:55:57 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:55:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:55:57 [info] index finished after resolve  [object Object] 
2025-04-03 09:55:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:55:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:55:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:55:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:55:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:01 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:01 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:01 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:01 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:03 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:03 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:03 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:03 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:06 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:08 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:08 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:10 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:10 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:10 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:10 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:16 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:16 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:16 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:16 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:19 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:19 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:19 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:25 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:27 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:34 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:37 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:37 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:37 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:39 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:39 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:39 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:41 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:43 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:46 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:46 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:46 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:46 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:48 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:50 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:50 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:52 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:52 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:52 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:56:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:56:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:56:59 [info] index finished after resolve  [object Object] 
2025-04-03 09:56:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:02 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:02 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:02 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:05 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:05 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:05 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:05 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:09 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:19 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:19 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:19 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:19 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:21 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:23 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:25 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:28 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:28 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:28 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:30 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:32 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:32 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:35 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:37 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:37 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:37 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:37 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:40 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:42 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:42 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:42 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:42 [info] refresh page data from resolve listeners 0 719   
2025-04-03 09:57:44 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 09:57:44 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 09:57:44 [info] index finished after resolve  [object Object] 
2025-04-03 09:57:44 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:12 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:12 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:12 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:12 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:18 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:21 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:23 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:23 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:25 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 11:37:28 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 11:37:28 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 11:37:28 [info] index finished after resolve  [object Object] 
2025-04-03 11:37:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:16:56 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:16:56 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:16:56 [info] index finished after resolve  [object Object] 
2025-04-03 16:16:56 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:16:59 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:16:59 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:16:59 [info] index finished after resolve  [object Object] 
2025-04-03 16:16:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:17:03 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:17:03 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:17:03 [info] index finished after resolve  [object Object] 
2025-04-03 16:17:03 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:17:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:17:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:17:06 [info] index finished after resolve  [object Object] 
2025-04-03 16:17:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:17:08 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:17:08 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:17:08 [info] index finished after resolve  [object Object] 
2025-04-03 16:17:08 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:17:10 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:17:10 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:17:10 [info] index finished after resolve  [object Object] 
2025-04-03 16:17:10 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:17:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:17:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:17:13 [info] index finished after resolve  [object Object] 
2025-04-03 16:17:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:29:45 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:29:45 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:29:45 [info] index finished after resolve  [object Object] 
2025-04-03 16:29:45 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:29:53 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:29:53 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:29:53 [info] index finished after resolve  [object Object] 
2025-04-03 16:29:53 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:33 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:35 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:39 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:39 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:39 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:39 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:41 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:43 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:43 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:43 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:43 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:45 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:45 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:45 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:45 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:30:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:30:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:30:48 [info] index finished after resolve  [object Object] 
2025-04-03 16:30:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:18 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:22 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:22 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:23 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:23 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:23 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:25 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:26 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:26 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:26 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:26 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:28 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:28 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:28 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:29 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:31 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:33 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:34 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:35 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:38 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:38 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:38 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:38 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:41 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:41 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:41 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:41 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:42 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:42 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:42 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:42 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:48 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:48 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:48 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:48 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:49 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:49 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:49 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:49 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:52 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:52 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:52 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:52 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:55 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:55 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:55 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:55 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:36:56 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:36:57 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:36:57 [info] index finished after resolve  [object Object] 
2025-04-03 16:36:57 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:00 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:00 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:00 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:00 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:02 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:02 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:02 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:07 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:07 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:07 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:07 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:10 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:10 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:10 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:10 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:13 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:37:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:37:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:37:15 [info] index finished after resolve  [object Object] 
2025-04-03 16:37:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:44:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:44:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:44:27 [info] index finished after resolve  [object Object] 
2025-04-03 16:44:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:44:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:44:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:44:30 [info] index finished after resolve  [object Object] 
2025-04-03 16:44:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:44:32 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:44:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:44:32 [info] index finished after resolve  [object Object] 
2025-04-03 16:44:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:44:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:44:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:44:34 [info] index finished after resolve  [object Object] 
2025-04-03 16:44:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:17 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:17 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:17 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:17 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:20 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:20 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:20 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:20 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:22 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:22 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:27 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:29 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:29 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:29 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:29 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:50:31 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:50:31 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:50:31 [info] index finished after resolve  [object Object] 
2025-04-03 16:50:31 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:05 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:05 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:05 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:05 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:16 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:16 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:16 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:16 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:21 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:21 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:21 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:21 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:25 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:25 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:25 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:25 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:30 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:30 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:30 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:32 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:32 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:35 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:35 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:35 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:35 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:37 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:37 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:37 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:37 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:51:40 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:51:40 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:51:40 [info] index finished after resolve  [object Object] 
2025-04-03 16:51:40 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:52:32 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:52:32 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:52:32 [info] index finished after resolve  [object Object] 
2025-04-03 16:52:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:52:34 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:52:34 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:52:34 [info] index finished after resolve  [object Object] 
2025-04-03 16:52:34 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:06 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:06 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:06 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:06 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:15 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:15 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:15 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:18 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:18 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:18 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:22 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:22 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:22 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:24 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:24 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:24 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:24 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:27 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:27 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:27 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:27 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:46 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:46 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:46 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:46 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:53:50 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:53:50 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:53:50 [info] index finished after resolve  [object Object] 
2025-04-03 16:53:50 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:07 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:07 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:07 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:07 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:09 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:09 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:09 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:09 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:11 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:11 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:11 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:11 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:13 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:13 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:13 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:26 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:26 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:26 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:26 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:28 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:28 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:28 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:54:33 [info] ignore file modify evnet 学习库/c/8 字符串.md   
2025-04-03 16:54:33 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-03 16:54:33 [info] index finished after resolve  [object Object] 
2025-04-03 16:54:33 [info] refresh page data from resolve listeners 0 719   
2025-04-03 16:59:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 16:59:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 16:59:59 [info] index finished after resolve  [object Object] 
2025-04-03 16:59:59 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:02 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:02 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:02 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:02 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:13 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:13 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:13 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:13 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:15 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:15 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:15 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:15 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:18 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:18 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:18 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:18 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:20 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:20 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:20 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:20 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:22 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:22 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:22 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:22 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:27 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:28 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:28 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:28 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:30 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:30 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:30 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:30 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:00:32 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:00:32 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:00:32 [info] index finished after resolve  [object Object] 
2025-04-03 17:00:32 [info] refresh page data from resolve listeners 0 719   
2025-04-03 17:01:30 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-17-01-29.png  [object Object] 
2025-04-03 17:01:30 [info] refresh page data from created listeners 0 720   
2025-04-03 17:01:31 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 17:01:32 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 17:01:32 [info] index finished after resolve  [object Object] 
2025-04-03 17:01:32 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:42 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:42 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:48 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:48 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:48 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:48 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:50 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:50 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:50 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:50 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:52 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:52 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:52 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:52 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:55 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:55 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:55 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:55 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:54:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:54:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:54:59 [info] index finished after resolve  [object Object] 
2025-04-03 19:54:59 [info] refresh page data from resolve listeners 0 720   
2025-04-03 19:55:10 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-19-55-10.png  [object Object] 
2025-04-03 19:55:10 [info] refresh page data from created listeners 0 721   
2025-04-03 19:55:12 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:13 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:13 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:13 [info] refresh page data from resolve listeners 0 721   
2025-04-03 19:55:20 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-19-55-20.png  [object Object] 
2025-04-03 19:55:20 [info] refresh page data from created listeners 0 722   
2025-04-03 19:55:22 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:22 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:22 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:22 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:55:30 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:30 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:30 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:30 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:55:32 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:32 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:32 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:32 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:55:34 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:34 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:34 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:34 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:55:42 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:42 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:42 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:42 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:55:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:55:44 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:55:44 [info] index finished after resolve  [object Object] 
2025-04-03 19:55:44 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:56:34 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:56:34 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:56:34 [info] index finished after resolve  [object Object] 
2025-04-03 19:56:34 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:56:44 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:56:44 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:56:44 [info] index finished after resolve  [object Object] 
2025-04-03 19:56:44 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:56:46 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:56:46 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:56:46 [info] index finished after resolve  [object Object] 
2025-04-03 19:56:46 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:58:44 [info] components database created cost 21 ms   
2025-04-03 19:58:44 [info] components index initializing...   
2025-04-03 19:58:47 [info] start to batch put pages: 7   
2025-04-03 19:58:47 [info] batch persist cost 7  655 
2025-04-03 19:58:47 [info] components index initialized, 722 files cost 2940 ms   
2025-04-03 19:58:47 [info] refresh page data from init listeners 0 722   
2025-04-03 19:58:49 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-03 19:58:53 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-03 19:58:53 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-03 19:58:53 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-03 19:58:53 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-03 19:58:53 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-03 19:58:53 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-03 19:58:54 [info] ignore file modify evnet 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md   
2025-04-03 19:58:54 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-03 19:58:54 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-03 19:58:54 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-03 19:58:54 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-03 19:58:54 [info] trigger 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md resolve  [object Object] 
2025-04-03 19:58:54 [info] index finished after resolve  [object Object] 
2025-04-03 19:58:54 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:59:15 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:59:15 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:59:15 [info] index finished after resolve  [object Object] 
2025-04-03 19:59:15 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:59:50 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:59:50 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:59:50 [info] index finished after resolve  [object Object] 
2025-04-03 19:59:50 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:59:53 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:59:53 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:59:53 [info] index finished after resolve  [object Object] 
2025-04-03 19:59:53 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:59:56 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:59:56 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:59:56 [info] index finished after resolve  [object Object] 
2025-04-03 19:59:56 [info] refresh page data from resolve listeners 0 722   
2025-04-03 19:59:59 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 19:59:59 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 19:59:59 [info] index finished after resolve  [object Object] 
2025-04-03 19:59:59 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:08 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:08 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:08 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:08 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:11 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:11 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:11 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:11 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:15 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:15 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:15 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:15 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:22 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:22 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:22 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:22 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:25 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:25 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:25 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:25 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:27 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:27 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:27 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:27 [info] refresh page data from resolve listeners 0 722   
2025-04-03 20:00:29 [info] ignore file modify evnet 学习库/stm32/4 I2C.md   
2025-04-03 20:00:29 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-03 20:00:29 [info] index finished after resolve  [object Object] 
2025-04-03 20:00:29 [info] refresh page data from resolve listeners 0 722   
