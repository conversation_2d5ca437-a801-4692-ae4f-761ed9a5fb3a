2025-03-11 00:07:39 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 00:07:39 [info] indexing created file components/logs/2025-03-11.components.log  [object Object] 
2025-03-11 00:07:39 [info] refresh page data from created listeners 0 545   
2025-03-11 00:07:45 [info] ignore file modify evnet Templater/日记/fleeting_note.md   
2025-03-11 00:07:45 [info] trigger Templater/日记/fleeting_note.md resolve  [object Object] 
2025-03-11 00:07:45 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:45 [info] refresh page data from resolve listeners 0 545   
2025-03-11 00:07:46 [info] ignore file modify evnet Templater/日记/日记模板.md   
2025-03-11 00:07:46 [info] trigger Templater/日记/日记模板.md resolve  [object Object] 
2025-03-11 00:07:46 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:46 [info] refresh page data from resolve listeners 0 545   
2025-03-11 00:07:47 [info] ignore file modify evnet Templater/日记/日记月复盘模板.md   
2025-03-11 00:07:47 [info] trigger Templater/日记/日记月复盘模板.md resolve  [object Object] 
2025-03-11 00:07:47 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:47 [info] refresh page data from resolve listeners 0 545   
2025-03-11 00:07:48 [info] indexing created file Excalidraw/Drawing 2025-03-10 23.52.30.excalidraw.md  [object Object] 
2025-03-11 00:07:48 [info] indexing created ignore file Excalidraw/Drawing 2025-03-10 23.52.30.excalidraw.md   
2025-03-11 00:07:48 [info] trigger Excalidraw/Drawing 2025-03-10 23.52.30.excalidraw.md resolve  [object Object] 
2025-03-11 00:07:48 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:48 [info] refresh page data from resolve listeners 0 546   
2025-03-11 00:07:50 [info] indexing created file 日记库.components.md  [object Object] 
2025-03-11 00:07:50 [info] indexing created ignore file 日记库.components.md   
2025-03-11 00:07:50 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:07:50 [info] trigger 日记库.components.md resolve  [object Object] 
2025-03-11 00:07:50 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:50 [info] refresh page data from resolve listeners 0 547   
2025-03-11 00:07:51 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 00:07:52 [info] ignore file modify evnet Home/components/view/快速导航.md   
2025-03-11 00:07:52 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:07:52 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:52 [info] refresh page data from resolve listeners 0 547   
2025-03-11 00:07:52 [info] refresh page data from delete listeners 0 546   
2025-03-11 00:07:53 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 545   
2025-03-11 00:07:53 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 544   
2025-03-11 00:07:53 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 543   
2025-03-11 00:07:53 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 542   
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 541   
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 540   
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 539   
2025-03-11 00:07:53 [info] refresh page data from delete listeners 0 538   
2025-03-11 00:07:54 [info] refresh page data from delete listeners 0 537   
2025-03-11 00:07:54 [info] refresh page data from delete listeners 0 536   
2025-03-11 00:07:54 [info] indexing created file 工作库/论文撰写/舌诊/综述.md  [object Object] 
2025-03-11 00:07:54 [info] indexing created ignore file 工作库/论文撰写/舌诊/综述.md   
2025-03-11 00:07:55 [info] trigger 工作库/论文撰写/舌诊/综述.md resolve  [object Object] 
2025-03-11 00:07:55 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:55 [info] refresh page data from resolve listeners 0 537   
2025-03-11 00:07:55 [info] indexing created file 工作库/论文撰写/舌诊/提问模拟.md  [object Object] 
2025-03-11 00:07:55 [info] indexing created ignore file 工作库/论文撰写/舌诊/提问模拟.md   
2025-03-11 00:07:55 [info] trigger 工作库/论文撰写/舌诊/提问模拟.md resolve  [object Object] 
2025-03-11 00:07:55 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:55 [info] refresh page data from resolve listeners 0 538   
2025-03-11 00:07:57 [info] indexing created file 工作库/论文撰写/晶体/小修.md  [object Object] 
2025-03-11 00:07:57 [info] indexing created ignore file 工作库/论文撰写/晶体/小修.md   
2025-03-11 00:07:57 [info] trigger 工作库/论文撰写/晶体/小修.md resolve  [object Object] 
2025-03-11 00:07:57 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:57 [info] refresh page data from resolve listeners 0 539   
2025-03-11 00:07:57 [info] refresh page data from delete listeners 0 538   
2025-03-11 00:07:57 [info] trigger 软件使用/Total commander/使用手册.md resolve  [object Object] 
2025-03-11 00:07:57 [info] refresh page data from delete listeners 0 537   
2025-03-11 00:07:58 [info] indexing created file 学习库/软件使用/Total commander/使用手册.md  [object Object] 
2025-03-11 00:07:58 [info] indexing created ignore file 学习库/软件使用/Total commander/使用手册.md   
2025-03-11 00:07:58 [info] trigger 学习库/软件使用/Total commander/使用手册.md resolve  [object Object] 
2025-03-11 00:07:58 [info] index finished after resolve  [object Object] 
2025-03-11 00:07:58 [info] refresh page data from resolve listeners 0 538   
2025-03-11 00:08:00 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241215090233.png  [object Object] 
2025-03-11 00:08:00 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:08:00 [info] refresh page data from created listeners 0 539   
2025-03-11 00:08:02 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241215090302.png  [object Object] 
2025-03-11 00:08:02 [info] refresh page data from created listeners 0 540   
2025-03-11 00:08:02 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:08:04 [info] indexing created file 学习库/软件使用/Total commander/Attachments/Pasted image 20240813112156.png  [object Object] 
2025-03-11 00:08:04 [info] refresh page data from created listeners 0 541   
2025-03-11 00:08:04 [info] trigger 学习库/软件使用/Total commander/使用手册.md resolve  [object Object] 
2025-03-11 00:08:04 [info] refresh page data from delete listeners 0 540   
2025-03-11 00:08:04 [info] indexing created file 学习库/英语/目录.md  [object Object] 
2025-03-11 00:08:04 [info] indexing created ignore file 学习库/英语/目录.md   
2025-03-11 00:08:04 [info] trigger 学习库/英语/目录.md resolve  [object Object] 
2025-03-11 00:08:04 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:04 [info] refresh page data from resolve listeners 0 541   
2025-03-11 00:08:06 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241215090242.png  [object Object] 
2025-03-11 00:08:06 [info] trigger ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:08:06 [info] refresh page data from created listeners 0 542   
2025-03-11 00:08:10 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241212151240.png  [object Object] 
2025-03-11 00:08:10 [info] refresh page data from created listeners 0 543   
2025-03-11 00:08:13 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241212150939.png  [object Object] 
2025-03-11 00:08:13 [info] refresh page data from created listeners 0 544   
2025-03-11 00:08:17 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241212151554.png  [object Object] 
2025-03-11 00:08:17 [info] refresh page data from created listeners 0 545   
2025-03-11 00:08:23 [info] indexing created file 工作库/论文撰写/舌诊/Attachments/Pasted image 20241212150852.png  [object Object] 
2025-03-11 00:08:23 [info] refresh page data from created listeners 0 546   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 545   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 544   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 543   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 542   
2025-03-11 00:08:23 [info] trigger Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 541   
2025-03-11 00:08:23 [info] trigger Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 540   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 539   
2025-03-11 00:08:23 [info] trigger python笔记/变量/变量 Excalidraw.md resolve  [object Object] 
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 538   
2025-03-11 00:08:23 [info] trigger Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 537   
2025-03-11 00:08:23 [info] refresh page data from delete listeners 0 536   
2025-03-11 00:08:23 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:23 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 535   
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 534   
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 533   
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 532   
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 531   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 530   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 529   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 528   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 527   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 526   
2025-03-11 00:08:24 [info] trigger 心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:24 [info] refresh page data from delete listeners 0 525   
2025-03-11 00:08:25 [info] refresh page data from delete listeners 0 524   
2025-03-11 00:08:25 [info] trigger 声音识别/倍频程.md resolve  [object Object] 
2025-03-11 00:08:25 [info] trigger 项目/声音识别/倍频程.md resolve  [object Object] 
2025-03-11 00:08:25 [info] refresh page data from delete listeners 0 523   
2025-03-11 00:08:25 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] refresh page data from delete listeners 0 522   
2025-03-11 00:08:25 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] refresh page data from delete listeners 0 521   
2025-03-11 00:08:25 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] refresh page data from delete listeners 0 520   
2025-03-11 00:08:25 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:25 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 519   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 518   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 517   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 516   
2025-03-11 00:08:29 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 515   
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 514   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 513   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 512   
2025-03-11 00:08:29 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 511   
2025-03-11 00:08:29 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 510   
2025-03-11 00:08:29 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 509   
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 508   
2025-03-11 00:08:29 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 507   
2025-03-11 00:08:29 [info] refresh page data from delete listeners 0 506   
2025-03-11 00:08:29 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 505   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 504   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 503   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 502   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 501   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 500   
2025-03-11 00:08:30 [info] trigger 比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 499   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 498   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 497   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 496   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 495   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 494   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 493   
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 492   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 491   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 490   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 489   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 488   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 487   
2025-03-11 00:08:30 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] trigger 项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:30 [info] refresh page data from delete listeners 0 486   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 485   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 484   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 483   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 482   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 481   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 480   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 479   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 478   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 477   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 476   
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 475   
2025-03-11 00:08:31 [info] refresh page data from delete listeners 0 474   
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:31 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:32 [info] indexing created file 工作库/项目/舌诊/阿里云/secret/ALIYUN.md  [object Object] 
2025-03-11 00:08:32 [info] indexing created ignore file 工作库/项目/舌诊/阿里云/secret/ALIYUN.md   
2025-03-11 00:08:32 [info] trigger 工作库/项目/舌诊/阿里云/secret/ALIYUN.md resolve  [object Object] 
2025-03-11 00:08:32 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:32 [info] refresh page data from resolve listeners 0 475   
2025-03-11 00:08:33 [info] indexing created file 工作库/项目/舌诊/阿里云/未命名.md  [object Object] 
2025-03-11 00:08:33 [info] indexing created ignore file 工作库/项目/舌诊/阿里云/未命名.md   
2025-03-11 00:08:33 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:33 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:33 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:33 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:33 [info] trigger 工作库/项目/舌诊/阿里云/未命名.md resolve  [object Object] 
2025-03-11 00:08:33 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:33 [info] refresh page data from resolve listeners 0 476   
2025-03-11 00:08:33 [info] indexing created file 工作库/项目/舌诊/阿里云/flask.md  [object Object] 
2025-03-11 00:08:33 [info] indexing created ignore file 工作库/项目/舌诊/阿里云/flask.md   
2025-03-11 00:08:33 [info] trigger 工作库/项目/舌诊/阿里云/flask.md resolve  [object Object] 
2025-03-11 00:08:33 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:33 [info] refresh page data from resolve listeners 0 477   
2025-03-11 00:08:34 [info] indexing created file 工作库/项目/舌诊/综述.md  [object Object] 
2025-03-11 00:08:34 [info] indexing created ignore file 工作库/项目/舌诊/综述.md   
2025-03-11 00:08:34 [info] trigger 工作库/项目/舌诊/综述.md resolve  [object Object] 
2025-03-11 00:08:34 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from resolve listeners 0 478   
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 477   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 476   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 475   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 474   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 473   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 472   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 471   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 470   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 469   
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 468   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 467   
2025-03-11 00:08:34 [info] trigger 声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 466   
2025-03-11 00:08:34 [info] trigger 声音识别/倍频程.md resolve  [object Object] 
2025-03-11 00:08:34 [info] refresh page data from delete listeners 0 465   
2025-03-11 00:08:34 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 464   
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 463   
2025-03-11 00:08:35 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 462   
2025-03-11 00:08:35 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 461   
2025-03-11 00:08:35 [info] trigger 声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 460   
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 459   
2025-03-11 00:08:35 [info] refresh page data from delete listeners 0 458   
2025-03-11 00:08:36 [info] indexing created file 工作库/项目/舌诊/SCI.md  [object Object] 
2025-03-11 00:08:36 [info] indexing created ignore file 工作库/项目/舌诊/SCI.md   
2025-03-11 00:08:36 [info] trigger 工作库/项目/舌诊/SCI.md resolve  [object Object] 
2025-03-11 00:08:36 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:36 [info] refresh page data from resolve listeners 0 459   
2025-03-11 00:08:36 [info] indexing created file 学习库/Anki/python/语句.md  [object Object] 
2025-03-11 00:08:36 [info] indexing created ignore file 学习库/Anki/python/语句.md   
2025-03-11 00:08:36 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-11 00:08:36 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:36 [info] refresh page data from resolve listeners 0 460   
2025-03-11 00:08:37 [info] indexing created file 学习库/Anki/python/test.md  [object Object] 
2025-03-11 00:08:37 [info] indexing created ignore file 学习库/Anki/python/test.md   
2025-03-11 00:08:37 [info] trigger 学习库/Anki/python/test.md resolve  [object Object] 
2025-03-11 00:08:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:37 [info] refresh page data from resolve listeners 0 461   
2025-03-11 00:08:37 [info] indexing created file 学习库/Anki/机器人学/未命名.md  [object Object] 
2025-03-11 00:08:37 [info] indexing created ignore file 学习库/Anki/机器人学/未命名.md   
2025-03-11 00:08:37 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:37 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:37 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:37 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:37 [info] trigger 学习库/Anki/机器人学/未命名.md resolve  [object Object] 
2025-03-11 00:08:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:37 [info] refresh page data from resolve listeners 0 462   
2025-03-11 00:08:38 [info] indexing created file 学习库/Anki/单词/python.md  [object Object] 
2025-03-11 00:08:38 [info] indexing created ignore file 学习库/Anki/单词/python.md   
2025-03-11 00:08:38 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-11 00:08:38 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:38 [info] refresh page data from resolve listeners 0 463   
2025-03-11 00:08:38 [info] indexing created file 学习库/Anki/python/面向对象.md  [object Object] 
2025-03-11 00:08:38 [info] indexing created ignore file 学习库/Anki/python/面向对象.md   
2025-03-11 00:08:38 [info] trigger 学习库/Anki/python/面向对象.md resolve  [object Object] 
2025-03-11 00:08:38 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:38 [info] refresh page data from resolve listeners 0 464   
2025-03-11 00:08:39 [info] indexing created file 工作库/项目/声音识别/倍频程.md  [object Object] 
2025-03-11 00:08:39 [info] indexing created ignore file 工作库/项目/声音识别/倍频程.md   
2025-03-11 00:08:39 [info] trigger 工作库/项目/声音识别/倍频程.md resolve  [object Object] 
2025-03-11 00:08:39 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:39 [info] refresh page data from resolve listeners 0 465   
2025-03-11 00:08:40 [info] indexing created file 学习库/心得/文献检索指南.md  [object Object] 
2025-03-11 00:08:40 [info] indexing created ignore file 学习库/心得/文献检索指南.md   
2025-03-11 00:08:40 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:08:40 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:40 [info] refresh page data from resolve listeners 0 466   
2025-03-11 00:08:40 [info] indexing created file 工作库/项目/声音识别/概念.md  [object Object] 
2025-03-11 00:08:40 [info] indexing created ignore file 工作库/项目/声音识别/概念.md   
2025-03-11 00:08:41 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 00:08:41 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:41 [info] refresh page data from resolve listeners 0 467   
2025-03-11 00:08:42 [info] indexing created file 学习库/Anki/python/文件操作.md  [object Object] 
2025-03-11 00:08:42 [info] indexing created ignore file 学习库/Anki/python/文件操作.md   
2025-03-11 00:08:42 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-11 00:08:42 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:42 [info] refresh page data from resolve listeners 0 468   
2025-03-11 00:08:42 [info] indexing created file 工作库/项目/舌诊/机械臂.md  [object Object] 
2025-03-11 00:08:42 [info] indexing created ignore file 工作库/项目/舌诊/机械臂.md   
2025-03-11 00:08:42 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-03-11 00:08:42 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:42 [info] refresh page data from resolve listeners 0 469   
2025-03-11 00:08:43 [info] indexing created file 学习库/Anki/python/变量.md  [object Object] 
2025-03-11 00:08:43 [info] indexing created ignore file 学习库/Anki/python/变量.md   
2025-03-11 00:08:43 [info] trigger python笔记/变量/变量 Excalidraw.md resolve  [object Object] 
2025-03-11 00:08:43 [info] trigger 学习库/Anki/python/变量.md resolve  [object Object] 
2025-03-11 00:08:43 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:43 [info] refresh page data from resolve listeners 0 470   
2025-03-11 00:08:44 [info] indexing created file 学习库/Anki/python/数据容器.md  [object Object] 
2025-03-11 00:08:44 [info] indexing created ignore file 学习库/Anki/python/数据容器.md   
2025-03-11 00:08:44 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:08:44 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:44 [info] refresh page data from resolve listeners 0 471   
2025-03-11 00:08:44 [info] indexing created file 工作库/比赛/智慧路灯/UART串口通信.md  [object Object] 
2025-03-11 00:08:44 [info] indexing created ignore file 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-11 00:08:44 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:44 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:44 [info] refresh page data from resolve listeners 0 472   
2025-03-11 00:08:45 [info] indexing created file 学习库/Anki/Deep learning/概念.md  [object Object] 
2025-03-11 00:08:45 [info] indexing created ignore file 学习库/Anki/Deep learning/概念.md   
2025-03-11 00:08:45 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:08:45 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:45 [info] refresh page data from resolve listeners 0 473   
2025-03-11 00:08:46 [info] indexing created file 工作库/项目/声音识别/窗函数.md  [object Object] 
2025-03-11 00:08:46 [info] indexing created ignore file 工作库/项目/声音识别/窗函数.md   
2025-03-11 00:08:46 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:08:46 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:46 [info] refresh page data from resolve listeners 0 474   
2025-03-11 00:08:47 [info] indexing created file 学习库/Anki/二级/公共基础.md  [object Object] 
2025-03-11 00:08:47 [info] indexing created ignore file 学习库/Anki/二级/公共基础.md   
2025-03-11 00:08:47 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 00:08:47 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:47 [info] refresh page data from resolve listeners 0 475   
2025-03-11 00:08:47 [info] indexing created file 工作库/项目/舌诊/未命名.md  [object Object] 
2025-03-11 00:08:47 [info] indexing created ignore file 工作库/项目/舌诊/未命名.md   
2025-03-11 00:08:47 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:08:47 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:08:47 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:08:47 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:08:47 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:08:47 [info] trigger 工作库/项目/舌诊/未命名.md resolve  [object Object] 
2025-03-11 00:08:47 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:47 [info] refresh page data from resolve listeners 0 476   
2025-03-11 00:08:48 [info] indexing created file 工作库/项目/舌诊/线程执行.md  [object Object] 
2025-03-11 00:08:48 [info] indexing created ignore file 工作库/项目/舌诊/线程执行.md   
2025-03-11 00:08:48 [info] trigger 工作库/项目/舌诊/线程执行.md resolve  [object Object] 
2025-03-11 00:08:48 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:48 [info] refresh page data from resolve listeners 0 477   
2025-03-11 00:08:49 [info] indexing created file 学习库/Anki/python/函数.md  [object Object] 
2025-03-11 00:08:49 [info] indexing created ignore file 学习库/Anki/python/函数.md   
2025-03-11 00:08:49 [info] trigger python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md resolve  [object Object] 
2025-03-11 00:08:49 [info] trigger python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-03-11 00:08:49 [info] trigger python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:08:49 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:08:49 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:08:49 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:08:49 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:49 [info] refresh page data from resolve listeners 0 478   
2025-03-11 00:08:50 [info] indexing created file 工作库/项目/舌诊/图像格式.md  [object Object] 
2025-03-11 00:08:50 [info] indexing created ignore file 工作库/项目/舌诊/图像格式.md   
2025-03-11 00:08:50 [info] trigger 工作库/项目/舌诊/图像格式.md resolve  [object Object] 
2025-03-11 00:08:50 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:50 [info] refresh page data from resolve listeners 0 479   
2025-03-11 00:08:51 [info] indexing created file 学习库/Anki/二级/程序设计题.md  [object Object] 
2025-03-11 00:08:51 [info] indexing created ignore file 学习库/Anki/二级/程序设计题.md   
2025-03-11 00:08:51 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:08:51 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:51 [info] refresh page data from resolve listeners 0 480   
2025-03-11 00:08:52 [info] indexing created file 工作库/项目/舌诊/Attachments/Pasted image 20241015211848.png  [object Object] 
2025-03-11 00:08:52 [info] refresh page data from created listeners 0 481   
2025-03-11 00:08:53 [info] indexing created file 学习库/Anki/二级/选择题.md  [object Object] 
2025-03-11 00:08:53 [info] indexing created ignore file 学习库/Anki/二级/选择题.md   
2025-03-11 00:08:53 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:08:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:08:53 [info] refresh page data from resolve listeners 0 482   
2025-03-11 00:08:54 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240520214647.png  [object Object] 
2025-03-11 00:08:54 [info] trigger 工作库/项目/声音识别/倍频程.md resolve  [object Object] 
2025-03-11 00:08:54 [info] refresh page data from created listeners 0 483   
2025-03-11 00:08:56 [info] indexing created file 工作库/项目/舌诊/Attachments/Pasted image 20241015210604.png  [object Object] 
2025-03-11 00:08:56 [info] refresh page data from created listeners 0 484   
2025-03-11 00:08:57 [info] indexing created file 工作库/项目/舌诊/Attachments/Pasted image 20241014093021.png  [object Object] 
2025-03-11 00:08:57 [info] refresh page data from created listeners 0 485   
2025-03-11 00:08:58 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019173727.png  [object Object] 
2025-03-11 00:08:58 [info] refresh page data from created listeners 0 486   
2025-03-11 00:08:58 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:08:59 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517100925.png  [object Object] 
2025-03-11 00:08:59 [info] refresh page data from created listeners 0 487   
2025-03-11 00:08:59 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:09:01 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517111337.png  [object Object] 
2025-03-11 00:09:01 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:09:01 [info] refresh page data from created listeners 0 488   
2025-03-11 00:09:03 [info] indexing created file 工作库/项目/舌诊/Attachments/Pasted image 20241015213508.png  [object Object] 
2025-03-11 00:09:03 [info] refresh page data from created listeners 0 489   
2025-03-11 00:09:04 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517101307.png  [object Object] 
2025-03-11 00:09:04 [info] refresh page data from created listeners 0 490   
2025-03-11 00:09:04 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:09:05 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517100252.png  [object Object] 
2025-03-11 00:09:05 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:09:05 [info] refresh page data from created listeners 0 491   
2025-03-11 00:09:06 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925154920.png  [object Object] 
2025-03-11 00:09:06 [info] refresh page data from created listeners 0 492   
2025-03-11 00:09:06 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 00:09:07 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925155305.png  [object Object] 
2025-03-11 00:09:07 [info] refresh page data from created listeners 0 493   
2025-03-11 00:09:07 [info] refresh page data from delete listeners 0 492   
2025-03-11 00:09:07 [info] refresh page data from delete listeners 0 491   
2025-03-11 00:09:07 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:07 [info] refresh page data from delete listeners 0 490   
2025-03-11 00:09:07 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 489   
2025-03-11 00:09:08 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 488   
2025-03-11 00:09:08 [info] trigger ROS/机器人学/机器人运动学/机器人运动学.md resolve  [object Object] 
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 487   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 486   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 485   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 484   
2025-03-11 00:09:08 [info] trigger ROS/机器人学/机器人运动学/机器人运动学.md resolve  [object Object] 
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 483   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 482   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 481   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 480   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 479   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 478   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 477   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 476   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 475   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 474   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 473   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 472   
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 471   
2025-03-11 00:09:08 [info] trigger python笔记/异常，模块，包/异常.md resolve  [object Object] 
2025-03-11 00:09:08 [info] refresh page data from delete listeners 0 470   
2025-03-11 00:09:08 [info] trigger python笔记/异常，模块，包/异常.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 469   
2025-03-11 00:09:09 [info] trigger python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 468   
2025-03-11 00:09:09 [info] trigger python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 467   
2025-03-11 00:09:09 [info] trigger python笔记/异常，模块，包/综合练习.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 466   
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 465   
2025-03-11 00:09:09 [info] trigger python笔记/python函数/函数.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 464   
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 463   
2025-03-11 00:09:09 [info] trigger python笔记/数据可视化/JSON数据.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 462   
2025-03-11 00:09:09 [info] trigger python笔记/数据可视化/pyecharts模块.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 461   
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 460   
2025-03-11 00:09:09 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 459   
2025-03-11 00:09:09 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 458   
2025-03-11 00:09:09 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 457   
2025-03-11 00:09:09 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 456   
2025-03-11 00:09:09 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:09 [info] refresh page data from delete listeners 0 455   
2025-03-11 00:09:09 [info] trigger python笔记/文件操作/文件编码.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 454   
2025-03-11 00:09:10 [info] trigger python笔记/文件操作/文件编码.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 453   
2025-03-11 00:09:10 [info] trigger python笔记/文件操作/文件操作.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 452   
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 451   
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 450   
2025-03-11 00:09:10 [info] trigger python笔记/数据可视化/柱状图绘制.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 449   
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 448   
2025-03-11 00:09:10 [info] trigger python笔记/数据容器/链表.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 447   
2025-03-11 00:09:10 [info] trigger python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 446   
2025-03-11 00:09:10 [info] trigger python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 445   
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 444   
2025-03-11 00:09:10 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 443   
2025-03-11 00:09:10 [info] trigger python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 442   
2025-03-11 00:09:10 [info] trigger python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 441   
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 440   
2025-03-11 00:09:10 [info] trigger python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 00:09:10 [info] refresh page data from delete listeners 0 439   
2025-03-11 00:09:10 [info] trigger python笔记/数据容器/数据容器.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 438   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 437   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 436   
2025-03-11 00:09:11 [info] trigger python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 435   
2025-03-11 00:09:11 [info] trigger python笔记/数据容器/元组.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 434   
2025-03-11 00:09:11 [info] trigger python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 433   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 432   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 431   
2025-03-11 00:09:11 [info] trigger python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 430   
2025-03-11 00:09:11 [info] trigger python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 429   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 428   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 427   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 426   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 425   
2025-03-11 00:09:11 [info] trigger python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 424   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 423   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 422   
2025-03-11 00:09:11 [info] refresh page data from delete listeners 0 421   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 420   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 419   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 418   
2025-03-11 00:09:12 [info] trigger ROS/ros入门.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 417   
2025-03-11 00:09:12 [info] trigger zotero 配置指南/Zotero Attanger.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 416   
2025-03-11 00:09:12 [info] trigger zotero 配置指南/Zotero Attanger.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 415   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 414   
2025-03-11 00:09:12 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 413   
2025-03-11 00:09:12 [info] trigger ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 412   
2025-03-11 00:09:12 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 411   
2025-03-11 00:09:12 [info] trigger python笔记/数据类型.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 410   
2025-03-11 00:09:12 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 409   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 408   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 407   
2025-03-11 00:09:12 [info] trigger python笔记/变量/变量.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 406   
2025-03-11 00:09:12 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 405   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 404   
2025-03-11 00:09:12 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 403   
2025-03-11 00:09:12 [info] refresh page data from delete listeners 0 402   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 401   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 400   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 399   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 398   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 397   
2025-03-11 00:09:13 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 396   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 395   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 394   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 393   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 392   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 391   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 390   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 389   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 388   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 387   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 386   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 385   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 384   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 383   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 382   
2025-03-11 00:09:13 [info] refresh page data from delete listeners 0 381   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 380   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 379   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 378   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 377   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 376   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 375   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 374   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 373   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 372   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 371   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 370   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 369   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 368   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 367   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 366   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 365   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 364   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 363   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 362   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 361   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 360   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 359   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 358   
2025-03-11 00:09:14 [info] refresh page data from delete listeners 0 357   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 356   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 355   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 354   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 353   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 352   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 351   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 350   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 349   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 348   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 347   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 346   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 345   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 344   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 343   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 342   
2025-03-11 00:09:15 [info] trigger 学习库/Anki/python/test.md resolve  [object Object] 
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 341   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 340   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 339   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 338   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 337   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 336   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 335   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 334   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 333   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 332   
2025-03-11 00:09:15 [info] refresh page data from delete listeners 0 331   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 330   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 329   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 328   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 327   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 326   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 325   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 324   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 323   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 322   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 321   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 320   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 319   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 318   
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 317   
2025-03-11 00:09:16 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 316   
2025-03-11 00:09:16 [info] trigger python笔记/算数运算符.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 315   
2025-03-11 00:09:16 [info] trigger python笔记/算数运算符.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 314   
2025-03-11 00:09:16 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 313   
2025-03-11 00:09:16 [info] trigger python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 312   
2025-03-11 00:09:16 [info] trigger python笔记/判断语句/布尔类型和比较运算符.md resolve  [object Object] 
2025-03-11 00:09:16 [info] refresh page data from delete listeners 0 311   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 310   
2025-03-11 00:09:17 [info] trigger ROS/ros入门.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 309   
2025-03-11 00:09:17 [info] trigger ROS/环境配置食用指南.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 308   
2025-03-11 00:09:17 [info] trigger ROS/ros入门.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 307   
2025-03-11 00:09:17 [info] trigger ROS/环境配置食用指南.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 306   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 305   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 304   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 303   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 302   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 301   
2025-03-11 00:09:17 [info] trigger obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 300   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 299   
2025-03-11 00:09:17 [info] trigger 文献阅读/图表库/YOLOv4/图表卡-YOLOv4结构图-.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 298   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 297   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 296   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 295   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 294   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 293   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 292   
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 291   
2025-03-11 00:09:17 [info] trigger Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 00:09:17 [info] refresh page data from delete listeners 0 290   
2025-03-11 00:09:17 [info] trigger Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/最终解决方案.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 289   
2025-03-11 00:09:18 [info] trigger Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/调整学习率.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 288   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 287   
2025-03-11 00:09:18 [info] trigger Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/只对训练集做最基础的变化.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 286   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 285   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 284   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 283   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 282   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 281   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 280   
2025-03-11 00:09:18 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 279   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 278   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 277   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 276   
2025-03-11 00:09:18 [info] trigger Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 275   
2025-03-11 00:09:18 [info] trigger Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 274   
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 273   
2025-03-11 00:09:18 [info] trigger Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 272   
2025-03-11 00:09:18 [info] trigger Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 271   
2025-03-11 00:09:18 [info] trigger Deep learning/训练实践/火灾检测/添加CBAM注意力机制.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 270   
2025-03-11 00:09:18 [info] trigger Deep learning/训练实践/火灾检测/增加SE注意力机制.md resolve  [object Object] 
2025-03-11 00:09:18 [info] refresh page data from delete listeners 0 269   
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 268   
2025-03-11 00:09:19 [info] trigger Deep learning/训练实践/火灾检测/增加SE注意力机制.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 267   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 266   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 265   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 264   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 263   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 262   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 261   
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 260   
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 259   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 258   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 257   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 256   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 255   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 254   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 253   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 252   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:19 [info] refresh page data from delete listeners 0 251   
2025-03-11 00:09:19 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 250   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/池化层/池化层（pooling layers).md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 249   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 248   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 247   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 246   
2025-03-11 00:09:20 [info] trigger Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 245   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 244   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 243   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 242   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 241   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 240   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 239   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 238   
2025-03-11 00:09:20 [info] trigger Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 237   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 236   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 235   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 234   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 233   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 232   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 231   
2025-03-11 00:09:20 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 230   
2025-03-11 00:09:20 [info] refresh page data from delete listeners 0 229   
2025-03-11 00:09:21 [info] trigger Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 228   
2025-03-11 00:09:21 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 227   
2025-03-11 00:09:21 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 226   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 225   
2025-03-11 00:09:21 [info] trigger Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 224   
2025-03-11 00:09:21 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 223   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 222   
2025-03-11 00:09:21 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 221   
2025-03-11 00:09:21 [info] trigger Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 220   
2025-03-11 00:09:21 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 219   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 218   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 217   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 216   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 215   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 214   
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 213   
2025-03-11 00:09:21 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 212   
2025-03-11 00:09:21 [info] trigger Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 211   
2025-03-11 00:09:21 [info] trigger Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 00:09:21 [info] trigger Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 210   
2025-03-11 00:09:21 [info] trigger Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 00:09:21 [info] refresh page data from delete listeners 0 209   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 208   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 207   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 206   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 205   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 204   
2025-03-11 00:09:22 [info] trigger Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-11 00:09:22 [info] trigger Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 203   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 202   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 201   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 200   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 199   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 198   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 197   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 196   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 195   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 194   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 193   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 192   
2025-03-11 00:09:22 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 191   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 190   
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 189   
2025-03-11 00:09:22 [info] trigger python笔记/数据容器/序列.md resolve  [object Object] 
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 188   
2025-03-11 00:09:22 [info] trigger python笔记/数据容器/集合.md resolve  [object Object] 
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 187   
2025-03-11 00:09:22 [info] trigger python笔记/数据容器/字典.md resolve  [object Object] 
2025-03-11 00:09:22 [info] refresh page data from delete listeners 0 186   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 185   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 184   
2025-03-11 00:09:23 [info] trigger Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:09:23 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 183   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 182   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 181   
2025-03-11 00:09:23 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 180   
2025-03-11 00:09:23 [info] trigger python笔记/变量/变量.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 179   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 178   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 177   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 176   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 175   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 174   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 173   
2025-03-11 00:09:23 [info] trigger obsidian 插件使用说明/css基础教学.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 172   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 171   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 170   
2025-03-11 00:09:23 [info] trigger 学习库/Anki/python/面向对象.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 169   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 168   
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 167   
2025-03-11 00:09:23 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:09:23 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:23 [info] refresh page data from delete listeners 0 166   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 165   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 164   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 163   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 162   
2025-03-11 00:09:24 [info] trigger ROS/机器人学/运动学和动力学的区别.md resolve  [object Object] 
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 161   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 160   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 159   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 158   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 157   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 156   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 155   
2025-03-11 00:09:24 [info] trigger python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:09:24 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:09:24 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:09:24 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 154   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 153   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 152   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 151   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 150   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 149   
2025-03-11 00:09:24 [info] trigger python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 148   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 147   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 146   
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 145   
2025-03-11 00:09:24 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 144   
2025-03-11 00:09:24 [info] trigger python笔记/数据容器/数据容器.md resolve  [object Object] 
2025-03-11 00:09:24 [info] refresh page data from delete listeners 0 143   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 142   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 141   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 140   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 139   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 138   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 137   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 136   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 135   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 134   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 133   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 132   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 131   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 130   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:09:25 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:09:25 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 129   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 128   
2025-03-11 00:09:25 [info] trigger 学习库/Anki/python/变量.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 127   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 126   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 125   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 124   
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 123   
2025-03-11 00:09:25 [info] trigger Home/未命名看板.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 122   
2025-03-11 00:09:25 [info] trigger Home/未命名看板.md resolve  [object Object] 
2025-03-11 00:09:25 [info] refresh page data from delete listeners 0 121   
2025-03-11 00:09:25 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 120   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 119   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 118   
2025-03-11 00:09:26 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:09:26 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 117   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 116   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 115   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 114   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 113   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 112   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 111   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 110   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 109   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 108   
2025-03-11 00:09:26 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 107   
2025-03-11 00:09:26 [info] refresh page data from delete listeners 0 106   
2025-03-11 00:11:48 [info] indexing created file 学习库/ROS/目录.md  [object Object] 
2025-03-11 00:11:48 [info] indexing created ignore file 学习库/ROS/目录.md   
2025-03-11 00:11:48 [info] trigger 学习库/ROS/目录.md resolve  [object Object] 
2025-03-11 00:11:48 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:48 [info] refresh page data from resolve listeners 0 107   
2025-03-11 00:11:49 [info] indexing created file 学习库/python笔记/模块.md  [object Object] 
2025-03-11 00:11:49 [info] indexing created ignore file 学习库/python笔记/模块.md   
2025-03-11 00:11:49 [info] trigger 学习库/python笔记/模块.md resolve  [object Object] 
2025-03-11 00:11:49 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:49 [info] refresh page data from resolve listeners 0 108   
2025-03-11 00:11:50 [info] indexing created file 学习库/python笔记/目录.md  [object Object] 
2025-03-11 00:11:50 [info] indexing created ignore file 学习库/python笔记/目录.md   
2025-03-11 00:11:50 [info] trigger 学习库/python笔记/目录.md resolve  [object Object] 
2025-03-11 00:11:50 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:50 [info] refresh page data from resolve listeners 0 109   
2025-03-11 00:11:51 [info] indexing created file 学习库/obsidian 插件使用说明/image-coverter.md  [object Object] 
2025-03-11 00:11:51 [info] indexing created ignore file 学习库/obsidian 插件使用说明/image-coverter.md   
2025-03-11 00:11:52 [info] trigger 学习库/obsidian 插件使用说明/image-coverter.md resolve  [object Object] 
2025-03-11 00:11:52 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:52 [info] refresh page data from resolve listeners 0 110   
2025-03-11 00:11:52 [info] indexing created file 学习库/obsidian 插件使用说明/目录.md  [object Object] 
2025-03-11 00:11:52 [info] indexing created ignore file 学习库/obsidian 插件使用说明/目录.md   
2025-03-11 00:11:52 [info] trigger 学习库/obsidian 插件使用说明/目录.md resolve  [object Object] 
2025-03-11 00:11:52 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:52 [info] refresh page data from resolve listeners 0 111   
2025-03-11 00:11:53 [info] indexing created file 文献库/文献库/文献库主页.md  [object Object] 
2025-03-11 00:11:53 [info] indexing created ignore file 文献库/文献库/文献库主页.md   
2025-03-11 00:11:53 [info] trigger 文献库/文献库/文献库主页.md resolve  [object Object] 
2025-03-11 00:11:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:53 [info] refresh page data from resolve listeners 0 112   
2025-03-11 00:11:53 [info] indexing created file 文献库/图表库/图表库主页.md  [object Object] 
2025-03-11 00:11:53 [info] indexing created ignore file 文献库/图表库/图表库主页.md   
2025-03-11 00:11:53 [info] trigger Home/未命名看板.md resolve  [object Object] 
2025-03-11 00:11:53 [info] trigger 文献库/图表库/图表库主页.md resolve  [object Object] 
2025-03-11 00:11:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:53 [info] refresh page data from resolve listeners 0 113   
2025-03-11 00:11:54 [info] indexing created file 日记库/fleeting_notes/2025-03-10.md  [object Object] 
2025-03-11 00:11:54 [info] indexing created ignore file 日记库/fleeting_notes/2025-03-10.md   
2025-03-11 00:11:54 [info] trigger 日记库/fleeting_notes/2025-03-10.md resolve  [object Object] 
2025-03-11 00:11:54 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:54 [info] refresh page data from resolve listeners 0 114   
2025-03-11 00:11:55 [info] indexing created file 日记库/fleeting_notes/2025-03-08.md  [object Object] 
2025-03-11 00:11:55 [info] indexing created ignore file 日记库/fleeting_notes/2025-03-08.md   
2025-03-11 00:11:55 [info] trigger 日记库/fleeting_notes/2025-03-08.md resolve  [object Object] 
2025-03-11 00:11:55 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:55 [info] refresh page data from resolve listeners 0 115   
2025-03-11 00:11:56 [info] indexing created file 日记库/month/2025-03.md  [object Object] 
2025-03-11 00:11:56 [info] indexing created ignore file 日记库/month/2025-03.md   
2025-03-11 00:11:56 [info] trigger 日记库/month/2025-03.md resolve  [object Object] 
2025-03-11 00:11:56 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:56 [info] refresh page data from resolve listeners 0 116   
2025-03-11 00:11:57 [info] indexing created file 日记库/week/25-11.md  [object Object] 
2025-03-11 00:11:57 [info] indexing created ignore file 日记库/week/25-11.md   
2025-03-11 00:11:57 [info] trigger 日记库/week/25-11.md resolve  [object Object] 
2025-03-11 00:11:57 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:57 [info] refresh page data from resolve listeners 0 117   
2025-03-11 00:11:57 [info] indexing created file 日记库/week/2025-W11.md  [object Object] 
2025-03-11 00:11:57 [info] indexing created ignore file 日记库/week/2025-W11.md   
2025-03-11 00:11:57 [info] trigger 日记库/week/2025-W11.md resolve  [object Object] 
2025-03-11 00:11:57 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:57 [info] refresh page data from resolve listeners 0 118   
2025-03-11 00:11:58 [info] indexing created file 学习库/Deep learning/概念库/评价指标/语义分割评价指标.md  [object Object] 
2025-03-11 00:11:58 [info] indexing created ignore file 学习库/Deep learning/概念库/评价指标/语义分割评价指标.md   
2025-03-11 00:11:58 [info] trigger 学习库/Deep learning/概念库/评价指标/语义分割评价指标.md resolve  [object Object] 
2025-03-11 00:11:58 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:58 [info] refresh page data from resolve listeners 0 119   
2025-03-11 00:11:58 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/原始YOLOv5n.md  [object Object] 
2025-03-11 00:11:58 [info] indexing created ignore file 学习库/Deep learning/训练实践/火灾检测/原始YOLOv5n.md   
2025-03-11 00:11:58 [info] trigger 学习库/Deep learning/训练实践/火灾检测/原始YOLOv5n.md resolve  [object Object] 
2025-03-11 00:11:58 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:58 [info] refresh page data from resolve listeners 0 120   
2025-03-11 00:11:59 [info] indexing created file 文献库/算法库/算法库主页.md  [object Object] 
2025-03-11 00:11:59 [info] indexing created ignore file 文献库/算法库/算法库主页.md   
2025-03-11 00:11:59 [info] trigger Home/未命名看板.md resolve  [object Object] 
2025-03-11 00:11:59 [info] trigger 文献库/算法库/算法库主页.md resolve  [object Object] 
2025-03-11 00:11:59 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:59 [info] refresh page data from resolve listeners 0 121   
2025-03-11 00:11:59 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/添加CBAM注意力机制.md  [object Object] 
2025-03-11 00:11:59 [info] indexing created ignore file 学习库/Deep learning/训练实践/火灾检测/添加CBAM注意力机制.md   
2025-03-11 00:11:59 [info] trigger 学习库/Deep learning/训练实践/火灾检测/添加CBAM注意力机制.md resolve  [object Object] 
2025-03-11 00:11:59 [info] index finished after resolve  [object Object] 
2025-03-11 00:11:59 [info] refresh page data from resolve listeners 0 122   
2025-03-11 00:12:00 [info] indexing created file 日记库/day/2025-03-09.md  [object Object] 
2025-03-11 00:12:00 [info] indexing created ignore file 日记库/day/2025-03-09.md   
2025-03-11 00:12:00 [info] trigger 日记库/day/2025-03-09.md resolve  [object Object] 
2025-03-11 00:12:00 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:00 [info] refresh page data from resolve listeners 0 123   
2025-03-11 00:12:00 [info] indexing created file 日记库/day/2025-03-10.md  [object Object] 
2025-03-11 00:12:00 [info] indexing created ignore file 日记库/day/2025-03-10.md   
2025-03-11 00:12:00 [info] trigger 日记库/day/2025-03-10.md resolve  [object Object] 
2025-03-11 00:12:00 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:00 [info] refresh page data from resolve listeners 0 124   
2025-03-11 00:12:01 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/增加验证集的数量.md  [object Object] 
2025-03-11 00:12:01 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/增加验证集的数量.md   
2025-03-11 00:12:01 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/增加验证集的数量.md resolve  [object Object] 
2025-03-11 00:12:01 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:01 [info] refresh page data from resolve listeners 0 125   
2025-03-11 00:12:02 [info] indexing created file 日记库/fleeting_notes/闪念.md  [object Object] 
2025-03-11 00:12:02 [info] indexing created ignore file 日记库/fleeting_notes/闪念.md   
2025-03-11 00:12:02 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-11 00:12:02 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:02 [info] refresh page data from resolve listeners 0 126   
2025-03-11 00:12:02 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/只对训练集做最基础的变化.md  [object Object] 
2025-03-11 00:12:02 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/只对训练集做最基础的变化.md   
2025-03-11 00:12:02 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/只对训练集做最基础的变化.md resolve  [object Object] 
2025-03-11 00:12:02 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:02 [info] refresh page data from resolve listeners 0 127   
2025-03-11 00:12:03 [info] indexing created file 日记库/fleeting_notes/记得要去补充学术之星的文件.md  [object Object] 
2025-03-11 00:12:03 [info] indexing created ignore file 日记库/fleeting_notes/记得要去补充学术之星的文件.md   
2025-03-11 00:12:03 [info] trigger 日记库/fleeting_notes/记得要去补充学术之星的文件.md resolve  [object Object] 
2025-03-11 00:12:03 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:03 [info] refresh page data from resolve listeners 0 128   
2025-03-11 00:12:04 [info] indexing created file 日记库/fleeting_notes/Untitled.md  [object Object] 
2025-03-11 00:12:04 [info] indexing created ignore file 日记库/fleeting_notes/Untitled.md   
2025-03-11 00:12:04 [info] trigger 日记库/fleeting_notes/Untitled.md resolve  [object Object] 
2025-03-11 00:12:04 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:04 [info] refresh page data from resolve listeners 0 129   
2025-03-11 00:12:04 [info] indexing created file 学习库/python笔记/算数运算符.md  [object Object] 
2025-03-11 00:12:04 [info] indexing created ignore file 学习库/python笔记/算数运算符.md   
2025-03-11 00:12:04 [info] trigger 学习库/python笔记/算数运算符.md resolve  [object Object] 
2025-03-11 00:12:04 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:04 [info] refresh page data from resolve listeners 0 130   
2025-03-11 00:12:05 [info] indexing created file 学习库/obsidian 插件使用说明/css基础教学.md  [object Object] 
2025-03-11 00:12:05 [info] indexing created ignore file 学习库/obsidian 插件使用说明/css基础教学.md   
2025-03-11 00:12:05 [info] trigger 学习库/obsidian 插件使用说明/css基础教学.md resolve  [object Object] 
2025-03-11 00:12:05 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:05 [info] refresh page data from resolve listeners 0 131   
2025-03-11 00:12:07 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/增加SE注意力机制.md  [object Object] 
2025-03-11 00:12:07 [info] indexing created ignore file 学习库/Deep learning/训练实践/火灾检测/增加SE注意力机制.md   
2025-03-11 00:12:07 [info] trigger 学习库/Deep learning/训练实践/火灾检测/增加SE注意力机制.md resolve  [object Object] 
2025-03-11 00:12:07 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:07 [info] refresh page data from resolve listeners 0 132   
2025-03-11 00:12:08 [info] indexing created file 学习库/python笔记/判断语句/布尔类型和比较运算符.md  [object Object] 
2025-03-11 00:12:08 [info] indexing created ignore file 学习库/python笔记/判断语句/布尔类型和比较运算符.md   
2025-03-11 00:12:08 [info] trigger 学习库/python笔记/判断语句/布尔类型和比较运算符.md resolve  [object Object] 
2025-03-11 00:12:08 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:08 [info] refresh page data from resolve listeners 0 133   
2025-03-11 00:12:08 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/最终解决方案.md  [object Object] 
2025-03-11 00:12:08 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/最终解决方案.md   
2025-03-11 00:12:08 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/最终解决方案.md resolve  [object Object] 
2025-03-11 00:12:08 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:08 [info] refresh page data from resolve listeners 0 134   
2025-03-11 00:12:09 [info] indexing created file 日记库/fleeting_notes/我想要测试以下.md  [object Object] 
2025-03-11 00:12:09 [info] indexing created ignore file 日记库/fleeting_notes/我想要测试以下.md   
2025-03-11 00:12:09 [info] trigger 日记库/fleeting_notes/我想要测试以下.md resolve  [object Object] 
2025-03-11 00:12:09 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:09 [info] refresh page data from resolve listeners 0 135   
2025-03-11 00:12:09 [info] ignore file modify evnet Home/components/view/快速导航.md   
2025-03-11 00:12:09 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:12:09 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:09 [info] refresh page data from resolve listeners 0 135   
2025-03-11 00:12:10 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/调整学习率.md  [object Object] 
2025-03-11 00:12:10 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/调整学习率.md   
2025-03-11 00:12:10 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/调整学习率.md resolve  [object Object] 
2025-03-11 00:12:10 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:10 [info] refresh page data from resolve listeners 0 136   
2025-03-11 00:12:11 [info] indexing created file 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.24.excalidraw.md  [object Object] 
2025-03-11 00:12:11 [info] indexing created ignore file 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.24.excalidraw.md   
2025-03-11 00:12:11 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.24.excalidraw.md resolve  [object Object] 
2025-03-11 00:12:11 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:11 [info] refresh page data from resolve listeners 0 137   
2025-03-11 00:12:11 [info] indexing created file 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.50.excalidraw.md  [object Object] 
2025-03-11 00:12:11 [info] indexing created ignore file 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.50.excalidraw.md   
2025-03-11 00:12:11 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 15.39.50.excalidraw.md resolve  [object Object] 
2025-03-11 00:12:11 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:11 [info] refresh page data from resolve listeners 0 138   
2025-03-11 00:12:12 [info] indexing created file 学习库/python笔记/attachments/Drawing 2023-12-28 16.00.28.excalidraw.md  [object Object] 
2025-03-11 00:12:12 [info] indexing created ignore file 学习库/python笔记/attachments/Drawing 2023-12-28 16.00.28.excalidraw.md   
2025-03-11 00:12:12 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 16.00.28.excalidraw.md resolve  [object Object] 
2025-03-11 00:12:12 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:12 [info] refresh page data from resolve listeners 0 139   
2025-03-11 00:12:12 [info] indexing created file 学习库/Deep learning/概念库/特征融合/concatenate和add.md  [object Object] 
2025-03-11 00:12:12 [info] indexing created ignore file 学习库/Deep learning/概念库/特征融合/concatenate和add.md   
2025-03-11 00:12:12 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-03-11 00:12:12 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:12 [info] refresh page data from resolve listeners 0 140   
2025-03-11 00:12:13 [info] indexing created file 学习库/python笔记/数据输入.md  [object Object] 
2025-03-11 00:12:13 [info] indexing created ignore file 学习库/python笔记/数据输入.md   
2025-03-11 00:12:13 [info] trigger 学习库/python笔记/数据输入.md resolve  [object Object] 
2025-03-11 00:12:13 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:13 [info] refresh page data from resolve listeners 0 141   
2025-03-11 00:12:14 [info] indexing created file 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md  [object Object] 
2025-03-11 00:12:14 [info] indexing created ignore file 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md   
2025-03-11 00:12:14 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-03-11 00:12:14 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:14 [info] refresh page data from resolve listeners 0 142   
2025-03-11 00:12:14 [info] indexing created file 学习库/Deep learning/Transformer.md  [object Object] 
2025-03-11 00:12:14 [info] indexing created ignore file 学习库/Deep learning/Transformer.md   
2025-03-11 00:12:14 [info] trigger 学习库/Deep learning/Transformer.md resolve  [object Object] 
2025-03-11 00:12:14 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:14 [info] refresh page data from resolve listeners 0 143   
2025-03-11 00:12:15 [info] indexing created file 学习库/Deep learning/概念库/标签分配.md  [object Object] 
2025-03-11 00:12:15 [info] indexing created ignore file 学习库/Deep learning/概念库/标签分配.md   
2025-03-11 00:12:15 [info] trigger 学习库/Deep learning/概念库/标签分配.md resolve  [object Object] 
2025-03-11 00:12:15 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:15 [info] refresh page data from resolve listeners 0 144   
2025-03-11 00:12:16 [info] indexing created file 学习库/Deep learning/概念库/机器学习.md  [object Object] 
2025-03-11 00:12:16 [info] indexing created ignore file 学习库/Deep learning/概念库/机器学习.md   
2025-03-11 00:12:16 [info] trigger 学习库/Deep learning/概念库/机器学习.md resolve  [object Object] 
2025-03-11 00:12:16 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:16 [info] refresh page data from resolve listeners 0 145   
2025-03-11 00:12:18 [info] indexing created file 学习库/python笔记/注释.md  [object Object] 
2025-03-11 00:12:18 [info] indexing created ignore file 学习库/python笔记/注释.md   
2025-03-11 00:12:18 [info] trigger 学习库/python笔记/注释.md resolve  [object Object] 
2025-03-11 00:12:18 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:18 [info] refresh page data from resolve listeners 0 146   
2025-03-11 00:12:19 [info] indexing created file 学习库/python笔记/变量/变量.md  [object Object] 
2025-03-11 00:12:19 [info] indexing created ignore file 学习库/python笔记/变量/变量.md   
2025-03-11 00:12:19 [info] trigger 学习库/python笔记/变量/变量.md resolve  [object Object] 
2025-03-11 00:12:19 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:19 [info] refresh page data from resolve listeners 0 147   
2025-03-11 00:12:20 [info] indexing created file 学习库/python笔记/数据容器/链表.md  [object Object] 
2025-03-11 00:12:20 [info] indexing created ignore file 学习库/python笔记/数据容器/链表.md   
2025-03-11 00:12:20 [info] trigger 学习库/python笔记/数据容器/链表.md resolve  [object Object] 
2025-03-11 00:12:20 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:20 [info] refresh page data from resolve listeners 0 148   
2025-03-11 00:12:21 [info] indexing created file 工作库/工作库.components  [object Object] 
2025-03-11 00:12:21 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:12:21 [info] refresh page data from created listeners 0 149   
2025-03-11 00:12:22 [info] indexing created file 学习库/python笔记/leetcode/两数相加.md  [object Object] 
2025-03-11 00:12:22 [info] indexing created ignore file 学习库/python笔记/leetcode/两数相加.md   
2025-03-11 00:12:22 [info] trigger 学习库/python笔记/leetcode/两数相加.md resolve  [object Object] 
2025-03-11 00:12:22 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:22 [info] refresh page data from resolve listeners 0 150   
2025-03-11 00:12:22 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/本来训练没问题，突然出现Permission denied/运行train.py出现permission denied.md  [object Object] 
2025-03-11 00:12:22 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/本来训练没问题，突然出现Permission denied/运行train.py出现permission denied.md   
2025-03-11 00:12:22 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/本来训练没问题，突然出现Permission denied/运行train.py出现permission denied.md resolve  [object Object] 
2025-03-11 00:12:22 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:22 [info] refresh page data from resolve listeners 0 151   
2025-03-11 00:12:24 [info] indexing created file 学习库/ROS/环境配置食用指南.md  [object Object] 
2025-03-11 00:12:24 [info] indexing created ignore file 学习库/ROS/环境配置食用指南.md   
2025-03-11 00:12:24 [info] trigger 学习库/ROS/环境配置食用指南.md resolve  [object Object] 
2025-03-11 00:12:24 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:24 [info] refresh page data from resolve listeners 0 152   
2025-03-11 00:12:24 [info] indexing created file 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -CE（交叉熵，cross-entropy）-.md  [object Object] 
2025-03-11 00:12:24 [info] indexing created ignore file 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -CE（交叉熵，cross-entropy）-.md   
2025-03-11 00:12:24 [info] trigger 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -CE（交叉熵，cross-entropy）-.md resolve  [object Object] 
2025-03-11 00:12:24 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:24 [info] refresh page data from resolve listeners 0 153   
2025-03-11 00:12:31 [info] indexing created file 学习库/python笔记/文件操作/文件编码.md  [object Object] 
2025-03-11 00:12:31 [info] indexing created ignore file 学习库/python笔记/文件操作/文件编码.md   
2025-03-11 00:12:31 [info] trigger 学习库/python笔记/文件操作/文件编码.md resolve  [object Object] 
2025-03-11 00:12:31 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:31 [info] refresh page data from resolve listeners 0 154   
2025-03-11 00:12:32 [info] indexing created file 学习库/Deep learning/概念库/语义分割/语义分割.md  [object Object] 
2025-03-11 00:12:32 [info] indexing created ignore file 学习库/Deep learning/概念库/语义分割/语义分割.md   
2025-03-11 00:12:32 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-03-11 00:12:32 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:32 [info] refresh page data from resolve listeners 0 155   
2025-03-11 00:12:32 [info] indexing created file 学习库/Deep learning/概念库/小目标检测头.md  [object Object] 
2025-03-11 00:12:32 [info] indexing created ignore file 学习库/Deep learning/概念库/小目标检测头.md   
2025-03-11 00:12:32 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-03-11 00:12:32 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:32 [info] refresh page data from resolve listeners 0 156   
2025-03-11 00:12:33 [info] indexing created file 学习库/python笔记/数据类型.md  [object Object] 
2025-03-11 00:12:33 [info] indexing created ignore file 学习库/python笔记/数据类型.md   
2025-03-11 00:12:33 [info] trigger 学习库/python笔记/数据类型.md resolve  [object Object] 
2025-03-11 00:12:33 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:33 [info] refresh page data from resolve listeners 0 157   
2025-03-11 00:12:33 [info] indexing created file 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md  [object Object] 
2025-03-11 00:12:33 [info] indexing created ignore file 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md   
2025-03-11 00:12:33 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-03-11 00:12:33 [info] trigger 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md resolve  [object Object] 
2025-03-11 00:12:34 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:34 [info] refresh page data from resolve listeners 0 158   
2025-03-11 00:12:34 [info] indexing created file 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -MSE（均方误差，Mean Square Error，MSE）-.md  [object Object] 
2025-03-11 00:12:34 [info] indexing created ignore file 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -MSE（均方误差，Mean Square Error，MSE）-.md   
2025-03-11 00:12:34 [info] trigger 学习库/Deep learning/概念库/评价指标/回归评价指标/算法卡 -MSE（均方误差，Mean Square Error，MSE）-.md resolve  [object Object] 
2025-03-11 00:12:34 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:34 [info] refresh page data from resolve listeners 0 159   
2025-03-11 00:12:35 [info] indexing created file 学习库/Deep learning/评价指标.md  [object Object] 
2025-03-11 00:12:35 [info] indexing created ignore file 学习库/Deep learning/评价指标.md   
2025-03-11 00:12:35 [info] trigger 学习库/Deep learning/评价指标.md resolve  [object Object] 
2025-03-11 00:12:35 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:35 [info] refresh page data from resolve listeners 0 160   
2025-03-11 00:12:37 [info] indexing created file 文献库/图表库/YOLOv4/图表卡-YOLOv4结构图-.md  [object Object] 
2025-03-11 00:12:37 [info] indexing created ignore file 文献库/图表库/YOLOv4/图表卡-YOLOv4结构图-.md   
2025-03-11 00:12:37 [info] trigger 文献库/图表库/YOLOv4/图表卡-YOLOv4结构图-.md resolve  [object Object] 
2025-03-11 00:12:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:37 [info] refresh page data from resolve listeners 0 161   
2025-03-11 00:12:37 [info] indexing created file 学习库/Deep learning/神经网络.md  [object Object] 
2025-03-11 00:12:37 [info] indexing created ignore file 学习库/Deep learning/神经网络.md   
2025-03-11 00:12:37 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 00:12:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:37 [info] refresh page data from resolve listeners 0 162   
2025-03-11 00:12:38 [info] indexing created file 学习库/obsidian 插件使用说明/QuickAdd.md  [object Object] 
2025-03-11 00:12:38 [info] indexing created ignore file 学习库/obsidian 插件使用说明/QuickAdd.md   
2025-03-11 00:12:38 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:12:38 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:38 [info] refresh page data from resolve listeners 0 163   
2025-03-11 00:12:39 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 00:12:39 [info] indexing created file 学习库/Deep learning/概念库/分类问题和回归问题/概念卡 - -regression problem (回归问题) & classification problem (分类问题)-.md  [object Object] 
2025-03-11 00:12:39 [info] indexing created ignore file 学习库/Deep learning/概念库/分类问题和回归问题/概念卡 - -regression problem (回归问题) & classification problem (分类问题)-.md   
2025-03-11 00:12:39 [info] trigger 学习库/Deep learning/概念库/分类问题和回归问题/概念卡 - -regression problem (回归问题) & classification problem (分类问题)-.md resolve  [object Object] 
2025-03-11 00:12:39 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:39 [info] refresh page data from resolve listeners 0 164   
2025-03-11 00:12:40 [info] indexing created file 学习库/obsidian 插件使用说明/Templater.md  [object Object] 
2025-03-11 00:12:40 [info] indexing created ignore file 学习库/obsidian 插件使用说明/Templater.md   
2025-03-11 00:12:40 [info] trigger 学习库/obsidian 插件使用说明/Templater.md resolve  [object Object] 
2025-03-11 00:12:40 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:40 [info] refresh page data from resolve listeners 0 165   
2025-03-11 00:12:40 [info] indexing created file 学习库/zotero 配置指南/Zotero Attanger.md  [object Object] 
2025-03-11 00:12:40 [info] indexing created ignore file 学习库/zotero 配置指南/Zotero Attanger.md   
2025-03-11 00:12:40 [info] trigger 学习库/zotero 配置指南/Zotero Attanger.md resolve  [object Object] 
2025-03-11 00:12:40 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:40 [info] refresh page data from resolve listeners 0 166   
2025-03-11 00:12:41 [info] indexing created file 学习库/python笔记/循环语句/while 循环.md  [object Object] 
2025-03-11 00:12:41 [info] indexing created ignore file 学习库/python笔记/循环语句/while 循环.md   
2025-03-11 00:12:41 [info] trigger 学习库/python笔记/循环语句/while 循环.md resolve  [object Object] 
2025-03-11 00:12:41 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:41 [info] refresh page data from resolve listeners 0 167   
2025-03-11 00:12:41 [info] indexing created file 学习库/ROS/机器人学/运动学和动力学的区别.md  [object Object] 
2025-03-11 00:12:41 [info] indexing created ignore file 学习库/ROS/机器人学/运动学和动力学的区别.md   
2025-03-11 00:12:41 [info] trigger 学习库/ROS/机器人学/运动学和动力学的区别.md resolve  [object Object] 
2025-03-11 00:12:41 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:41 [info] refresh page data from resolve listeners 0 168   
2025-03-11 00:12:42 [info] indexing created file 学习库/python笔记/数据可视化/JSON数据.md  [object Object] 
2025-03-11 00:12:42 [info] indexing created ignore file 学习库/python笔记/数据可视化/JSON数据.md   
2025-03-11 00:12:42 [info] trigger 学习库/python笔记/数据可视化/JSON数据.md resolve  [object Object] 
2025-03-11 00:12:42 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:42 [info] refresh page data from resolve listeners 0 169   
2025-03-11 00:12:43 [info] indexing created file 学习库/Deep learning/Unet/Unet.md  [object Object] 
2025-03-11 00:12:43 [info] indexing created ignore file 学习库/Deep learning/Unet/Unet.md   
2025-03-11 00:12:43 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-11 00:12:43 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:43 [info] refresh page data from resolve listeners 0 170   
2025-03-11 00:12:44 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-11 00:12:44 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-11 00:12:44 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:44 [info] refresh page data from resolve listeners 0 170   
2025-03-11 00:12:45 [info] indexing created file 文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md  [object Object] 
2025-03-11 00:12:45 [info] indexing created ignore file 文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md   
2025-03-11 00:12:45 [info] trigger 文献库/算法库/CIOU/算法卡 -CIOU（完全交并比，complete intersection over union）-.md resolve  [object Object] 
2025-03-11 00:12:45 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:45 [info] refresh page data from resolve listeners 0 171   
2025-03-11 00:12:46 [info] indexing created file 学习库/Deep learning/概念库/Softmax函数.md  [object Object] 
2025-03-11 00:12:46 [info] indexing created ignore file 学习库/Deep learning/概念库/Softmax函数.md   
2025-03-11 00:12:46 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:12:46 [info] trigger 学习库/Deep learning/概念库/Softmax函数.md resolve  [object Object] 
2025-03-11 00:12:46 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:46 [info] refresh page data from resolve listeners 0 172   
2025-03-11 00:12:46 [info] indexing created file 学习库/python笔记/leetcode/两数之和.md  [object Object] 
2025-03-11 00:12:46 [info] indexing created ignore file 学习库/python笔记/leetcode/两数之和.md   
2025-03-11 00:12:46 [info] trigger 学习库/python笔记/leetcode/两数之和.md resolve  [object Object] 
2025-03-11 00:12:46 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:46 [info] refresh page data from resolve listeners 0 173   
2025-03-11 00:12:47 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md  [object Object] 
2025-03-11 00:12:47 [info] indexing created ignore file 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md   
2025-03-11 00:12:47 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-11 00:12:47 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:12:47 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:47 [info] refresh page data from resolve listeners 0 174   
2025-03-11 00:12:48 [info] indexing created file 学习库/python笔记/数据可视化/pyecharts模块.md  [object Object] 
2025-03-11 00:12:48 [info] indexing created ignore file 学习库/python笔记/数据可视化/pyecharts模块.md   
2025-03-11 00:12:48 [info] trigger 学习库/python笔记/数据可视化/pyecharts模块.md resolve  [object Object] 
2025-03-11 00:12:48 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:48 [info] refresh page data from resolve listeners 0 175   
2025-03-11 00:12:49 [info] indexing created file 学习库/obsidian 插件使用说明/永久记忆.md  [object Object] 
2025-03-11 00:12:49 [info] indexing created ignore file 学习库/obsidian 插件使用说明/永久记忆.md   
2025-03-11 00:12:49 [info] trigger 学习库/obsidian 插件使用说明/永久记忆.md resolve  [object Object] 
2025-03-11 00:12:49 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:49 [info] refresh page data from resolve listeners 0 176   
2025-03-11 00:12:49 [info] indexing created file 学习库/python笔记/attachments/Drawing 2023-12-28 15.55.49.excalidraw.md  [object Object] 
2025-03-11 00:12:49 [info] indexing created ignore file 学习库/python笔记/attachments/Drawing 2023-12-28 15.55.49.excalidraw.md   
2025-03-11 00:12:50 [info] trigger 学习库/python笔记/attachments/Drawing 2023-12-28 15.55.49.excalidraw.md resolve  [object Object] 
2025-03-11 00:12:50 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:50 [info] refresh page data from resolve listeners 0 177   
2025-03-11 00:12:52 [info] indexing created file 学习库/python笔记/数据容器/序列.md  [object Object] 
2025-03-11 00:12:52 [info] indexing created ignore file 学习库/python笔记/数据容器/序列.md   
2025-03-11 00:12:52 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:12:52 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:12:52 [info] trigger 学习库/python笔记/数据容器/序列.md resolve  [object Object] 
2025-03-11 00:12:52 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:52 [info] refresh page data from resolve listeners 0 178   
2025-03-11 00:12:53 [info] indexing created file 学习库/python笔记/变量/运算符.md  [object Object] 
2025-03-11 00:12:53 [info] indexing created ignore file 学习库/python笔记/变量/运算符.md   
2025-03-11 00:12:53 [info] trigger 学习库/Anki/python/变量.md resolve  [object Object] 
2025-03-11 00:12:53 [info] trigger 学习库/python笔记/变量/运算符.md resolve  [object Object] 
2025-03-11 00:12:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:53 [info] refresh page data from resolve listeners 0 179   
2025-03-11 00:12:54 [info] indexing created file 学习库/python笔记/异常，模块，包/包.md  [object Object] 
2025-03-11 00:12:54 [info] indexing created ignore file 学习库/python笔记/异常，模块，包/包.md   
2025-03-11 00:12:54 [info] trigger 学习库/python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:12:54 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:54 [info] refresh page data from resolve listeners 0 180   
2025-03-11 00:12:56 [info] indexing created file 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md  [object Object] 
2025-03-11 00:12:56 [info] indexing created ignore file 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md   
2025-03-11 00:12:56 [info] trigger 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md resolve  [object Object] 
2025-03-11 00:12:56 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:56 [info] refresh page data from resolve listeners 0 181   
2025-03-11 00:12:58 [info] indexing created file 学习库/Deep learning/概念库/目标检测的结构组成/-概念卡- - -目标检测的结构组成-.mm  [object Object] 
2025-03-11 00:12:58 [info] refresh page data from created listeners 0 182   
2025-03-11 00:12:59 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-11 00:12:59 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:12:59 [info] index finished after resolve  [object Object] 
2025-03-11 00:12:59 [info] refresh page data from resolve listeners 0 182   
2025-03-11 00:13:01 [info] indexing created file 学习库/Deep learning/概念库/欧氏距离&曼哈顿距离/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md  [object Object] 
2025-03-11 00:13:01 [info] indexing created ignore file 学习库/Deep learning/概念库/欧氏距离&曼哈顿距离/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md   
2025-03-11 00:13:01 [info] trigger 学习库/Deep learning/概念库/欧氏距离&曼哈顿距离/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md resolve  [object Object] 
2025-03-11 00:13:01 [info] index finished after resolve  [object Object] 
2025-03-11 00:13:01 [info] refresh page data from resolve listeners 0 183   
2025-03-11 00:13:03 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md  [object Object] 
2025-03-11 00:13:03 [info] indexing created ignore file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md   
2025-03-11 00:13:03 [info] trigger 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡-}--{欧式距离＆曼哈顿距离}-L3V9T7TD.md resolve  [object Object] 
2025-03-11 00:13:03 [info] index finished after resolve  [object Object] 
2025-03-11 00:13:03 [info] refresh page data from resolve listeners 0 184   
2025-03-11 00:13:04 [info] indexing created file 学习库/obsidian 插件使用说明/Obsidian 快捷键一览表.md  [object Object] 
2025-03-11 00:13:04 [info] indexing created ignore file 学习库/obsidian 插件使用说明/Obsidian 快捷键一览表.md   
2025-03-11 00:13:04 [info] trigger 学习库/obsidian 插件使用说明/Obsidian 快捷键一览表.md resolve  [object Object] 
2025-03-11 00:13:04 [info] index finished after resolve  [object Object] 
2025-03-11 00:13:04 [info] refresh page data from resolve listeners 0 185   
2025-03-11 00:19:59 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 00:19:59 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/训练结果分析.md  [object Object] 
2025-03-11 00:19:59 [info] indexing created ignore file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/训练结果分析.md   
2025-03-11 00:19:59 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/训练结果分析.md resolve  [object Object] 
2025-03-11 00:19:59 [info] index finished after resolve  [object Object] 
2025-03-11 00:19:59 [info] refresh page data from resolve listeners 0 186   
2025-03-11 00:20:08 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/机器人运动学.md  [object Object] 
2025-03-11 00:20:08 [info] indexing created ignore file 学习库/ROS/机器人学/机器人运动学/机器人运动学.md   
2025-03-11 00:20:08 [info] trigger 学习库/ROS/机器人学/运动学和动力学的区别.md resolve  [object Object] 
2025-03-11 00:20:08 [info] trigger 学习库/ROS/机器人学/机器人运动学/机器人运动学.md resolve  [object Object] 
2025-03-11 00:20:08 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:08 [info] refresh page data from resolve listeners 0 187   
2025-03-11 00:20:09 [info] indexing created file 学习库/python笔记/异常，模块，包/综合练习.md  [object Object] 
2025-03-11 00:20:09 [info] indexing created ignore file 学习库/python笔记/异常，模块，包/综合练习.md   
2025-03-11 00:20:09 [info] trigger 学习库/python笔记/异常，模块，包/综合练习.md resolve  [object Object] 
2025-03-11 00:20:09 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:09 [info] refresh page data from resolve listeners 0 188   
2025-03-11 00:20:09 [info] indexing created file 学习库/python笔记/数据容器/元组.md  [object Object] 
2025-03-11 00:20:09 [info] indexing created ignore file 学习库/python笔记/数据容器/元组.md   
2025-03-11 00:20:09 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:20:09 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:09 [info] trigger 学习库/python笔记/数据容器/元组.md resolve  [object Object] 
2025-03-11 00:20:09 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:09 [info] refresh page data from resolve listeners 0 189   
2025-03-11 00:20:10 [info] indexing created file 学习库/python笔记/循环语句/for  循环.md  [object Object] 
2025-03-11 00:20:10 [info] indexing created ignore file 学习库/python笔记/循环语句/for  循环.md   
2025-03-11 00:20:10 [info] trigger 学习库/python笔记/循环语句/for  循环.md resolve  [object Object] 
2025-03-11 00:20:10 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:10 [info] refresh page data from resolve listeners 0 190   
2025-03-11 00:20:11 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126221329.png  [object Object] 
2025-03-11 00:20:11 [info] refresh page data from created listeners 0 191   
2025-03-11 00:20:11 [info] ignore file modify evnet 学习库/Anki/Deep learning/概念.md   
2025-03-11 00:20:11 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:20:11 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:11 [info] refresh page data from resolve listeners 0 191   
2025-03-11 00:20:12 [info] indexing created file 学习库/Deep learning/概念库/评价指标/回归评价指标/-概念卡- - -均方误差(MSE)&均方根误差(RMSE)-.mm  [object Object] 
2025-03-11 00:20:12 [info] refresh page data from created listeners 0 192   
2025-03-11 00:20:13 [info] indexing created file 学习库/python笔记/数据容器/集合.md  [object Object] 
2025-03-11 00:20:13 [info] indexing created ignore file 学习库/python笔记/数据容器/集合.md   
2025-03-11 00:20:13 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:20:13 [info] trigger 学习库/python笔记/数据容器/集合.md resolve  [object Object] 
2025-03-11 00:20:13 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:13 [info] refresh page data from resolve listeners 0 193   
2025-03-11 00:20:14 [info] indexing created file 学习库/python笔记/数据容器/字符串.md  [object Object] 
2025-03-11 00:20:14 [info] indexing created ignore file 学习库/python笔记/数据容器/字符串.md   
2025-03-11 00:20:14 [info] trigger 学习库/python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 00:20:14 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:14 [info] refresh page data from resolve listeners 0 194   
2025-03-11 00:20:15 [info] indexing created file 学习库/python笔记/数据可视化/柱状图绘制.md  [object Object] 
2025-03-11 00:20:15 [info] indexing created ignore file 学习库/python笔记/数据可视化/柱状图绘制.md   
2025-03-11 00:20:15 [info] trigger 学习库/python笔记/数据可视化/柱状图绘制.md resolve  [object Object] 
2025-03-11 00:20:15 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:15 [info] refresh page data from resolve listeners 0 195   
2025-03-11 00:20:16 [info] indexing created file 文献库/文献库/舌诊/综述/Robot-assisted sialolithotomy with sialoendoscopy- a review of safety, efficacy and cost.md  [object Object] 
2025-03-11 00:20:16 [info] indexing created ignore file 文献库/文献库/舌诊/综述/Robot-assisted sialolithotomy with sialoendoscopy- a review of safety, efficacy and cost.md   
2025-03-11 00:20:16 [info] trigger 文献库/文献库/舌诊/综述/Robot-assisted sialolithotomy with sialoendoscopy- a review of safety, efficacy and cost.md resolve  [object Object] 
2025-03-11 00:20:16 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:16 [info] refresh page data from resolve listeners 0 196   
2025-03-11 00:20:17 [info] indexing created file 学习库/python笔记/判断语句/if语句.md  [object Object] 
2025-03-11 00:20:17 [info] indexing created ignore file 学习库/python笔记/判断语句/if语句.md   
2025-03-11 00:20:17 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-11 00:20:17 [info] trigger 学习库/python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 00:20:17 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:17 [info] refresh page data from resolve listeners 0 197   
2025-03-11 00:20:18 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-11 00:20:18 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:20:18 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:18 [info] refresh page data from resolve listeners 0 197   
2025-03-11 00:20:18 [info] indexing created file 学习库/python笔记/面向对象/封装，继承，和多态.md  [object Object] 
2025-03-11 00:20:18 [info] indexing created ignore file 学习库/python笔记/面向对象/封装，继承，和多态.md   
2025-03-11 00:20:18 [info] trigger 学习库/Anki/python/面向对象.md resolve  [object Object] 
2025-03-11 00:20:18 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 00:20:18 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:18 [info] refresh page data from resolve listeners 0 198   
2025-03-11 00:20:19 [info] indexing created file 学习库/python笔记/python函数/函数进阶.md  [object Object] 
2025-03-11 00:20:19 [info] indexing created ignore file 学习库/python笔记/python函数/函数进阶.md   
2025-03-11 00:20:19 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:20:19 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:20 [info] trigger 学习库/python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-03-11 00:20:20 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:20 [info] refresh page data from resolve listeners 0 199   
2025-03-11 00:20:20 [info] indexing created file 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md  [object Object] 
2025-03-11 00:20:20 [info] indexing created ignore file 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md   
2025-03-11 00:20:20 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 00:20:20 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:20 [info] refresh page data from resolve listeners 0 200   
2025-03-11 00:20:21 [info] indexing created file 学习库/python笔记/异常，模块，包/异常.md  [object Object] 
2025-03-11 00:20:21 [info] indexing created ignore file 学习库/python笔记/异常，模块，包/异常.md   
2025-03-11 00:20:21 [info] trigger 学习库/python笔记/异常，模块，包/异常.md resolve  [object Object] 
2025-03-11 00:20:21 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:21 [info] refresh page data from resolve listeners 0 201   
2025-03-11 00:20:22 [info] indexing created file 学习库/python笔记/数据容器/字典.md  [object Object] 
2025-03-11 00:20:22 [info] indexing created ignore file 学习库/python笔记/数据容器/字典.md   
2025-03-11 00:20:22 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:22 [info] trigger 学习库/python笔记/数据容器/字典.md resolve  [object Object] 
2025-03-11 00:20:22 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:22 [info] refresh page data from resolve listeners 0 202   
2025-03-11 00:20:23 [info] indexing created file 学习库/python笔记/数据可视化/地图绘制.md  [object Object] 
2025-03-11 00:20:23 [info] indexing created ignore file 学习库/python笔记/数据可视化/地图绘制.md   
2025-03-11 00:20:23 [info] trigger 学习库/python笔记/数据可视化/地图绘制.md resolve  [object Object] 
2025-03-11 00:20:23 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:23 [info] refresh page data from resolve listeners 0 203   
2025-03-11 00:20:24 [info] indexing created file 学习库/python笔记/数据容器/通用操作.md  [object Object] 
2025-03-11 00:20:24 [info] indexing created ignore file 学习库/python笔记/数据容器/通用操作.md   
2025-03-11 00:20:24 [info] trigger 学习库/python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:20:24 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:24 [info] refresh page data from resolve listeners 0 204   
2025-03-11 00:20:24 [info] indexing created file 学习库/Deep learning/yolov5/yaml文件讲解.md  [object Object] 
2025-03-11 00:20:24 [info] indexing created ignore file 学习库/Deep learning/yolov5/yaml文件讲解.md   
2025-03-11 00:20:24 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 00:20:24 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:24 [info] refresh page data from resolve listeners 0 205   
2025-03-11 00:20:25 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----均方误差(MSE)&均方根误差(RMSE)--55HFZRTG.md  [object Object] 
2025-03-11 00:20:25 [info] indexing created ignore file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----均方误差(MSE)&均方根误差(RMSE)--55HFZRTG.md   
2025-03-11 00:20:25 [info] trigger 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----均方误差(MSE)&均方根误差(RMSE)--55HFZRTG.md resolve  [object Object] 
2025-03-11 00:20:25 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:25 [info] refresh page data from resolve listeners 0 206   
2025-03-11 00:20:27 [info] indexing created file 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md  [object Object] 
2025-03-11 00:20:27 [info] indexing created ignore file 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-11 00:20:27 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:20:27 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:20:27 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:20:27 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:27 [info] refresh page data from resolve listeners 0 207   
2025-03-11 00:20:27 [info] indexing created file 学习库/python笔记/异常，模块，包/模块.md  [object Object] 
2025-03-11 00:20:27 [info] indexing created ignore file 学习库/python笔记/异常，模块，包/模块.md   
2025-03-11 00:20:27 [info] trigger 学习库/python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:20:27 [info] trigger 学习库/python笔记/异常，模块，包/模块.md resolve  [object Object] 
2025-03-11 00:20:27 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:27 [info] refresh page data from resolve listeners 0 208   
2025-03-11 00:20:28 [info] indexing created file 学习库/Deep learning/Attention.md  [object Object] 
2025-03-11 00:20:28 [info] indexing created ignore file 学习库/Deep learning/Attention.md   
2025-03-11 00:20:28 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:20:28 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:28 [info] refresh page data from resolve listeners 0 209   
2025-03-11 00:20:29 [info] indexing created file 学习库/python笔记/数据容器/列表.md  [object Object] 
2025-03-11 00:20:29 [info] indexing created ignore file 学习库/python笔记/数据容器/列表.md   
2025-03-11 00:20:29 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/python笔记/数据容器/元组.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/python笔记/数据容器/集合.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:20:29 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 00:20:29 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:29 [info] refresh page data from resolve listeners 0 210   
2025-03-11 00:20:30 [info] indexing created file 学习库/python笔记/文件操作/文件操作.md  [object Object] 
2025-03-11 00:20:30 [info] indexing created ignore file 学习库/python笔记/文件操作/文件操作.md   
2025-03-11 00:20:30 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-11 00:20:30 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:30 [info] trigger 学习库/python笔记/文件操作/文件操作.md resolve  [object Object] 
2025-03-11 00:20:30 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:30 [info] refresh page data from resolve listeners 0 211   
2025-03-11 00:20:30 [info] indexing created file 学习库/python笔记/异常，模块，包/attachements/Pasted image 20240524092933.png  [object Object] 
2025-03-11 00:20:30 [info] refresh page data from created listeners 0 212   
2025-03-11 00:20:31 [info] trigger 学习库/python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 00:20:32 [info] indexing created file 学习库/python笔记/字面量.md  [object Object] 
2025-03-11 00:20:32 [info] indexing created ignore file 学习库/python笔记/字面量.md   
2025-03-11 00:20:32 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:32 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:20:32 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:20:32 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:32 [info] refresh page data from resolve listeners 0 213   
2025-03-11 00:20:33 [info] indexing created file 学习库/ROS/ros入门.md  [object Object] 
2025-03-11 00:20:33 [info] indexing created ignore file 学习库/ROS/ros入门.md   
2025-03-11 00:20:33 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-11 00:20:33 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:33 [info] refresh page data from resolve listeners 0 214   
2025-03-11 00:20:33 [info] indexing created file 学习库/python笔记/数据容器/数据容器.md  [object Object] 
2025-03-11 00:20:33 [info] indexing created ignore file 学习库/python笔记/数据容器/数据容器.md   
2025-03-11 00:20:33 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:20:33 [info] trigger 学习库/python笔记/数据容器/数据容器.md resolve  [object Object] 
2025-03-11 00:20:33 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:33 [info] refresh page data from resolve listeners 0 215   
2025-03-11 00:20:34 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009200556.png  [object Object] 
2025-03-11 00:20:34 [info] refresh page data from created listeners 0 216   
2025-03-11 00:20:35 [info] indexing created file 学习库/ROS/机器人学/机器视觉/相机标定.md  [object Object] 
2025-03-11 00:20:35 [info] indexing created ignore file 学习库/ROS/机器人学/机器视觉/相机标定.md   
2025-03-11 00:20:35 [info] trigger 学习库/ROS/机器人学/机器视觉/相机标定.md resolve  [object Object] 
2025-03-11 00:20:35 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:35 [info] refresh page data from resolve listeners 0 217   
2025-03-11 00:20:36 [info] indexing created file 学习库/python笔记/变量/变量 Excalidraw.md  [object Object] 
2025-03-11 00:20:36 [info] indexing created ignore file 学习库/python笔记/变量/变量 Excalidraw.md   
2025-03-11 00:20:36 [info] trigger 学习库/python笔记/变量/变量.md resolve  [object Object] 
2025-03-11 00:20:36 [info] trigger 学习库/python笔记/变量/变量 Excalidraw.md resolve  [object Object] 
2025-03-11 00:20:36 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:36 [info] refresh page data from resolve listeners 0 218   
2025-03-11 00:20:36 [info] indexing created file 学习库/Deep learning/概念库/语义分割/未命名.md  [object Object] 
2025-03-11 00:20:36 [info] indexing created ignore file 学习库/Deep learning/概念库/语义分割/未命名.md   
2025-03-11 00:20:36 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:20:37 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:20:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:37 [info] refresh page data from resolve listeners 0 219   
2025-03-11 00:20:37 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----目标检测的结构组成--TH33I46H.md  [object Object] 
2025-03-11 00:20:37 [info] indexing created ignore file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----目标检测的结构组成--TH33I46H.md   
2025-03-11 00:20:37 [info] trigger 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{概念卡}----目标检测的结构组成--TH33I46H.md resolve  [object Object] 
2025-03-11 00:20:37 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:37 [info] refresh page data from resolve listeners 0 220   
2025-03-11 00:20:38 [info] indexing created file 学习库/Deep learning/概念库/目标检测的结构组成/{概念卡}----目标检测的结构组成--TH33I46H.md  [object Object] 
2025-03-11 00:20:38 [info] indexing created ignore file 学习库/Deep learning/概念库/目标检测的结构组成/{概念卡}----目标检测的结构组成--TH33I46H.md   
2025-03-11 00:20:38 [info] trigger 学习库/Deep learning/概念库/目标检测的结构组成/{概念卡}----目标检测的结构组成--TH33I46H.md resolve  [object Object] 
2025-03-11 00:20:38 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:38 [info] refresh page data from resolve listeners 0 221   
2025-03-11 00:20:39 [info] indexing created file 学习库/obsidian 插件使用说明/Admonition.md  [object Object] 
2025-03-11 00:20:39 [info] indexing created ignore file 学习库/obsidian 插件使用说明/Admonition.md   
2025-03-11 00:20:39 [info] trigger 学习库/obsidian 插件使用说明/Admonition.md resolve  [object Object] 
2025-03-11 00:20:39 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:39 [info] refresh page data from resolve listeners 0 222   
2025-03-11 00:20:40 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-03-11 00:20:40 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 00:20:40 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:40 [info] refresh page data from resolve listeners 0 222   
2025-03-11 00:20:41 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{算法卡}---(DBC-NMS)@2023-11-25--Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning--AZ4D2QVD.md  [object Object] 
2025-03-11 00:20:41 [info] indexing created ignore file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{算法卡}---(DBC-NMS)@2023-11-25--Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning--AZ4D2QVD.md   
2025-03-11 00:20:41 [info] trigger 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/{算法卡}---(DBC-NMS)@2023-11-25--Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning--AZ4D2QVD.md resolve  [object Object] 
2025-03-11 00:20:41 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:41 [info] refresh page data from resolve listeners 0 223   
2025-03-11 00:20:41 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md  [object Object] 
2025-03-11 00:20:41 [info] indexing created ignore file 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md   
2025-03-11 00:20:42 [info] trigger 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md resolve  [object Object] 
2025-03-11 00:20:42 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:42 [info] refresh page data from resolve listeners 0 224   
2025-03-11 00:20:42 [info] indexing created file 学习库/python笔记/面向对象/类，方法，和对象.md  [object Object] 
2025-03-11 00:20:42 [info] indexing created ignore file 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-11 00:20:42 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:20:42 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:42 [info] refresh page data from resolve listeners 0 225   
2025-03-11 00:20:43 [info] indexing created file 文献库/文献库/舌诊/综述/Application of Data Fusion in Traditional Chinese Medicine- A Review.md  [object Object] 
2025-03-11 00:20:43 [info] indexing created ignore file 文献库/文献库/舌诊/综述/Application of Data Fusion in Traditional Chinese Medicine- A Review.md   
2025-03-11 00:20:43 [info] trigger 文献库/文献库/舌诊/综述/Application of Data Fusion in Traditional Chinese Medicine- A Review.md resolve  [object Object] 
2025-03-11 00:20:43 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:43 [info] refresh page data from resolve listeners 0 226   
2025-03-11 00:20:44 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-11 00:20:44 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:20:44 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:44 [info] refresh page data from resolve listeners 0 226   
2025-03-11 00:20:45 [info] indexing created file 文献库/文献库/舌诊/机械臂/Position-based visual servo control of dual robotic arms with unknown kinematic models- A cerebellum- inspired approach.md  [object Object] 
2025-03-11 00:20:45 [info] indexing created ignore file 文献库/文献库/舌诊/机械臂/Position-based visual servo control of dual robotic arms with unknown kinematic models- A cerebellum- inspired approach.md   
2025-03-11 00:20:45 [info] trigger 文献库/文献库/舌诊/机械臂/Position-based visual servo control of dual robotic arms with unknown kinematic models- A cerebellum- inspired approach.md resolve  [object Object] 
2025-03-11 00:20:45 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:45 [info] refresh page data from resolve listeners 0 227   
2025-03-11 00:20:46 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729165304.png  [object Object] 
2025-03-11 00:20:46 [info] refresh page data from created listeners 0 228   
2025-03-11 00:20:46 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:20:46 [info] indexing created file 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md  [object Object] 
2025-03-11 00:20:46 [info] indexing created ignore file 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md   
2025-03-11 00:20:46 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:20:46 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:46 [info] refresh page data from resolve listeners 0 229   
2025-03-11 00:20:47 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009152312.png  [object Object] 
2025-03-11 00:20:47 [info] refresh page data from created listeners 0 230   
2025-03-11 00:20:48 [info] indexing created file 学习库/python笔记/python函数/函数.md  [object Object] 
2025-03-11 00:20:48 [info] indexing created ignore file 学习库/python笔记/python函数/函数.md   
2025-03-11 00:20:48 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/python笔记/python函数/Drawing 2024-04-25 10.59.22.excalidraw.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/python笔记/数据容器/通用操作.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 00:20:48 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-11 00:20:48 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:48 [info] refresh page data from resolve listeners 0 231   
2025-03-11 00:20:49 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111161259.png  [object Object] 
2025-03-11 00:20:49 [info] refresh page data from created listeners 0 232   
2025-03-11 00:20:49 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250227160400.png  [object Object] 
2025-03-11 00:20:50 [info] refresh page data from created listeners 0 233   
2025-03-11 00:20:50 [info] indexing created file 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md  [object Object] 
2025-03-11 00:20:50 [info] indexing created ignore file 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md   
2025-03-11 00:20:50 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:20:50 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:50 [info] refresh page data from resolve listeners 0 234   
2025-03-11 00:20:51 [info] indexing created file 文献库/文献库/舌诊/综述/A survey of artificial intelligence in tongue image for disease diagnosis and syndrome differentiation.md  [object Object] 
2025-03-11 00:20:51 [info] indexing created ignore file 文献库/文献库/舌诊/综述/A survey of artificial intelligence in tongue image for disease diagnosis and syndrome differentiation.md   
2025-03-11 00:20:51 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:20:51 [info] trigger 文献库/文献库/舌诊/综述/A survey of artificial intelligence in tongue image for disease diagnosis and syndrome differentiation.md resolve  [object Object] 
2025-03-11 00:20:51 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:51 [info] refresh page data from resolve listeners 0 235   
2025-03-11 00:20:52 [info] indexing created file 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md  [object Object] 
2025-03-11 00:20:52 [info] indexing created ignore file 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md   
2025-03-11 00:20:52 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 00:20:52 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:52 [info] refresh page data from resolve listeners 0 236   
2025-03-11 00:20:53 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/导论.md  [object Object] 
2025-03-11 00:20:53 [info] indexing created ignore file 学习库/ROS/机器人学/机器人运动学/导论.md   
2025-03-11 00:20:53 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:20:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:53 [info] refresh page data from resolve listeners 0 237   
2025-03-11 00:20:54 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126213809.png  [object Object] 
2025-03-11 00:20:54 [info] refresh page data from created listeners 0 238   
2025-03-11 00:20:55 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730111724.png  [object Object] 
2025-03-11 00:20:55 [info] refresh page data from created listeners 0 239   
2025-03-11 00:20:55 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:20:55 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-11 00:20:55 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:20:55 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:55 [info] refresh page data from resolve listeners 0 239   
2025-03-11 00:20:56 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241011100818.png  [object Object] 
2025-03-11 00:20:56 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 00:20:56 [info] refresh page data from created listeners 0 240   
2025-03-11 00:20:57 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md  [object Object] 
2025-03-11 00:20:57 [info] indexing created ignore file 学习库/Deep learning/概念库/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md   
2025-03-11 00:20:57 [info] trigger 学习库/Deep learning/概念库/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md resolve  [object Object] 
2025-03-11 00:20:57 [info] index finished after resolve  [object Object] 
2025-03-11 00:20:57 [info] refresh page data from resolve listeners 0 241   
2025-03-11 00:20:58 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729162504.png  [object Object] 
2025-03-11 00:20:58 [info] refresh page data from created listeners 0 242   
2025-03-11 00:20:58 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:20:59 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013213220.png  [object Object] 
2025-03-11 00:20:59 [info] refresh page data from created listeners 0 243   
2025-03-11 00:20:59 [info] trigger 学习库/ROS/机器人学/机器人运动学/机器人运动学.md resolve  [object Object] 
2025-03-11 00:21:00 [info] indexing created file 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md  [object Object] 
2025-03-11 00:21:00 [info] indexing created ignore file 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md   
2025-03-11 00:21:00 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:21:00 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:00 [info] refresh page data from resolve listeners 0 244   
2025-03-11 00:21:01 [info] indexing created file 文献库/文献库/舌诊/Tongue crack recognition using segmentation based deep learning.md  [object Object] 
2025-03-11 00:21:01 [info] indexing created ignore file 文献库/文献库/舌诊/Tongue crack recognition using segmentation based deep learning.md   
2025-03-11 00:21:01 [info] trigger 文献库/文献库/舌诊/Tongue crack recognition using segmentation based deep learning.md resolve  [object Object] 
2025-03-11 00:21:01 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:01 [info] refresh page data from resolve listeners 0 245   
2025-03-11 00:21:02 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/Pasted image 20240730155551.png  [object Object] 
2025-03-11 00:21:02 [info] refresh page data from created listeners 0 246   
2025-03-11 00:21:02 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:21:03 [info] indexing created file 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md  [object Object] 
2025-03-11 00:21:03 [info] indexing created ignore file 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md   
2025-03-11 00:21:03 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 00:21:03 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:03 [info] refresh page data from resolve listeners 0 247   
2025-03-11 00:21:04 [info] indexing created file 文献库/文献库/舌诊/attachments/WFRGNIWR.png  [object Object] 
2025-03-11 00:21:04 [info] refresh page data from created listeners 0 248   
2025-03-11 00:21:05 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240806155210.png  [object Object] 
2025-03-11 00:21:05 [info] refresh page data from created listeners 0 249   
2025-03-11 00:21:05 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:21:05 [info] indexing created file 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md  [object Object] 
2025-03-11 00:21:05 [info] indexing created ignore file 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md   
2025-03-11 00:21:05 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:21:05 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:05 [info] refresh page data from resolve listeners 0 250   
2025-03-11 00:21:06 [info] indexing created file 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md  [object Object] 
2025-03-11 00:21:06 [info] indexing created ignore file 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md   
2025-03-11 00:21:06 [info] trigger 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md resolve  [object Object] 
2025-03-11 00:21:06 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:06 [info] refresh page data from resolve listeners 0 251   
2025-03-11 00:21:07 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/(2022-1)-Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning-A5JBAKIP.md  [object Object] 
2025-03-11 00:21:07 [info] indexing created ignore file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/(2022-1)-Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning-A5JBAKIP.md   
2025-03-11 00:21:07 [info] trigger 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/(2022-1)-Smart-Count-System-Based-on-Object-Detection-Using-Deep-Learning-A5JBAKIP.md resolve  [object Object] 
2025-03-11 00:21:07 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:07 [info] refresh page data from resolve listeners 0 252   
2025-03-11 00:21:08 [info] indexing created file 日记库/日记库.components  [object Object] 
2025-03-11 00:21:08 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 00:21:08 [info] refresh page data from created listeners 0 253   
2025-03-11 00:21:10 [info] indexing created file 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md  [object Object] 
2025-03-11 00:21:10 [info] indexing created ignore file 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md   
2025-03-11 00:21:10 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 00:21:10 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:10 [info] refresh page data from resolve listeners 0 254   
2025-03-11 00:21:10 [info] indexing created file 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md  [object Object] 
2025-03-11 00:21:10 [info] indexing created ignore file 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md   
2025-03-11 00:21:10 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 00:21:10 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:10 [info] refresh page data from resolve listeners 0 255   
2025-03-11 00:21:11 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Drawing 2024-10-09 15.17.52.excalidraw.md  [object Object] 
2025-03-11 00:21:11 [info] indexing created ignore file 学习库/ROS/机器人学/机器人运动学/Attachments/Drawing 2024-10-09 15.17.52.excalidraw.md   
2025-03-11 00:21:11 [info] trigger 学习库/ROS/机器人学/机器人运动学/Attachments/Drawing 2024-10-09 15.17.52.excalidraw.md resolve  [object Object] 
2025-03-11 00:21:11 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:11 [info] refresh page data from resolve listeners 0 256   
2025-03-11 00:21:12 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013195924.png  [object Object] 
2025-03-11 00:21:12 [info] refresh page data from created listeners 0 257   
2025-03-11 00:21:13 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250227160140.png  [object Object] 
2025-03-11 00:21:13 [info] refresh page data from created listeners 0 258   
2025-03-11 00:21:14 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117153735.png  [object Object] 
2025-03-11 00:21:14 [info] refresh page data from created listeners 0 259   
2025-03-11 00:21:14 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 00:21:15 [info] indexing created file 学习库/ROS/Attachments/Pasted image 20240803100838.png  [object Object] 
2025-03-11 00:21:15 [info] refresh page data from created listeners 0 260   
2025-03-11 00:21:15 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-11 00:21:16 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813095636.png  [object Object] 
2025-03-11 00:21:16 [info] refresh page data from created listeners 0 261   
2025-03-11 00:21:16 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 00:21:17 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117154446.png  [object Object] 
2025-03-11 00:21:17 [info] refresh page data from created listeners 0 262   
2025-03-11 00:21:17 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 00:21:18 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729155300.png  [object Object] 
2025-03-11 00:21:18 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 00:21:18 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:21:18 [info] refresh page data from created listeners 0 263   
2025-03-11 00:21:19 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240710215045.png  [object Object] 
2025-03-11 00:21:19 [info] refresh page data from created listeners 0 264   
2025-03-11 00:21:19 [info] trigger 学习库/python笔记/数据容器/链表.md resolve  [object Object] 
2025-03-11 00:21:20 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815170854.png  [object Object] 
2025-03-11 00:21:20 [info] refresh page data from created listeners 0 265   
2025-03-11 00:21:20 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 00:21:22 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013161811.png  [object Object] 
2025-03-11 00:21:22 [info] refresh page data from created listeners 0 266   
2025-03-11 00:21:23 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231229111052.png  [object Object] 
2025-03-11 00:21:23 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 00:21:23 [info] refresh page data from created listeners 0 267   
2025-03-11 00:21:24 [info] indexing created file 学习库/Deep learning/概念库/空间金字塔池化/attachments/Pasted image 20240124205549.png  [object Object] 
2025-03-11 00:21:24 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-03-11 00:21:24 [info] refresh page data from created listeners 0 268   
2025-03-11 00:21:25 [info] indexing created file 学习库/ROS/Attachments/Pasted image 20240727172249.png  [object Object] 
2025-03-11 00:21:25 [info] trigger 学习库/ROS/环境配置食用指南.md resolve  [object Object] 
2025-03-11 00:21:25 [info] refresh page data from created listeners 0 269   
2025-03-11 00:21:26 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729153712.png  [object Object] 
2025-03-11 00:21:26 [info] refresh page data from created listeners 0 270   
2025-03-11 00:21:26 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:21:27 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730110119.png  [object Object] 
2025-03-11 00:21:27 [info] refresh page data from created listeners 0 271   
2025-03-11 00:21:27 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:21:28 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226213214.png  [object Object] 
2025-03-11 00:21:28 [info] trigger 学习库/python笔记/数据类型.md resolve  [object Object] 
2025-03-11 00:21:28 [info] refresh page data from created listeners 0 272   
2025-03-11 00:21:29 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Quicker_20240729_102754.png  [object Object] 
2025-03-11 00:21:29 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:21:29 [info] refresh page data from created listeners 0 273   
2025-03-11 00:21:30 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250228152417.png  [object Object] 
2025-03-11 00:21:30 [info] refresh page data from created listeners 0 274   
2025-03-11 00:21:31 [info] indexing created file 学习库/Deep learning/概念库/欧氏距离&曼哈顿距离/-概念卡 -- -欧式距离＆曼哈顿距离-.mm  [object Object] 
2025-03-11 00:21:31 [info] refresh page data from created listeners 0 275   
2025-03-11 00:21:32 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240730002523.png  [object Object] 
2025-03-11 00:21:32 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 00:21:32 [info] trigger 学习库/Deep learning/概念库/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md resolve  [object Object] 
2025-03-11 00:21:32 [info] refresh page data from created listeners 0 276   
2025-03-11 00:21:33 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729170234.png  [object Object] 
2025-03-11 00:21:33 [info] refresh page data from created listeners 0 277   
2025-03-11 00:21:35 [info] indexing created file 学习库/zotero 配置指南/Attachments/Pasted image 20241016211631.png  [object Object] 
2025-03-11 00:21:35 [info] trigger 学习库/zotero 配置指南/Zotero Attanger.md resolve  [object Object] 
2025-03-11 00:21:35 [info] refresh page data from created listeners 0 278   
2025-03-11 00:21:36 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/attachments/Pasted image 20240118104816.png  [object Object] 
2025-03-11 00:21:36 [info] refresh page data from created listeners 0 279   
2025-03-11 00:21:36 [info] trigger 学习库/Deep learning/训练实践/火灾检测/添加CBAM注意力机制.md resolve  [object Object] 
2025-03-11 00:21:37 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730171955.png  [object Object] 
2025-03-11 00:21:37 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:21:37 [info] refresh page data from created listeners 0 280   
2025-03-11 00:21:39 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013195050.png  [object Object] 
2025-03-11 00:21:39 [info] refresh page data from created listeners 0 281   
2025-03-11 00:21:40 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106171834.png  [object Object] 
2025-03-11 00:21:40 [info] refresh page data from created listeners 0 282   
2025-03-11 00:21:40 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:21:41 [info] indexing created file 学习库/zotero 配置指南/Attachments/Pasted image 20241016211747.png  [object Object] 
2025-03-11 00:21:41 [info] trigger 学习库/zotero 配置指南/Zotero Attanger.md resolve  [object Object] 
2025-03-11 00:21:41 [info] refresh page data from created listeners 0 283   
2025-03-11 00:21:42 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815161528.png  [object Object] 
2025-03-11 00:21:42 [info] refresh page data from created listeners 0 284   
2025-03-11 00:21:42 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 00:21:43 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013203928.png  [object Object] 
2025-03-11 00:21:43 [info] refresh page data from created listeners 0 285   
2025-03-11 00:21:43 [info] trigger 学习库/ROS/机器人学/机器人运动学/机器人运动学.md resolve  [object Object] 
2025-03-11 00:21:44 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106172003.png  [object Object] 
2025-03-11 00:21:44 [info] refresh page data from created listeners 0 286   
2025-03-11 00:21:44 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:21:45 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013195532.png  [object Object] 
2025-03-11 00:21:45 [info] refresh page data from created listeners 0 287   
2025-03-11 00:21:46 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240102112724.png  [object Object] 
2025-03-11 00:21:46 [info] trigger 学习库/python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 00:21:46 [info] refresh page data from created listeners 0 288   
2025-03-11 00:21:47 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126210901.png  [object Object] 
2025-03-11 00:21:47 [info] refresh page data from created listeners 0 289   
2025-03-11 00:21:49 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240817152114.png  [object Object] 
2025-03-11 00:21:49 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 00:21:49 [info] refresh page data from created listeners 0 290   
2025-03-11 00:21:50 [info] indexing created file 学习库/python笔记/数据可视化/Attachments/Pasted image 20240720104554.png  [object Object] 
2025-03-11 00:21:50 [info] refresh page data from created listeners 0 291   
2025-03-11 00:21:51 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106172054.png  [object Object] 
2025-03-11 00:21:51 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:21:51 [info] refresh page data from created listeners 0 292   
2025-03-11 00:21:52 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-1.png  [object Object] 
2025-03-11 00:21:52 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 00:21:52 [info] refresh page data from created listeners 0 293   
2025-03-11 00:21:53 [info] indexing created file 学习库/obsidian 插件使用说明/MarkDown教程 Obsidian版 2022.4.22.md  [object Object] 
2025-03-11 00:21:53 [info] indexing created ignore file 学习库/obsidian 插件使用说明/MarkDown教程 Obsidian版 2022.4.22.md   
2025-03-11 00:21:53 [info] trigger 学习库/obsidian 插件使用说明/MarkDown教程 Obsidian版 2022.4.22.md resolve  [object Object] 
2025-03-11 00:21:53 [info] index finished after resolve  [object Object] 
2025-03-11 00:21:53 [info] refresh page data from resolve listeners 0 294   
2025-03-11 00:21:54 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019174524.png  [object Object] 
2025-03-11 00:21:54 [info] refresh page data from created listeners 0 295   
2025-03-11 00:21:54 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:21:57 [info] indexing created file 学习库/python笔记/数据可视化/Attachments/Pasted image 20240724104901.png  [object Object] 
2025-03-11 00:21:57 [info] refresh page data from created listeners 0 296   
2025-03-11 00:21:58 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241011164048.png  [object Object] 
2025-03-11 00:21:58 [info] refresh page data from created listeners 0 297   
2025-03-11 00:21:59 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/Pasted image 20240120114804.png  [object Object] 
2025-03-11 00:21:59 [info] refresh page data from created listeners 0 298   
2025-03-11 00:22:00 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517101141.png  [object Object] 
2025-03-11 00:22:00 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:22:00 [info] refresh page data from created listeners 0 299   
2025-03-11 00:22:01 [info] indexing created file 工作库/毕设/开题.md  [object Object] 
2025-03-11 00:22:01 [info] indexing created ignore file 工作库/毕设/开题.md   
2025-03-11 00:22:01 [info] trigger 工作库/毕设/开题.md resolve  [object Object] 
2025-03-11 00:22:01 [info] index finished after resolve  [object Object] 
2025-03-11 00:22:01 [info] refresh page data from resolve listeners 0 300   
2025-03-11 00:22:02 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019174517.png  [object Object] 
2025-03-11 00:22:02 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:22:02 [info] refresh page data from created listeners 0 301   
2025-03-11 00:22:03 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241011163959.png  [object Object] 
2025-03-11 00:22:03 [info] refresh page data from created listeners 0 302   
2025-03-11 00:22:04 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019174458.png  [object Object] 
2025-03-11 00:22:04 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:22:04 [info] refresh page data from created listeners 0 303   
2025-03-11 00:22:05 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019174508.png  [object Object] 
2025-03-11 00:22:05 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 00:22:05 [info] refresh page data from created listeners 0 304   
2025-03-11 00:22:06 [info] indexing created file 学习库/python笔记/leetcode/Attachments/Pasted image 20240710213400.png  [object Object] 
2025-03-11 00:22:06 [info] refresh page data from created listeners 0 305   
2025-03-11 00:22:07 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20240706101124.png  [object Object] 
2025-03-11 00:22:07 [info] refresh page data from created listeners 0 306   
2025-03-11 00:22:07 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 00:22:08 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/tmp6067.png  [object Object] 
2025-03-11 00:22:08 [info] refresh page data from created listeners 0 307   
2025-03-11 00:22:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:22:09 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240503161713.png  [object Object] 
2025-03-11 00:22:09 [info] trigger 学习库/python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 00:22:09 [info] refresh page data from created listeners 0 308   
2025-03-11 00:22:10 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/Pasted image 20240120113636.png  [object Object] 
2025-03-11 00:22:10 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 00:22:10 [info] refresh page data from created listeners 0 309   
2025-03-11 00:22:12 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013201939.png  [object Object] 
2025-03-11 00:22:12 [info] refresh page data from created listeners 0 310   
2025-03-11 00:22:13 [info] indexing created file 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/attachments/双重注意力机制.png  [object Object] 
2025-03-11 00:22:13 [info] trigger 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md resolve  [object Object] 
2025-03-11 00:22:13 [info] refresh page data from created listeners 0 311   
2025-03-11 00:22:15 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730172720.png  [object Object] 
2025-03-11 00:22:15 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:22:15 [info] refresh page data from created listeners 0 312   
2025-03-11 00:22:16 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517101748.png  [object Object] 
2025-03-11 00:22:16 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 00:22:16 [info] refresh page data from created listeners 0 313   
2025-03-11 00:22:17 [info] indexing created file 学习库/python笔记/数据可视化/Attachments/Pasted image 20240724105416.png  [object Object] 
2025-03-11 00:22:17 [info] refresh page data from created listeners 0 314   
2025-03-11 00:22:17 [info] trigger 学习库/python笔记/数据可视化/柱状图绘制.md resolve  [object Object] 
2025-03-11 00:22:18 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106172145.png  [object Object] 
2025-03-11 00:22:18 [info] refresh page data from created listeners 0 315   
2025-03-11 00:22:18 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:22:19 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/attachments/Pasted image 20240118093127.png  [object Object] 
2025-03-11 00:22:19 [info] trigger 学习库/Deep learning/训练实践/火灾检测/增加SE注意力机制.md resolve  [object Object] 
2025-03-11 00:22:19 [info] refresh page data from created listeners 0 316   
2025-03-11 00:22:20 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106171212.png  [object Object] 
2025-03-11 00:22:20 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 00:22:20 [info] refresh page data from created listeners 0 317   
2025-03-11 00:22:21 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730110336.png  [object Object] 
2025-03-11 00:22:21 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 00:22:21 [info] refresh page data from created listeners 0 318   
2025-03-11 09:04:18 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 09:04:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 09:04:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 09:04:19 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-11 09:04:19 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-11 09:04:19 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-11 09:04:19 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-11 09:04:19 [info] components database created cost 1 ms   
2025-03-11 09:04:19 [info] components index initializing...   
2025-03-11 09:04:19 [info] components index initialized, 318 files cost 39 ms   
2025-03-11 09:04:19 [info] refresh page data from init listeners 0 318   
2025-03-11 09:04:19 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 09:04:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 09:04:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 09:04:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 09:04:22 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 09:04:22 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 09:04:22 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 09:04:22 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 09:04:24 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517102643.png  [object Object] 
2025-03-11 09:04:24 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 09:04:24 [info] refresh page data from created listeners 0 319   
2025-03-11 09:04:25 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/Pasted image 20240730145412.png  [object Object] 
2025-03-11 09:04:25 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-11 09:04:25 [info] refresh page data from created listeners 0 320   
2025-03-11 09:04:25 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106171317.png  [object Object] 
2025-03-11 09:04:25 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:04:25 [info] refresh page data from created listeners 0 321   
2025-03-11 09:04:26 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815161742.png  [object Object] 
2025-03-11 09:04:26 [info] refresh page data from created listeners 0 322   
2025-03-11 09:04:26 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 09:04:26 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228165702.png  [object Object] 
2025-03-11 09:04:26 [info] refresh page data from created listeners 0 323   
2025-03-11 09:04:26 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:04:27 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/Pasted image 20240120121420.png  [object Object] 
2025-03-11 09:04:27 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 09:04:27 [info] refresh page data from created listeners 0 324   
2025-03-11 09:04:27 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117153415.png  [object Object] 
2025-03-11 09:04:27 [info] refresh page data from created listeners 0 325   
2025-03-11 09:04:27 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 09:04:28 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910162002.png  [object Object] 
2025-03-11 09:04:28 [info] refresh page data from created listeners 0 326   
2025-03-11 09:04:28 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:04:29 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925154440.png  [object Object] 
2025-03-11 09:04:29 [info] refresh page data from created listeners 0 327   
2025-03-11 09:04:29 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 09:04:30 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013162843.png  [object Object] 
2025-03-11 09:04:30 [info] refresh page data from created listeners 0 328   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 327   
2025-03-11 09:04:30 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 326   
2025-03-11 09:04:30 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 325   
2025-03-11 09:04:30 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 324   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 323   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 322   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 321   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 320   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 319   
2025-03-11 09:04:30 [info] refresh page data from delete listeners 0 318   
2025-03-11 09:04:31 [info] indexing created file 工作库/项目/论文撰写/舌诊/综述.md  [object Object] 
2025-03-11 09:04:31 [info] indexing created ignore file 工作库/项目/论文撰写/舌诊/综述.md   
2025-03-11 09:04:31 [info] trigger 工作库/项目/论文撰写/舌诊/综述.md resolve  [object Object] 
2025-03-11 09:04:31 [info] index finished after resolve  [object Object] 
2025-03-11 09:04:31 [info] refresh page data from resolve listeners 0 319   
2025-03-11 09:04:31 [info] indexing created file 工作库/项目/论文撰写/舌诊/提问模拟.md  [object Object] 
2025-03-11 09:04:31 [info] indexing created ignore file 工作库/项目/论文撰写/舌诊/提问模拟.md   
2025-03-11 09:04:31 [info] trigger 工作库/项目/论文撰写/舌诊/提问模拟.md resolve  [object Object] 
2025-03-11 09:04:31 [info] index finished after resolve  [object Object] 
2025-03-11 09:04:31 [info] refresh page data from resolve listeners 0 320   
2025-03-11 09:04:32 [info] indexing created file 工作库/项目/论文撰写/晶体/小修.md  [object Object] 
2025-03-11 09:04:32 [info] indexing created ignore file 工作库/项目/论文撰写/晶体/小修.md   
2025-03-11 09:04:32 [info] trigger 工作库/项目/论文撰写/晶体/小修.md resolve  [object Object] 
2025-03-11 09:04:32 [info] index finished after resolve  [object Object] 
2025-03-11 09:04:32 [info] refresh page data from resolve listeners 0 321   
2025-03-11 09:04:32 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241215090233.png  [object Object] 
2025-03-11 09:04:32 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:32 [info] refresh page data from created listeners 0 322   
2025-03-11 09:04:33 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241215090302.png  [object Object] 
2025-03-11 09:04:33 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:33 [info] refresh page data from created listeners 0 323   
2025-03-11 09:04:33 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241215090242.png  [object Object] 
2025-03-11 09:04:33 [info] refresh page data from created listeners 0 324   
2025-03-11 09:04:33 [info] trigger 学习库/ROS/机器人学/机器视觉/视觉伺服控制.md resolve  [object Object] 
2025-03-11 09:04:34 [info] indexing created file 学习库/ROS/Attachments/Pasted image 20240805163511.png  [object Object] 
2025-03-11 09:04:34 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-11 09:04:34 [info] refresh page data from created listeners 0 325   
2025-03-11 09:04:35 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111162713.png  [object Object] 
2025-03-11 09:04:35 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:04:35 [info] refresh page data from created listeners 0 326   
2025-03-11 09:04:35 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241011164131.png  [object Object] 
2025-03-11 09:04:35 [info] refresh page data from created listeners 0 327   
2025-03-11 09:04:36 [info] indexing created file 学习库/python笔记/数据可视化/Attachments/Pasted image 20240722205820.png  [object Object] 
2025-03-11 09:04:36 [info] refresh page data from created listeners 0 328   
2025-03-11 09:04:36 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117153523.png  [object Object] 
2025-03-11 09:04:37 [info] refresh page data from created listeners 0 329   
2025-03-11 09:04:37 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 09:04:37 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250109160519.png  [object Object] 
2025-03-11 09:04:37 [info] trigger 文献库/文献库/舌诊/机械臂/Position-based visual servo control of dual robotic arms with unknown kinematic models- A cerebellum- inspired approach.md resolve  [object Object] 
2025-03-11 09:04:37 [info] refresh page data from created listeners 0 330   
2025-03-11 09:04:38 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240104093239.png  [object Object] 
2025-03-11 09:04:38 [info] refresh page data from created listeners 0 331   
2025-03-11 09:04:39 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925153912.png  [object Object] 
2025-03-11 09:04:39 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 09:04:39 [info] refresh page data from created listeners 0 332   
2025-03-11 09:04:39 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813100306.png  [object Object] 
2025-03-11 09:04:39 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:04:39 [info] refresh page data from created listeners 0 333   
2025-03-11 09:04:40 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117154154.png  [object Object] 
2025-03-11 09:04:40 [info] refresh page data from created listeners 0 334   
2025-03-11 09:04:40 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 09:04:40 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20250117154400.png  [object Object] 
2025-03-11 09:04:40 [info] refresh page data from created listeners 0 335   
2025-03-11 09:04:40 [info] trigger 文献库/文献库/舌诊/机械臂/Uncalibrated visual servo control algorithm based on TL-MobileNet-YOLO.md resolve  [object Object] 
2025-03-11 09:04:41 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240731103159.png  [object Object] 
2025-03-11 09:04:41 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 09:04:41 [info] refresh page data from created listeners 0 336   
2025-03-11 09:04:42 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126220723.png  [object Object] 
2025-03-11 09:04:42 [info] refresh page data from created listeners 0 337   
2025-03-11 09:04:42 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813095153.png  [object Object] 
2025-03-11 09:04:43 [info] trigger 学习库/Anki/python/test.md resolve  [object Object] 
2025-03-11 09:04:43 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:04:43 [info] refresh page data from created listeners 0 338   
2025-03-11 09:04:43 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925153957.png  [object Object] 
2025-03-11 09:04:43 [info] refresh page data from created listeners 0 339   
2025-03-11 09:04:43 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 09:04:44 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106170314.png  [object Object] 
2025-03-11 09:04:44 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:04:44 [info] refresh page data from created listeners 0 340   
2025-03-11 09:04:44 [info] indexing created file 工作库/项目/舌诊/Attachments/Pasted image 20241015211103.png  [object Object] 
2025-03-11 09:04:44 [info] refresh page data from created listeners 0 341   
2025-03-11 09:04:45 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240730092903.png  [object Object] 
2025-03-11 09:04:45 [info] refresh page data from created listeners 0 342   
2025-03-11 09:04:46 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240817154133.png  [object Object] 
2025-03-11 09:04:46 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 09:04:46 [info] refresh page data from created listeners 0 343   
2025-03-11 09:04:46 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515112830.png  [object Object] 
2025-03-11 09:04:46 [info] refresh page data from created listeners 0 344   
2025-03-11 09:04:46 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:04:47 [info] indexing created file 文献库/文献库/舌诊/attachments/JIP2W5JZ.png  [object Object] 
2025-03-11 09:04:47 [info] refresh page data from created listeners 0 345   
2025-03-11 09:04:48 [info] indexing created file 学习库/python笔记/异常，模块，包/attachements/Pasted image 20240522203143.png  [object Object] 
2025-03-11 09:04:48 [info] refresh page data from created listeners 0 346   
2025-03-11 09:04:48 [info] trigger 学习库/python笔记/异常，模块，包/异常.md resolve  [object Object] 
2025-03-11 09:04:48 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729095613.png  [object Object] 
2025-03-11 09:04:48 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 09:04:48 [info] refresh page data from created listeners 0 347   
2025-03-11 09:04:49 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019173312.png  [object Object] 
2025-03-11 09:04:49 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 09:04:49 [info] refresh page data from created listeners 0 348   
2025-03-11 09:04:50 [info] indexing created file 学习库/Deep learning/yolov5/attachments/Pasted image 20240123205552.png  [object Object] 
2025-03-11 09:04:50 [info] trigger 学习库/Deep learning/概念库/特征融合/concatenate和add.md resolve  [object Object] 
2025-03-11 09:04:50 [info] refresh page data from created listeners 0 349   
2025-03-11 09:04:51 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517110728.png  [object Object] 
2025-03-11 09:04:51 [info] refresh page data from created listeners 0 350   
2025-03-11 09:04:51 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 09:04:52 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517111040.png  [object Object] 
2025-03-11 09:04:52 [info] refresh page data from created listeners 0 351   
2025-03-11 09:04:52 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815165800.png  [object Object] 
2025-03-11 09:04:52 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:04:52 [info] refresh page data from created listeners 0 352   
2025-03-11 09:04:53 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240428174547.png  [object Object] 
2025-03-11 09:04:53 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 09:04:53 [info] refresh page data from created listeners 0 353   
2025-03-11 09:04:54 [info] indexing created file 学习库/python笔记/文件操作/attachements/Pasted image 20240521103339.png  [object Object] 
2025-03-11 09:04:54 [info] trigger 学习库/python笔记/文件操作/文件编码.md resolve  [object Object] 
2025-03-11 09:04:54 [info] refresh page data from created listeners 0 354   
2025-03-11 09:04:54 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228153835.png  [object Object] 
2025-03-11 09:04:54 [info] refresh page data from created listeners 0 355   
2025-03-11 09:04:55 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240102114838.png  [object Object] 
2025-03-11 09:04:55 [info] refresh page data from created listeners 0 356   
2025-03-11 09:04:56 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240102114854.png  [object Object] 
2025-03-11 09:04:56 [info] trigger 学习库/python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 09:04:56 [info] refresh page data from created listeners 0 357   
2025-03-11 09:04:57 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111152638.png  [object Object] 
2025-03-11 09:04:57 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:04:57 [info] refresh page data from created listeners 0 358   
2025-03-11 09:04:57 [info] indexing created file 学习库/Deep learning/yolov5/attachments/Pasted image 20240123221549.png  [object Object] 
2025-03-11 09:04:57 [info] refresh page data from created listeners 0 359   
2025-03-11 09:04:58 [info] indexing created file 学习库/Deep learning/yolov5/attachments/目标检测的尺度.png  [object Object] 
2025-03-11 09:04:58 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 09:04:58 [info] trigger 学习库/Deep learning/概念库/小目标检测头.md resolve  [object Object] 
2025-03-11 09:04:58 [info] refresh page data from created listeners 0 360   
2025-03-11 09:04:59 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013204604.png  [object Object] 
2025-03-11 09:04:59 [info] refresh page data from created listeners 0 361   
2025-03-11 09:04:59 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20240706095850.png  [object Object] 
2025-03-11 09:05:00 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:05:00 [info] refresh page data from created listeners 0 362   
2025-03-11 09:05:00 [info] indexing created file 学习库/Deep learning/概念库/池化层/attachments/Pasted image 20240124210626.png  [object Object] 
2025-03-11 09:05:01 [info] trigger 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md resolve  [object Object] 
2025-03-11 09:05:01 [info] refresh page data from created listeners 0 363   
2025-03-11 09:05:02 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910163754.png  [object Object] 
2025-03-11 09:05:02 [info] refresh page data from created listeners 0 364   
2025-03-11 09:05:02 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:02 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/bda132ec94ebe94bd598dd9ae9fa04d5.gif  [object Object] 
2025-03-11 09:05:02 [info] refresh page data from created listeners 0 365   
2025-03-11 09:05:02 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 09:05:03 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226204650.png  [object Object] 
2025-03-11 09:05:03 [info] refresh page data from created listeners 0 366   
2025-03-11 09:05:04 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226204708.png  [object Object] 
2025-03-11 09:05:04 [info] trigger 学习库/python笔记/变量/变量.md resolve  [object Object] 
2025-03-11 09:05:04 [info] refresh page data from created listeners 0 367   
2025-03-11 09:05:04 [info] indexing created file 文献库/文献库/舌诊/attachments/BQJPRVIF.png  [object Object] 
2025-03-11 09:05:04 [info] refresh page data from created listeners 0 368   
2025-03-11 09:05:05 [info] indexing created file 学习库/ROS/Attachments/Pasted image 20240727154533.png  [object Object] 
2025-03-11 09:05:05 [info] trigger 学习库/ROS/环境配置食用指南.md resolve  [object Object] 
2025-03-11 09:05:05 [info] refresh page data from created listeners 0 369   
2025-03-11 09:05:06 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20240706151929.png  [object Object] 
2025-03-11 09:05:06 [info] refresh page data from created listeners 0 370   
2025-03-11 09:05:06 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:05:06 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111164234.png  [object Object] 
2025-03-11 09:05:06 [info] refresh page data from created listeners 0 371   
2025-03-11 09:05:06 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:05:07 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013210148.png  [object Object] 
2025-03-11 09:05:07 [info] refresh page data from created listeners 0 372   
2025-03-11 09:05:07 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111152528.png  [object Object] 
2025-03-11 09:05:08 [info] refresh page data from created listeners 0 373   
2025-03-11 09:05:08 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:05:08 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517103025.png  [object Object] 
2025-03-11 09:05:08 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 09:05:08 [info] refresh page data from created listeners 0 374   
2025-03-11 09:05:09 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/3d8813449101f99a106a74cee1754184.gif  [object Object] 
2025-03-11 09:05:09 [info] refresh page data from created listeners 0 375   
2025-03-11 09:05:09 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231229111131.png  [object Object] 
2025-03-11 09:05:09 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:05:09 [info] refresh page data from created listeners 0 376   
2025-03-11 09:05:10 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240517111557.png  [object Object] 
2025-03-11 09:05:10 [info] refresh page data from created listeners 0 377   
2025-03-11 09:05:10 [info] trigger 工作库/项目/声音识别/窗函数.md resolve  [object Object] 
2025-03-11 09:05:11 [info] indexing created file 学习库/Deep learning/yolov5/attachments/Pasted image 20240123163728.png  [object Object] 
2025-03-11 09:05:11 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 09:05:11 [info] refresh page data from created listeners 0 378   
2025-03-11 09:05:12 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126202546.png  [object Object] 
2025-03-11 09:05:12 [info] refresh page data from created listeners 0 379   
2025-03-11 09:05:12 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910162950.png  [object Object] 
2025-03-11 09:05:12 [info] refresh page data from created listeners 0 380   
2025-03-11 09:05:13 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228174352.png  [object Object] 
2025-03-11 09:05:13 [info] refresh page data from created listeners 0 381   
2025-03-11 09:05:14 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008151321.png  [object Object] 
2025-03-11 09:05:14 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:05:14 [info] refresh page data from created listeners 0 382   
2025-03-11 09:05:14 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013163446.png  [object Object] 
2025-03-11 09:05:14 [info] refresh page data from created listeners 0 383   
2025-03-11 09:05:15 [info] ignore file modify evnet 工作库/工作库.components   
2025-03-11 09:05:15 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/3bb7fc0d90165becc7ed5033534fdac4.gif  [object Object] 
2025-03-11 09:05:16 [info] refresh page data from created listeners 0 384   
2025-03-11 09:05:16 [info] indexing created file 学习库/python笔记/文件操作/attachements/Pasted image 20240521103857.png  [object Object] 
2025-03-11 09:05:16 [info] trigger 学习库/python笔记/文件操作/文件编码.md resolve  [object Object] 
2025-03-11 09:05:16 [info] refresh page data from created listeners 0 385   
2025-03-11 09:05:17 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240305113457.png  [object Object] 
2025-03-11 09:05:17 [info] trigger 学习库/python笔记/数据容器/数据容器.md resolve  [object Object] 
2025-03-11 09:05:17 [info] refresh page data from created listeners 0 386   
2025-03-11 09:05:18 [info] indexing created file 文献库/文献库/舌诊/attachments/N7J4CQQM.png  [object Object] 
2025-03-11 09:05:18 [info] refresh page data from created listeners 0 387   
2025-03-11 09:05:18 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515111308.png  [object Object] 
2025-03-11 09:05:19 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:05:19 [info] refresh page data from created listeners 0 388   
2025-03-11 09:05:19 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228155731.png  [object Object] 
2025-03-11 09:05:19 [info] refresh page data from created listeners 0 389   
2025-03-11 09:05:20 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228155837.png  [object Object] 
2025-03-11 09:05:20 [info] refresh page data from created listeners 0 390   
2025-03-11 09:05:20 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:05:21 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815165525.png  [object Object] 
2025-03-11 09:05:21 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:05:21 [info] refresh page data from created listeners 0 391   
2025-03-11 09:05:21 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228152519.png  [object Object] 
2025-03-11 09:05:21 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:05:21 [info] refresh page data from created listeners 0 392   
2025-03-11 09:05:22 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-6.png  [object Object] 
2025-03-11 09:05:22 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 09:05:22 [info] refresh page data from created listeners 0 393   
2025-03-11 09:05:23 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910160550.png  [object Object] 
2025-03-11 09:05:23 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:23 [info] refresh page data from created listeners 0 394   
2025-03-11 09:05:23 [info] indexing created file 学习库/python笔记/数据可视化/Attachments/动态柱状图.gif  [object Object] 
2025-03-11 09:05:23 [info] refresh page data from created listeners 0 395   
2025-03-11 09:05:24 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008152233.png  [object Object] 
2025-03-11 09:05:24 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:05:24 [info] refresh page data from created listeners 0 396   
2025-03-11 09:05:25 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-4.png  [object Object] 
2025-03-11 09:05:25 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 09:05:25 [info] refresh page data from created listeners 0 397   
2025-03-11 09:05:25 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240428152152.png  [object Object] 
2025-03-11 09:05:25 [info] refresh page data from created listeners 0 398   
2025-03-11 09:05:26 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240817162602.png  [object Object] 
2025-03-11 09:05:26 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 09:05:26 [info] refresh page data from created listeners 0 399   
2025-03-11 09:05:27 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910163053.png  [object Object] 
2025-03-11 09:05:27 [info] refresh page data from created listeners 0 400   
2025-03-11 09:05:27 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:27 [info] indexing created file 学习库/python笔记/异常，模块，包/attachements/Pasted image 20240524085334.png  [object Object] 
2025-03-11 09:05:27 [info] refresh page data from created listeners 0 401   
2025-03-11 09:05:27 [info] trigger 学习库/python笔记/异常，模块，包/包.md resolve  [object Object] 
2025-03-11 09:05:28 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815170104.png  [object Object] 
2025-03-11 09:05:28 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:05:28 [info] refresh page data from created listeners 0 402   
2025-03-11 09:05:29 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241212151240.png  [object Object] 
2025-03-11 09:05:29 [info] refresh page data from created listeners 0 403   
2025-03-11 09:05:30 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111164058.png  [object Object] 
2025-03-11 09:05:30 [info] refresh page data from created listeners 0 404   
2025-03-11 09:05:30 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:05:30 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240428171616.png  [object Object] 
2025-03-11 09:05:30 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 09:05:30 [info] refresh page data from created listeners 0 405   
2025-03-11 09:05:31 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250227165524.png  [object Object] 
2025-03-11 09:05:31 [info] refresh page data from created listeners 0 406   
2025-03-11 09:05:32 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009153357.png  [object Object] 
2025-03-11 09:05:32 [info] refresh page data from created listeners 0 407   
2025-03-11 09:05:32 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:05:32 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240503155648.png  [object Object] 
2025-03-11 09:05:32 [info] trigger 学习库/python笔记/数据容器/元组.md resolve  [object Object] 
2025-03-11 09:05:32 [info] refresh page data from created listeners 0 408   
2025-03-11 09:05:33 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008150458.png  [object Object] 
2025-03-11 09:05:33 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:05:33 [info] refresh page data from created listeners 0 409   
2025-03-11 09:05:34 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106165215.png  [object Object] 
2025-03-11 09:05:34 [info] refresh page data from created listeners 0 410   
2025-03-11 09:05:34 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:05:35 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240102111207.png  [object Object] 
2025-03-11 09:05:35 [info] refresh page data from created listeners 0 411   
2025-03-11 09:05:35 [info] trigger 学习库/python笔记/判断语句/if语句.md resolve  [object Object] 
2025-03-11 09:05:35 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910102057.png  [object Object] 
2025-03-11 09:05:35 [info] refresh page data from created listeners 0 412   
2025-03-11 09:05:35 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:36 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111111254.png  [object Object] 
2025-03-11 09:05:36 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:05:36 [info] refresh page data from created listeners 0 413   
2025-03-11 09:05:37 [info] indexing created file 学习库/python笔记/数据可视化/attachements/Pasted image 20240531105141.png  [object Object] 
2025-03-11 09:05:37 [info] refresh page data from created listeners 0 414   
2025-03-11 09:05:37 [info] trigger 学习库/python笔记/数据可视化/pyecharts模块.md resolve  [object Object] 
2025-03-11 09:05:38 [info] indexing created file 学习库/Deep learning/概念库/卷积层/Attachments/b6ca1dfbf6eacd96531ac2fd26a5eec9.gif  [object Object] 
2025-03-11 09:05:38 [info] refresh page data from created listeners 0 415   
2025-03-11 09:05:38 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/20190101220640484 - 副本.jpeg  [object Object] 
2025-03-11 09:05:38 [info] refresh page data from created listeners 0 416   
2025-03-11 09:05:39 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/20190101220640484.gif  [object Object] 
2025-03-11 09:05:39 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 09:05:39 [info] refresh page data from created listeners 0 417   
2025-03-11 09:05:40 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240817162439.png  [object Object] 
2025-03-11 09:05:40 [info] refresh page data from created listeners 0 418   
2025-03-11 09:05:40 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 09:05:41 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241212150939.png  [object Object] 
2025-03-11 09:05:41 [info] refresh page data from created listeners 0 419   
2025-03-11 09:05:41 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226215929.png  [object Object] 
2025-03-11 09:05:41 [info] refresh page data from created listeners 0 420   
2025-03-11 09:05:41 [info] trigger 学习库/python笔记/算数运算符.md resolve  [object Object] 
2025-03-11 09:05:42 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240817110144.png  [object Object] 
2025-03-11 09:05:42 [info] refresh page data from created listeners 0 421   
2025-03-11 09:05:42 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 09:05:43 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515103905.png  [object Object] 
2025-03-11 09:05:43 [info] refresh page data from created listeners 0 422   
2025-03-11 09:05:43 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:05:43 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815164429.png  [object Object] 
2025-03-11 09:05:44 [info] refresh page data from created listeners 0 423   
2025-03-11 09:05:44 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:05:44 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910164833.png  [object Object] 
2025-03-11 09:05:44 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:44 [info] refresh page data from created listeners 0 424   
2025-03-11 09:05:45 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226215308.png  [object Object] 
2025-03-11 09:05:45 [info] refresh page data from created listeners 0 425   
2025-03-11 09:05:45 [info] trigger 学习库/python笔记/算数运算符.md resolve  [object Object] 
2025-03-11 09:05:46 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111164643.png  [object Object] 
2025-03-11 09:05:46 [info] refresh page data from created listeners 0 426   
2025-03-11 09:05:46 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:05:47 [info] indexing created file 文献库/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/(2022-1) Smart Count System Based on Object Detection Using Deep Learning.mm  [object Object] 
2025-03-11 09:05:47 [info] refresh page data from created listeners 0 427   
2025-03-11 09:05:47 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-3.png  [object Object] 
2025-03-11 09:05:47 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 09:05:47 [info] refresh page data from created listeners 0 428   
2025-03-11 09:05:48 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20240605152225.jpg  [object Object] 
2025-03-11 09:05:48 [info] refresh page data from created listeners 0 429   
2025-03-11 09:05:49 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240731103953.png  [object Object] 
2025-03-11 09:05:49 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 09:05:49 [info] refresh page data from created listeners 0 430   
2025-03-11 09:05:49 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910162304.png  [object Object] 
2025-03-11 09:05:49 [info] refresh page data from created listeners 0 431   
2025-03-11 09:05:49 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:05:50 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240729095710.png  [object Object] 
2025-03-11 09:05:50 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 09:05:50 [info] refresh page data from created listeners 0 432   
2025-03-11 09:05:51 [info] indexing created file 学习库/python笔记/数据可视化/attachements/Pasted image 20240529202912.png  [object Object] 
2025-03-11 09:05:51 [info] trigger 学习库/python笔记/数据可视化/JSON数据.md resolve  [object Object] 
2025-03-11 09:05:51 [info] refresh page data from created listeners 0 433   
2025-03-11 09:05:52 [info] indexing created file 学习库/python笔记/文件操作/attachements/Pasted image 20240522164411.png  [object Object] 
2025-03-11 09:05:52 [info] trigger 学习库/python笔记/文件操作/文件操作.md resolve  [object Object] 
2025-03-11 09:05:52 [info] refresh page data from created listeners 0 434   
2025-03-11 09:05:53 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240503165632.png  [object Object] 
2025-03-11 09:05:53 [info] trigger 学习库/python笔记/数据容器/字符串.md resolve  [object Object] 
2025-03-11 09:05:53 [info] refresh page data from created listeners 0 435   
2025-03-11 09:05:54 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241012100547.png  [object Object] 
2025-03-11 09:05:54 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:05:54 [info] refresh page data from created listeners 0 436   
2025-03-11 09:05:55 [info] indexing created file 学习库/python笔记/数据容器/attachments/Pasted image 20240428152523.png  [object Object] 
2025-03-11 09:05:55 [info] refresh page data from created listeners 0 437   
2025-03-11 09:05:55 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-11 09:05:55 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/attachments/Pasted image 20231230114758.png  [object Object] 
2025-03-11 09:05:55 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/调整学习率.md resolve  [object Object] 
2025-03-11 09:05:55 [info] refresh page data from created listeners 0 438   
2025-03-11 09:05:56 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925160036.png  [object Object] 
2025-03-11 09:05:56 [info] refresh page data from created listeners 0 439   
2025-03-11 09:05:56 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 09:05:57 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/attachments/Pasted image 20240118093302.png  [object Object] 
2025-03-11 09:05:57 [info] trigger 学习库/Deep learning/训练实践/火灾检测/增加SE注意力机制.md resolve  [object Object] 
2025-03-11 09:05:57 [info] refresh page data from created listeners 0 440   
2025-03-11 09:05:58 [info] indexing created file 学习库/Deep learning/训练实践/火灾检测/attachments/Pasted image 20240118104739.png  [object Object] 
2025-03-11 09:05:58 [info] refresh page data from created listeners 0 441   
2025-03-11 09:05:59 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515110133.png  [object Object] 
2025-03-11 09:05:59 [info] refresh page data from created listeners 0 442   
2025-03-11 09:06:00 [info] indexing created file 学习库/python笔记/循环语句/attachments/Pasted image 20240104102649.png  [object Object] 
2025-03-11 09:06:00 [info] refresh page data from created listeners 0 443   
2025-03-11 09:06:01 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241011165938.png  [object Object] 
2025-03-11 09:06:01 [info] refresh page data from created listeners 0 444   
2025-03-11 09:06:01 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:06:02 [info] indexing created file 学习库/python笔记/python函数/attachments/Pasted image 20240425095811.png  [object Object] 
2025-03-11 09:06:02 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-11 09:06:02 [info] refresh page data from created listeners 0 445   
2025-03-11 09:06:02 [info] indexing created file 学习库/python笔记/异常，模块，包/attachements/Pasted image 20240522201751.png  [object Object] 
2025-03-11 09:06:02 [info] trigger 学习库/python笔记/异常，模块，包/异常.md resolve  [object Object] 
2025-03-11 09:06:02 [info] refresh page data from created listeners 0 446   
2025-03-11 09:06:03 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241013164935.png  [object Object] 
2025-03-11 09:06:03 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:06:03 [info] refresh page data from created listeners 0 447   
2025-03-11 09:06:04 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231228180054.png  [object Object] 
2025-03-11 09:06:04 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:06:04 [info] refresh page data from created listeners 0 448   
2025-03-11 09:06:05 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106164932.png  [object Object] 
2025-03-11 09:06:05 [info] refresh page data from created listeners 0 449   
2025-03-11 09:06:05 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:06:06 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813090737.png  [object Object] 
2025-03-11 09:06:06 [info] refresh page data from created listeners 0 450   
2025-03-11 09:06:06 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813090808.png  [object Object] 
2025-03-11 09:06:06 [info] refresh page data from created listeners 0 451   
2025-03-11 09:06:07 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813090824.png  [object Object] 
2025-03-11 09:06:07 [info] refresh page data from created listeners 0 452   
2025-03-11 09:06:07 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:06:08 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008095639.png  [object Object] 
2025-03-11 09:06:08 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 09:06:08 [info] refresh page data from created listeners 0 453   
2025-03-11 09:06:09 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241212151554.png  [object Object] 
2025-03-11 09:06:09 [info] refresh page data from created listeners 0 454   
2025-03-11 09:06:09 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515200716.png  [object Object] 
2025-03-11 09:06:09 [info] refresh page data from created listeners 0 455   
2025-03-11 09:06:09 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:06:10 [info] indexing created file 学习库/Deep learning/概念库/上采样；下采样/attachments/20190101212350912.gif  [object Object] 
2025-03-11 09:06:10 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-03-11 09:06:10 [info] refresh page data from created listeners 0 456   
2025-03-11 09:06:11 [info] indexing created file 学习库/python笔记/判断语句/attachments/Pasted image 20240102105907.png  [object Object] 
2025-03-11 09:06:11 [info] refresh page data from created listeners 0 457   
2025-03-11 09:06:12 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240803170338.png  [object Object] 
2025-03-11 09:06:12 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 09:06:12 [info] refresh page data from created listeners 0 458   
2025-03-11 09:06:13 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240804221113.png  [object Object] 
2025-03-11 09:06:13 [info] refresh page data from created listeners 0 459   
2025-03-11 09:06:13 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-11 09:06:14 [info] indexing created file 学习库/python笔记/面向对象/Attachments/Pasted image 20240731104350.png  [object Object] 
2025-03-11 09:06:14 [info] refresh page data from created listeners 0 460   
2025-03-11 09:06:14 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-11 09:06:15 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-2.png  [object Object] 
2025-03-11 09:06:15 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 09:06:15 [info] refresh page data from created listeners 0 461   
2025-03-11 09:06:16 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111155920.png  [object Object] 
2025-03-11 09:06:16 [info] refresh page data from created listeners 0 462   
2025-03-11 09:06:16 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:06:16 [info] indexing created file 工作库/项目/声音识别/attachements/频域图.gif  [object Object] 
2025-03-11 09:06:16 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:06:16 [info] refresh page data from created listeners 0 463   
2025-03-11 09:06:17 [info] indexing created file 学习库/Deep learning/yolov5/attachments/157381276-6e8429f3-c759-4aef-aea8-034438919457.png  [object Object] 
2025-03-11 09:06:17 [info] trigger 学习库/Deep learning/yolov5/yaml文件讲解.md resolve  [object Object] 
2025-03-11 09:06:17 [info] refresh page data from created listeners 0 464   
2025-03-11 09:06:18 [info] indexing created file 学习库/obsidian 插件使用说明/Attachments/QuickAdd-5.png  [object Object] 
2025-03-11 09:06:18 [info] trigger 学习库/obsidian 插件使用说明/QuickAdd.md resolve  [object Object] 
2025-03-11 09:06:18 [info] refresh page data from created listeners 0 465   
2025-03-11 09:06:19 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/GIF 2024-10-12 9-07-31.gif  [object Object] 
2025-03-11 09:06:19 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:06:19 [info] refresh page data from created listeners 0 466   
2025-03-11 09:06:20 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008153216.png  [object Object] 
2025-03-11 09:06:20 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:06:20 [info] refresh page data from created listeners 0 467   
2025-03-11 09:06:21 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813095341.png  [object Object] 
2025-03-11 09:06:21 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:06:21 [info] refresh page data from created listeners 0 468   
2025-03-11 09:06:22 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231226192846.png  [object Object] 
2025-03-11 09:06:22 [info] refresh page data from created listeners 0 469   
2025-03-11 09:06:22 [info] trigger 学习库/python笔记/字面量.md resolve  [object Object] 
2025-03-11 09:06:22 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008110538.png  [object Object] 
2025-03-11 09:06:22 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:06:23 [info] refresh page data from created listeners 0 470   
2025-03-11 09:06:23 [info] indexing created file 学习库/python笔记/异常，模块，包/attachements/Pasted image 20240529112205.png  [object Object] 
2025-03-11 09:06:24 [info] trigger 学习库/python笔记/异常，模块，包/综合练习.md resolve  [object Object] 
2025-03-11 09:06:24 [info] refresh page data from created listeners 0 471   
2025-03-11 09:06:24 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815160306.png  [object Object] 
2025-03-11 09:06:24 [info] refresh page data from created listeners 0 472   
2025-03-11 09:06:24 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 09:06:26 [info] indexing created file 学习库/obsidian 插件使用说明/css基础教学.pdf  [object Object] 
2025-03-11 09:06:26 [info] refresh page data from created listeners 0 473   
2025-03-11 09:06:26 [info] trigger 学习库/obsidian 插件使用说明/css基础教学.md resolve  [object Object] 
2025-03-11 09:06:27 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/GIF 2024-10-12 8-58-25.gif  [object Object] 
2025-03-11 09:06:27 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:06:27 [info] refresh page data from created listeners 0 474   
2025-03-11 09:06:28 [info] indexing created file 学习库/zotero 配置指南/Attachments/Pasted image 20241015170137.png  [object Object] 
2025-03-11 09:06:28 [info] refresh page data from created listeners 0 475   
2025-03-11 09:06:29 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008145042.png  [object Object] 
2025-03-11 09:06:29 [info] refresh page data from created listeners 0 476   
2025-03-11 09:06:30 [info] indexing created file 学习库/python笔记/attachments/Pasted image 20231230112837.png  [object Object] 
2025-03-11 09:06:30 [info] trigger 学习库/python笔记/判断语句/布尔类型和比较运算符.md resolve  [object Object] 
2025-03-11 09:06:30 [info] refresh page data from created listeners 0 477   
2025-03-11 09:06:31 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/attachments/Pasted image 20231230114754.png  [object Object] 
2025-03-11 09:06:31 [info] refresh page data from created listeners 0 478   
2025-03-11 09:06:32 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111163604.png  [object Object] 
2025-03-11 09:06:32 [info] refresh page data from created listeners 0 479   
2025-03-11 09:06:32 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:06:33 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815161640.png  [object Object] 
2025-03-11 09:06:33 [info] refresh page data from created listeners 0 480   
2025-03-11 09:06:33 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 09:06:34 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Pasted image 20240911090035.png  [object Object] 
2025-03-11 09:06:34 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-03-11 09:06:34 [info] refresh page data from created listeners 0 481   
2025-03-11 09:06:35 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009201823.png  [object Object] 
2025-03-11 09:06:35 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-11 09:06:35 [info] refresh page data from created listeners 0 482   
2025-03-11 09:06:36 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815160506.png  [object Object] 
2025-03-11 09:06:36 [info] refresh page data from created listeners 0 483   
2025-03-11 09:06:36 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 09:06:37 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Attachments/Pasted image 20250126220657.png  [object Object] 
2025-03-11 09:06:37 [info] refresh page data from created listeners 0 484   
2025-03-11 09:06:38 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20241008100523.png  [object Object] 
2025-03-11 09:06:38 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 09:06:38 [info] refresh page data from created listeners 0 485   
2025-03-11 09:06:39 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815155935.png  [object Object] 
2025-03-11 09:06:39 [info] refresh page data from created listeners 0 486   
2025-03-11 09:06:39 [info] trigger 文献库/文献库/舌诊/Weakly Supervised Deep Learning for Tooth-Marked Tongue Recognition.md resolve  [object Object] 
2025-03-11 09:06:40 [info] indexing created file 工作库/项目/声音识别/attachements/时域图.gif  [object Object] 
2025-03-11 09:06:40 [info] refresh page data from created listeners 0 487   
2025-03-11 09:06:40 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:06:40 [info] indexing created file 文献库/文献库/舌诊/机械臂/Attachments/Pasted image 20241111111006.png  [object Object] 
2025-03-11 09:06:41 [info] refresh page data from created listeners 0 488   
2025-03-11 09:06:41 [info] trigger 文献库/文献库/舌诊/机械臂/Visual sensor fusion based autonomous robotic system for assistive drinking.md resolve  [object Object] 
2025-03-11 09:06:42 [info] indexing created file 文献库/算法库/DBC-NMS/-算法卡- - (DBC-NMS)@2023-11-25--Smart Count System Based on Object Detection Using Deep Learning-.mm  [object Object] 
2025-03-11 09:06:42 [info] refresh page data from created listeners 0 489   
2025-03-11 09:06:43 [info] indexing created file 工作库/项目/声音识别/attachements/Pasted image 20240515161514.png  [object Object] 
2025-03-11 09:06:43 [info] refresh page data from created listeners 0 490   
2025-03-11 09:06:44 [info] indexing created file 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/attachments/VLCFI5UB.png  [object Object] 
2025-03-11 09:06:44 [info] refresh page data from created listeners 0 491   
2025-03-11 09:06:44 [info] trigger 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md resolve  [object Object] 
2025-03-11 09:06:45 [info] indexing created file 工作库/项目/论文撰写/舌诊/Attachments/Pasted image 20241212150852.png  [object Object] 
2025-03-11 09:06:45 [info] refresh page data from created listeners 0 492   
2025-03-11 09:06:46 [info] indexing created file 文献库/图表库/YOLOv4/attachments/6M8TPE43.png  [object Object] 
2025-03-11 09:06:46 [info] refresh page data from created listeners 0 493   
2025-03-11 09:06:46 [info] trigger 文献库/图表库/YOLOv4/图表卡-YOLOv4结构图-.md resolve  [object Object] 
2025-03-11 09:06:48 [info] indexing created file 学习库/心得/Attachments/Pasted image 20240925110625.png  [object Object] 
2025-03-11 09:06:48 [info] refresh page data from created listeners 0 494   
2025-03-11 09:06:48 [info] trigger 学习库/心得/文献检索指南.md resolve  [object Object] 
2025-03-11 09:06:49 [info] indexing created file 工作库/项目/声音识别/attachements/相位谱.gif  [object Object] 
2025-03-11 09:06:49 [info] refresh page data from created listeners 0 495   
2025-03-11 09:06:49 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:06:50 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240910160527.png  [object Object] 
2025-03-11 09:06:50 [info] refresh page data from created listeners 0 496   
2025-03-11 09:06:50 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:06:51 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/Quicker_20240729_101434.png  [object Object] 
2025-03-11 09:06:51 [info] trigger 学习库/Deep learning/概念库/语义分割/未命名.md resolve  [object Object] 
2025-03-11 09:06:51 [info] refresh page data from created listeners 0 497   
2025-03-11 09:06:52 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/attachments/Pasted image 20240102144924.png  [object Object] 
2025-03-11 09:06:52 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/只对训练集做最基础的变化.md resolve  [object Object] 
2025-03-11 09:06:52 [info] refresh page data from created listeners 0 498   
2025-03-11 09:06:53 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106171748.png  [object Object] 
2025-03-11 09:06:53 [info] refresh page data from created listeners 0 499   
2025-03-11 09:06:53 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:06:54 [info] indexing created file 文献库/文献库/舌诊/舌象分割/Attachments/Pasted image 20241106171405.png  [object Object] 
2025-03-11 09:06:55 [info] refresh page data from created listeners 0 500   
2025-03-11 09:06:55 [info] trigger 文献库/文献库/舌诊/舌象分割/A novel tongue coating segmentation method based on improved TransUNet.md resolve  [object Object] 
2025-03-11 09:06:56 [info] indexing created file 学习库/Deep learning/Attachments/Pasted image 20240706093227.png  [object Object] 
2025-03-11 09:06:56 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:06:56 [info] refresh page data from created listeners 0 501   
2025-03-11 09:06:57 [info] indexing created file 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/attachments/Pasted image 20240104094319.png  [object Object] 
2025-03-11 09:06:57 [info] trigger 学习库/Deep learning/训练实践/晶体训练/YOLOv5/线状情况分析/最终解决方案.md resolve  [object Object] 
2025-03-11 09:06:57 [info] refresh page data from created listeners 0 502   
2025-03-11 09:06:58 [info] indexing created file 工作库/项目/声音识别/attachements/正弦波.gif  [object Object] 
2025-03-11 09:06:58 [info] refresh page data from created listeners 0 503   
2025-03-11 09:06:58 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:07:00 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240813095625.png  [object Object] 
2025-03-11 09:07:00 [info] refresh page data from created listeners 0 504   
2025-03-11 09:07:00 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:07:01 [info] indexing created file 学习库/ROS/Attachments/Nodes-TopicandService-16542449255392.gif  [object Object] 
2025-03-11 09:07:01 [info] refresh page data from created listeners 0 505   
2025-03-11 09:07:01 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-11 09:07:03 [info] indexing created file 工作库/比赛/智慧路灯/Attachments/Pasted image 20241019174735.png  [object Object] 
2025-03-11 09:07:03 [info] refresh page data from created listeners 0 506   
2025-03-11 09:07:03 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-11 09:07:05 [info] indexing created file 文献库/文献库/舌诊/attachments/Pasted image 20240815170734.png  [object Object] 
2025-03-11 09:07:05 [info] refresh page data from created listeners 0 507   
2025-03-11 09:07:05 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:07:08 [info] indexing created file 工作库/项目/声音识别/attachements/不同振幅叠加 1.gif  [object Object] 
2025-03-11 09:07:08 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:07:08 [info] refresh page data from created listeners 0 508   
2025-03-11 09:07:11 [info] indexing created file 工作库/项目/声音识别/attachements/不同振幅叠加.gif  [object Object] 
2025-03-11 09:07:11 [info] refresh page data from created listeners 0 509   
2025-03-11 09:07:16 [info] indexing created file 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/attachments/EV7XYZXD.png  [object Object] 
2025-03-11 09:07:16 [info] refresh page data from created listeners 0 510   
2025-03-11 09:07:16 [info] trigger 文献库/文献库/应用型/PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection/(2021-08-04) PAG-YOLO- A Portable Attention-Guided YOLO Network for Small Ship Detection.md resolve  [object Object] 
2025-03-11 09:07:21 [info] indexing created file 学习库/Deep learning/Attachments/GIF 2024-10-8 14-54-29.gif  [object Object] 
2025-03-11 09:07:21 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:07:21 [info] refresh page data from created listeners 0 511   
2025-03-11 09:07:26 [info] indexing created file 工作库/项目/声音识别/attachements/不同相位叠加.gif  [object Object] 
2025-03-11 09:07:26 [info] refresh page data from created listeners 0 512   
2025-03-11 09:07:26 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:07:32 [info] indexing created file 学习库/Deep learning/Attachments/GIF 2024-10-8 10-21-33.gif  [object Object] 
2025-03-11 09:07:32 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-11 09:07:32 [info] refresh page data from created listeners 0 513   
2025-03-11 09:07:38 [info] indexing created file 学习库/Deep learning/Attachments/GIF 2024-10-8 11-09-20.gif  [object Object] 
2025-03-11 09:07:38 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-11 09:07:38 [info] refresh page data from created listeners 0 514   
2025-03-11 09:07:47 [info] indexing created file 工作库/项目/声音识别/attachements/不同频率叠加.gif  [object Object] 
2025-03-11 09:07:47 [info] refresh page data from created listeners 0 515   
2025-03-11 09:07:47 [info] trigger 工作库/项目/声音识别/概念.md resolve  [object Object] 
2025-03-11 09:07:47 [info] ignore file modify evnet Home/Home.md   
2025-03-11 09:07:48 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-11 09:07:48 [info] index finished after resolve  [object Object] 
2025-03-11 09:07:48 [info] refresh page data from resolve listeners 0 515   
2025-03-11 09:07:48 [info] ignore file modify evnet Home/components/view/weather.md   
2025-03-11 09:07:48 [info] trigger Home/components/view/weather.md resolve  [object Object] 
2025-03-11 09:07:48 [info] index finished after resolve  [object Object] 
2025-03-11 09:07:48 [info] refresh page data from resolve listeners 0 515   
2025-03-11 09:07:49 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 09:07:51 [info] indexing created file Templater/工作/未命名.md  [object Object] 
2025-03-11 09:07:51 [info] indexing created ignore file Templater/工作/未命名.md   
2025-03-11 09:07:51 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 09:07:51 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 09:07:51 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 09:07:51 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 09:07:51 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 09:07:51 [info] trigger Templater/工作/未命名.md resolve  [object Object] 
2025-03-11 09:07:51 [info] index finished after resolve  [object Object] 
2025-03-11 09:07:51 [info] refresh page data from resolve listeners 0 516   
2025-03-11 09:09:18 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 09:10:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-11 11:24:55 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 11:24:57 [info] refresh page data from delete listeners 0 515   
2025-03-11 11:24:57 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-11 11:24:57 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-11 11:24:57 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-11 11:24:57 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-11 11:24:57 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-11 11:24:59 [info] indexing created file 工作库/template/比赛模板.md  [object Object] 
2025-03-11 11:24:59 [info] indexing created ignore file 工作库/template/比赛模板.md   
2025-03-11 11:24:59 [info] trigger 工作库/template/比赛模板.md resolve  [object Object] 
2025-03-11 11:24:59 [info] index finished after resolve  [object Object] 
2025-03-11 11:24:59 [info] refresh page data from resolve listeners 0 516   
2025-03-11 11:24:59 [info] refresh page data from delete listeners 0 515   
2025-03-11 11:24:59 [info] refresh page data from delete listeners 0 514   
2025-03-11 11:24:59 [info] refresh page data from delete listeners 0 513   
2025-03-11 11:25:00 [info] indexing created file 日记库/template/日记模板.md  [object Object] 
2025-03-11 11:25:00 [info] indexing created ignore file 日记库/template/日记模板.md   
2025-03-11 11:25:00 [info] trigger 日记库/template/日记模板.md resolve  [object Object] 
2025-03-11 11:25:00 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:00 [info] refresh page data from resolve listeners 0 514   
2025-03-11 11:25:00 [info] indexing created file 日记库/template/日记月复盘模板.md  [object Object] 
2025-03-11 11:25:00 [info] indexing created ignore file 日记库/template/日记月复盘模板.md   
2025-03-11 11:25:00 [info] trigger 日记库/template/日记月复盘模板.md resolve  [object Object] 
2025-03-11 11:25:00 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:00 [info] refresh page data from resolve listeners 0 515   
2025-03-11 11:25:01 [info] indexing created file 日记库/template/fleeting_note.md  [object Object] 
2025-03-11 11:25:01 [info] indexing created ignore file 日记库/template/fleeting_note.md   
2025-03-11 11:25:01 [info] trigger 日记库/template/fleeting_note.md resolve  [object Object] 
2025-03-11 11:25:01 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:01 [info] refresh page data from resolve listeners 0 516   
2025-03-11 11:25:02 [info] indexing created file 日记库/day/2025-03-11.md  [object Object] 
2025-03-11 11:25:02 [info] indexing created ignore file 日记库/day/2025-03-11.md   
2025-03-11 11:25:02 [info] trigger 日记库/day/2025-03-11.md resolve  [object Object] 
2025-03-11 11:25:02 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:02 [info] refresh page data from resolve listeners 0 517   
2025-03-11 11:25:03 [info] refresh page data from delete listeners 0 516   
2025-03-11 11:25:03 [info] indexing created file 工作库/项目/舌诊/毕设/开题.md  [object Object] 
2025-03-11 11:25:03 [info] indexing created ignore file 工作库/项目/舌诊/毕设/开题.md   
2025-03-11 11:25:03 [info] trigger 工作库/项目/舌诊/毕设/开题.md resolve  [object Object] 
2025-03-11 11:25:03 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:03 [info] refresh page data from resolve listeners 0 517   
2025-03-11 11:25:04 [info] indexing created file 工作库/template/项目模板.md  [object Object] 
2025-03-11 11:25:04 [info] indexing created ignore file 工作库/template/项目模板.md   
2025-03-11 11:25:04 [info] trigger 工作库/template/项目模板.md resolve  [object Object] 
2025-03-11 11:25:04 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:04 [info] refresh page data from resolve listeners 0 518   
2025-03-11 11:25:05 [info] indexing created file 日记库/day/2025-02-27.md  [object Object] 
2025-03-11 11:25:05 [info] indexing created ignore file 日记库/day/2025-02-27.md   
2025-03-11 11:25:05 [info] trigger 日记库/day/2025-02-27.md resolve  [object Object] 
2025-03-11 11:25:05 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:05 [info] refresh page data from resolve listeners 0 519   
2025-03-11 11:25:05 [info] indexing created file 日记库/day/工作/2025-03-11.md  [object Object] 
2025-03-11 11:25:05 [info] indexing created ignore file 日记库/day/工作/2025-03-11.md   
2025-03-11 11:25:05 [info] trigger 日记库/day/工作/2025-03-11.md resolve  [object Object] 
2025-03-11 11:25:05 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:05 [info] refresh page data from resolve listeners 0 520   
2025-03-11 11:25:06 [info] ignore file modify evnet 日记库/日记库.components   
2025-03-11 11:25:07 [info] indexing created file 工作库/template/工作记录模板.md  [object Object] 
2025-03-11 11:25:07 [info] indexing created ignore file 工作库/template/工作记录模板.md   
2025-03-11 11:25:07 [info] trigger 工作库/template/工作记录模板.md resolve  [object Object] 
2025-03-11 11:25:07 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:07 [info] refresh page data from resolve listeners 0 521   
2025-03-11 11:25:07 [info] ignore file modify evnet 工作库/工作库.components   
2025-03-11 11:25:08 [info] indexing created file 学习库/学习库.components  [object Object] 
2025-03-11 11:25:08 [info] trigger Home/components/view/快速导航.md resolve  [object Object] 
2025-03-11 11:25:08 [info] refresh page data from created listeners 0 522   
2025-03-11 11:25:09 [info] indexing created file att/通用学习模板-2025-03-11.png  [object Object] 
2025-03-11 11:25:09 [info] refresh page data from created listeners 0 523   
2025-03-11 11:25:10 [info] indexing created file 学习库/template/通用学习模板.md  [object Object] 
2025-03-11 11:25:10 [info] indexing created ignore file 学习库/template/通用学习模板.md   
2025-03-11 11:25:10 [info] trigger 学习库/template/通用学习模板.md resolve  [object Object] 
2025-03-11 11:25:10 [info] index finished after resolve  [object Object] 
2025-03-11 11:25:10 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:47:17 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 12:47:17 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:47:17 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:47:17 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:47:18 [info] components database created cost 1 ms   
2025-03-11 12:47:18 [info] components index initializing...   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:47:18 [info] components index initialized, 524 files cost 59 ms   
2025-03-11 12:47:18 [info] refresh page data from init listeners 0 524   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:47:18 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:47:19 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:47:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:47:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:47:19 [error] check new version failed  Error: Request failed, status 403 
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:47:20 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:48:50 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-11 12:48:50 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-11 12:48:50 [info] index finished after resolve  [object Object] 
2025-03-11 12:48:50 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:49:29 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-11 12:49:29 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-11 12:49:29 [info] index finished after resolve  [object Object] 
2025-03-11 12:49:29 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:50:01 [info] ignore file modify evnet 学习库/Anki/python/语句.md   
2025-03-11 12:50:01 [info] trigger 学习库/Anki/python/语句.md resolve  [object Object] 
2025-03-11 12:50:01 [info] index finished after resolve  [object Object] 
2025-03-11 12:50:01 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:51:08 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:51:08 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:51:08 [info] index finished after resolve  [object Object] 
2025-03-11 12:51:08 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:51:44 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:51:44 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:51:44 [info] index finished after resolve  [object Object] 
2025-03-11 12:51:44 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:52:02 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:52:02 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:52:02 [info] index finished after resolve  [object Object] 
2025-03-11 12:52:02 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:52:17 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 12:54:04 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:54:04 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:54:05 [info] index finished after resolve  [object Object] 
2025-03-11 12:54:05 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:54:16 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:54:16 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:54:16 [info] index finished after resolve  [object Object] 
2025-03-11 12:54:16 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:54:17 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:54:18 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:54:18 [info] index finished after resolve  [object Object] 
2025-03-11 12:54:18 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:54:37 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:54:37 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:54:37 [info] index finished after resolve  [object Object] 
2025-03-11 12:54:37 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:55:13 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-11 12:55:13 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-11 12:55:13 [info] index finished after resolve  [object Object] 
2025-03-11 12:55:13 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:58:38 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 12:58:45 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-03-11 12:58:45 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 12:58:45 [info] index finished after resolve  [object Object] 
2025-03-11 12:58:45 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:58:46 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-03-11 12:58:46 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-11 12:58:46 [info] index finished after resolve  [object Object] 
2025-03-11 12:58:46 [info] refresh page data from resolve listeners 0 524   
2025-03-11 12:59:13 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-11 12:59:14 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:59:14 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:59:14 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:59:15 [info] components database created cost 1 ms   
2025-03-11 12:59:15 [info] components index initializing...   
2025-03-11 12:59:15 [info] components index initialized, 524 files cost 112 ms   
2025-03-11 12:59:15 [info] refresh page data from init listeners 0 524   
2025-03-11 12:59:15 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:59:15 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:59:15 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:59:15 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:59:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:59:16 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:59:16 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 12:59:17 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 13:00:55 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-11 13:00:55 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 13:00:55 [info] index finished after resolve  [object Object] 
2025-03-11 13:00:55 [info] refresh page data from resolve listeners 0 524   
2025-03-11 13:02:04 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-11 13:02:04 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 13:02:04 [info] index finished after resolve  [object Object] 
2025-03-11 13:02:04 [info] refresh page data from resolve listeners 0 524   
2025-03-11 13:02:12 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-11 13:02:12 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-11 13:02:12 [info] index finished after resolve  [object Object] 
2025-03-11 13:02:12 [info] refresh page data from resolve listeners 0 524   
2025-03-11 13:30:39 [info] upgrade db  [object Object] 
2025-03-11 13:30:39 [info] components database created cost 66 ms   
2025-03-11 13:30:39 [info] components index initializing...   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-11 13:30:40 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-11 13:30:41 [info] components index initialized, 524 files cost 1425 ms   
2025-03-11 13:30:41 [info] refresh page data from init listeners 0 524   
2025-03-11 13:31:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 13:31:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-11 13:31:37 [info] auth: Request failed. The certificate for this server is invalid. You might be connecting to a server that is pretending to be “api.ob-components.com” which could put your confidential information at risk.   
2025-03-11 13:31:46 [error] Load Component Error: {"id":"988560c2-3b05-473a-b563-50945fd33a14","type":"chart","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2024-12-27T05:57:54.921Z","updateAt":"2024-12-27T05:57:54.921Z","chartType":"heatmap","filter":{"id":"e6f7d84a-499d-48a6-b779-351efdb7bfd8","type":"group","operator":"and","conditions":[{"id":"d1ef6824-5eda-4d93-999a-611680655bae","type":"filter","operator":"contains","property":"${file.parent}","value":"area/journal/day","conditions":[]}]},"labelProperty":"${file.basename}","labelFormat":"$none","valueProperty":"${file.words}","valuePrecision":0,"sortField":"xAxisValue","sortOrder":"asc","maxHeight":300,"chartColorSet":"default","chartLabelPosition":"top","showDataValue":false,"valueScaleStartFromZero":true,"smoothLine":false,"fillArea":false,"options":{"minValue":1,"maxValue":3000,"cellSize":12,"numberOfSegments":4,"showSplitLine":false,"firstDayOfWeek":1,"dateRange":{"type":"currentYear","latestValue":180,"latestUnit":"day"}},"title":"今年日记字词数热力图"}, error detail:   {"message":"document[Kth(0x3aec)]['computedStyleMap'] is not a function. (In 'document[Kth(0x3aec)]['computedStyleMap']()', 'document[Kth(0x3aec)]['computedStyleMap']' is undefined)","stack":"@\nuseMemo@\nRBt@\nHBt@\nh1Z@\nh4z@\nh45@\nh43@\nh42@\nh3B@\nh3w@\nh3w@[native code]\nx@\nR@"},
HBt
Zio
div
W@
xv
nao
Suspense
div
W@
W@
W@
div
W@
W@
div
div
uao
Bro
div
So
zdr
Zio
div
W@
xv
W@
noe 
2025-03-11 13:31:46 [error] Load Component Error: {"id":"cef44a38-95e6-431c-a6ca-f0fffedff3e9","type":"chart","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2024-12-27T05:57:54.921Z","updateAt":"2024-12-27T05:57:54.921Z","chartType":"bar","filter":{"id":"e6f7d84a-499d-48a6-b779-351efdb7bfd8","type":"group","operator":"and","conditions":[{"id":"d1ef6824-5eda-4d93-999a-611680655bae","type":"filter","operator":"contains","property":"${file.parent}","value":"journal/day","conditions":[]}]},"labelProperty":"${file.basename}","labelFormat":"$timeFormat","valueProperty":"$file_count","valuePrecision":0,"sortField":"xAxisValue","sortOrder":"asc","maxHeight":300,"chartColorSet":"default","chartLabelPosition":"top","showDataValue":true,"valueScaleStartFromZero":true,"smoothLine":false,"fillArea":false,"options":{"minValue":1,"maxValue":3000,"cellSize":12,"numberOfSegments":4,"showSplitLine":false,"firstDayOfWeek":1,"dateRange":{"type":"currentYear","latestValue":180,"latestUnit":"day"}},"title":"季度日记数","valueFormatPattern":"yyyy-'Q'q","labelRotation":0}, error detail:   {"message":"document[Kth(0x3aec)]['computedStyleMap'] is not a function. (In 'document[Kth(0x3aec)]['computedStyleMap']()', 'document[Kth(0x3aec)]['computedStyleMap']' is undefined)","stack":"@\nuseMemo@\nRBt@\nHBt@\nh1Z@\nh4z@\nh45@\nh43@\nh42@\nh3B@\nh3w@\nh3w@[native code]\nx@\nR@"},
HBt
Zio
div
W@
xv
nao
Suspense
div
W@
W@
W@
div
W@
W@
div
div
uao
Bro
div
So
zdr
Zio
div
W@
xv
W@
noe 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query  [object Object] 
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/4   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:46 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:51 [info] refresh page data from reload listeners 7 0   
2025-03-11 13:31:51 [info] start to recreateObjectStore   
2025-03-11 13:31:51 [info] delete database success   
2025-03-11 13:31:51 [info] Rebuild onupgradeneeded   
2025-03-11 13:31:51 [info] Rebuild DB opened  [object IDBDatabase] 
2025-03-11 13:31:51 [info] recreate object store cost 51 ms   
2025-03-11 13:31:51 [info] components index initializing...   
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query  [object Object] 
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 3/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 1/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 6/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 4/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 1/0   
2025-03-11 13:31:51 [info] query changed, compare cost 0ms, data length diff 3/0   
2025-03-11 13:31:52 [info] components index initialized, 524 files cost 439 ms   
2025-03-11 13:31:52 [info] refresh page data from init listeners 7 524   
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query  [object Object] 
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/4   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:52 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:54 [info] refresh page data from reload listeners 7 0   
2025-03-11 13:31:54 [info] start to recreateObjectStore   
2025-03-11 13:31:54 [info] delete database success   
2025-03-11 13:31:54 [info] Rebuild onupgradeneeded   
2025-03-11 13:31:54 [info] Rebuild DB opened  [object IDBDatabase] 
2025-03-11 13:31:54 [info] recreate object store cost 26 ms   
2025-03-11 13:31:54 [info] components index initializing...   
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query  [object Object] 
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 3/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 1/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 6/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 4/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 1/0   
2025-03-11 13:31:54 [info] query changed, compare cost 0ms, data length diff 3/0   
2025-03-11 13:31:55 [info] components index initialized, 524 files cost 385 ms   
2025-03-11 13:31:55 [info] refresh page data from init listeners 7 524   
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query  [object Object] 
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/6   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/4   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-03-11 13:31:55 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-03-11 13:31:59 [info] match pageData is  [object Object] 
2025-03-11 13:31:59 [info] match pageData is  [object Object] 
