2025-03-08 17:50:41 [info] upgrade db  [object Object] 
2025-03-08 17:50:41 [info] components database created cost 86 ms   
2025-03-08 17:50:41 [info] components index initializing...   
2025-03-08 17:50:41 [info] indexing created file components/logs/2025-03-08.components.log  [object Object] 
2025-03-08 17:50:43 [info] components index initialized, 496 files cost 1565 ms   
2025-03-08 17:50:43 [info] refresh page data from init listeners 0 496   
2025-03-08 17:52:05 [info] components database created cost 1 ms   
2025-03-08 17:52:05 [info] components index initializing...   
2025-03-08 17:52:05 [info] components index initialized, 497 files cost 67 ms   
2025-03-08 17:52:05 [info] refresh page data from init listeners 0 497   
2025-03-08 19:45:44 [info] components database created cost 57 ms   
2025-03-08 19:45:44 [info] components index initializing...   
2025-03-08 19:45:44 [info] components index initialized, 497 files cost 128 ms   
2025-03-08 19:45:44 [info] refresh page data from init listeners 0 497   
2025-03-08 19:45:48 [info] ignore file modify evnet Home.md   
2025-03-08 19:45:48 [info] trigger Home.md resolve  [object Object] 
2025-03-08 19:45:48 [info] index finished after resolve  [object Object] 
2025-03-08 19:45:48 [info] refresh page data from resolve listeners 0 497   
2025-03-08 19:45:51 [info] indexing created file components/view1/20250308194317.components  [object Object] 
2025-03-08 19:45:51 [info] refresh page data from created listeners 0 498   
2025-03-08 19:45:51 [info] trigger Home.md resolve  [object Object] 
2025-03-08 19:45:52 [info] indexing created file components/view/kanban.components  [object Object] 
2025-03-08 19:45:52 [info] refresh page data from created listeners 0 499   
2025-03-08 19:45:52 [info] trigger Home.md resolve  [object Object] 
2025-03-08 19:45:53 [info] indexing created file components/view/home.components  [object Object] 
2025-03-08 19:45:53 [info] trigger Home.md resolve  [object Object] 
2025-03-08 19:45:53 [info] refresh page data from created listeners 0 500   
2025-03-08 19:45:53 [info] ignore file modify evnet components/view/home.components   
2025-03-08 19:45:55 [info] indexing created file Deep learning/概念库/目标检测的结构组成/-概念卡- - -目标检测的结构组成-.mm  [object Object] 
2025-03-08 19:45:55 [info] refresh page data from created listeners 0 501   
2025-03-08 19:45:56 [info] indexing created file Deep learning/概念库/评价指标/回归评价指标/-概念卡- - -均方误差(MSE)&均方根误差(RMSE)-.mm  [object Object] 
2025-03-08 19:45:56 [info] refresh page data from created listeners 0 502   
2025-03-08 19:45:57 [info] indexing created file att/js/getweather.js  [object Object] 
2025-03-08 19:45:57 [info] refresh page data from created listeners 0 503   
2025-03-08 19:45:58 [info] indexing created file att/js/dv_weatherSvg.js  [object Object] 
2025-03-08 19:45:58 [info] refresh page data from created listeners 0 504   
2025-03-08 19:46:00 [info] indexing created file Deep learning/概念库/欧氏距离&曼哈顿距离/-概念卡 -- -欧式距离＆曼哈顿距离-.mm  [object Object] 
2025-03-08 19:46:00 [info] refresh page data from created listeners 0 505   
2025-03-08 19:46:03 [info] indexing created file 文献阅读/文献库/应用型/Smart Count System Based on Object Detection Using Deep Learning/(2022-1) Smart Count System Based on Object Detection Using Deep Learning.mm  [object Object] 
2025-03-08 19:46:03 [info] refresh page data from created listeners 0 506   
2025-03-08 19:46:05 [info] indexing created file 文献阅读/算法库/DBC-NMS/-算法卡- - (DBC-NMS)@2023-11-25--Smart Count System Based on Object Detection Using Deep Learning-.mm  [object Object] 
2025-03-08 19:46:05 [info] refresh page data from created listeners 0 507   
2025-03-08 19:46:39 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-08 19:46:55 [info] ignore file modify evnet components/view/home.components   
2025-03-08 19:46:55 [info] ignore file modify evnet components/view/kanban.components   
2025-03-08 19:46:55 [info] ignore file modify evnet components/view1/20250308194317.components   
2025-03-08 20:00:05 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-08 20:00:05 [info] refresh page data from delete listeners 0 506   
2025-03-08 20:00:05 [info] trigger Home.md resolve  [object Object] 
2025-03-08 20:00:06 [info] ignore file modify evnet Home.md   
2025-03-08 20:00:06 [info] trigger Home.md resolve  [object Object] 
2025-03-08 20:00:06 [info] index finished after resolve  [object Object] 
2025-03-08 20:00:06 [info] refresh page data from resolve listeners 0 506   
2025-03-08 20:00:07 [info] indexing created file img/love.jpeg  [object Object] 
2025-03-08 20:00:07 [info] refresh page data from created listeners 0 507   
2025-03-08 20:00:07 [info] indexing created file components/view/remember.components  [object Object] 
2025-03-08 20:00:07 [info] trigger Home.md resolve  [object Object] 
2025-03-08 20:00:07 [info] refresh page data from created listeners 0 508   
2025-03-08 21:57:01 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-08 21:57:06 [info] indexing created file img/my_precious.jpg  [object Object] 
2025-03-08 21:57:06 [info] refresh page data from created listeners 0 509   
2025-03-08 21:57:06 [info] refresh page data from delete listeners 0 508   
2025-03-08 21:57:06 [info] indexing created file WEI's.md  [object Object] 
2025-03-08 21:57:06 [info] indexing created ignore file WEI's.md   
2025-03-08 21:57:06 [info] trigger WEI's.md resolve  [object Object] 
2025-03-08 21:57:06 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:06 [info] refresh page data from resolve listeners 0 509   
2025-03-08 21:57:06 [info] indexing created file 未命名 3.md  [object Object] 
2025-03-08 21:57:06 [info] indexing created ignore file 未命名 3.md   
2025-03-08 21:57:06 [info] refresh page data from delete listeners 0 508   
2025-03-08 21:57:06 [info] trigger 未命名 3.md resolve  [object Object] 
2025-03-08 21:57:06 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:06 [info] refresh page data from resolve listeners 0 509   
2025-03-08 21:57:06 [info] refresh page data from delete listeners 0 508   
2025-03-08 21:57:06 [info] indexing created file components/view/flash.components  [object Object] 
2025-03-08 21:57:06 [info] refresh page data from created listeners 0 509   
2025-03-08 21:57:07 [info] indexing created file fleeting_notes/记得要去补充学术之星的文件.md  [object Object] 
2025-03-08 21:57:07 [info] indexing created ignore file fleeting_notes/记得要去补充学术之星的文件.md   
2025-03-08 21:57:07 [info] trigger fleeting_notes/记得要去补充学术之星的文件.md resolve  [object Object] 
2025-03-08 21:57:07 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:07 [info] refresh page data from resolve listeners 0 510   
2025-03-08 21:57:07 [info] refresh page data from delete listeners 0 509   
2025-03-08 21:57:07 [info] ignore file modify evnet Home.md   
2025-03-08 21:57:07 [info] trigger Home.md resolve  [object Object] 
2025-03-08 21:57:07 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:07 [info] refresh page data from resolve listeners 0 509   
2025-03-08 21:57:07 [info] indexing created file components/view/fleeting.components  [object Object] 
2025-03-08 21:57:07 [info] trigger Home.md resolve  [object Object] 
2025-03-08 21:57:07 [info] refresh page data from created listeners 0 510   
2025-03-08 21:57:07 [info] indexing created file fleeting_notes/2025-03-08.md  [object Object] 
2025-03-08 21:57:07 [info] indexing created ignore file fleeting_notes/2025-03-08.md   
2025-03-08 21:57:07 [info] trigger fleeting_notes/2025-03-08.md resolve  [object Object] 
2025-03-08 21:57:07 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:07 [info] refresh page data from resolve listeners 0 511   
2025-03-08 21:57:08 [info] indexing created file Templater/fleeting_note.md  [object Object] 
2025-03-08 21:57:08 [info] indexing created ignore file Templater/fleeting_note.md   
2025-03-08 21:57:08 [info] trigger Templater/fleeting_note.md resolve  [object Object] 
2025-03-08 21:57:08 [info] index finished after resolve  [object Object] 
2025-03-08 21:57:08 [info] refresh page data from resolve listeners 0 512   
2025-03-08 21:57:08 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:57:08 [info] query  [object Object] 
2025-03-08 21:57:08 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:57:08 [info] indexing created file components/view/Home.components  [object Object] 
2025-03-08 21:57:08 [info] refresh page data from created listeners 1 513   
2025-03-08 21:57:08 [info] trigger 未命名 3.md resolve  [object Object] 
2025-03-08 21:57:09 [info] query  [object Object] 
2025-03-08 21:57:12 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:57:13 [error] Load Component Error: {"id":"c97e3fc5-e2a5-4d05-8cf8-75f38dd4ed43","type":"multi","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2025-03-08T11:48:52.296Z","updateAt":"2025-03-08T11:48:52.296Z","components":[{"componentId":"0e3e98b4-71a3-4af3-bff6-6d929a10ff05","widthRatio":48.8686526357759},{"componentId":"fd069b88-b751-4746-a7c5-137433a5f1cf","widthRatio":51.1313473642241}],"layoutType":"column","locked":false,"layoutOptions":{}}, error detail:   {"message":"Component not found: 288ed3ff-f109-49c4-986e-d156091f9529","stack":"Error: Component not found: 288ed3ff-f109-49c4-986e-d156091f9529\n    at ioe.getById (plugin:components:1:5210938)\n    at Ar (plugin:components:1:1249554)\n    at xre (plugin:components:1:4934690)\n    at Odr (plugin:components:1:4935821)\n    at h1Z (plugin:components:1:723550)\n    at h2G (plugin:components:1:739949)\n    at h4z (plugin:components:1:797137)\n    at h45 (plugin:components:1:790087)\n    at h43 (plugin:components:1:789994)\n    at h42 (plugin:components:1:789813)"},
    at Odr (plugin:components:1:4935773)
    at div
    at DMr (plugin:components:1:1219730)
    at div
    at iDt (plugin:components:1:1217010)
    at SMr (plugin:components:1:1218044)
    at Vdr (plugin:components:1:4937602)
    at div
    at So (plugin:components:1:1250209)
    at zdr (plugin:components:1:4940336)
    at Zio (plugin:components:1:5155629)
    at div
    at kx (plugin:components:1:1245018)
    at xv (plugin:components:1:5154004)
    at kx (plugin:components:1:1245018)
    at noe (plugin:components:1:5207594)
    at div 
2025-03-08 21:57:13 [error] Load Component Error: {"id":"c97e3fc5-e2a5-4d05-8cf8-75f38dd4ed43","type":"multi","titleAlign":"center","tabTitle":"","maxWidthRatio":-1,"showBorder":true,"showShadow":false,"createAt":"2025-03-08T11:48:52.296Z","updateAt":"2025-03-08T11:48:52.296Z","components":[{"componentId":"0e3e98b4-71a3-4af3-bff6-6d929a10ff05","widthRatio":48.8686526357759},{"componentId":"fd069b88-b751-4746-a7c5-137433a5f1cf","widthRatio":51.1313473642241}],"layoutType":"column","locked":false,"layoutOptions":{}}, error detail:   {"message":"Component not found: 288ed3ff-f109-49c4-986e-d156091f9529","stack":"Error: Component not found: 288ed3ff-f109-49c4-986e-d156091f9529\n    at ioe.getById (plugin:components:1:5210938)\n    at ioe.getMultiLayout (plugin:components:1:5211122)\n    at xv (plugin:components:1:5154107)\n    at h1Z (plugin:components:1:723550)\n    at h2G (plugin:components:1:739949)\n    at h4z (plugin:components:1:797137)\n    at h45 (plugin:components:1:790087)\n    at h43 (plugin:components:1:789994)\n    at h42 (plugin:components:1:789813)\n    at h3B (plugin:components:1:784655)"},
    at xv (plugin:components:1:5154004)
    at div
    at RMr (plugin:components:1:1222715)
    at div
    at IMr (plugin:components:1:1222476)
    at div
    at iDt (plugin:components:1:1217010)
    at SMr (plugin:components:1:1218044)
    at Vdr (plugin:components:1:4937602)
    at div
    at So (plugin:components:1:1250209)
    at zdr (plugin:components:1:4940336)
    at Zio (plugin:components:1:5155629)
    at div
    at kx (plugin:components:1:1245018)
    at xv (plugin:components:1:5154004)
    at kx (plugin:components:1:1245018)
    at noe (plugin:components:1:5207594)
    at div 
2025-03-08 21:57:15 [info] refresh page data from reload listeners 1 0   
2025-03-08 21:57:15 [info] start to recreateObjectStore   
2025-03-08 21:57:15 [info] delete database success   
2025-03-08 21:57:15 [info] Rebuild onupgradeneeded   
2025-03-08 21:57:15 [info] Rebuild DB opened  [object IDBDatabase] 
2025-03-08 21:57:15 [info] recreate object store cost 38 ms   
2025-03-08 21:57:15 [info] components index initializing...   
2025-03-08 21:57:15 [info] query  [object Object] 
2025-03-08 21:57:15 [info] query changed, compare cost 0ms, data length diff 2/0   
2025-03-08 21:57:16 [info] components index initialized, 513 files cost 974 ms   
2025-03-08 21:57:16 [info] refresh page data from init listeners 1 513   
2025-03-08 21:57:16 [info] query  [object Object] 
2025-03-08 21:57:16 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:57:29 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-08 21:57:30 [info] query  [object Object] 
2025-03-08 21:57:30 [info] query  [object Object] 
2025-03-08 21:57:31 [info] upgrade db  [object Object] 
2025-03-08 21:57:31 [info] clear all page data in upgrade   
2025-03-08 21:57:31 [info] components database created cost 12 ms   
2025-03-08 21:57:31 [info] components index initializing...   
2025-03-08 21:57:31 [info] query  [object Object] 
2025-03-08 21:57:31 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:57:31 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:57:31 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 21:57:33 [info] components index initialized, 513 files cost 2777 ms   
2025-03-08 21:57:33 [info] refresh page data from init listeners 1 513   
2025-03-08 21:57:33 [info] query  [object Object] 
2025-03-08 21:57:33 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:57:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 21:59:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 21:59:20 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:20 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:59:21 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:21 [info] query  [object Object] 
2025-03-08 21:59:21 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:59:21 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 21:59:21 [info] query  [object Object] 
2025-03-08 21:59:21 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:59:22 [info] ignore file modify evnet components/view/flash.components   
2025-03-08 21:59:22 [info] query  [object Object] 
2025-03-08 21:59:22 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:59:22 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 21:59:23 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:24 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:59:30 [info] query  [object Object] 
2025-03-08 21:59:30 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:59:30 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:59:30 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 21:59:30 [info] query  [object Object] 
2025-03-08 21:59:30 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 21:59:30 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 21:59:30 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 21:59:30 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:33 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:33 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:38 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 21:59:40 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:04 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:05 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:05 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:06 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:07 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:19 [info] query  [object Object] 
2025-03-08 22:00:19 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:00:19 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:19 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:00:19 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 22:00:26 [info] query  [object Object] 
2025-03-08 22:00:26 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:00:26 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:00:26 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 22:00:26 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:30 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:00:45 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:01:01 [info] indexing created file components/view/test.components  [object Object] 
2025-03-08 22:01:01 [info] refresh page data from created listeners 0 514   
2025-03-08 22:01:27 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:01:38 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:01:49 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:02:07 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:02:15 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:02:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 22:03:54 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:04:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 22:04:20 [info] ignore file modify evnet 未命名 1.md   
2025-03-08 22:04:20 [info] trigger 未命名 1.md resolve  [object Object] 
2025-03-08 22:04:20 [info] index finished after resolve  [object Object] 
2025-03-08 22:04:20 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:04:33 [info] ignore file modify evnet 变量.md   
2025-03-08 22:04:33 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 22:04:33 [info] index finished after resolve  [object Object] 
2025-03-08 22:04:33 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:04:45 [info] ignore file modify evnet 变量.md   
2025-03-08 22:04:45 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 22:04:45 [info] index finished after resolve  [object Object] 
2025-03-08 22:04:45 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:04:49 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:05:02 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:05:12 [info] ignore file modify evnet 变量.md   
2025-03-08 22:05:12 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 22:05:12 [info] index finished after resolve  [object Object] 
2025-03-08 22:05:12 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:05:26 [info] ignore file modify evnet 变量.md   
2025-03-08 22:05:26 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 22:05:26 [info] index finished after resolve  [object Object] 
2025-03-08 22:05:26 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:06:27 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:06:44 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:06:55 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:07:13 [info] ignore file modify evnet components/view/test.components   
2025-03-08 22:07:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 22:09:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 22:09:41 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:09:41 [info] ignore file modify evnet components/view/Home.components   
2025-03-08 22:09:42 [info] query  [object Object] 
2025-03-08 22:09:43 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:09:43 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 22:09:43 [info] query  [object Object] 
2025-03-08 22:09:43 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:09:43 [info] ignore file modify evnet components/view/flash.components   
2025-03-08 22:09:46 [info] query  [object Object] 
2025-03-08 22:09:46 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:09:46 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:09:46 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 22:09:50 [info] query  [object Object] 
2025-03-08 22:09:50 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 22:09:50 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:09:50 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 22:09:52 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:09:55 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:10:04 [info] ignore file modify evnet Home.md   
2025-03-08 22:10:04 [info] trigger Home.md resolve  [object Object] 
2025-03-08 22:10:04 [info] index finished after resolve  [object Object] 
2025-03-08 22:10:04 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:10:15 [info] ignore file modify evnet Home.md   
2025-03-08 22:10:15 [info] trigger Home.md resolve  [object Object] 
2025-03-08 22:10:15 [info] index finished after resolve  [object Object] 
2025-03-08 22:10:15 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:10:33 [info] ignore file modify evnet Home.md   
2025-03-08 22:10:33 [info] trigger Home.md resolve  [object Object] 
2025-03-08 22:10:33 [info] index finished after resolve  [object Object] 
2025-03-08 22:10:33 [info] refresh page data from resolve listeners 0 514   
2025-03-08 22:10:54 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:11:04 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:11:16 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:11:31 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:11:39 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:11:53 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:02 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:16 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:20 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:22 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:22 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:26 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 22:12:49 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 22:12:52 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 23:12:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:12:16 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 23:12:18 [info] indexing created file 闪念.md  [object Object] 
2025-03-08 23:12:18 [info] indexing created ignore file 闪念.md   
2025-03-08 23:12:18 [info] trigger 闪念.md resolve  [object Object] 
2025-03-08 23:12:18 [info] index finished after resolve  [object Object] 
2025-03-08 23:12:18 [info] refresh page data from resolve listeners 6 515   
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:19 [info] query  [object Object] 
2025-03-08 23:12:28 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 23:12:29 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 23:13:38 [info] ignore file modify evnet components/view/remember.components   
2025-03-08 23:13:39 [info] query  [object Object] 
2025-03-08 23:13:39 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 23:13:39 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 23:13:42 [info] query  [object Object] 
2025-03-08 23:13:42 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 23:13:42 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 23:13:50 [info] query  [object Object] 
2025-03-08 23:13:50 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 23:13:50 [info] query  [object Object] 
2025-03-08 23:13:50 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 23:13:50 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 23:13:50 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 23:13:54 [info] query  [object Object] 
2025-03-08 23:13:54 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-03-08 23:13:54 [info] ignore file modify evnet components/view/fleeting.components   
2025-03-08 23:14:15 [info] refresh page data from delete listeners 0 514   
2025-03-08 23:14:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:15:43 [info] refresh page data from delete listeners 0 513   
2025-03-08 23:15:43 [info] refresh page data from delete listeners 0 512   
2025-03-08 23:15:43 [info] trigger Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-08 23:15:43 [info] trigger 文献阅读/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-08 23:15:43 [info] trigger 文献阅读/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-08 23:15:43 [info] trigger 文献阅读/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-08 23:15:43 [info] trigger 文献阅读/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-08 23:16:43 [info] refresh page data from delete listeners 0 511   
2025-03-08 23:16:45 [info] indexing created file fleeting_notes/闪念.md  [object Object] 
2025-03-08 23:16:45 [info] indexing created ignore file fleeting_notes/闪念.md   
2025-03-08 23:16:45 [info] trigger fleeting_notes/闪念.md resolve  [object Object] 
2025-03-08 23:16:45 [info] index finished after resolve  [object Object] 
2025-03-08 23:16:45 [info] refresh page data from resolve listeners 0 512   
2025-03-08 23:19:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:22:45 [info] refresh page data from delete listeners 0 511   
2025-03-08 23:24:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:29:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:32:47 [info] indexing created file components/view/time.components  [object Object] 
2025-03-08 23:32:47 [info] refresh page data from created listeners 0 512   
2025-03-08 23:32:48 [info] ignore file modify evnet Home.md   
2025-03-08 23:32:48 [info] trigger Home.md resolve  [object Object] 
2025-03-08 23:32:48 [info] index finished after resolve  [object Object] 
2025-03-08 23:32:48 [info] refresh page data from resolve listeners 0 512   
2025-03-08 23:34:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:39:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:44:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:49:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:52:43 [info] refresh page data from delete listeners 0 511   
2025-03-08 23:52:44 [info] ignore file modify evnet 未命名 1.md   
2025-03-08 23:52:44 [info] refresh page data from delete listeners 0 510   
2025-03-08 23:52:44 [info] trigger 未命名 1.md resolve  [object Object] 
2025-03-08 23:52:44 [info] index finished after resolve  [object Object] 
2025-03-08 23:52:44 [info] refresh page data from resolve listeners 0 510   
2025-03-08 23:52:44 [info] refresh page data from delete listeners 0 509   
2025-03-08 23:52:44 [info] refresh page data from delete listeners 0 508   
2025-03-08 23:52:44 [info] refresh page data from delete listeners 0 507   
2025-03-08 23:52:44 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 23:52:44 [info] trigger 未命名 3.md resolve  [object Object] 
2025-03-08 23:52:44 [info] refresh page data from delete listeners 0 506   
2025-03-08 23:52:44 [info] ignore file modify evnet 未命名 3.md   
2025-03-08 23:52:44 [info] trigger 未命名 3.md resolve  [object Object] 
2025-03-08 23:52:44 [info] index finished after resolve  [object Object] 
2025-03-08 23:52:44 [info] refresh page data from resolve listeners 0 506   
2025-03-08 23:52:45 [info] indexing created file Home/components/view/time.components  [object Object] 
2025-03-08 23:52:45 [info] refresh page data from created listeners 0 507   
2025-03-08 23:52:45 [info] indexing created file Home/components/view/remember.components  [object Object] 
2025-03-08 23:52:45 [info] refresh page data from created listeners 0 508   
2025-03-08 23:52:46 [info] indexing created file Home/components/view/flash.components  [object Object] 
2025-03-08 23:52:46 [info] refresh page data from created listeners 0 509   
2025-03-08 23:52:46 [info] indexing created file Home/components/view/fleeting.components  [object Object] 
2025-03-08 23:52:46 [info] refresh page data from created listeners 0 510   
2025-03-08 23:52:47 [info] indexing created file Home/components/view/Home.components  [object Object] 
2025-03-08 23:52:47 [info] refresh page data from created listeners 0 511   
2025-03-08 23:52:47 [info] trigger 变量.md resolve  [object Object] 
2025-03-08 23:52:47 [info] trigger 未命名 3.md resolve  [object Object] 
2025-03-08 23:54:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-08 23:59:25 [info] auth: net::ERR_CERT_DATE_INVALID   
