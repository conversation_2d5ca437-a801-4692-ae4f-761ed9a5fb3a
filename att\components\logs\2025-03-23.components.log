2025-03-23 02:37:21 [info] ignore file modify evnet 学习库/二级/数据库.md   
2025-03-23 02:37:21 [info] indexing created file components/logs/2025-03-23.components.log  [object Object] 
2025-03-23 02:37:21 [info] refresh page data from created listeners 0 640   
2025-03-23 02:37:21 [info] trigger 学习库/二级/数据库.md resolve  [object Object] 
2025-03-23 02:37:21 [info] index finished after resolve  [object Object] 
2025-03-23 02:37:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:41:15 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-23 02:41:15 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-23 02:41:15 [info] index finished after resolve  [object Object] 
2025-03-23 02:41:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:41:15 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-23 02:41:15 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-23 02:41:15 [info] index finished after resolve  [object Object] 
2025-03-23 02:41:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:41:46 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-23 02:41:46 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-23 02:41:46 [info] index finished after resolve  [object Object] 
2025-03-23 02:41:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:44:46 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-23 02:44:46 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-23 02:44:46 [info] index finished after resolve  [object Object] 
2025-03-23 02:44:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:44:46 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-23 02:44:46 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-23 02:44:46 [info] index finished after resolve  [object Object] 
2025-03-23 02:44:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:45:46 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-23 02:45:46 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-23 02:45:46 [info] index finished after resolve  [object Object] 
2025-03-23 02:45:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:54:47 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-23 02:54:47 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-23 02:54:47 [info] index finished after resolve  [object Object] 
2025-03-23 02:54:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 02:54:47 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-23 02:54:47 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-23 02:54:47 [info] index finished after resolve  [object Object] 
2025-03-23 02:54:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:41:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:41:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:41:40 [info] index finished after resolve  [object Object] 
2025-03-23 08:41:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:14 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:16 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:16 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:16 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:18 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:18 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:21 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:23 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:23 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:25 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:25 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:27 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:27 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:27 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:27 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:31 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:33 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:33 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:33 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:35 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:37 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:40 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:42 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:47 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:49 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:49 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:55 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:55 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:42:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:42:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:42:57 [info] index finished after resolve  [object Object] 
2025-03-23 08:42:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:00 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:02 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:05 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:07 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:09 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:09 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:43:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:43:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:43:15 [info] index finished after resolve  [object Object] 
2025-03-23 08:43:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:14 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:21 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:28 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:31 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:34 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:37 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:42 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:44 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:44 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:46 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:48 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:51 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:54 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:54 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:54 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:54 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:44:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:44:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:44:58 [info] index finished after resolve  [object Object] 
2025-03-23 08:44:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:02 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:04 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:04 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:04 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:04 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:11 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:15 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:19 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:22 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:22 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:24 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:24 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:24 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:24 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:35 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:37 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:39 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:39 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:42 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:45:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:45:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:45:58 [info] index finished after resolve  [object Object] 
2025-03-23 08:45:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:01 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:01 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:01 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:01 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:03 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:03 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:05 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:07 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:10 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:10 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:12 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:14 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:19 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:21 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:23 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:23 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:26 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:28 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:31 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:36 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:36 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:36 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:36 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:38 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:38 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:41 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:43 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:46 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:48 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:50 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:50 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:50 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:50 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:52 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:52 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:52 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:52 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:54 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:54 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:54 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:54 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:46:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:46:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:46:57 [info] index finished after resolve  [object Object] 
2025-03-23 08:46:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:47:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:47:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:47:03 [info] index finished after resolve  [object Object] 
2025-03-23 08:47:03 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:47:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:47:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:47:05 [info] index finished after resolve  [object Object] 
2025-03-23 08:47:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:47:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:47:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:47:48 [info] index finished after resolve  [object Object] 
2025-03-23 08:47:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:47:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:47:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:47:51 [info] index finished after resolve  [object Object] 
2025-03-23 08:47:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:35 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:37 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:39 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:39 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:41 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:48 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:51 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:53 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:56 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:49:59 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:49:59 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:49:59 [info] index finished after resolve  [object Object] 
2025-03-23 08:49:59 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:07 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:11 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:14 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:16 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:16 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:16 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:18 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:18 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:21 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:23 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:23 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:26 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:29 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:29 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:31 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:33 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:33 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:33 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:35 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:50:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:50:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:50:40 [info] index finished after resolve  [object Object] 
2025-03-23 08:50:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:53:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:53:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:53:42 [info] index finished after resolve  [object Object] 
2025-03-23 08:53:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:53:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:53:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:53:44 [info] index finished after resolve  [object Object] 
2025-03-23 08:53:44 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:53:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:53:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:53:46 [info] index finished after resolve  [object Object] 
2025-03-23 08:53:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:53:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:53:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:53:56 [info] index finished after resolve  [object Object] 
2025-03-23 08:53:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:21 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:28 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:30 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:32 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:32 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:34 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:36 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:37 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:39 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:39 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:41 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:45 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:47 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:49 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:49 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:54 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:54 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:54 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:54 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:57 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:54:59 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:54:59 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:54:59 [info] index finished after resolve  [object Object] 
2025-03-23 08:54:59 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:02 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:10 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:10 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:13 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:13 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:13 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:17 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:17 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:19 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:22 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:22 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:24 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:24 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:24 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:24 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:26 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:28 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:31 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:34 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:42 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:45 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:49 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:49 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:51 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:53 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:56 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:55:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:55:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:55:58 [info] index finished after resolve  [object Object] 
2025-03-23 08:55:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:00 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:02 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:06 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:07 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:26 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:28 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:41 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:43 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:47 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:53 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:56:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:56:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:56:57 [info] index finished after resolve  [object Object] 
2025-03-23 08:56:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 08:57:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 08:57:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 08:57:03 [info] index finished after resolve  [object Object] 
2025-03-23 08:57:03 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:04:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:04:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:04:30 [info] index finished after resolve  [object Object] 
2025-03-23 09:04:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:05:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:05:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:05:19 [info] index finished after resolve  [object Object] 
2025-03-23 09:05:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:18 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:18 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:20 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:20 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:20 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:22 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:22 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:25 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:25 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:27 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:27 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:27 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:27 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:29 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:29 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:31 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:34 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:37 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:41 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:46 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:48 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:50 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:50 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:50 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:50 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:53 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:08:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:08:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:08:55 [info] index finished after resolve  [object Object] 
2025-03-23 09:08:55 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:05 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:07 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:10 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:10 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:14 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:18 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:18 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:22 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:22 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:25 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:25 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:28 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:31 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:31 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:33 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:33 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:33 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:35 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:38 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:38 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:40 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:46 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:48 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:53 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:56 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:09:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:09:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:09:58 [info] index finished after resolve  [object Object] 
2025-03-23 09:09:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:38 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:38 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:40 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:42 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:45 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:47 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:13:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:13:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:13:49 [info] index finished after resolve  [object Object] 
2025-03-23 09:13:49 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:05 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:08 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:08 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:08 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:08 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:10 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:10 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:12 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:15 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:17 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:17 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:19 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:22 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:22 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:26 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:28 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:30 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:33 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:33 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:35 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:37 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:39 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:39 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:41 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:45 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:48 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:50 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:50 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:50 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:50 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:55 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:55 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:14:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:14:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:14:58 [info] index finished after resolve  [object Object] 
2025-03-23 09:14:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:15:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:15:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:15:00 [info] index finished after resolve  [object Object] 
2025-03-23 09:15:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:15:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:15:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:15:07 [info] index finished after resolve  [object Object] 
2025-03-23 09:15:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:09 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:09 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:11 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:14 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:16 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:16 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:16 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:18 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:18 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:26 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:29 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:29 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:35 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:37 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:40 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:42 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:42 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:44 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:44 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:46 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:48 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:48 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:48 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:48 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:51 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:53 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:55 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:55 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:19:59 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:19:59 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:19:59 [info] index finished after resolve  [object Object] 
2025-03-23 09:19:59 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:02 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:04 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:04 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:04 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:04 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:06 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:07 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:10 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:10 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:12 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:15 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:17 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:17 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:20 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:20 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:25 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:25 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:36 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:36 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:36 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:36 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:45 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:47 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:53 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:56 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:20:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:20:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:20:58 [info] index finished after resolve  [object Object] 
2025-03-23 09:20:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:00 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:03 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:03 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:05 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:08 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:08 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:08 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:08 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:11 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:21:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:21:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:21:14 [info] index finished after resolve  [object Object] 
2025-03-23 09:21:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:12 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:17 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:17 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:19 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:21 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:23 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:23 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:25 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:25 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:28 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:28 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:30 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:33 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:33 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:33 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:35 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:35 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:38 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:38 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:41 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:23:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:23:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:23:46 [info] index finished after resolve  [object Object] 
2025-03-23 09:23:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:27:06 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:27:06 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:27:06 [info] index finished after resolve  [object Object] 
2025-03-23 09:27:06 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:27:08 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:27:08 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:27:08 [info] index finished after resolve  [object Object] 
2025-03-23 09:27:08 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:27:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:27:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:27:11 [info] index finished after resolve  [object Object] 
2025-03-23 09:27:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:27:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:27:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:27:15 [info] index finished after resolve  [object Object] 
2025-03-23 09:27:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:27:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:27:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:27:19 [info] index finished after resolve  [object Object] 
2025-03-23 09:27:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:33:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:33:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:33:32 [info] index finished after resolve  [object Object] 
2025-03-23 09:33:32 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:33:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:33:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:33:37 [info] index finished after resolve  [object Object] 
2025-03-23 09:33:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:33:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:33:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:33:39 [info] index finished after resolve  [object Object] 
2025-03-23 09:33:39 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:33:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:33:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:33:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:33:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:33:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:33:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:33:45 [info] index finished after resolve  [object Object] 
2025-03-23 09:33:45 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:50:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:50:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:50:34 [info] index finished after resolve  [object Object] 
2025-03-23 09:50:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:50:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:50:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:50:57 [info] index finished after resolve  [object Object] 
2025-03-23 09:50:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:00 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:02 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:06 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:06 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:06 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:11 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:15 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:15 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:17 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:17 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:19 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:51:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:51:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:51:21 [info] index finished after resolve  [object Object] 
2025-03-23 09:51:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:26 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:29 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:29 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:32 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:32 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:34 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:36 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:36 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:36 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:36 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:38 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:38 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:41 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:41 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:43 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:43 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:46 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:49 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:49 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:53:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:53:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:53:58 [info] index finished after resolve  [object Object] 
2025-03-23 09:53:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:01 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:01 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:01 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:01 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:04 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:04 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:04 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:04 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:07 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:53 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:53 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:55 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:55 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:54:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:54:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:54:57 [info] index finished after resolve  [object Object] 
2025-03-23 09:54:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:02 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:02 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:04 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:04 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:04 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:04 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:07 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:09 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:09 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:11 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:11 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:14 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 09:55:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 09:55:16 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 09:55:16 [info] index finished after resolve  [object Object] 
2025-03-23 09:55:16 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:01:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:01:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:01:37 [info] index finished after resolve  [object Object] 
2025-03-23 10:01:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:01:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:01:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:01:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:01:40 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:01:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:01:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:01:57 [info] index finished after resolve  [object Object] 
2025-03-23 10:01:57 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:07 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:13 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:13 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:13 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:26 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:26 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:46 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:46 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:51 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:51 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:54 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:54 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:54 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:54 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:56 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:56 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:02:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:02:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:02:58 [info] index finished after resolve  [object Object] 
2025-03-23 10:02:58 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:03 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:03 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:05 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:12 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:30 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:32 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:32 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:34 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:34 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:03:36 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:03:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:03:37 [info] index finished after resolve  [object Object] 
2025-03-23 10:03:37 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:00 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:05 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:05 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:12 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:12 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:14 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:14 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:14 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:14 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:19 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:19 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:21 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:21 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:30 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:30 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:04:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:04:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:04:32 [info] index finished after resolve  [object Object] 
2025-03-23 10:04:32 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:05:24 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:05:24 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:05:24 [info] index finished after resolve  [object Object] 
2025-03-23 10:05:24 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:09:33 [info] indexing created file 学习库/c/attachments/运算符-2025-03-23-10-09-33.png  [object Object] 
2025-03-23 10:09:33 [info] refresh page data from created listeners 0 641   
2025-03-23 10:09:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:09:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:09:35 [info] index finished after resolve  [object Object] 
2025-03-23 10:09:35 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:09:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:09:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:09:44 [info] index finished after resolve  [object Object] 
2025-03-23 10:09:44 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:09:46 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:09:46 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:09:46 [info] index finished after resolve  [object Object] 
2025-03-23 10:09:46 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:03 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:03 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:05 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:05 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:07 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:09 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:09 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:27 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:27 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:27 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:27 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:29 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:29 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:43 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:43 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:10:44 [info] refresh page data from delete listeners 0 640   
2025-03-23 10:10:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:44 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:44 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:10:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:10:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:10:47 [info] index finished after resolve  [object Object] 
2025-03-23 10:10:47 [info] refresh page data from resolve listeners 0 640   
2025-03-23 10:13:39 [info] indexing created file 学习库/c/attachments/运算符-2025-03-23-10-13-39.png  [object Object] 
2025-03-23 10:13:39 [info] refresh page data from created listeners 0 641   
2025-03-23 10:13:41 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:13:41 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:13:41 [info] index finished after resolve  [object Object] 
2025-03-23 10:13:41 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:13:55 [info] indexing created file 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md  [object Object] 
2025-03-23 10:13:55 [info] indexing created ignore file 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:13:55 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:13:55 [info] index finished after resolve  [object Object] 
2025-03-23 10:13:55 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:13:56 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-23 10:13:56 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-23 10:13:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-23 10:13:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-23 10:13:56 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-23 10:14:12 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:14:12 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:14:12 [info] index finished after resolve  [object Object] 
2025-03-23 10:14:12 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:14:23 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:14:23 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:14:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:14:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:14:40 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:14:40 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:14:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:14:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:15:11 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:15:11 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:15:11 [info] index finished after resolve  [object Object] 
2025-03-23 10:15:11 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:15:30 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:15:30 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:15:30 [info] index finished after resolve  [object Object] 
2025-03-23 10:15:30 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:15:51 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:15:51 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:15:51 [info] index finished after resolve  [object Object] 
2025-03-23 10:15:51 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:16:08 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:16:08 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:16:08 [info] index finished after resolve  [object Object] 
2025-03-23 10:16:08 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:16:29 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:16:29 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:16:29 [info] index finished after resolve  [object Object] 
2025-03-23 10:16:29 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:16:47 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:16:47 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:16:47 [info] index finished after resolve  [object Object] 
2025-03-23 10:16:47 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:17:12 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:17:12 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:17:12 [info] index finished after resolve  [object Object] 
2025-03-23 10:17:12 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:17:34 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:17:34 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:17:34 [info] index finished after resolve  [object Object] 
2025-03-23 10:17:34 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:17:49 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:17:50 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:17:50 [info] index finished after resolve  [object Object] 
2025-03-23 10:17:50 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:18:08 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:18:08 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:18:08 [info] index finished after resolve  [object Object] 
2025-03-23 10:18:08 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:18:23 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:18:23 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:18:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:18:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:19:07 [info] ignore file modify evnet 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md   
2025-03-23 10:19:07 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:19:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:19:07 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:19:22 [info] refresh page data from delete listeners 0 641   
2025-03-23 10:19:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:19:22 [info] trigger 学习库/c/attachments/Drawing 2025-03-23 10.13.55.excalidraw.md resolve  [object Object] 
2025-03-23 10:19:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:19:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:19:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:19:23 [info] refresh page data from resolve listeners 0 641   
2025-03-23 10:19:25 [info] indexing created file 学习库/c/attachments/运算符-2025-03-23-10-19-25.png  [object Object] 
2025-03-23 10:19:25 [info] refresh page data from created listeners 0 642   
2025-03-23 10:19:27 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:19:27 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:19:27 [info] index finished after resolve  [object Object] 
2025-03-23 10:19:27 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:19:30 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-23 10:19:30 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-23 10:19:30 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-23 10:19:30 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-23 10:19:30 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-23 10:19:35 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-23 10:19:36 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-23 10:19:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-23 10:19:36 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-23 10:19:36 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-23 10:19:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:19:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:19:57 [info] index finished after resolve  [object Object] 
2025-03-23 10:19:57 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:27:48 [info] ignore file modify evnet 学习库/c/从这开始吧.md   
2025-03-23 10:27:48 [info] trigger 学习库/c/从这开始吧.md resolve  [object Object] 
2025-03-23 10:27:48 [info] index finished after resolve  [object Object] 
2025-03-23 10:27:48 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:32:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:32:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:32:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:32:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:32:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:32:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:32:43 [info] index finished after resolve  [object Object] 
2025-03-23 10:32:43 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:36:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:36:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:36:57 [info] index finished after resolve  [object Object] 
2025-03-23 10:36:57 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:00 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:03 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:03 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:03 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:03 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:11 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:11 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:13 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:13 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:17 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:17 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:37:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:37:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:37:28 [info] index finished after resolve  [object Object] 
2025-03-23 10:37:28 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:06 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:06 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:06 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:06 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:08 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:08 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:08 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:08 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:10 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:10 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:13 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:13 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:13 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:15 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:15 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:17 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:17 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:20 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:20 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:20 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:22 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:22 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:24 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:24 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:24 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:24 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:28 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:28 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:30 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:30 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:32 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:32 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:35 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:35 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:37 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:37 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:39 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:39 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:39 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:39 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:42 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:42 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:49 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:49 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:52 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:52 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:52 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:52 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:54 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:54 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:54 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:54 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:39:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:39:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:39:57 [info] index finished after resolve  [object Object] 
2025-03-23 10:39:57 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:00 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:02 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:02 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:05 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:05 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:07 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:13 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:13 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:13 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:15 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:15 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:19 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:19 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:19 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:19 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:21 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:21 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:21 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:21 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:24 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:24 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:24 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:24 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:30 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:30 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:30 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:30 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:32 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:32 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:34 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:34 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:38 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:38 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:42 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:42 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:47 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:47 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:47 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:47 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:49 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:49 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:51 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:51 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:56 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:56 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:40:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:40:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:40:58 [info] index finished after resolve  [object Object] 
2025-03-23 10:40:58 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:00 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:02 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:02 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:02 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:02 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:05 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:05 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:05 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:05 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:07 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:09 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:09 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:11 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:11 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:15 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:15 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:15 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:15 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:18 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:18 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:20 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:20 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:20 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:22 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:22 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:29 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:29 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:33 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:33 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:33 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:33 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:35 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:35 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:35 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:35 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:38 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:38 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:44 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:44 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:53 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:53 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:56 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:56 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:56 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:56 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:41:58 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:41:58 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:41:58 [info] index finished after resolve  [object Object] 
2025-03-23 10:41:58 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:17 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:17 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:25 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:25 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:25 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:25 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:32 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:32 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:32 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:32 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:34 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:34 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:34 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:34 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:38 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:38 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:38 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:38 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:42 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:42 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:42 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:42 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:42:44 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:42:44 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:42:44 [info] index finished after resolve  [object Object] 
2025-03-23 10:42:44 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:22 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:22 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:22 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:22 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:26 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:26 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:26 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:26 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:29 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:29 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:29 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:29 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:31 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:31 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:31 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:31 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:37 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:37 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:40 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:40 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:40 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:40 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:43 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:43 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:43 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:43 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:45 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:45 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:45 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:45 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:49 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:49 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:49 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:49 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:51 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:51 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:51 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:51 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:53 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:53 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:53 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:53 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:55 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:55 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:46:57 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:46:57 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:46:58 [info] index finished after resolve  [object Object] 
2025-03-23 10:46:58 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:01 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:01 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:01 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:01 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:07 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:07 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:07 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:07 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:09 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:09 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:09 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:09 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:11 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:11 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:11 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:11 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:13 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:13 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:13 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:13 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:16 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:16 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:16 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:16 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:18 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:18 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:18 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:18 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:20 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:20 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:20 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:47:28 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:47:28 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:47:28 [info] index finished after resolve  [object Object] 
2025-03-23 10:47:28 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:55:17 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:55:17 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:55:17 [info] index finished after resolve  [object Object] 
2025-03-23 10:55:17 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:55:20 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:55:20 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:55:20 [info] index finished after resolve  [object Object] 
2025-03-23 10:55:20 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:55:23 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:55:23 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:55:23 [info] index finished after resolve  [object Object] 
2025-03-23 10:55:23 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:58:55 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:58:55 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:58:55 [info] index finished after resolve  [object Object] 
2025-03-23 10:58:55 [info] refresh page data from resolve listeners 0 642   
2025-03-23 10:59:00 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 10:59:00 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 10:59:00 [info] index finished after resolve  [object Object] 
2025-03-23 10:59:00 [info] refresh page data from resolve listeners 0 642   
2025-03-23 11:04:10 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 11:04:10 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 11:04:10 [info] index finished after resolve  [object Object] 
2025-03-23 11:04:10 [info] refresh page data from resolve listeners 0 642   
2025-03-23 11:04:12 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-23 11:04:12 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-23 11:04:12 [info] index finished after resolve  [object Object] 
2025-03-23 11:04:12 [info] refresh page data from resolve listeners 0 642   
2025-03-23 19:17:24 [info] indexing created file 学习库/stm32/未命名.md  [object Object] 
2025-03-23 19:17:24 [info] indexing created ignore file 学习库/stm32/未命名.md   
2025-03-23 19:17:24 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-23 19:17:24 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-23 19:17:24 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-23 19:17:24 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-23 19:17:24 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-23 19:17:25 [info] trigger 学习库/stm32/未命名.md resolve  [object Object] 
2025-03-23 19:17:25 [info] index finished after resolve  [object Object] 
2025-03-23 19:17:25 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:17:39 [info] refresh page data from rename listeners 0 643   
2025-03-23 19:17:39 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-23 19:17:39 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-23 19:17:39 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-23 19:17:39 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-23 19:17:39 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-23 19:20:00 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:20:00 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:20:00 [info] index finished after resolve  [object Object] 
2025-03-23 19:20:00 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:20:02 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:20:02 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:20:02 [info] index finished after resolve  [object Object] 
2025-03-23 19:20:02 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:20:23 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:20:23 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:20:23 [info] index finished after resolve  [object Object] 
2025-03-23 19:20:23 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:20:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:20:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:20:26 [info] index finished after resolve  [object Object] 
2025-03-23 19:20:26 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:21:40 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:21:40 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:21:40 [info] index finished after resolve  [object Object] 
2025-03-23 19:21:40 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:11 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:11 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:11 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:11 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:13 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:13 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:13 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:13 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:18 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:18 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:18 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:18 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:22 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:22 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:22 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:22 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:25 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:25 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:25 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:25 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:28 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:28 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:28 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:28 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:30 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:30 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:22:32 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:22:32 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:22:32 [info] index finished after resolve  [object Object] 
2025-03-23 19:22:32 [info] refresh page data from resolve listeners 0 643   
2025-03-23 19:25:00 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-25-00.png  [object Object] 
2025-03-23 19:25:00 [info] refresh page data from created listeners 0 644   
2025-03-23 19:25:01 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:01 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:01 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:01 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:07 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:07 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:07 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:07 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:09 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:09 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:09 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:09 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:13 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:13 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:13 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:13 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:15 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:19 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:21 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:21 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:21 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:21 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:24 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:24 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:24 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:24 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:26 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:26 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:30 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:30 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:32 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:32 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:32 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:32 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:34 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:34 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:34 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:34 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:37 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:37 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:37 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:37 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:39 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:39 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:39 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:39 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:42 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:42 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:42 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:42 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:44 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:44 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:44 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:44 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:47 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:47 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:47 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:47 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:49 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:49 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:49 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:49 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:52 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:52 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:52 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:52 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:54 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:54 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:54 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:54 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:57 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:57 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:57 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:57 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:25:59 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:25:59 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:25:59 [info] index finished after resolve  [object Object] 
2025-03-23 19:25:59 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:26:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:26:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:26:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:26:19 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:19 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:21 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:21 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:21 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:21 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:24 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:24 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:24 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:24 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:30 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:30 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:36 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:36 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:36 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:36 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:54 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:54 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:54 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:54 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:56 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:56 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:56 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:56 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:56 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:56 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:56 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:56 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:57 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:57 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:57 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:57 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:27:59 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:27:59 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:27:59 [info] index finished after resolve  [object Object] 
2025-03-23 19:27:59 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:01 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:01 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:01 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:01 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:09 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:09 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:09 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:09 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:15 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:23 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:23 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:23 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:23 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:26 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:26 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:28 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:28 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:28 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:28 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:31 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:31 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:31 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:31 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:34 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:34 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:34 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:34 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:37 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:37 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:37 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:37 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:28:39 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:28:39 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:28:39 [info] index finished after resolve  [object Object] 
2025-03-23 19:28:39 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-23 19:29:29 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-23 19:29:29 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-23 19:29:29 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-23 19:29:29 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-23 19:29:34 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:34 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:34 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:34 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:38 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:38 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:38 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:38 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:40 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:40 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:40 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:40 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:42 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:42 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:42 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:42 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:45 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:45 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:45 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:45 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:51 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:51 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:51 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:51 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:29:54 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:29:54 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:29:54 [info] index finished after resolve  [object Object] 
2025-03-23 19:29:54 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:32:46 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:32:46 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:32:46 [info] index finished after resolve  [object Object] 
2025-03-23 19:32:46 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:32:49 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:32:49 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:32:49 [info] index finished after resolve  [object Object] 
2025-03-23 19:32:49 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:32:51 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:32:51 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:32:51 [info] index finished after resolve  [object Object] 
2025-03-23 19:32:51 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:33:05 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:33:05 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:33:05 [info] index finished after resolve  [object Object] 
2025-03-23 19:33:05 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:33:10 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:33:10 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:33:10 [info] index finished after resolve  [object Object] 
2025-03-23 19:33:10 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:33:18 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:33:18 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:33:18 [info] index finished after resolve  [object Object] 
2025-03-23 19:33:18 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:00 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:00 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:00 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:00 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:02 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:02 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:02 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:02 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:10 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:11 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:11 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:11 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:13 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:13 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:13 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:13 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:15 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:17 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:17 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:17 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:17 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:19 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:21 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:21 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:21 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:21 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:23 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:23 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:23 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:23 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:35:25 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:35:25 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:35:25 [info] index finished after resolve  [object Object] 
2025-03-23 19:35:25 [info] refresh page data from resolve listeners 0 644   
2025-03-23 19:36:24 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-36-24.png  [object Object] 
2025-03-23 19:36:24 [info] refresh page data from created listeners 0 645   
2025-03-23 19:36:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:26 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:26 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:34 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:34 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:34 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:34 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:36 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:36 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:36 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:36 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:38 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:38 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:38 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:38 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:41 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:41 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:41 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:41 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:43 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:43 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:43 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:43 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:45 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:45 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:45 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:45 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:47 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:47 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:47 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:47 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:50 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:50 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:50 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:50 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:52 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:52 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:52 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:52 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:56 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:56 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:56 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:56 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:36:59 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:36:59 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:36:59 [info] index finished after resolve  [object Object] 
2025-03-23 19:36:59 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:37:01 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:37:01 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:37:01 [info] index finished after resolve  [object Object] 
2025-03-23 19:37:01 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:37:12 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:37:12 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:37:12 [info] index finished after resolve  [object Object] 
2025-03-23 19:37:12 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:37:35 [info] trigger 学习库/stm32/attachments/启动-2025-03-23-19-36-24.png resolve  [object Object] 
2025-03-23 19:37:35 [info] index finished after resolve  [object Object] 
2025-03-23 19:37:35 [info] refresh page data from modify listeners 0 645   
2025-03-23 19:41:11 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:11 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:11 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:11 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:41:13 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:13 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:13 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:13 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:41:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:15 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:41:17 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:17 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:17 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:17 [info] refresh page data from resolve listeners 0 645   
2025-03-23 19:41:28 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-41-28.png  [object Object] 
2025-03-23 19:41:28 [info] refresh page data from created listeners 0 646   
2025-03-23 19:41:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:30 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:30 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:41:56 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:56 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:56 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:56 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:41:58 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:41:58 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:41:58 [info] index finished after resolve  [object Object] 
2025-03-23 19:41:58 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:01 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:42:01 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:42:01 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:01 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:03 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:42:03 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:42:03 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:03 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:05 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:42:05 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:42:05 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:05 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:07 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:42:07 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:42:07 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:07 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:10 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:42:10 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:42:10 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:10 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:42:33 [info] trigger 学习库/stm32/attachments/启动-2025-03-23-19-41-28.png resolve  [object Object] 
2025-03-23 19:42:33 [info] index finished after resolve  [object Object] 
2025-03-23 19:42:33 [info] refresh page data from modify listeners 0 646   
2025-03-23 19:43:04 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:04 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:04 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:04 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:08 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:08 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:08 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:08 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:10 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:10 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:10 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:10 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:12 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:12 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:12 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:12 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:15 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:19 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:21 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:21 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:21 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:21 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:24 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:24 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:24 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:24 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:27 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:27 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:27 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:27 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:29 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:29 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:29 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:29 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:31 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:32 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:32 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:32 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:34 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:34 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:34 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:34 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:36 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:36 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:36 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:36 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:39 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:39 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:39 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:39 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:40 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:41 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:41 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:41 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:43:58 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:43:58 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:43:58 [info] index finished after resolve  [object Object] 
2025-03-23 19:43:58 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:10 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:10 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:10 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:10 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:12 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:12 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:12 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:12 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:14 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:14 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:14 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:14 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:16 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:16 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:16 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:16 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:19 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:19 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:19 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:19 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:23 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:23 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:23 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:23 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:26 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:26 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:28 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:28 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:28 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:28 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:30 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:30 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:33 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:33 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:33 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:33 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:35 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:35 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:35 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:35 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:38 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:38 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:38 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:38 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:40 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:40 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:40 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:40 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:42 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:42 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:42 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:42 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:44 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:44 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:44 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:44 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:47 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:47 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:47 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:47 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:49 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:49 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:49 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:49 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:44:52 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:44:52 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:44:52 [info] index finished after resolve  [object Object] 
2025-03-23 19:44:52 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:45:02 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:45:02 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:45:02 [info] index finished after resolve  [object Object] 
2025-03-23 19:45:02 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:49:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:49:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:49:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:49:15 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:49:17 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:49:17 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:49:17 [info] index finished after resolve  [object Object] 
2025-03-23 19:49:17 [info] refresh page data from resolve listeners 0 646   
2025-03-23 19:51:13 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-51-13.png  [object Object] 
2025-03-23 19:51:13 [info] refresh page data from created listeners 0 647   
2025-03-23 19:51:15 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:51:15 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:51:15 [info] index finished after resolve  [object Object] 
2025-03-23 19:51:15 [info] refresh page data from resolve listeners 0 647   
2025-03-23 19:51:44 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:51:44 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:51:44 [info] index finished after resolve  [object Object] 
2025-03-23 19:51:44 [info] refresh page data from resolve listeners 0 647   
2025-03-23 19:51:48 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:51:48 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:51:48 [info] index finished after resolve  [object Object] 
2025-03-23 19:51:48 [info] refresh page data from resolve listeners 0 647   
2025-03-23 19:52:01 [info] indexing created file 学习库/stm32/attachments/启动-2025-03-23-19-52-01.png  [object Object] 
2025-03-23 19:52:01 [info] refresh page data from created listeners 0 648   
2025-03-23 19:52:03 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-23 19:52:03 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-23 19:52:03 [info] index finished after resolve  [object Object] 
2025-03-23 19:52:03 [info] refresh page data from resolve listeners 0 648   
