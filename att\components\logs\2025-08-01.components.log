2025-08-01 23:54:13.222 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:54:13.281 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:54:13.283 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:54:14.825 [info] components database created cost 7 ms   
2025-08-01 23:54:14.827 [info] components index initializing...   
2025-08-01 23:54:16.100 [info] indexing created file components/logs/2025-08-01.components.log  [object Object] 
2025-08-01 23:54:16.274 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-01 23:54:16.494 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:54:16.654 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:54:17.223 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-01 23:54:17.307 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-01 23:54:17.338 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-01 23:54:17.357 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-01 23:54:17.360 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-01 23:54:17.451 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-01 23:54:17.503 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-01 23:54:18.004 [info] start to batch put pages: 5   
2025-08-01 23:54:18.008 [info] batch persist cost 5  4 
2025-08-01 23:54:18.028 [info] components index initialized, 997 files cost 3210 ms   
2025-08-01 23:54:18.028 [info] refresh page data from init listeners 0 997   
2025-08-01 23:54:18.566 [debug] ignore file modify evnet 学习库/Deep learning/概念库/交叉验证.md   
2025-08-01 23:54:18.623 [info] trigger 学习库/Deep learning/概念库/交叉验证.md resolve  [object Object] 
2025-08-01 23:54:18.626 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:18.628 [info] refresh page data from resolve listeners 0 997   
2025-08-01 23:54:20.299 [info] indexing created file Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md  [object Object] 
2025-08-01 23:54:20.300 [info] indexing created ignore file Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md   
2025-08-01 23:54:20.362 [info] trigger Excalidraw/Drawing 2025-07-19 08.28.31.excalidraw.md resolve  [object Object] 
2025-08-01 23:54:20.364 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:20.366 [info] refresh page data from resolve listeners 0 998   
2025-08-01 23:54:20.736 [debug] ignore file modify evnet 工作库/项目/舌诊/tensorRT.md   
2025-08-01 23:54:20.755 [info] trigger 工作库/项目/舌诊/tensorRT.md resolve  [object Object] 
2025-08-01 23:54:20.756 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:20.758 [info] refresh page data from resolve listeners 0 998   
2025-08-01 23:54:21.117 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-08-01 23:54:21.126 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-08-01 23:54:21.127 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:21.129 [info] refresh page data from resolve listeners 0 998   
2025-08-01 23:54:21.578 [info] indexing created file 日记库/fleeting_notes/2025-07-19.md  [object Object] 
2025-08-01 23:54:21.578 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-19.md   
2025-08-01 23:54:21.603 [info] trigger 日记库/fleeting_notes/2025-07-19.md resolve  [object Object] 
2025-08-01 23:54:21.606 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:21.608 [info] refresh page data from resolve listeners 0 999   
2025-08-01 23:54:22.032 [info] indexing created file components/logs/2025-07-19.components.log  [object Object] 
2025-08-01 23:54:22.040 [info] refresh page data from created listeners 0 1000   
2025-08-01 23:54:22.428 [info] indexing created file 日记库/fleeting_notes/2025-07-18.md  [object Object] 
2025-08-01 23:54:22.428 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-18.md   
2025-08-01 23:54:22.440 [info] trigger 日记库/fleeting_notes/2025-07-18.md resolve  [object Object] 
2025-08-01 23:54:22.441 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:22.443 [info] refresh page data from resolve listeners 0 1001   
2025-08-01 23:54:23.142 [info] indexing created file components/logs/2025-07-18.components.log  [object Object] 
2025-08-01 23:54:23.149 [info] refresh page data from created listeners 0 1002   
2025-08-01 23:54:23.566 [info] indexing created file 折腾库/未命名.md  [object Object] 
2025-08-01 23:54:23.566 [info] indexing created ignore file 折腾库/未命名.md   
2025-08-01 23:54:23.576 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-01 23:54:23.586 [info] trigger 折腾库/未命名.md resolve  [object Object] 
2025-08-01 23:54:23.591 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:23.592 [info] refresh page data from resolve listeners 0 1003   
2025-08-01 23:54:24.476 [info] indexing created file components/logs/2025-07-20.components.log  [object Object] 
2025-08-01 23:54:24.482 [info] refresh page data from created listeners 0 1004   
2025-08-01 23:54:25.211 [info] indexing created file 工作库/项目/论文撰写/未命名.md  [object Object] 
2025-08-01 23:54:25.211 [info] indexing created ignore file 工作库/项目/论文撰写/未命名.md   
2025-08-01 23:54:25.226 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-01 23:54:25.248 [info] trigger 工作库/项目/论文撰写/未命名.md resolve  [object Object] 
2025-08-01 23:54:25.250 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:25.251 [info] refresh page data from resolve listeners 0 1005   
2025-08-01 23:54:27.024 [info] indexing created file Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md  [object Object] 
2025-08-01 23:54:27.025 [info] indexing created ignore file Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md   
2025-08-01 23:54:27.111 [info] trigger Excalidraw/Drawing 2025-07-19 11.20.54.excalidraw.md resolve  [object Object] 
2025-08-01 23:54:27.119 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:27.120 [info] refresh page data from resolve listeners 0 1006   
2025-08-01 23:54:28.156 [info] indexing created file components/logs/2025-07-21.components.log  [object Object] 
2025-08-01 23:54:28.162 [info] refresh page data from created listeners 0 1007   
2025-08-01 23:54:31.617 [info] indexing created file components/logs/2025-07-22.components.log  [object Object] 
2025-08-01 23:54:31.628 [info] refresh page data from created listeners 0 1008   
2025-08-01 23:54:39.138 [debug] ignore file modify evnet 学习库/python笔记/项目管理.md   
2025-08-01 23:54:39.209 [info] trigger 学习库/python笔记/项目管理.md resolve  [object Object] 
2025-08-01 23:54:39.212 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:39.214 [info] refresh page data from resolve listeners 0 1008   
2025-08-01 23:54:39.571 [debug] ignore file modify evnet 学习库/Anki/机器人学/未命名.md   
2025-08-01 23:54:39.632 [info] trigger 学习库/Anki/机器人学/未命名.md resolve  [object Object] 
2025-08-01 23:54:39.633 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:39.635 [info] refresh page data from resolve listeners 0 1008   
2025-08-01 23:54:46.727 [info] indexing created file components/logs/2025-07-23.components.log  [object Object] 
2025-08-01 23:54:46.777 [info] refresh page data from created listeners 0 1009   
2025-08-01 23:54:48.008 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-08-01 23:54:48.078 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-08-01 23:54:48.102 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:48.104 [info] refresh page data from resolve listeners 0 1009   
2025-08-01 23:54:48.508 [debug] ignore file modify evnet 学习库/软件使用/Total commander/使用手册.md   
2025-08-01 23:54:48.571 [info] trigger 学习库/软件使用/Total commander/使用手册.md resolve  [object Object] 
2025-08-01 23:54:48.573 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:48.575 [info] refresh page data from resolve listeners 0 1009   
2025-08-01 23:54:48.941 [info] indexing created file 学习库/软件使用/vscode/未命名.md  [object Object] 
2025-08-01 23:54:48.941 [info] indexing created ignore file 学习库/软件使用/vscode/未命名.md   
2025-08-01 23:54:48.985 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-01 23:54:49.011 [info] trigger 学习库/软件使用/vscode/未命名.md resolve  [object Object] 
2025-08-01 23:54:49.016 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:49.018 [info] refresh page data from resolve listeners 0 1010   
2025-08-01 23:54:49.481 [info] indexing created file components/logs/2025-07-24.components.log  [object Object] 
2025-08-01 23:54:49.529 [info] refresh page data from created listeners 0 1011   
2025-08-01 23:54:50.493 [info] indexing created file components/logs/2025-07-26.components.log  [object Object] 
2025-08-01 23:54:50.559 [info] refresh page data from created listeners 0 1012   
2025-08-01 23:54:53.402 [info] indexing created file 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md  [object Object] 
2025-08-01 23:54:53.403 [info] indexing created ignore file 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-08-01 23:54:53.507 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-08-01 23:54:53.512 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:53.513 [info] refresh page data from resolve listeners 0 1013   
2025-08-01 23:54:54.160 [info] indexing created file components/logs/2025-07-28.components.log  [object Object] 
2025-08-01 23:54:54.215 [info] refresh page data from created listeners 0 1014   
2025-08-01 23:54:54.321 [info] refresh page data from delete listeners 0 1013   
2025-08-01 23:54:55.064 [info] indexing created file 学习库/Artificial Intelligence/Prompt, Agent, MCP.md  [object Object] 
2025-08-01 23:54:55.065 [info] indexing created ignore file 学习库/Artificial Intelligence/Prompt, Agent, MCP.md   
2025-08-01 23:54:55.124 [info] trigger 学习库/Artificial Intelligence/Prompt, Agent, MCP.md resolve  [object Object] 
2025-08-01 23:54:55.130 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:55.132 [info] refresh page data from resolve listeners 0 1014   
2025-08-01 23:54:55.538 [info] indexing created file 学习库/Anki/Artificial Intelligence/未命名 1.md  [object Object] 
2025-08-01 23:54:55.538 [info] indexing created ignore file 学习库/Anki/Artificial Intelligence/未命名 1.md   
2025-08-01 23:54:55.652 [info] trigger 学习库/Anki/Artificial Intelligence/未命名 1.md resolve  [object Object] 
2025-08-01 23:54:55.654 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:55.656 [info] refresh page data from resolve listeners 0 1015   
2025-08-01 23:54:56.732 [info] indexing created file 学习库/Artificial Intelligence/mcp.md  [object Object] 
2025-08-01 23:54:56.733 [info] indexing created ignore file 学习库/Artificial Intelligence/mcp.md   
2025-08-01 23:54:56.796 [info] trigger 学习库/Artificial Intelligence/mcp.md resolve  [object Object] 
2025-08-01 23:54:56.800 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:56.801 [info] refresh page data from resolve listeners 0 1016   
2025-08-01 23:54:58.175 [info] indexing created file components/logs/2025-07-29.components.log  [object Object] 
2025-08-01 23:54:58.233 [info] refresh page data from created listeners 0 1017   
2025-08-01 23:54:59.306 [debug] ignore file modify evnet 工作库/项目/舌诊/人脸识别.md   
2025-08-01 23:54:59.374 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-08-01 23:54:59.377 [info] index finished after resolve  [object Object] 
2025-08-01 23:54:59.378 [info] refresh page data from resolve listeners 0 1017   
2025-08-01 23:55:08.717 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法（Gradient Descent Algorithm）-2025-07-31-21-15-34.png  [object Object] 
2025-08-01 23:55:08.767 [info] refresh page data from created listeners 0 1018   
2025-08-01 23:55:13.314 [info] indexing created file components/logs/2025-07-31.components.log  [object Object] 
2025-08-01 23:55:13.365 [info] refresh page data from created listeners 0 1019   
2025-08-01 23:55:13.841 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-08-01 23:55:13.842 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:13.844 [info] refresh page data from modify listeners 0 1019   
2025-08-01 23:55:15.457 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-01-08-45-13.png  [object Object] 
2025-08-01 23:55:15.509 [info] refresh page data from created listeners 0 1020   
2025-08-01 23:55:16.230 [debug] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-08-01 23:55:16.306 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-08-01 23:55:16.308 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:16.310 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 23:55:17.127 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-01 23:55:17.214 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 23:55:17.216 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:17.217 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 23:55:18.199 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-01 23:55:18.292 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-01 23:55:18.295 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:18.297 [info] refresh page data from resolve listeners 0 1020   
2025-08-01 23:55:20.564 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md  [object Object] 
2025-08-01 23:55:20.565 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md   
2025-08-01 23:55:20.869 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-07-31 17.48.31.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:20.914 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:20.916 [info] refresh page data from resolve listeners 0 1021   
2025-08-01 23:55:40.656 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型 2025-08-01 15.09.14.excalidraw.md  [object Object] 
2025-08-01 23:55:40.656 [info] indexing created ignore file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型 2025-08-01 15.09.14.excalidraw.md   
2025-08-01 23:55:40.757 [info] trigger 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型 2025-08-01 15.09.14.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:40.760 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:40.761 [info] refresh page data from resolve listeners 0 1022   
2025-08-01 23:55:41.144 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-08-01 23:55:41.199 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-08-01 23:55:41.201 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:41.202 [info] refresh page data from resolve listeners 0 1022   
2025-08-01 23:55:41.603 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/5. 逻辑回归（Logisitic Regression).md  [object Object] 
2025-08-01 23:55:41.603 [info] indexing created ignore file 学习库/Deep learning/pytorch/excalidraw/5. 逻辑回归（Logisitic Regression).md   
2025-08-01 23:55:41.664 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-01 23:55:41.667 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-08-01 23:55:41.693 [info] trigger 学习库/Deep learning/pytorch/excalidraw/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-01 23:55:41.702 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:41.704 [info] refresh page data from resolve listeners 0 1023   
2025-08-01 23:55:42.081 [info] indexing created file Excalidraw/Drawing 2025-08-01 15.45.47.excalidraw.md  [object Object] 
2025-08-01 23:55:42.082 [info] indexing created ignore file Excalidraw/Drawing 2025-08-01 15.45.47.excalidraw.md   
2025-08-01 23:55:42.144 [info] trigger Excalidraw/Drawing 2025-08-01 15.45.47.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:42.146 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:42.148 [info] refresh page data from resolve listeners 0 1024   
2025-08-01 23:55:42.552 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/1. 线性模型（Linear Model）.md  [object Object] 
2025-08-01 23:55:42.553 [info] indexing created ignore file 学习库/Deep learning/pytorch/excalidraw/1. 线性模型（Linear Model）.md   
2025-08-01 23:55:42.615 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-01 23:55:42.618 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-01 23:55:42.620 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-08-01 23:55:42.622 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-08-01 23:55:42.651 [info] trigger 学习库/Deep learning/pytorch/excalidraw/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 23:55:42.655 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:42.656 [info] refresh page data from resolve listeners 0 1025   
2025-08-01 23:55:50.557 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-08-01 16.41.28.excalidraw.md  [object Object] 
2025-08-01 23:55:50.557 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-08-01 16.41.28.excalidraw.md   
2025-08-01 23:55:50.630 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-08-01 16.41.28.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:50.632 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:50.634 [info] refresh page data from resolve listeners 0 1026   
2025-08-01 23:55:51.461 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-08-01 23:55:51.554 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:51.566 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:51.568 [info] refresh page data from resolve listeners 0 1026   
2025-08-01 23:55:56.088 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-01 23:55:56.170 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-01 23:55:56.173 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:56.175 [info] refresh page data from resolve listeners 0 1026   
2025-08-01 23:55:56.626 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）2025-08-01 20.51.12.excalidraw.md  [object Object] 
2025-08-01 23:55:56.626 [info] indexing created ignore file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）2025-08-01 20.51.12.excalidraw.md   
2025-08-01 23:55:56.719 [info] trigger 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）2025-08-01 20.51.12.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:56.729 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:56.731 [info] refresh page data from resolve listeners 0 1027   
2025-08-01 23:55:57.738 [info] indexing created file att/QuickAdd/scripts/creatExcalidraw.js  [object Object] 
2025-08-01 23:55:57.810 [info] refresh page data from created listeners 0 1028   
2025-08-01 23:55:58.328 [info] indexing created file 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md  [object Object] 
2025-08-01 23:55:58.328 [info] indexing created ignore file 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md   
2025-08-01 23:55:58.480 [info] trigger 学习库/Deep learning/pytorch/excalidraw/3. 反向传播（Back Propagation）.excalidraw.md resolve  [object Object] 
2025-08-01 23:55:58.483 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:58.485 [info] refresh page data from resolve listeners 0 1029   
2025-08-01 23:55:59.130 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-01 23:55:59.200 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-01 23:55:59.235 [info] index finished after resolve  [object Object] 
2025-08-01 23:55:59.236 [info] refresh page data from resolve listeners 0 1029   
2025-08-01 23:59:19.153 [info] components database created cost 123 ms   
2025-08-01 23:59:19.154 [info] components index initializing...   
2025-08-01 23:59:21.685 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-01 23:59:21.772 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-01 23:59:21.816 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:59:21.818 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-01 23:59:21.818 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-01 23:59:21.897 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-01 23:59:22.871 [info] start to batch put pages: 6   
2025-08-01 23:59:22.873 [info] batch persist cost 6  2 
2025-08-01 23:59:22.895 [info] components index initialized, 1029 files cost 3865 ms   
2025-08-01 23:59:22.895 [info] refresh page data from init listeners 0 1029   
