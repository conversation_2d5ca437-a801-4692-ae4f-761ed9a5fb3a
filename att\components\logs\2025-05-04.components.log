2025-05-04 08:47:08 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md  [object Object] 
2025-05-04 08:47:08 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md   
2025-05-04 08:47:08 [info] indexing created file components/logs/2025-05-04.components.log  [object Object] 
2025-05-04 08:47:08 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md resolve  [object Object] 
2025-05-04 08:47:08 [info] refresh page data from created listeners 0 869   
2025-05-04 08:47:08 [info] index finished after resolve  [object Object] 
2025-05-04 08:47:08 [info] refresh page data from resolve listeners 0 870   
2025-05-04 08:47:17 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md   
2025-05-04 08:47:17 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.08.excalidraw.md resolve  [object Object] 
2025-05-04 08:47:17 [info] index finished after resolve  [object Object] 
2025-05-04 08:47:17 [info] refresh page data from resolve listeners 0 870   
2025-05-04 08:47:20 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md  [object Object] 
2025-05-04 08:47:20 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md   
2025-05-04 08:47:20 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md resolve  [object Object] 
2025-05-04 08:47:20 [info] index finished after resolve  [object Object] 
2025-05-04 08:47:20 [info] refresh page data from resolve listeners 0 871   
2025-05-04 08:47:26 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md   
2025-05-04 08:47:26 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.20.excalidraw.md resolve  [object Object] 
2025-05-04 08:47:26 [info] index finished after resolve  [object Object] 
2025-05-04 08:47:26 [info] refresh page data from resolve listeners 0 871   
2025-05-04 08:47:31 [info] components database created cost 1 ms   
2025-05-04 08:47:31 [info] components index initializing...   
2025-05-04 08:47:32 [info] start to batch put pages: 8   
2025-05-04 08:47:33 [info] batch persist cost 8  1442 
2025-05-04 08:47:33 [info] components index initialized, 871 files cost 1661 ms   
2025-05-04 08:47:33 [info] refresh page data from init listeners 0 871   
2025-05-04 08:47:34 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-04 08:47:35 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-04 08:47:35 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-04 08:47:35 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-04 08:47:35 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-04 08:47:35 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-04 08:47:40 [info] indexing created file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md  [object Object] 
2025-05-04 08:47:40 [info] indexing created ignore file Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md   
2025-05-04 08:47:40 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md resolve  [object Object] 
2025-05-04 08:47:40 [info] index finished after resolve  [object Object] 
2025-05-04 08:47:40 [info] refresh page data from resolve listeners 0 872   
2025-05-04 08:48:12 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md   
2025-05-04 08:48:12 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md resolve  [object Object] 
2025-05-04 08:48:12 [info] index finished after resolve  [object Object] 
2025-05-04 08:48:12 [info] refresh page data from resolve listeners 0 872   
2025-05-04 08:48:47 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md   
2025-05-04 08:48:47 [info] trigger Excalidraw/Drawing 2025-05-04 08.47.40.excalidraw.md resolve  [object Object] 
2025-05-04 08:48:47 [info] index finished after resolve  [object Object] 
2025-05-04 08:48:47 [info] refresh page data from resolve listeners 0 872   
2025-05-04 09:20:55 [info] indexing created file 学习库/Deep learning/训练实践/未命名.md  [object Object] 
2025-05-04 09:20:55 [info] indexing created ignore file 学习库/Deep learning/训练实践/未命名.md   
2025-05-04 09:20:55 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-05-04 09:20:55 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-05-04 09:20:55 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-05-04 09:20:55 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-05-04 09:20:55 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-05-04 09:20:55 [info] trigger 学习库/Deep learning/训练实践/未命名.md resolve  [object Object] 
2025-05-04 09:20:55 [info] index finished after resolve  [object Object] 
2025-05-04 09:20:55 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:03 [info] refresh page data from rename listeners 0 873   
2025-05-04 09:21:03 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-05-04 09:21:03 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-05-04 09:21:03 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-05-04 09:21:03 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-05-04 09:21:03 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-05-04 09:21:12 [info] refresh page data from rename listeners 0 873   
2025-05-04 09:21:15 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:15 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:15 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:15 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:17 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:17 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:17 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:17 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:20 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:20 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:20 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:20 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:48 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:48 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:48 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:48 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:50 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:50 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:50 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:50 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:21:52 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:21:52 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:21:52 [info] index finished after resolve  [object Object] 
2025-05-04 09:21:52 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:22:04 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:04 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:04 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:04 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:22:06 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:06 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:06 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:06 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:22:08 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:08 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:08 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:08 [info] refresh page data from resolve listeners 0 873   
2025-05-04 09:22:21 [info] indexing created file 学习库/Deep learning/训练实践/attachments/Netron 可视化-2025-05-04-09-22-21.png  [object Object] 
2025-05-04 09:22:21 [info] refresh page data from created listeners 0 874   
2025-05-04 09:22:23 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:23 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:23 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:23 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:22:28 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:28 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:28 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:28 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:22:32 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:32 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:32 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:32 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:22:50 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:50 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:50 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:50 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:22:54 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:54 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:54 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:54 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:22:59 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:22:59 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:22:59 [info] index finished after resolve  [object Object] 
2025-05-04 09:22:59 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:13 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:13 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:13 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:13 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:16 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:16 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:16 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:16 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:28 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:28 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:28 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:28 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:30 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:30 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:30 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:30 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:34 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:34 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:34 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:34 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:36 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:36 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:36 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:36 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:38 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:38 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:38 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:38 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:42 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:42 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:42 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:42 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:45 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:45 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:45 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:45 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:47 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:47 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:47 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:47 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:23:58 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:23:58 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:23:58 [info] index finished after resolve  [object Object] 
2025-05-04 09:23:58 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:00 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:00 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:00 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:00 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:07 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:07 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:07 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:07 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:10 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:10 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:10 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:10 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:12 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:12 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:12 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:12 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:21 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:21 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:21 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:21 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:25 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:25 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:25 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:25 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:27 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:27 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:27 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:27 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:30 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:30 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:30 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:30 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:48 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:48 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:48 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:48 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:51 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:51 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:51 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:51 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:53 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:53 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:53 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:53 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:24:56 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:24:56 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:24:56 [info] index finished after resolve  [object Object] 
2025-05-04 09:24:56 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:09 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:09 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:09 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:09 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:11 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:11 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:11 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:11 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:14 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:14 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:14 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:14 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:17 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:17 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:17 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:17 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:20 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:20 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:20 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:20 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:22 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:22 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:22 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:22 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:27 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:27 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:27 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:27 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:31 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:31 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:31 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:31 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:33 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:33 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:33 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:33 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:36 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:36 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:36 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:36 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:39 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:39 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:39 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:39 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:48 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:48 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:48 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:48 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:51 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:51 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:51 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:51 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:54 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:54 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:54 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:54 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:56 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:56 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:56 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:56 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:25:59 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:25:59 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:25:59 [info] index finished after resolve  [object Object] 
2025-05-04 09:25:59 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:01 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:01 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:01 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:01 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:03 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:03 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:03 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:03 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:05 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:05 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:05 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:05 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:13 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:13 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:13 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:13 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:15 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:15 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:15 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:15 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:23 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:23 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:23 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:23 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:25 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:25 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:25 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:25 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:27 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:27 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:27 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:27 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:29 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:29 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:29 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:29 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:34 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:34 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:34 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:34 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:37 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:37 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:37 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:37 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:40 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:40 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:40 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:40 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:43 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:43 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:43 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:43 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:49 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:49 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:49 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:49 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:52 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:52 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:52 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:52 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:56 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:56 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:56 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:56 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:26:58 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:26:58 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:26:58 [info] index finished after resolve  [object Object] 
2025-05-04 09:26:58 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:05 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:05 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:05 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:05 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:08 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:08 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:08 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:08 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:11 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:11 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:11 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:11 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:14 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:14 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:14 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:14 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:17 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:17 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:17 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:17 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:19 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:19 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:19 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:19 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:23 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:23 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:23 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:23 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:25 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:25 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:25 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:25 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:27 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:27 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:27 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:27 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:29 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:29 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:29 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:29 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:31 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:31 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:31 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:31 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:41 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:41 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:41 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:41 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:48 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:48 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:48 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:48 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:51 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:51 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:51 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:51 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:54 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:54 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:54 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:54 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:27:57 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:27:57 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:27:57 [info] index finished after resolve  [object Object] 
2025-05-04 09:27:57 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:03 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:03 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:03 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:03 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:07 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:07 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:07 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:07 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:16 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:16 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:16 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:16 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:24 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:24 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:24 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:24 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:26 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:26 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:26 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:26 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:34 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:34 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:34 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:34 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:36 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:36 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:36 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:36 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:43 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:43 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:43 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:43 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:45 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:45 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:45 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:45 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:28:56 [info] ignore file modify evnet 学习库/Deep learning/训练实践/Netron 可视化.md   
2025-05-04 09:28:56 [info] trigger 学习库/Deep learning/训练实践/Netron 可视化.md resolve  [object Object] 
2025-05-04 09:28:56 [info] index finished after resolve  [object Object] 
2025-05-04 09:28:56 [info] refresh page data from resolve listeners 0 874   
2025-05-04 09:33:58 [info] trigger 学习库/Deep learning/pytorch/6. 处理多维特征的输入 resolve  [object Object] 
2025-05-04 09:33:58 [info] index finished after resolve  [object Object] 
2025-05-04 09:33:58 [info] refresh page data from modify listeners 0 874   
2025-05-04 09:33:58 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-05-04 09:33:58 [info] index finished after resolve  [object Object] 
2025-05-04 09:33:58 [info] refresh page data from modify listeners 0 874   
2025-05-04 09:33:58 [info] ignore file modify evnet 学习库/Deep learning/概念库/激活函数.md   
2025-05-04 09:33:58 [info] trigger 学习库/Deep learning/概念库/激活函数.md resolve  [object Object] 
2025-05-04 09:33:58 [info] index finished after resolve  [object Object] 
2025-05-04 09:33:58 [info] refresh page data from resolve listeners 0 874   
