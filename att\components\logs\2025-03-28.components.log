2025-03-28 08:05:33 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-28 08:05:33 [info] indexing created file components/logs/2025-03-28.components.log  [object Object] 
2025-03-28 08:05:33 [info] refresh page data from created listeners 0 678   
2025-03-28 08:06:15 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 08:06:15 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 08:06:15 [info] index finished after resolve  [object Object] 
2025-03-28 08:06:15 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:07:15 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-28 08:07:15 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-28 08:07:15 [info] index finished after resolve  [object Object] 
2025-03-28 08:07:15 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:07:16 [info] ignore file modify evnet 学习库/二级/数据库.md   
2025-03-28 08:07:16 [info] trigger 学习库/二级/数据库.md resolve  [object Object] 
2025-03-28 08:07:16 [info] index finished after resolve  [object Object] 
2025-03-28 08:07:16 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:07:16 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-28 08:07:16 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-28 08:07:16 [info] index finished after resolve  [object Object] 
2025-03-28 08:07:16 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:07:17 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-28 08:07:17 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-28 08:07:17 [info] index finished after resolve  [object Object] 
2025-03-28 08:07:17 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:08:15 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-28 08:08:15 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-28 08:08:15 [info] index finished after resolve  [object Object] 
2025-03-28 08:08:15 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:08:16 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-28 08:08:16 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-28 08:08:16 [info] index finished after resolve  [object Object] 
2025-03-28 08:08:16 [info] refresh page data from resolve listeners 0 678   
2025-03-28 08:39:29 [info] indexing created file 未命名 1.md  [object Object] 
2025-03-28 08:39:29 [info] indexing created ignore file 未命名 1.md   
2025-03-28 08:39:30 [info] trigger 未命名 1.md resolve  [object Object] 
2025-03-28 08:39:30 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:39:35 [info] refresh page data from rename listeners 0 679   
2025-03-28 08:39:38 [info] ignore file modify evnet 5 函数.md   
2025-03-28 08:39:38 [info] trigger 5 函数.md resolve  [object Object] 
2025-03-28 08:39:38 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:39:41 [info] ignore file modify evnet 5 函数.md   
2025-03-28 08:39:41 [info] trigger 5 函数.md resolve  [object Object] 
2025-03-28 08:39:41 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:39:42 [info] ignore file modify evnet 5 函数.md   
2025-03-28 08:39:42 [info] trigger 5 函数.md resolve  [object Object] 
2025-03-28 08:39:42 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:39:43 [info] ignore file modify evnet 5 函数.md   
2025-03-28 08:39:43 [info] trigger 5 函数.md resolve  [object Object] 
2025-03-28 08:39:43 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:39:44 [info] ignore file modify evnet 5 函数.md   
2025-03-28 08:39:44 [info] trigger 5 函数.md resolve  [object Object] 
2025-03-28 08:39:44 [info] index finished after resolve  [object Object] 
2025-03-28 08:39:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:41:37 [info] refresh page data from rename listeners 0 679   
2025-03-28 08:44:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:44:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:44:23 [info] index finished after resolve  [object Object] 
2025-03-28 08:44:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:44:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:44:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:44:25 [info] index finished after resolve  [object Object] 
2025-03-28 08:44:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:03 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:06 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:08 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:08 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:08 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:10 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:10 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:10 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:16 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:16 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:16 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:19 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:19 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:19 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:22 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:22 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:22 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:24 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:24 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:24 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:24 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:28 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:28 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:30 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:30 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:30 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:33 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:37 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:39 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:41 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:43 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:43 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:43 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:51 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:45:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:45:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:45:53 [info] index finished after resolve  [object Object] 
2025-03-28 08:45:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:46:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:46:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:46:26 [info] index finished after resolve  [object Object] 
2025-03-28 08:46:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:46:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:46:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:46:31 [info] index finished after resolve  [object Object] 
2025-03-28 08:46:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:48:36 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:48:36 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:48:36 [info] index finished after resolve  [object Object] 
2025-03-28 08:48:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:11 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:14 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:14 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:14 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:16 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:16 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:16 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:20 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:20 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:20 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:23 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:49:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:49:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:49:33 [info] index finished after resolve  [object Object] 
2025-03-28 08:49:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 08:58:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 08:58:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 08:58:27 [info] index finished after resolve  [object Object] 
2025-03-28 08:58:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:44 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:46 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:47 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:49 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:51 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:53 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:03:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:03:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:03:56 [info] index finished after resolve  [object Object] 
2025-03-28 09:03:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:03 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:05 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:07 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:07 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:07 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:13 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:19 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:19 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:19 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:21 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:21 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:21 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:23 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:28 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:28 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:30 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:30 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:30 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:32 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:32 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:32 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:34 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:36 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:36 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:36 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:39 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:42 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:44 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:06:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:06:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:06:53 [info] index finished after resolve  [object Object] 
2025-03-28 09:06:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:02 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:02 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:02 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:09 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:09 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:09 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:30 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:30 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:30 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:32 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:32 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:32 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:34 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:37 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:40 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:40 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:40 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:42 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:47 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:47 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:51 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:55 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:57 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:57 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:57 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:07:59 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:07:59 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:07:59 [info] index finished after resolve  [object Object] 
2025-03-28 09:07:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:01 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:01 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:01 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:03 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:06 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:08 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:08 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:08 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:13 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:19 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:19 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:19 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:24 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:24 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:24 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:24 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:27 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:29 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:31 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:34 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:37 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:41 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:43 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:43 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:43 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:45 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:45 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:45 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:47 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:48 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:50 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:50 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:50 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:53 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:55 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:08:58 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:08:58 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:08:58 [info] index finished after resolve  [object Object] 
2025-03-28 09:08:58 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:09:02 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:09:02 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:09:02 [info] index finished after resolve  [object Object] 
2025-03-28 09:09:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:09:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:09:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:09:05 [info] index finished after resolve  [object Object] 
2025-03-28 09:09:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:09:07 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:09:07 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:09:07 [info] index finished after resolve  [object Object] 
2025-03-28 09:09:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:09:09 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:09:09 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:09:09 [info] index finished after resolve  [object Object] 
2025-03-28 09:09:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:09:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:09:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:09:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:09:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:00 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:00 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:00 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:00 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:02 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:02 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:02 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:04 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:04 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:04 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:06 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:13 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:15 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:15 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:15 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:17 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:17 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:17 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:17 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:20 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:20 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:20 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:22 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:22 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:22 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:10:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:10:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:10:26 [info] index finished after resolve  [object Object] 
2025-03-28 09:10:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:25 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:27 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:29 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:34 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:36 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:36 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:36 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:49 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:54 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:16:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:16:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:16:56 [info] index finished after resolve  [object Object] 
2025-03-28 09:16:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:03 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:05 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:07 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:07 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:07 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:10 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:10 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:10 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:14 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:14 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:14 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:18 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:26 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:29 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:33 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:35 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:35 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:35 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:39 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:41 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:54 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:17:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:17:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:17:56 [info] index finished after resolve  [object Object] 
2025-03-28 09:17:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:00 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:00 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:00 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:00 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:03 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:07 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:07 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:07 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:12 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:12 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:12 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:15 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:15 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:15 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:21 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:21 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:21 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:25 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:27 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:18:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:18:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:18:34 [info] index finished after resolve  [object Object] 
2025-03-28 09:18:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:35:36 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:35:36 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:35:36 [info] index finished after resolve  [object Object] 
2025-03-28 09:35:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:35:38 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:35:38 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:35:38 [info] index finished after resolve  [object Object] 
2025-03-28 09:35:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:35:40 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:35:40 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:35:40 [info] index finished after resolve  [object Object] 
2025-03-28 09:35:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:35:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:35:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:35:42 [info] index finished after resolve  [object Object] 
2025-03-28 09:35:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:29 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:31 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:35 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:35 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:35 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:38 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:38 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:38 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:40 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:40 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:40 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:42 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:44 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:46 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:46 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:46 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:50 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:50 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:50 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:52 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:52 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:52 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:55 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:57 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:36:57 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:36:57 [info] index finished after resolve  [object Object] 
2025-03-28 09:36:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:36:59 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:37:00 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:37:00 [info] index finished after resolve  [object Object] 
2025-03-28 09:37:00 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:37:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:37:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:37:03 [info] index finished after resolve  [object Object] 
2025-03-28 09:37:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:37:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:37:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:37:05 [info] index finished after resolve  [object Object] 
2025-03-28 09:37:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:37:09 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:37:09 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:37:09 [info] index finished after resolve  [object Object] 
2025-03-28 09:37:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:37:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:37:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:37:11 [info] index finished after resolve  [object Object] 
2025-03-28 09:37:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:44:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:44:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:44:53 [info] index finished after resolve  [object Object] 
2025-03-28 09:44:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:44:58 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:44:58 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:44:58 [info] index finished after resolve  [object Object] 
2025-03-28 09:44:58 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:45:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:45:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:45:06 [info] index finished after resolve  [object Object] 
2025-03-28 09:45:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:45:12 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:45:12 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:45:12 [info] index finished after resolve  [object Object] 
2025-03-28 09:45:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:46:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:46:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:46:27 [info] index finished after resolve  [object Object] 
2025-03-28 09:46:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:46:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:46:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:46:29 [info] index finished after resolve  [object Object] 
2025-03-28 09:46:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:46:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:46:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:46:33 [info] index finished after resolve  [object Object] 
2025-03-28 09:46:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:19 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:19 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:19 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:21 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:21 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:21 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:26 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:43 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:43 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:43 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:49 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:48:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:48:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:48:56 [info] index finished after resolve  [object Object] 
2025-03-28 09:48:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:49:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:49:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:49:28 [info] index finished after resolve  [object Object] 
2025-03-28 09:49:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:53:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:53:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:53:23 [info] index finished after resolve  [object Object] 
2025-03-28 09:53:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 09:53:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 09:53:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 09:53:25 [info] index finished after resolve  [object Object] 
2025-03-28 09:53:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:12:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:12:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:12:27 [info] index finished after resolve  [object Object] 
2025-03-28 10:12:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:14:40 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:14:40 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:14:40 [info] index finished after resolve  [object Object] 
2025-03-28 10:14:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:14:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:14:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:14:44 [info] index finished after resolve  [object Object] 
2025-03-28 10:14:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:14:48 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:14:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:14:49 [info] index finished after resolve  [object Object] 
2025-03-28 10:14:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:14:57 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:14:57 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:14:57 [info] index finished after resolve  [object Object] 
2025-03-28 10:14:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:16:59 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:16:59 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:16:59 [info] index finished after resolve  [object Object] 
2025-03-28 10:16:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:04 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:04 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:04 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:06 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:09 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:09 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:09 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:25 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:27 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:29 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:31 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:33 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:37 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:40 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:42 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:44 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:46 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:46 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:46 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:48 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:48 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:48 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:51 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:17:57 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:17:57 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:17:57 [info] index finished after resolve  [object Object] 
2025-03-28 10:17:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:06 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:06 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:06 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:06 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:08 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:08 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:08 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:11 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:11 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:11 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:13 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:16 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:16 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:16 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:18 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:18:21 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:18:21 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:18:21 [info] index finished after resolve  [object Object] 
2025-03-28 10:18:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:10 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:10 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:10 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:13 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:15 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:15 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:15 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:18 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:20 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:20 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:20 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:22 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:22 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:22 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:24 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:24 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:24 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:24 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:26 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:28 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:28 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:32 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:32 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:32 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:34 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:37 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:41 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:34:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:34:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:34:53 [info] index finished after resolve  [object Object] 
2025-03-28 10:34:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:33 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:43 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:43 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:43 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:45 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:45 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:45 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:47 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:47 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:51 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:53 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:53 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:53 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:55 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:36:59 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:36:59 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:36:59 [info] index finished after resolve  [object Object] 
2025-03-28 10:36:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:02 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:02 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:02 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:04 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:04 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:04 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:12 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:12 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:12 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:19 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:19 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:19 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:21 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:21 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:21 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:23 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:25 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:28 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:28 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:30 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:30 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:30 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:32 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:32 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:32 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:37 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:44 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:46 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:46 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:46 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:52 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:52 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:52 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:37:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:37:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:37:55 [info] index finished after resolve  [object Object] 
2025-03-28 10:37:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:31 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:33 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:35 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:35 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:35 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:37 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:39 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:42 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:45 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:45 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:45 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:49 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:52 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:52 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:52 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:38:57 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:38:57 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:38:57 [info] index finished after resolve  [object Object] 
2025-03-28 10:38:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:39:04 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:39:04 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:39:04 [info] index finished after resolve  [object Object] 
2025-03-28 10:39:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:39:13 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:39:13 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:39:13 [info] index finished after resolve  [object Object] 
2025-03-28 10:39:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:41:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:41:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:41:49 [info] index finished after resolve  [object Object] 
2025-03-28 10:41:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:41:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:41:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:41:51 [info] index finished after resolve  [object Object] 
2025-03-28 10:41:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:41:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:41:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:41:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:41:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:23 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:28 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:28 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:28 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:30 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:30 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:30 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:32 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:32 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:32 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:37 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:37 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:37 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:40 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:40 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:40 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:42 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:42 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:42 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:45 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:45 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:45 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:47 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:47 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:51 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:51 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:51 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:55 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:55 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:55 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:42:59 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:42:59 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:42:59 [info] index finished after resolve  [object Object] 
2025-03-28 10:42:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:02 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:02 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:02 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:05 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:08 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:08 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:08 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:15 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:15 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:15 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:18 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:20 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:20 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:20 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:22 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:22 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:22 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:25 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:25 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:25 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:27 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:27 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:27 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:29 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:29 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:29 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:34 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:34 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:34 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:36 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:36 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:36 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:38 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:38 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:38 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:41 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:44 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:49 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:49 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:49 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:52 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:52 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:52 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:43:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:43:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:43:56 [info] index finished after resolve  [object Object] 
2025-03-28 10:43:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:01 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:01 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:01 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:03 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:03 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:03 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:05 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:05 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:05 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:16 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:16 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:16 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:18 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:18 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:18 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:20 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:20 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:20 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:23 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:23 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:23 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:26 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:26 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:26 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:31 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:31 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:31 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:33 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:33 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:33 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:39 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:39 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:39 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:41 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:41 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:41 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:44 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:47 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:47 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:47 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:50 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:50 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:50 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:52 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:52 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:52 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:44:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:44:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:44:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:44:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:47:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:47:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:47:56 [info] index finished after resolve  [object Object] 
2025-03-28 10:47:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:48:07 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:48:07 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:48:07 [info] index finished after resolve  [object Object] 
2025-03-28 10:48:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:57:54 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:57:54 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:57:54 [info] index finished after resolve  [object Object] 
2025-03-28 10:57:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 10:57:56 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 10:57:56 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 10:57:56 [info] index finished after resolve  [object Object] 
2025-03-28 10:57:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 11:01:44 [info] ignore file modify evnet 学习库/c/5 函数.md   
2025-03-28 11:01:44 [info] trigger 学习库/c/5 函数.md resolve  [object Object] 
2025-03-28 11:01:44 [info] index finished after resolve  [object Object] 
2025-03-28 11:01:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:55:56 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:55:57 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:55:57 [info] index finished after resolve  [object Object] 
2025-03-28 13:55:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:15 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:15 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:15 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:17 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:18 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:18 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:22 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:22 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:22 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:29 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:29 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:29 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:31 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:31 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:31 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:56:33 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:56:34 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:56:34 [info] index finished after resolve  [object Object] 
2025-03-28 13:56:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:01 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:02 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:02 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:04 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:05 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:05 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:07 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:08 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:08 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:10 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:10 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:10 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:19 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:20 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:20 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:22 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:23 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:23 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:42 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:42 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:42 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:46 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:46 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:46 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:49 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:50 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:50 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:52 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:52 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:52 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:54 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:54 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:54 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:56 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:56 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:56 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:57:59 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:57:59 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:57:59 [info] index finished after resolve  [object Object] 
2025-03-28 13:57:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:02 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:02 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:02 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:04 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:04 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:04 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:08 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:08 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:08 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:08 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:13 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:13 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:13 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:16 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:16 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:16 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:18 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:18 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:18 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:18 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:20 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:21 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:21 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:23 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:23 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:23 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:25 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:25 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:25 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:27 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:27 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:27 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:30 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:30 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:30 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:32 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:32 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:32 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:34 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:34 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:34 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:36 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:36 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:36 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:58:39 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:58:40 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:58:40 [info] index finished after resolve  [object Object] 
2025-03-28 13:58:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:07 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:07 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:07 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:25 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:25 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:25 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:27 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:27 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:27 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:39 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:39 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:39 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:41 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:41 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:41 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:48 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:48 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:48 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 13:59:50 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 13:59:50 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 13:59:50 [info] index finished after resolve  [object Object] 
2025-03-28 13:59:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:01 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:01 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:09 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:09 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:09 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:12 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:12 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:14 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:14 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:16 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:16 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:16 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:21 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:21 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:21 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:24 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:24 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:24 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:24 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:27 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:27 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:27 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:29 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:29 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:29 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:33 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:33 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:39 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:39 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:42 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:42 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:42 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:44 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:44 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:47 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:47 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:47 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:50 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:50 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:00:52 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:00:52 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:00:52 [info] index finished after resolve  [object Object] 
2025-03-28 14:00:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:02:13 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-28 14:02:13 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-28 14:02:13 [info] index finished after resolve  [object Object] 
2025-03-28 14:02:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:14 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:14 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:16 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:17 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:17 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:17 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:20 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:20 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:20 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:22 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:22 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:22 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:24 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:24 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:24 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:24 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:26 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:26 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:29 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:29 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:29 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:31 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:31 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:33 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:33 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:47 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:47 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:47 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:49 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:49 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:49 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:54 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:54 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:54 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:22:58 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:22:58 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:22:58 [info] index finished after resolve  [object Object] 
2025-03-28 14:22:58 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:00 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:00 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:00 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:00 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:02 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:02 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:02 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:05 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:05 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:05 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:15 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:15 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:15 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:39 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:39 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:41 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:41 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:41 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:43 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:43 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:43 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:45 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:45 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:45 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:23:51 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:23:51 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:23:51 [info] index finished after resolve  [object Object] 
2025-03-28 14:23:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:25 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:25 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:25 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:27 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:27 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:27 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:31 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:31 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:34 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:34 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:34 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:34 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:44 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:44 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:47 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:47 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:47 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:49 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:49 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:49 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:51 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:51 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:51 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:53 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:53 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:24:56 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:24:56 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:24:56 [info] index finished after resolve  [object Object] 
2025-03-28 14:24:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:02 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:02 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:02 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:02 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:04 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:04 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:04 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:04 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:10 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:10 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:10 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:15 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:15 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:15 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:19 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:19 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:19 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:27 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:27 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:27 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:32 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:32 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:32 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:32 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:37 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:37 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:37 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:39 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:39 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:42 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:42 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:42 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:47 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:47 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:47 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:47 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:53 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:53 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:56 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:56 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:56 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:25:58 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:25:58 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:25:58 [info] index finished after resolve  [object Object] 
2025-03-28 14:25:58 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:01 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:01 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:05 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:05 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:05 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:13 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:13 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:13 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:13 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:22 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:22 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:22 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:25 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:25 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:25 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:27 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:27 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:27 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:27 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:29 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:29 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:29 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:29 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:33 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:33 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:36 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:36 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:36 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:36 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:39 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:39 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:45 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:45 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:45 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:45 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:48 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:48 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:48 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:50 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:50 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:52 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:52 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:52 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:54 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:54 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:54 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:56 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:56 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:56 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:26:59 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:26:59 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:26:59 [info] index finished after resolve  [object Object] 
2025-03-28 14:26:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:01 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:01 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:03 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:03 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:03 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:11 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:11 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:11 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:11 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:14 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:14 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:16 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:16 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:16 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:16 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:19 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:19 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:19 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:19 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:21 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:21 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:21 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:25 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:25 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:25 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:31 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:31 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:38 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:38 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:38 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:40 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:40 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:40 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:43 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:43 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:43 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:46 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:46 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:48 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:48 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:48 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:51 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:51 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:51 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:53 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:53 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:53 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:55 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:55 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:55 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:27:58 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:27:58 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:27:58 [info] index finished after resolve  [object Object] 
2025-03-28 14:27:58 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:01 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:01 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:03 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:03 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:03 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:05 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:05 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:05 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:15 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:15 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:15 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:15 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:17 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:17 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:17 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:17 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:28 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:28 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:28 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:28 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:31 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:31 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:31 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:33 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:33 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:37 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:37 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:37 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:39 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:39 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:42 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:42 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:42 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:42 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:44 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:44 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:44 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:46 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:46 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:48 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:48 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:48 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:48 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:51 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:51 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:51 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:51 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:54 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:54 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:54 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:56 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:56 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:56 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:56 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:28:59 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:28:59 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:28:59 [info] index finished after resolve  [object Object] 
2025-03-28 14:28:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:03 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:03 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:03 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:05 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:05 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:05 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:10 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:10 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:10 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:14 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:14 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:17 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:17 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:17 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:17 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:20 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:20 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:20 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:20 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:22 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:22 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:22 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:22 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:25 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:25 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:25 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:25 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:33 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:33 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:37 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:37 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:37 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:37 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:40 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:40 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:40 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:43 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:43 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:43 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:46 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:46 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:52 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:52 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:52 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:55 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:55 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:55 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:55 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:29:57 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:29:57 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:29:57 [info] index finished after resolve  [object Object] 
2025-03-28 14:29:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:00 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:00 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:00 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:00 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:09 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:09 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:09 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:09 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:17 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:17 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:17 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:17 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:21 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:21 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:21 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:21 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:23 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:23 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:23 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:23 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:26 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:26 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:26 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:30 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:30 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:30 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:30 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:33 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:33 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:33 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:38 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:38 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:38 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:38 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:41 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:41 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:41 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:41 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:43 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:43 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:43 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:43 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:49 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:49 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:49 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:49 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:52 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:52 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:52 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:54 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:54 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:54 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:54 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:57 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:57 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:57 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:57 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:30:59 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:30:59 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:30:59 [info] index finished after resolve  [object Object] 
2025-03-28 14:30:59 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:01 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:01 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:03 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:03 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:03 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:03 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:07 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:07 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:07 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:10 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:10 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:10 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:12 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:12 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:12 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:14 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:14 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:14 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:35 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:35 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:35 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:40 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:40 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:40 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:40 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:46 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:46 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:46 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:50 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:50 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:50 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:31:52 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:31:52 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:31:52 [info] index finished after resolve  [object Object] 
2025-03-28 14:31:52 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:32:01 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:32:01 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:32:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:32:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:32:05 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:32:05 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:32:05 [info] index finished after resolve  [object Object] 
2025-03-28 14:32:05 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:32:10 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-28 14:32:10 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-28 14:32:10 [info] index finished after resolve  [object Object] 
2025-03-28 14:32:10 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:46:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:46:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:46:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:46:39 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:46:56 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-28-14-46-56.png  [object Object] 
2025-03-28 14:46:56 [info] refresh page data from created listeners 0 680   
2025-03-28 14:46:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:46:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:46:58 [info] index finished after resolve  [object Object] 
2025-03-28 14:46:58 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:31 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:31 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:31 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:33 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:36 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:36 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:48 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:48 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:51 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:51 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:53 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:56 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:56 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:48:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:48:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:48:58 [info] index finished after resolve  [object Object] 
2025-03-28 14:48:58 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:49:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:49:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:49:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:49:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:50:43 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-28 14:50:43 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-28 14:50:43 [info] index finished after resolve  [object Object] 
2025-03-28 14:50:43 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:50:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:50:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:50:49 [info] index finished after resolve  [object Object] 
2025-03-28 14:50:49 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:50:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:50:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:50:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:50:53 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:51:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:51:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:51:04 [info] index finished after resolve  [object Object] 
2025-03-28 14:51:04 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:51:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:51:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:51:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:51:26 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:51:31 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-03-28 14:51:31 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-03-28 14:51:31 [info] index finished after resolve  [object Object] 
2025-03-28 14:51:31 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:51:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:51:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:51:34 [info] index finished after resolve  [object Object] 
2025-03-28 14:51:34 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:54:59 [info] refresh page data from delete listeners 0 679   
2025-03-28 14:54:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:01 [info] refresh page data from resolve listeners 0 679   
2025-03-28 14:55:20 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-28-14-55-20.png  [object Object] 
2025-03-28 14:55:20 [info] refresh page data from created listeners 0 680   
2025-03-28 14:55:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:22 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:22 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:26 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:28 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:28 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:30 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:30 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:32 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:32 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:34 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:34 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:36 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:36 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:36 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:36 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:38 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:38 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:42 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:42 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:44 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:47 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:47 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:47 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:47 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:55:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:55:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:55:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:55:50 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:22 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:22 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:24 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:24 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:26 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:28 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:28 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:33 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:33 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:35 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:37 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:37 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:37 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:37 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:40 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:40 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:44 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:46 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:50 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:53 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:53 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:53 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:53 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:55 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:55 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:57 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:57 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:57 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:56:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:56:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:56:59 [info] index finished after resolve  [object Object] 
2025-03-28 14:56:59 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:01 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:03 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:03 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:07 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:07 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:07 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:07 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:12 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:12 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:14 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:14 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:16 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:16 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:16 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:16 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:18 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:18 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:20 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:20 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:23 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:23 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:26 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:26 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:28 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:28 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:28 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:28 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:30 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:30 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:32 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:32 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:32 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:32 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:35 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:35 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:39 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:39 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:41 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:41 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:41 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:41 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:44 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:44 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:46 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:46 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:48 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:48 [info] refresh page data from resolve listeners 0 680   
2025-03-28 14:57:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 14:57:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 14:57:50 [info] index finished after resolve  [object Object] 
2025-03-28 14:57:50 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:06:12 [info] components database created cost 41 ms   
2025-03-28 15:06:12 [info] components index initializing...   
2025-03-28 15:06:14 [info] start to batch put pages: 9   
2025-03-28 15:06:14 [info] batch persist cost 9  767 
2025-03-28 15:06:15 [info] components index initialized, 680 files cost 2626 ms   
2025-03-28 15:06:15 [info] refresh page data from init listeners 0 680   
2025-03-28 15:06:15 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 15:06:15 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 15:06:16 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-28 15:06:16 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-28 15:06:16 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-28 15:06:16 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-28 15:14:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:14:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:14:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:14:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:24:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:24:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:24:42 [info] index finished after resolve  [object Object] 
2025-03-28 15:24:42 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:24:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:24:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:24:59 [info] index finished after resolve  [object Object] 
2025-03-28 15:24:59 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:43:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:43:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:43:30 [info] index finished after resolve  [object Object] 
2025-03-28 15:43:30 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:46:19 [info] trigger 学习库/stm32/attachments/串口-2025-03-28-14-55-20.png resolve  [object Object] 
2025-03-28 15:46:19 [info] index finished after resolve  [object Object] 
2025-03-28 15:46:19 [info] refresh page data from modify listeners 0 680   
2025-03-28 15:46:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:46:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:46:52 [info] index finished after resolve  [object Object] 
2025-03-28 15:46:52 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:47:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:47:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:47:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:47:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:47:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:47:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:47:04 [info] index finished after resolve  [object Object] 
2025-03-28 15:47:04 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:47:11 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:47:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:47:12 [info] index finished after resolve  [object Object] 
2025-03-28 15:47:12 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:47:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:47:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:47:14 [info] index finished after resolve  [object Object] 
2025-03-28 15:47:14 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:47:58 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:47:58 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:47:58 [info] index finished after resolve  [object Object] 
2025-03-28 15:47:58 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:04 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:04 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:06 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:06 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:08 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:08 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:14 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:14 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:14 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:14 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:19 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:19 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:22 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:22 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:24 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:24 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:39 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:39 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:42 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:42 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:42 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:42 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:45 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:45 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:49 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:49 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:49 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:49 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:51 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:51 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:54 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:54 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:48:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:48:56 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:48:56 [info] index finished after resolve  [object Object] 
2025-03-28 15:48:56 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:03 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:03 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:03 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:03 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:13 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:13 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:13 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:13 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:16 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:16 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:16 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:16 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:19 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:19 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:19 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:19 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:23 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:23 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:23 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:23 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:34 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:34 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:34 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:34 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:39 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:39 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:45 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:45 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:52 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:52 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:52 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:52 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:54 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:54 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:54 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:54 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:49:56 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:49:57 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:49:57 [info] index finished after resolve  [object Object] 
2025-03-28 15:49:57 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:00 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:09 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:09 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:09 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:09 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:12 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:12 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:15 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:15 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:18 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:18 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:24 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:24 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:27 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:27 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:29 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:29 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:29 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:29 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:33 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:33 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:50:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:50:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:50:35 [info] index finished after resolve  [object Object] 
2025-03-28 15:50:35 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:38 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:38 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:40 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:40 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:43 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:43 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:43 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:43 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:45 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:45 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:45 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:45 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:48 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:48 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:48 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:48 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:51 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:51 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:51 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:51 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:54:59 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:54:59 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:54:59 [info] index finished after resolve  [object Object] 
2025-03-28 15:54:59 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:55:01 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:55:01 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:55:01 [info] index finished after resolve  [object Object] 
2025-03-28 15:55:01 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:55:04 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:55:04 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:55:04 [info] index finished after resolve  [object Object] 
2025-03-28 15:55:04 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:55:06 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:55:06 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:55:06 [info] index finished after resolve  [object Object] 
2025-03-28 15:55:06 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:55:08 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:55:08 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:55:08 [info] index finished after resolve  [object Object] 
2025-03-28 15:55:08 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:55:11 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:55:11 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:55:11 [info] index finished after resolve  [object Object] 
2025-03-28 15:55:11 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:58:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:58:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:58:20 [info] index finished after resolve  [object Object] 
2025-03-28 15:58:20 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:30 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:30 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:33 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:33 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:35 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:35 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:35 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:35 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:38 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:38 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:38 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:38 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:40 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:40 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:40 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:40 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:44 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:44 [info] refresh page data from resolve listeners 0 680   
2025-03-28 15:59:46 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 15:59:46 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 15:59:46 [info] index finished after resolve  [object Object] 
2025-03-28 15:59:46 [info] refresh page data from resolve listeners 0 680   
2025-03-28 16:02:33 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:02:33 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:02:33 [info] index finished after resolve  [object Object] 
2025-03-28 16:02:33 [info] refresh page data from resolve listeners 0 680   
2025-03-28 16:02:39 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:02:39 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:02:39 [info] index finished after resolve  [object Object] 
2025-03-28 16:02:39 [info] refresh page data from resolve listeners 0 680   
2025-03-28 16:03:12 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:12 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:12 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:12 [info] refresh page data from resolve listeners 0 680   
2025-03-28 16:03:12 [info] indexing created file 学习库/stm32/attachments/串口-2025-03-28-16-03-12.png  [object Object] 
2025-03-28 16:03:13 [info] refresh page data from created listeners 0 681   
2025-03-28 16:03:15 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:15 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:15 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:15 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:18 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:18 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:18 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:18 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:20 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:20 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:20 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:20 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:22 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:22 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:22 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:22 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:24 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:24 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:24 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:24 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:26 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:26 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:26 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:26 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:03:30 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 16:03:30 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 16:03:30 [info] index finished after resolve  [object Object] 
2025-03-28 16:03:30 [info] refresh page data from resolve listeners 0 681   
2025-03-28 16:24:36 [info] components database created cost 9 ms   
2025-03-28 16:24:36 [info] components index initializing...   
2025-03-28 16:24:36 [info] start to batch put pages: 5   
2025-03-28 16:24:38 [info] batch persist cost 5  1908 
2025-03-28 16:24:38 [info] components index initialized, 681 files cost 2290 ms   
2025-03-28 16:24:38 [info] refresh page data from init listeners 0 681   
2025-03-28 16:24:39 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 16:24:39 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 16:24:40 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-28 16:24:40 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-28 16:24:40 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-28 16:24:40 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-28 18:59:55 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-28 18:59:55 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-28 18:59:55 [info] index finished after resolve  [object Object] 
2025-03-28 18:59:55 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:12:42 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 19:12:43 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-28 19:12:43 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-28 19:12:43 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-28 19:12:43 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-28 19:13:00 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-28 19:13:00 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-28 19:13:00 [info] index finished after resolve  [object Object] 
2025-03-28 19:13:00 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:13:02 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-28 19:13:02 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-28 19:13:02 [info] index finished after resolve  [object Object] 
2025-03-28 19:13:02 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:13:08 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-28 19:13:08 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-28 19:13:08 [info] index finished after resolve  [object Object] 
2025-03-28 19:13:08 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:13:16 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-28 19:13:16 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-28 19:13:16 [info] index finished after resolve  [object Object] 
2025-03-28 19:13:16 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:13:19 [info] ignore file modify evnet 学习库/linux/orbslam.md   
2025-03-28 19:13:19 [info] trigger 学习库/linux/orbslam.md resolve  [object Object] 
2025-03-28 19:13:19 [info] index finished after resolve  [object Object] 
2025-03-28 19:13:19 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:15:41 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-28 19:15:41 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-28 19:15:41 [info] index finished after resolve  [object Object] 
2025-03-28 19:15:41 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:15:44 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-28 19:15:45 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-28 19:15:45 [info] index finished after resolve  [object Object] 
2025-03-28 19:15:45 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:15:50 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-28 19:15:50 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-28 19:15:50 [info] index finished after resolve  [object Object] 
2025-03-28 19:15:50 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:15:53 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md   
2025-03-28 19:15:53 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-03-28 19:15:53 [info] index finished after resolve  [object Object] 
2025-03-28 19:15:53 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:18:38 [info] ignore file modify evnet 学习库/linux/shell.md   
2025-03-28 19:18:38 [info] trigger 学习库/linux/shell.md resolve  [object Object] 
2025-03-28 19:18:38 [info] index finished after resolve  [object Object] 
2025-03-28 19:18:38 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:00 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:01 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:01 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:01 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:01 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:01 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:01 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:01 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:05 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:05 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:05 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:05 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:09 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:09 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:09 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:09 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:15 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:15 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:15 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:15 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:35 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:35 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:35 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:35 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:39 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:39 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:39 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:39 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:36:42 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:36:42 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:36:42 [info] index finished after resolve  [object Object] 
2025-03-28 19:36:42 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:06 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:06 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:06 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:06 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:13 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:13 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:13 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:13 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:20 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:20 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:20 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:20 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:22 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:22 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:22 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:22 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:31 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:31 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:31 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:31 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:34 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:34 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:34 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:34 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:45 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:45 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:45 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:45 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:37:49 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:37:49 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:37:49 [info] index finished after resolve  [object Object] 
2025-03-28 19:37:49 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:38:11 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:38:11 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:38:11 [info] index finished after resolve  [object Object] 
2025-03-28 19:38:11 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:38:52 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:38:52 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:38:52 [info] index finished after resolve  [object Object] 
2025-03-28 19:38:52 [info] refresh page data from resolve listeners 0 681   
2025-03-28 19:38:57 [info] ignore file modify evnet 学习库/Deep learning/Unet/Unet.md   
2025-03-28 19:38:57 [info] trigger 学习库/Deep learning/Unet/Unet.md resolve  [object Object] 
2025-03-28 19:38:57 [info] index finished after resolve  [object Object] 
2025-03-28 19:38:57 [info] refresh page data from resolve listeners 0 681   
2025-03-28 20:09:23 [info] indexing created file 学习库/c/未命名.md  [object Object] 
2025-03-28 20:09:23 [info] indexing created ignore file 学习库/c/未命名.md   
2025-03-28 20:09:23 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-28 20:09:23 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-28 20:09:23 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-28 20:09:23 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-28 20:09:23 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-28 20:09:23 [info] trigger 学习库/c/未命名.md resolve  [object Object] 
2025-03-28 20:09:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:09:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:09:27 [info] refresh page data from rename listeners 0 682   
2025-03-28 20:09:27 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-28 20:09:27 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-28 20:09:27 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-28 20:09:27 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-28 20:09:27 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-28 20:09:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:09:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:09:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:09:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:09:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:09:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:09:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:09:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:09:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:09:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:09:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:09:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:05 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:05 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:07 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:07 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:12 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:14 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:16 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:16 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:27 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:27 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:34 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:34 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:10:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:10:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:10:40 [info] index finished after resolve  [object Object] 
2025-03-28 20:10:40 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:16:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:16:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:16:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:16:48 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:16:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:16:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:16:50 [info] index finished after resolve  [object Object] 
2025-03-28 20:16:50 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:12 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:14 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:17 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:17 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:19 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:19 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:24 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:24 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:26 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:33 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:33 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:39 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:39 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:41 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:41 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:17:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:17:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:17:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:17:43 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:19 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:19 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:25 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:25 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:27 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:27 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:29 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:29 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:34 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:34 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:18:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:18:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:18:40 [info] index finished after resolve  [object Object] 
2025-03-28 20:18:40 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:41 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:41 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:43 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:47 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:47 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:50 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:50 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:52 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:52 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:54 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:54 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:24:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:24:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:24:56 [info] index finished after resolve  [object Object] 
2025-03-28 20:24:56 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:00 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:00 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:02 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:02 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:05 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:05 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:12 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:14 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:16 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:16 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:33 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:33 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:38 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:40 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:40 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:42 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:42 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:45 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:47 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:47 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:53 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:53 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:55 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:55 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:25:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:25:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:25:57 [info] index finished after resolve  [object Object] 
2025-03-28 20:25:57 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:00 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:00 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:02 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:02 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:04 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:04 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:06 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:06 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:08 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:08 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:11 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:11 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:13 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:13 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:15 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:20 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:20 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:25 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:25 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:26:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:26:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:26:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:26:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:11 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:11 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:13 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:13 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:15 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:20 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:20 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:22 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:22 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:24 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:24 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:33 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:33 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:38 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:53 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:53 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:56 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:56 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:27:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:27:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:27:59 [info] index finished after resolve  [object Object] 
2025-03-28 20:27:59 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:05 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:05 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:07 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:07 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:22 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:22 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:24 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:24 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:27 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:27 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:29 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:29 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:31 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:31 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:33 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:33 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:38 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:41 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:41 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:43 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:45 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:48 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:28:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:28:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:28:50 [info] index finished after resolve  [object Object] 
2025-03-28 20:28:50 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:30:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:30:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:30:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:30:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:30:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:30:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:30:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:30:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:30:37 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:30:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:30:37 [info] index finished after resolve  [object Object] 
2025-03-28 20:30:37 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:31:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:31:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:31:13 [info] index finished after resolve  [object Object] 
2025-03-28 20:31:13 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:31:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:31:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:31:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:31:15 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:11 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:11 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:14 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:45 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:32:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:32:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:32:49 [info] index finished after resolve  [object Object] 
2025-03-28 20:32:49 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:33:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:33:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:33:44 [info] index finished after resolve  [object Object] 
2025-03-28 20:33:44 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:33:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:33:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:33:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:33:46 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:11 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:11 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:17 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:17 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:19 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:19 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:27 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:27 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:30 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:37 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:37 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:37 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:39 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:39 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:43 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:45 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:48 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:53 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:53 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:34:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:34:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:34:57 [info] index finished after resolve  [object Object] 
2025-03-28 20:34:57 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:35:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:35:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:35:01 [info] index finished after resolve  [object Object] 
2025-03-28 20:35:01 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:35:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:35:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:35:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:35:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:35:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:35:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:35:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:35:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:35:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:35:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:35:24 [info] index finished after resolve  [object Object] 
2025-03-28 20:35:24 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:35:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:35:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:35:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:35:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:36:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:36:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:36:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:36:12 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:38:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:38:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:38:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:38:32 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:38:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:38:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:38:34 [info] index finished after resolve  [object Object] 
2025-03-28 20:38:34 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:38:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:38:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:38:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:38:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:11 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:11 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:15 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:17 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:17 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:36 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:36 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:39 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:39 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:41 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:41 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:43 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:46 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:49 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:49 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:51 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:51 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:54 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:54 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:41:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:41:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:41:57 [info] index finished after resolve  [object Object] 
2025-03-28 20:41:57 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:00 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:00 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:04 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:04 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:06 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:06 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:13 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:13 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:13 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:15 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:20 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:20 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:22 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:22 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:38 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:42 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:42 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:44 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:44 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:47 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:47 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:49 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:49 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:52 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:52 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:42:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:42:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:42:54 [info] index finished after resolve  [object Object] 
2025-03-28 20:42:54 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:21 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:21 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:25 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:25 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:44 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:44 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:46 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:48 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:50 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:50 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:52 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:52 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:56 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:56 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:44:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:44:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:44:58 [info] index finished after resolve  [object Object] 
2025-03-28 20:44:58 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:01 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:01 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:03 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:03 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:05 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:05 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:07 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:07 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:09 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:09 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:31 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:31 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:45:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:45:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:45:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:45:35 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:47:37 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:47:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:47:37 [info] index finished after resolve  [object Object] 
2025-03-28 20:47:37 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:47:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:47:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:47:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:47:46 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:47:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:47:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:47:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:47:48 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:01 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:01 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:04 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:04 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:06 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:06 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:08 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:08 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:10 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:10 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:12 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:14 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:16 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:16 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:18 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:18 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:23 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:26 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:28 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:44 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:44 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:49:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:49:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:49:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:49:46 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:50:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:00 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:00 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:50:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:03 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:03 [info] refresh page data from resolve listeners 0 682   
2025-03-28 20:50:04 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-28-20-50-04.png  [object Object] 
2025-03-28 20:50:04 [info] refresh page data from created listeners 0 683   
2025-03-28 20:50:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:06 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:06 [info] refresh page data from resolve listeners 0 683   
2025-03-28 20:50:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:43 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:43 [info] refresh page data from resolve listeners 0 683   
2025-03-28 20:50:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:45 [info] refresh page data from resolve listeners 0 683   
2025-03-28 20:50:48 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-28-20-50-48.png  [object Object] 
2025-03-28 20:50:48 [info] refresh page data from created listeners 0 684   
2025-03-28 20:50:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:49 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:49 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:50:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:56 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:56 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:50:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:50:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:50:59 [info] index finished after resolve  [object Object] 
2025-03-28 20:50:59 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:51:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:51:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:51:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:51:12 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:51:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:51:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:51:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:51:15 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:51:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:51:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:51:17 [info] index finished after resolve  [object Object] 
2025-03-28 20:51:17 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:51:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:51:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:51:25 [info] index finished after resolve  [object Object] 
2025-03-28 20:51:25 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:51:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:51:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:51:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:51:32 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:22 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:22 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:26 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:46 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:46 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:48 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:48 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:53 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:53 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:55 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:55 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:53:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:53:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:53:59 [info] index finished after resolve  [object Object] 
2025-03-28 20:53:59 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:02 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:02 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:05 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:05 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:07 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:07 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:12 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:14 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:14 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:23 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:26 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:28 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:28 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:30 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:30 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:38 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:40 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:40 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:42 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:42 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:45 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:45 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:49 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:49 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:54:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:54:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:54:59 [info] index finished after resolve  [object Object] 
2025-03-28 20:54:59 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:55:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:55:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:55:02 [info] index finished after resolve  [object Object] 
2025-03-28 20:55:02 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:00 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:00 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:08 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:08 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:12 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:12 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:15 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:15 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:19 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:19 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:23 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:23 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:27 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:27 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:29 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:29 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:56:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:56:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:56:32 [info] index finished after resolve  [object Object] 
2025-03-28 20:56:32 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:57:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:57:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:57:07 [info] index finished after resolve  [object Object] 
2025-03-28 20:57:07 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:57:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:57:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:57:10 [info] index finished after resolve  [object Object] 
2025-03-28 20:57:10 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:57:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:57:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:57:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:57:26 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:26 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:26 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:29 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:29 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:31 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:31 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:33 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:33 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:35 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:35 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:38 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:38 [info] refresh page data from resolve listeners 0 684   
2025-03-28 20:58:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 20:58:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 20:58:40 [info] index finished after resolve  [object Object] 
2025-03-28 20:58:40 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:00:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:00:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:00:41 [info] index finished after resolve  [object Object] 
2025-03-28 21:00:41 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:00:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:00:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:00:43 [info] index finished after resolve  [object Object] 
2025-03-28 21:00:43 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:00:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:00:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:00:45 [info] index finished after resolve  [object Object] 
2025-03-28 21:00:45 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:00:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:00:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:00:47 [info] index finished after resolve  [object Object] 
2025-03-28 21:00:47 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:10 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:10 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:15 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:15 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:18 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:18 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:20 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:20 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:23 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:23 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:28 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:28 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:32 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:32 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:34 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:34 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:36 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:36 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:38 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:38 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:41 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:41 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:43 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:43 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:46 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:46 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:48 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:48 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:52 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:52 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:54 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:54 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:56 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:56 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:01:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:01:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:01:58 [info] index finished after resolve  [object Object] 
2025-03-28 21:01:58 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:00 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:00 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:02 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:02 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:05 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:05 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:07 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:07 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:09 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:09 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:11 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:11 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:14 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:14 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:16 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:16 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:20 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:20 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:23 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:23 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:25 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:25 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:28 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:28 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:30 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:30 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:32 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:32 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:02:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:02:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:02:36 [info] index finished after resolve  [object Object] 
2025-03-28 21:02:36 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:04:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:04:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:04:20 [info] index finished after resolve  [object Object] 
2025-03-28 21:04:20 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:09 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:09 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:14 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:14 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:44 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:44 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:44 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:44 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:47 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:47 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:49 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:49 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:51 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:51 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:53 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:53 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:57 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:57 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:11:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:11:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:11:59 [info] index finished after resolve  [object Object] 
2025-03-28 21:11:59 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:01 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:01 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:03 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:03 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:25 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:25 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:27 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:27 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:29 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:29 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:31 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:31 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:12:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:12:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:12:34 [info] index finished after resolve  [object Object] 
2025-03-28 21:12:34 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:28 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:28 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:31 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:31 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:33 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:33 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:40 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:40 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:43 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:43 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:45 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:45 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:48 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:48 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:50 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:50 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:53 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:53 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:55 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:55 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:13:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:13:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:13:57 [info] index finished after resolve  [object Object] 
2025-03-28 21:13:57 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:22:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-28 21:22:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-28 21:22:56 [info] index finished after resolve  [object Object] 
2025-03-28 21:22:56 [info] refresh page data from resolve listeners 0 684   
2025-03-28 21:25:26 [info] components database created cost 4 ms   
2025-03-28 21:25:26 [info] components index initializing...   
2025-03-28 21:25:26 [info] start to batch put pages: 5   
2025-03-28 21:25:28 [info] batch persist cost 5  2265 
2025-03-28 21:25:28 [info] components index initialized, 684 files cost 2481 ms   
2025-03-28 21:25:28 [info] refresh page data from init listeners 0 684   
2025-03-28 21:25:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 21:25:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-28 21:25:30 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-28 21:25:30 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-28 21:25:30 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-28 21:25:30 [info] ignore file modify evnet Home/components/view/文件检索.components   
