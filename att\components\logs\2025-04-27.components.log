2025-04-27 09:23:34 [info] components database created cost 1 ms   
2025-04-27 09:23:34 [info] components index initializing...   
2025-04-27 09:23:34 [info] start to batch put pages: 5   
2025-04-27 09:23:34 [info] batch persist cost 5  3 
2025-04-27 09:23:35 [info] components index initialized, 835 files cost 1227 ms   
2025-04-27 09:23:35 [info] refresh page data from init listeners 0 835   
2025-04-27 09:23:37 [info] indexing created file components/logs/2025-04-27.components.log  [object Object] 
2025-04-27 09:23:37 [info] refresh page data from created listeners 0 836   
2025-04-27 09:23:38 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 09:23:38 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 09:23:38 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 09:23:38 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 09:23:38 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 09:23:38 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 09:24:03 [info] indexing created file 学习库/Deep learning/概念库/pytorch/未命名.md  [object Object] 
2025-04-27 09:24:03 [info] indexing created ignore file 学习库/Deep learning/概念库/pytorch/未命名.md   
2025-04-27 09:24:03 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-27 09:24:03 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-27 09:24:03 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-27 09:24:03 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-27 09:24:03 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-27 09:24:03 [info] trigger 学习库/Deep learning/概念库/pytorch/未命名.md resolve  [object Object] 
2025-04-27 09:24:03 [info] index finished after resolve  [object Object] 
2025-04-27 09:24:03 [info] refresh page data from resolve listeners 0 837   
2025-04-27 09:24:16 [info] refresh page data from rename listeners 0 837   
2025-04-27 09:24:16 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-27 09:24:16 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-27 09:24:16 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-27 09:24:16 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-27 09:24:16 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-27 09:24:20 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 09:24:20 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 09:24:20 [info] index finished after resolve  [object Object] 
2025-04-27 09:24:20 [info] refresh page data from resolve listeners 0 837   
2025-04-27 09:24:24 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 09:24:24 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 09:24:24 [info] index finished after resolve  [object Object] 
2025-04-27 09:24:24 [info] refresh page data from resolve listeners 0 837   
2025-04-27 09:24:29 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 09:24:29 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 09:24:29 [info] index finished after resolve  [object Object] 
2025-04-27 09:24:29 [info] refresh page data from resolve listeners 0 837   
2025-04-27 09:24:34 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 09:24:34 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 09:24:34 [info] index finished after resolve  [object Object] 
2025-04-27 09:24:34 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:11 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:11 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:11 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:11 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:16 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:16 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:16 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:16 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:22 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:22 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:22 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:22 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:24 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:24 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:24 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:24 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:27 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:27 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:27 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:27 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:29 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:29 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:29 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:29 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:37 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:37 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:37 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:37 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:39 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:39 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:39 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:39 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:41 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:41 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:41 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:41 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:43 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:43 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:43 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:43 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:46:45 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:46:45 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:46:45 [info] index finished after resolve  [object Object] 
2025-04-27 10:46:45 [info] refresh page data from resolve listeners 0 837   
2025-04-27 10:55:13 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 10:55:13 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 10:55:13 [info] index finished after resolve  [object Object] 
2025-04-27 10:55:13 [info] refresh page data from resolve listeners 0 837   
2025-04-27 11:07:06 [info] indexing created file 学习库/Deep learning/attachments/Lecture_02_Linear_Model.pdf  [object Object] 
2025-04-27 11:07:06 [info] refresh page data from created listeners 0 838   
2025-04-27 11:08:36 [info] refresh page data from rename listeners 0 838   
2025-04-27 11:09:49 [info] indexing created file 学习库/Deep learning/pytorch/attachments/Lecture_02_Linear_Model.pdf  [object Object] 
2025-04-27 11:09:49 [info] refresh page data from created listeners 0 839   
2025-04-27 11:09:49 [info] refresh page data from delete listeners 0 838   
2025-04-27 11:13:23 [info] indexing created file 学习库/Deep learning/概念库/pytorch/1. 线性模型.md  [object Object] 
2025-04-27 11:13:23 [info] indexing created ignore file 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 11:13:23 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:13:23 [info] index finished after resolve  [object Object] 
2025-04-27 11:13:23 [info] refresh page data from resolve listeners 0 839   
2025-04-27 11:14:03 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 11:14:03 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:14:03 [info] index finished after resolve  [object Object] 
2025-04-27 11:14:03 [info] refresh page data from resolve listeners 0 839   
2025-04-27 11:14:38 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 11:14:38 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:14:38 [info] index finished after resolve  [object Object] 
2025-04-27 11:14:38 [info] refresh page data from resolve listeners 0 839   
2025-04-27 11:15:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 11:15:04 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 11:15:04 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 11:15:04 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 11:15:04 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 11:15:05 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 11:15:05 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 11:15:05 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 11:15:05 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 11:15:05 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 11:15:28 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 11:15:28 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:15:28 [info] index finished after resolve  [object Object] 
2025-04-27 11:15:28 [info] refresh page data from resolve listeners 0 839   
2025-04-27 11:15:41 [info] ignore file modify evnet 学习库/Deep learning/概念库/pytorch/1. 线性模型.md   
2025-04-27 11:15:41 [info] trigger 学习库/Deep learning/概念库/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:15:41 [info] index finished after resolve  [object Object] 
2025-04-27 11:15:41 [info] refresh page data from resolve listeners 0 839   
2025-04-27 11:21:28 [info] refresh page data from delete listeners 0 838   
2025-04-27 11:21:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:21:45 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:21:45 [info] index finished after resolve  [object Object] 
2025-04-27 11:21:45 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:21:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:21:47 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:21:47 [info] index finished after resolve  [object Object] 
2025-04-27 11:21:47 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:21:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:21:49 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:21:49 [info] index finished after resolve  [object Object] 
2025-04-27 11:21:49 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:21:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:21:51 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:21:51 [info] index finished after resolve  [object Object] 
2025-04-27 11:21:51 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:21:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:21:55 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:21:55 [info] index finished after resolve  [object Object] 
2025-04-27 11:21:55 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:22:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:22:03 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:22:03 [info] index finished after resolve  [object Object] 
2025-04-27 11:22:03 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:22:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:22:07 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:22:07 [info] index finished after resolve  [object Object] 
2025-04-27 11:22:07 [info] refresh page data from resolve listeners 0 838   
2025-04-27 11:22:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 11:22:10 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 11:22:10 [info] index finished after resolve  [object Object] 
2025-04-27 11:22:10 [info] refresh page data from resolve listeners 0 838   
2025-04-27 14:52:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 14:52:25 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 14:52:25 [info] index finished after resolve  [object Object] 
2025-04-27 14:52:25 [info] refresh page data from resolve listeners 0 838   
2025-04-27 14:53:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 14:53:22 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 14:53:22 [info] index finished after resolve  [object Object] 
2025-04-27 14:53:22 [info] refresh page data from resolve listeners 0 838   
2025-04-27 15:02:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:02:35 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:02:35 [info] index finished after resolve  [object Object] 
2025-04-27 15:02:35 [info] refresh page data from resolve listeners 0 838   
2025-04-27 15:04:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:04:22 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:04:22 [info] index finished after resolve  [object Object] 
2025-04-27 15:04:22 [info] refresh page data from resolve listeners 0 838   
2025-04-27 15:04:26 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型-2025-04-27-15-04-26.png  [object Object] 
2025-04-27 15:04:26 [info] refresh page data from created listeners 0 839   
2025-04-27 15:04:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:04:28 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:04:28 [info] index finished after resolve  [object Object] 
2025-04-27 15:04:28 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:04:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:04:39 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:04:39 [info] index finished after resolve  [object Object] 
2025-04-27 15:04:39 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:04:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:04:52 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:04:52 [info] index finished after resolve  [object Object] 
2025-04-27 15:04:52 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:04:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:04:54 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:04:54 [info] index finished after resolve  [object Object] 
2025-04-27 15:04:54 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:08:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:08:14 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:08:14 [info] index finished after resolve  [object Object] 
2025-04-27 15:08:14 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:08:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:08:43 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:08:43 [info] index finished after resolve  [object Object] 
2025-04-27 15:08:43 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:08:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:08:46 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:08:46 [info] index finished after resolve  [object Object] 
2025-04-27 15:08:46 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:08:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:08:53 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:08:53 [info] index finished after resolve  [object Object] 
2025-04-27 15:08:53 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:09:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:09:00 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:09:00 [info] index finished after resolve  [object Object] 
2025-04-27 15:09:00 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:09:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:09:07 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:09:07 [info] index finished after resolve  [object Object] 
2025-04-27 15:09:07 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:09:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:09:40 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:09:40 [info] index finished after resolve  [object Object] 
2025-04-27 15:09:40 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:09:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:09:48 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:09:48 [info] index finished after resolve  [object Object] 
2025-04-27 15:09:48 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:19:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:19:05 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:19:05 [info] index finished after resolve  [object Object] 
2025-04-27 15:19:05 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:19:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:19:23 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:19:23 [info] index finished after resolve  [object Object] 
2025-04-27 15:19:23 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:19:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:19:32 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:19:32 [info] index finished after resolve  [object Object] 
2025-04-27 15:19:32 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:20:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:20:02 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:20:02 [info] index finished after resolve  [object Object] 
2025-04-27 15:20:02 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:20:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:20:09 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:20:09 [info] index finished after resolve  [object Object] 
2025-04-27 15:20:09 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:20:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:20:11 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:20:11 [info] index finished after resolve  [object Object] 
2025-04-27 15:20:11 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:21:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:21:34 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:21:34 [info] index finished after resolve  [object Object] 
2025-04-27 15:21:34 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:15 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:15 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:15 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:19 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:19 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:19 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:27 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:27 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:27 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:29 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:29 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:29 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:32 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:32 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:32 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:22:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:22:59 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:22:59 [info] index finished after resolve  [object Object] 
2025-04-27 15:22:59 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:25:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:25:05 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:25:05 [info] index finished after resolve  [object Object] 
2025-04-27 15:25:05 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:25:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:25:10 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:25:10 [info] index finished after resolve  [object Object] 
2025-04-27 15:25:10 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:25:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:25:12 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:25:12 [info] index finished after resolve  [object Object] 
2025-04-27 15:25:12 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:26:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型.md   
2025-04-27 15:26:27 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型.md resolve  [object Object] 
2025-04-27 15:26:27 [info] index finished after resolve  [object Object] 
2025-04-27 15:26:27 [info] refresh page data from resolve listeners 0 839   
2025-04-27 15:28:43 [info] refresh page data from rename listeners 0 839   
2025-04-27 15:28:47 [info] indexing created file 学习库/Deep learning/pytorch/attachments/未命名.md  [object Object] 
2025-04-27 15:28:47 [info] indexing created ignore file 学习库/Deep learning/pytorch/attachments/未命名.md   
2025-04-27 15:28:47 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-27 15:28:47 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-27 15:28:47 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-27 15:28:47 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-27 15:28:47 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-27 15:28:47 [info] trigger 学习库/Deep learning/pytorch/attachments/未命名.md resolve  [object Object] 
2025-04-27 15:28:47 [info] index finished after resolve  [object Object] 
2025-04-27 15:28:47 [info] refresh page data from resolve listeners 0 840   
2025-04-27 15:29:04 [info] refresh page data from rename listeners 0 840   
2025-04-27 15:29:04 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-27 15:29:04 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-27 15:29:04 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-27 15:29:04 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-27 15:29:04 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-27 15:29:10 [info] refresh page data from rename listeners 0 840   
2025-04-27 15:29:35 [info] refresh page data from rename listeners 0 840   
2025-04-27 15:29:47 [info] refresh page data from rename listeners 0 840   
2025-04-27 15:31:49 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_03_Gradient_Descent.pdf.crdownload  [object Object] 
2025-04-27 15:31:49 [info] refresh page data from created listeners 0 841   
2025-04-27 15:31:49 [info] indexing created file 学习库/Deep learning/pytorch/PDF/Lecture_03_Gradient_Descent.pdf  [object Object] 
2025-04-27 15:31:49 [info] refresh page data from created listeners 0 842   
2025-04-27 15:31:49 [info] trigger 学习库/Deep learning/pytorch/PDF/Lecture_03_Gradient_Descent.pdf resolve  [object Object] 
2025-04-27 15:31:49 [info] index finished after resolve  [object Object] 
2025-04-27 15:31:49 [info] refresh page data from modify listeners 0 842   
2025-04-27 15:31:49 [info] refresh page data from delete listeners 0 841   
2025-04-27 15:36:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（）.md   
2025-04-27 15:36:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（）.md resolve  [object Object] 
2025-04-27 15:36:43 [info] index finished after resolve  [object Object] 
2025-04-27 15:36:43 [info] refresh page data from resolve listeners 0 841   
2025-04-27 15:37:07 [info] indexing created file 学习库/Deep learning/pytorch/2. 梯度下降算法.md  [object Object] 
2025-04-27 15:37:07 [info] indexing created ignore file 学习库/Deep learning/pytorch/2. 梯度下降算法.md   
2025-04-27 15:37:07 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.md resolve  [object Object] 
2025-04-27 15:37:07 [info] index finished after resolve  [object Object] 
2025-04-27 15:37:07 [info] refresh page data from resolve listeners 0 842   
2025-04-27 15:37:08 [info] refresh page data from delete listeners 0 841   
2025-04-27 15:37:17 [info] indexing created file 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）md  [object Object] 
2025-04-27 15:37:17 [info] refresh page data from created listeners 0 842   
2025-04-27 15:37:18 [info] refresh page data from delete listeners 0 841   
2025-04-27 15:37:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）md resolve  [object Object] 
2025-04-27 15:37:19 [info] index finished after resolve  [object Object] 
2025-04-27 15:37:19 [info] refresh page data from modify listeners 0 841   
2025-04-27 16:33:13 [info] indexing created file 学习库/Deep learning/pytorch/Linear  [object Object] 
2025-04-27 16:33:13 [info] refresh page data from created listeners 0 842   
2025-04-27 16:33:24 [info] indexing created file 学习库/Deep learning/pytorch/LinearModel.py  [object Object] 
2025-04-27 16:33:24 [info] refresh page data from created listeners 0 843   
2025-04-27 16:33:25 [info] refresh page data from delete listeners 0 842   
2025-04-27 16:34:11 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-04-27 16:34:11 [info] index finished after resolve  [object Object] 
2025-04-27 16:34:11 [info] refresh page data from modify listeners 0 842   
2025-04-27 16:46:17 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）md resolve  [object Object] 
2025-04-27 16:46:17 [info] index finished after resolve  [object Object] 
2025-04-27 16:46:17 [info] refresh page data from modify listeners 0 842   
2025-04-27 16:48:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）md resolve  [object Object] 
2025-04-27 16:48:51 [info] index finished after resolve  [object Object] 
2025-04-27 16:48:51 [info] refresh page data from modify listeners 0 842   
2025-04-27 16:49:16 [info] components database created cost 3 ms   
2025-04-27 16:49:16 [info] components index initializing...   
2025-04-27 16:49:17 [info] start to batch put pages: 5   
2025-04-27 16:49:18 [info] batch persist cost 5  1278 
2025-04-27 16:49:18 [info] components index initialized, 842 files cost 1549 ms   
2025-04-27 16:49:18 [info] refresh page data from init listeners 0 842   
2025-04-27 16:49:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 16:49:20 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 16:49:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 16:49:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 16:49:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 16:49:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 16:49:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-27 16:49:35 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-27 16:49:35 [info] index finished after resolve  [object Object] 
2025-04-27 16:49:35 [info] refresh page data from resolve listeners 0 842   
2025-04-27 16:49:54 [info] indexing created file 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md  [object Object] 
2025-04-27 16:49:54 [info] indexing created ignore file 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:49:54 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:49:54 [info] index finished after resolve  [object Object] 
2025-04-27 16:49:54 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:49:55 [info] refresh page data from delete listeners 0 842   
2025-04-27 16:50:59 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md  [object Object] 
2025-04-27 16:50:59 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:50:59 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:50:59 [info] index finished after resolve  [object Object] 
2025-04-27 16:50:59 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:51:00 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 16:51:00 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 16:51:00 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 16:51:00 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 16:51:00 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 16:51:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:51:22 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:51:22 [info] index finished after resolve  [object Object] 
2025-04-27 16:51:22 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:51:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:51:32 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:51:32 [info] index finished after resolve  [object Object] 
2025-04-27 16:51:32 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:51:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:51:50 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:51:50 [info] index finished after resolve  [object Object] 
2025-04-27 16:51:50 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:52:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:52:08 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:52:08 [info] index finished after resolve  [object Object] 
2025-04-27 16:52:08 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:53:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:53:01 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:53:01 [info] index finished after resolve  [object Object] 
2025-04-27 16:53:01 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:53:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:53:20 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:53:20 [info] index finished after resolve  [object Object] 
2025-04-27 16:53:20 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:53:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:53:38 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:53:38 [info] index finished after resolve  [object Object] 
2025-04-27 16:53:38 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:54:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:54:16 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:54:16 [info] index finished after resolve  [object Object] 
2025-04-27 16:54:16 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:54:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:54:24 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:54:24 [info] index finished after resolve  [object Object] 
2025-04-27 16:54:24 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:54:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:54:26 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:54:26 [info] index finished after resolve  [object Object] 
2025-04-27 16:54:26 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:54:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:54:29 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:54:29 [info] index finished after resolve  [object Object] 
2025-04-27 16:54:29 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:55:09 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-16-55-09.png  [object Object] 
2025-04-27 16:55:09 [info] refresh page data from created listeners 0 844   
2025-04-27 16:55:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:11 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:11 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:11 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:19 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:19 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:31 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:31 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:31 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:36 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:36 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:38 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:38 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:38 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:40 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:40 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:42 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:42 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:42 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:55:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:55:45 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:55:45 [info] index finished after resolve  [object Object] 
2025-04-27 16:55:45 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:56:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:56:00 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:56:00 [info] index finished after resolve  [object Object] 
2025-04-27 16:56:00 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:56:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:56:32 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:56:32 [info] index finished after resolve  [object Object] 
2025-04-27 16:56:32 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:56:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:56:39 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:56:39 [info] index finished after resolve  [object Object] 
2025-04-27 16:56:39 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:56:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:56:45 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:56:45 [info] index finished after resolve  [object Object] 
2025-04-27 16:56:45 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:56:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-27 16:56:57 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-27 16:56:57 [info] index finished after resolve  [object Object] 
2025-04-27 16:56:57 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:57:16 [info] refresh page data from delete listeners 0 843   
2025-04-27 16:57:16 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:57:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:57:18 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:57:18 [info] index finished after resolve  [object Object] 
2025-04-27 16:57:18 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:57:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:57:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:57:20 [info] index finished after resolve  [object Object] 
2025-04-27 16:57:20 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:58:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:58:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:58:40 [info] index finished after resolve  [object Object] 
2025-04-27 16:58:40 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:58:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:58:59 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:58:59 [info] index finished after resolve  [object Object] 
2025-04-27 16:58:59 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:59:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:59:08 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:59:08 [info] index finished after resolve  [object Object] 
2025-04-27 16:59:08 [info] refresh page data from resolve listeners 0 843   
2025-04-27 16:59:13 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-16-59-13.png  [object Object] 
2025-04-27 16:59:13 [info] refresh page data from created listeners 0 844   
2025-04-27 16:59:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:59:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:59:15 [info] index finished after resolve  [object Object] 
2025-04-27 16:59:15 [info] refresh page data from resolve listeners 0 844   
2025-04-27 16:59:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 16:59:21 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 16:59:21 [info] index finished after resolve  [object Object] 
2025-04-27 16:59:21 [info] refresh page data from resolve listeners 0 844   
2025-04-27 17:00:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 17:00:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 17:00:36 [info] index finished after resolve  [object Object] 
2025-04-27 17:00:36 [info] refresh page data from resolve listeners 0 844   
2025-04-27 17:00:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 17:00:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 17:00:40 [info] index finished after resolve  [object Object] 
2025-04-27 17:00:40 [info] refresh page data from resolve listeners 0 844   
2025-04-27 17:00:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 17:00:45 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 17:00:45 [info] index finished after resolve  [object Object] 
2025-04-27 17:00:45 [info] refresh page data from resolve listeners 0 844   
2025-04-27 19:33:12 [info] ignore file modify evnet 学习库/python笔记/Numpy/2. 数组的创建.md   
2025-04-27 19:33:12 [info] trigger 学习库/python笔记/Numpy/2. 数组的创建.md resolve  [object Object] 
2025-04-27 19:33:12 [info] index finished after resolve  [object Object] 
2025-04-27 19:33:12 [info] refresh page data from resolve listeners 0 844   
2025-04-27 21:54:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 21:54:46 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 21:54:46 [info] index finished after resolve  [object Object] 
2025-04-27 21:54:46 [info] refresh page data from resolve listeners 0 844   
2025-04-27 21:56:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 21:56:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 21:56:20 [info] index finished after resolve  [object Object] 
2025-04-27 21:56:20 [info] refresh page data from resolve listeners 0 844   
2025-04-27 21:56:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 21:56:42 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 21:56:42 [info] index finished after resolve  [object Object] 
2025-04-27 21:56:42 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:00:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:00:48 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:00:48 [info] index finished after resolve  [object Object] 
2025-04-27 22:00:48 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:00:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:00:56 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:00:56 [info] index finished after resolve  [object Object] 
2025-04-27 22:00:56 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:09 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:09 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:09 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:15 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:15 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:18 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:18 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:18 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:20 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:20 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:32 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:32 [info] refresh page data from resolve listeners 0 844   
2025-04-27 22:01:56 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-01-56.png  [object Object] 
2025-04-27 22:01:56 [info] refresh page data from created listeners 0 845   
2025-04-27 22:01:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:01:58 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:01:58 [info] index finished after resolve  [object Object] 
2025-04-27 22:01:58 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:07 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:07 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:07 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:10 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:10 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:10 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:15 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:15 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:18 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:18 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:18 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:20 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:20 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:28 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:28 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:28 [info] refresh page data from resolve listeners 0 845   
2025-04-27 22:02:56 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-02-56.png  [object Object] 
2025-04-27 22:02:56 [info] refresh page data from created listeners 0 846   
2025-04-27 22:02:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:02:58 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:02:58 [info] index finished after resolve  [object Object] 
2025-04-27 22:02:58 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:03:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:03:05 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:03:05 [info] index finished after resolve  [object Object] 
2025-04-27 22:03:05 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:03:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:03:11 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:03:11 [info] index finished after resolve  [object Object] 
2025-04-27 22:03:11 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:07 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:07 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:07 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:15 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:15 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:15 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:17 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:17 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:17 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:19 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:19 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:22 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:22 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:22 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:34 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:34 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:34 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:36 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:36 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:38 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:38 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:38 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:41 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:41 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:41 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:43 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:43 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:46 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:46 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:46 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:48 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:48 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:48 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:54 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:54 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:54 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:07:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:07:59 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:07:59 [info] index finished after resolve  [object Object] 
2025-04-27 22:07:59 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:10 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:10 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:10 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:17 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:17 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:17 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:19 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:19 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:22 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:22 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:22 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:25 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:25 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:25 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:27 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:27 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:27 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:29 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:29 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:29 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:32 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:32 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:34 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:34 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:34 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:44 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:44 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:44 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:47 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:47 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:47 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:49 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:49 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:49 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:51 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:51 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:08:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:08:54 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:08:54 [info] index finished after resolve  [object Object] 
2025-04-27 22:08:54 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:02 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:02 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:02 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:04 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:04 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:04 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:06 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:06 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:06 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:08 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:08 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:08 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:11 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:11 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:11 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:13 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:13 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:13 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:16 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:16 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:16 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:21 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:21 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:21 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:23 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:23 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:23 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:26 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:26 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:26 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:32 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:32 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:36 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:36 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:38 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:38 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:38 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:40 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:40 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:42 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:42 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:42 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:48 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:48 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:48 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:09:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:09:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:09:51 [info] index finished after resolve  [object Object] 
2025-04-27 22:09:51 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:10:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:10:01 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:10:01 [info] index finished after resolve  [object Object] 
2025-04-27 22:10:01 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:10:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:10:03 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:10:03 [info] index finished after resolve  [object Object] 
2025-04-27 22:10:03 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:10:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:10:08 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:10:08 [info] index finished after resolve  [object Object] 
2025-04-27 22:10:08 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:10:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:10:10 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:10:10 [info] index finished after resolve  [object Object] 
2025-04-27 22:10:10 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:10:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:10:32 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:10:32 [info] index finished after resolve  [object Object] 
2025-04-27 22:10:32 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:13:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:13:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:13:40 [info] index finished after resolve  [object Object] 
2025-04-27 22:13:40 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:13:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:13:49 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:13:49 [info] index finished after resolve  [object Object] 
2025-04-27 22:13:49 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:13:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:13:58 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:13:58 [info] index finished after resolve  [object Object] 
2025-04-27 22:13:58 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:04 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:04 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:04 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:07 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:07 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:07 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:20 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:20 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:20 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:23 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:23 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:23 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:26 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:26 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:26 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:29 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:29 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:29 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:31 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:31 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:31 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:36 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:36 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:40 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:40 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:40 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:43 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:43 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:47 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:47 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:47 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:51 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:51 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:57 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:57 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:57 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:14:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:14:59 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:14:59 [info] index finished after resolve  [object Object] 
2025-04-27 22:14:59 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:06 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:06 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:06 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:09 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:09 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:09 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:11 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:11 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:11 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:14 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:14 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:14 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:16 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:16 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:16 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:19 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:19 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:45 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:45 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:45 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:51 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:51 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:15:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:15:56 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:15:56 [info] index finished after resolve  [object Object] 
2025-04-27 22:15:56 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:16:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:16:54 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:16:54 [info] index finished after resolve  [object Object] 
2025-04-27 22:16:54 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:16:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:16:57 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:16:57 [info] index finished after resolve  [object Object] 
2025-04-27 22:16:57 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:17:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:00 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:00 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:00 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:17:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:02 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:02 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:02 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:17:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:04 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:04 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:04 [info] refresh page data from resolve listeners 0 846   
2025-04-27 22:17:12 [info] indexing created file 学习库/Deep learning/pytorch/attachments/2. 梯度下降算法.（Gradient Descent Algorithm）-2025-04-27-22-17-12.png  [object Object] 
2025-04-27 22:17:12 [info] refresh page data from created listeners 0 847   
2025-04-27 22:17:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:14 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:14 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:14 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:17:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:19 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:19 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:17:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 22:17:26 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 22:17:26 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 22:17:26 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 22:17:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 22:17:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-27 22:17:27 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-27 22:17:27 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-27 22:17:27 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-27 22:17:27 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-27 22:17:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:51 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:51 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:51 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:17:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:17:56 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:17:56 [info] index finished after resolve  [object Object] 
2025-04-27 22:17:56 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:00 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:00 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:00 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:21 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:21 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:21 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:23 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:23 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:23 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:27 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:27 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:27 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:29 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:29 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:29 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:18:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:18:31 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:18:31 [info] index finished after resolve  [object Object] 
2025-04-27 22:18:31 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:28:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:28:19 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:28:19 [info] index finished after resolve  [object Object] 
2025-04-27 22:28:19 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:30:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:30:13 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:30:13 [info] index finished after resolve  [object Object] 
2025-04-27 22:30:13 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:30:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:30:29 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:30:29 [info] index finished after resolve  [object Object] 
2025-04-27 22:30:29 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:30:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:30:36 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:30:36 [info] index finished after resolve  [object Object] 
2025-04-27 22:30:36 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:31:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:31:30 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:31:30 [info] index finished after resolve  [object Object] 
2025-04-27 22:31:30 [info] refresh page data from resolve listeners 0 847   
2025-04-27 22:31:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md   
2025-04-27 22:31:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法.（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-27 22:31:43 [info] index finished after resolve  [object Object] 
2025-04-27 22:31:43 [info] refresh page data from resolve listeners 0 847   
