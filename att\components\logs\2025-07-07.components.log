2025-07-07 23:26:59.649 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-07-07 23:26:59.650 [info] components database created cost 2 ms   
2025-07-07 23:26:59.651 [info] components index initializing...   
2025-07-07 23:26:59.848 [info] start to batch put pages: 5   
2025-07-07 23:26:59.851 [info] batch persist cost 5  3 
2025-07-07 23:26:59.908 [info] components index initialized, 939 files cost 260 ms   
2025-07-07 23:26:59.908 [info] refresh page data from init listeners 0 939   
2025-07-07 23:27:00.899 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-07 23:27:01.380 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-07 23:27:01.788 [info] indexing created file components/logs/2025-07-07.components.log  [object Object] 
2025-07-07 23:27:02.096 [info] refresh page data from created listeners 0 940   
2025-07-07 23:27:02.490 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-07 23:27:02.510 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-07 23:27:02.527 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-07 23:27:02.560 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-07 23:27:05.650 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-07 23:27:05.695 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-07 23:27:05.696 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:05.698 [info] refresh page data from resolve listeners 0 940   
2025-07-07 23:27:06.885 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md   
2025-07-07 23:27:06.915 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-07-07 23:27:06.919 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:06.921 [info] refresh page data from resolve listeners 0 940   
2025-07-07 23:27:08.531 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-21-37.png  [object Object] 
2025-07-07 23:27:08.538 [info] refresh page data from created listeners 0 941   
2025-07-07 23:27:10.933 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-41-38.png  [object Object] 
2025-07-07 23:27:10.937 [info] refresh page data from created listeners 0 942   
2025-07-07 23:27:12.427 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-09-46-01.png  [object Object] 
2025-07-07 23:27:12.430 [info] refresh page data from created listeners 0 943   
2025-07-07 23:27:13.683 [info] trigger 学习库/Deep learning/概念库/attachments/Pasted image 20240730110336.png resolve  [object Object] 
2025-07-07 23:27:13.685 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:13.686 [info] refresh page data from modify listeners 0 943   
2025-07-07 23:27:16.304 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-01-26.png  [object Object] 
2025-07-07 23:27:16.312 [info] refresh page data from created listeners 0 944   
2025-07-07 23:27:17.889 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络-2025-07-07-10-09-21.png  [object Object] 
2025-07-07 23:27:17.893 [info] refresh page data from created listeners 0 945   
2025-07-07 23:27:19.208 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-25-06.png  [object Object] 
2025-07-07 23:27:19.210 [info] refresh page data from created listeners 0 946   
2025-07-07 23:27:20.710 [info] indexing created file 学习库/Deep learning/概念库/attachments/卷积和转置卷积-2025-07-07-10-28-53.png  [object Object] 
2025-07-07 23:27:20.716 [info] refresh page data from created listeners 0 947   
2025-07-07 23:27:22.525 [debug] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-07-07 23:27:22.633 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-07 23:27:22.642 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:22.657 [info] refresh page data from resolve listeners 0 947   
2025-07-07 23:27:23.082 [debug] ignore file modify evnet 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md   
2025-07-07 23:27:23.148 [info] trigger 学习库/Deep learning/概念库/池化层/池化层（pooling layers).md resolve  [object Object] 
2025-07-07 23:27:23.163 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:23.177 [info] refresh page data from resolve listeners 0 947   
2025-07-07 23:27:24.569 [debug] ignore file modify evnet 学习库/Deep learning/概念库/上采样；下采样.md   
2025-07-07 23:27:24.651 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-07 23:27:24.661 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:24.672 [info] refresh page data from resolve listeners 0 947   
2025-07-07 23:27:25.053 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-07-07 23:27:25.060 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:25.070 [info] refresh page data from modify listeners 0 947   
2025-07-07 23:27:26.549 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-16-36.png  [object Object] 
2025-07-07 23:27:26.562 [info] refresh page data from created listeners 0 948   
2025-07-07 23:27:28.453 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-21-40.png  [object Object] 
2025-07-07 23:27:28.472 [info] refresh page data from created listeners 0 949   
2025-07-07 23:27:30.631 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-16-48-37.png  [object Object] 
2025-07-07 23:27:30.650 [info] refresh page data from created listeners 0 950   
2025-07-07 23:27:33.839 [info] indexing created file 学习库/Deep learning/pytorch/attachments/9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-17-18-19.png  [object Object] 
2025-07-07 23:27:33.861 [info] refresh page data from created listeners 0 951   
2025-07-07 23:27:34.378 [info] indexing created file 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md  [object Object] 
2025-07-07 23:27:34.379 [info] indexing created ignore file 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-07-07 23:27:34.542 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-07-07 23:27:34.551 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:34.564 [info] refresh page data from resolve listeners 0 952   
2025-07-07 23:27:35.498 [info] indexing created file 学习库/Latex/Latex 从入门到如土.md  [object Object] 
2025-07-07 23:27:35.499 [info] indexing created ignore file 学习库/Latex/Latex 从入门到如土.md   
2025-07-07 23:27:35.572 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-07 23:27:35.577 [info] index finished after resolve  [object Object] 
2025-07-07 23:27:35.591 [info] refresh page data from resolve listeners 0 953   
2025-07-07 23:31:54.260 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-07 23:31:54.423 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-07 23:31:54.551 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-07 23:31:54.787 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-07 23:31:54.791 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-07 23:31:54.876 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-07 23:31:55.129 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-07 23:31:55.254 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-07 23:31:55.312 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-07 23:31:55.352 [debug] ignore file modify evnet Home/components/view/文件检索.components   
