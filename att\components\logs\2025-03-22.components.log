2025-03-22 08:17:26 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-22 08:17:26 [info] indexing created file components/logs/2025-03-22.components.log  [object Object] 
2025-03-22 08:17:26 [info] refresh page data from created listeners 0 633   
2025-03-22 08:17:26 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-22 08:17:26 [info] index finished after resolve  [object Object] 
2025-03-22 08:17:26 [info] refresh page data from resolve listeners 0 633   
2025-03-22 08:17:26 [info] ignore file modify evnet 学习库/二级/数据库.md   
2025-03-22 08:17:26 [info] trigger 学习库/二级/数据库.md resolve  [object Object] 
2025-03-22 08:17:26 [info] index finished after resolve  [object Object] 
2025-03-22 08:17:26 [info] refresh page data from resolve listeners 0 633   
2025-03-22 08:17:27 [info] ignore file modify evnet 学习库/二级/数据结构.md   
2025-03-22 08:17:27 [info] trigger 学习库/二级/数据结构.md resolve  [object Object] 
2025-03-22 08:17:27 [info] index finished after resolve  [object Object] 
2025-03-22 08:17:27 [info] refresh page data from resolve listeners 0 633   
2025-03-22 13:01:46 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [info] components database created cost 2 ms   
2025-03-22 13:01:46 [info] components index initializing...   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [info] start to batch put pages: 7   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [info] batch persist cost 7  3 
2025-03-22 13:01:46 [info] components index initialized, 633 files cost 120 ms   
2025-03-22 13:01:46 [info] refresh page data from init listeners 0 633   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 13:01:46 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 13:01:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 13:01:47 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 13:01:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 13:01:49 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 13:01:49 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 13:01:49 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 13:01:49 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 13:01:51 [info] ignore file modify evnet 学习库/python笔记/数据容器/列表.md   
2025-03-22 13:01:51 [info] trigger 学习库/python笔记/数据容器/列表.md resolve  [object Object] 
2025-03-22 13:01:51 [info] index finished after resolve  [object Object] 
2025-03-22 13:01:51 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:35:37 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:35:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-22 16:35:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-22 16:35:37 [info] index finished after resolve  [object Object] 
2025-03-22 16:35:37 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:35:39 [info] ignore file modify evnet 学习库/python笔记/python函数/函数进阶.md   
2025-03-22 16:35:39 [info] trigger 学习库/python笔记/python函数/函数进阶.md resolve  [object Object] 
2025-03-22 16:35:39 [info] index finished after resolve  [object Object] 
2025-03-22 16:35:39 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:35:40 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-22 16:35:40 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-22 16:35:40 [info] index finished after resolve  [object Object] 
2025-03-22 16:35:40 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:35:42 [info] trigger 学习库/ROS/机器人学/机器人运动学/Attachments/Pasted image 20241009201823.png resolve  [object Object] 
2025-03-22 16:35:42 [info] index finished after resolve  [object Object] 
2025-03-22 16:35:42 [info] refresh page data from modify listeners 0 633   
2025-03-22 16:35:43 [info] ignore file modify evnet 学习库/ROS/机器人学/机器人运动学/导论.md   
2025-03-22 16:35:43 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-03-22 16:35:43 [info] index finished after resolve  [object Object] 
2025-03-22 16:35:43 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:37:09 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:37:10 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:37:10 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:37:10 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:37:10 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:37:45 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:37:50 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:37:50 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:37:50 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:37:50 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:37:50 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:38:08 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:08 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:08 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:08 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:38:37 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:37 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:37 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:37 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:38:39 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:39 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:39 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:39 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:38:42 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:42 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:42 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:42 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:38:45 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:45 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:45 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:45 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:38:47 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:38:47 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:38:47 [info] index finished after resolve  [object Object] 
2025-03-22 16:38:47 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:39:22 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:39:42 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:40:26 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:40:26 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:40:26 [info] index finished after resolve  [object Object] 
2025-03-22 16:40:26 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:40:41 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:40:41 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:40:41 [info] index finished after resolve  [object Object] 
2025-03-22 16:40:41 [info] refresh page data from resolve listeners 0 637   
2025-03-22 16:40:56 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:40:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:40:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:40:56 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:40:56 [info] components database created cost 21 ms   
2025-03-22 16:40:56 [info] components index initializing...   
2025-03-22 16:40:58 [info] start to batch put pages: 5   
2025-03-22 16:40:59 [info] batch persist cost 5  654 
2025-03-22 16:40:59 [info] components index initialized, 637 files cost 2562 ms   
2025-03-22 16:40:59 [info] refresh page data from init listeners 0 637   
2025-03-22 16:40:59 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:40:59 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:40:59 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:40:59 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:41:00 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:41:00 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:42:08 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:42:08 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:42:08 [info] index finished after resolve  [object Object] 
2025-03-22 16:42:08 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:56:45 [info] ignore file modify evnet Home/Home.md   
2025-03-22 16:56:45 [info] trigger Home/Home.md resolve  [object Object] 
2025-03-22 16:56:45 [info] index finished after resolve  [object Object] 
2025-03-22 16:56:45 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:56:45 [info] ignore file modify evnet Home/components/view/weather.md   
2025-03-22 16:56:46 [info] trigger Home/components/view/weather.md resolve  [object Object] 
2025-03-22 16:56:46 [info] index finished after resolve  [object Object] 
2025-03-22 16:56:46 [info] refresh page data from resolve listeners 0 633   
2025-03-22 16:56:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:58:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:58:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:58:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:58:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:58:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:58:48 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-22 16:58:48 [info] components database created cost 0 ms   
2025-03-22 16:58:48 [info] components index initializing...   
2025-03-22 16:58:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:58:48 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:58:48 [info] start to batch put pages: 5   
2025-03-22 16:58:49 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:58:49 [info] batch persist cost 5  1643 
2025-03-22 16:58:49 [info] components index initialized, 633 files cost 1853 ms   
2025-03-22 16:58:49 [info] refresh page data from init listeners 0 633   
2025-03-22 16:58:49 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:58:50 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:58:50 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 16:58:51 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 16:58:51 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:58:51 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 16:58:51 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 16:58:51 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:59:05 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:59:08 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:59:09 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:59:12 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 16:59:13 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 23:57:30 [info] components database created cost 14 ms   
2025-03-22 23:57:30 [info] components index initializing...   
2025-03-22 23:57:31 [info] start to batch put pages: 5   
2025-03-22 23:57:31 [info] batch persist cost 5  245 
2025-03-22 23:57:31 [info] components index initialized, 633 files cost 1874 ms   
2025-03-22 23:57:31 [info] refresh page data from init listeners 0 633   
2025-03-22 23:57:32 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 23:57:32 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-22 23:57:33 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-22 23:57:33 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-22 23:57:33 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-22 23:57:33 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-22 23:57:35 [info] ignore file modify evnet 学习库/python笔记/面向对象/封装，继承，和多态.md   
2025-03-22 23:57:35 [info] trigger 学习库/python笔记/面向对象/封装，继承，和多态.md resolve  [object Object] 
2025-03-22 23:57:35 [info] index finished after resolve  [object Object] 
2025-03-22 23:57:35 [info] refresh page data from resolve listeners 0 633   
2025-03-22 23:57:36 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-03-22 23:57:37 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-03-22 23:57:37 [info] index finished after resolve  [object Object] 
2025-03-22 23:57:37 [info] refresh page data from resolve listeners 0 633   
2025-03-22 23:57:37 [info] ignore file modify evnet 学习库/c/运算符.md   
2025-03-22 23:57:37 [info] trigger 学习库/c/运算符.md resolve  [object Object] 
2025-03-22 23:57:37 [info] index finished after resolve  [object Object] 
2025-03-22 23:57:37 [info] refresh page data from resolve listeners 0 633   
2025-03-22 23:57:38 [info] ignore file modify evnet 学习库/GIT食用指南/常用指令.md   
2025-03-22 23:57:38 [info] trigger 学习库/GIT食用指南/常用指令.md resolve  [object Object] 
2025-03-22 23:57:38 [info] index finished after resolve  [object Object] 
2025-03-22 23:57:38 [info] refresh page data from resolve listeners 0 633   
2025-03-22 23:57:41 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-22-22-31-03.png  [object Object] 
2025-03-22 23:57:41 [info] refresh page data from created listeners 0 634   
2025-03-22 23:57:41 [info] indexing created file 学习库/GIT食用指南/attachments/GIT 工作流-2025-03-22-22-32-21.png  [object Object] 
2025-03-22 23:57:41 [info] refresh page data from created listeners 0 635   
2025-03-22 23:57:42 [info] ignore file modify evnet 学习库/GIT食用指南/GIT 工作流.md   
2025-03-22 23:57:42 [info] trigger 学习库/GIT食用指南/GIT 工作流.md resolve  [object Object] 
2025-03-22 23:57:42 [info] index finished after resolve  [object Object] 
2025-03-22 23:57:42 [info] refresh page data from resolve listeners 0 635   
2025-03-22 23:58:55 [info] ignore file modify evnet 学习库/嵌入式秋招经验贴.md   
2025-03-22 23:58:55 [info] trigger 学习库/嵌入式秋招经验贴.md resolve  [object Object] 
2025-03-22 23:58:55 [info] index finished after resolve  [object Object] 
2025-03-22 23:58:55 [info] refresh page data from resolve listeners 0 635   
2025-03-22 23:58:58 [info] ignore file modify evnet 学习库/嵌入式秋招经验贴.md   
2025-03-22 23:58:58 [info] trigger 学习库/嵌入式秋招经验贴.md resolve  [object Object] 
2025-03-22 23:58:58 [info] index finished after resolve  [object Object] 
2025-03-22 23:58:58 [info] refresh page data from resolve listeners 0 635   
