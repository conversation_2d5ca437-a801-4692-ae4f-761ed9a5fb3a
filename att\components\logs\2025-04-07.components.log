2025-04-07 23:34:10 [info] components database created cost 0 ms   
2025-04-07 23:34:10 [info] components index initializing...   
2025-04-07 23:34:10 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-07 23:34:10 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-07 23:34:10 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-07 23:34:10 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-07 23:34:10 [info] start to batch put pages: 5   
2025-04-07 23:34:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-07 23:34:12 [info] batch persist cost 5  1628 
2025-04-07 23:34:12 [info] components index initialized, 690 files cost 1835 ms   
2025-04-07 23:34:12 [info] refresh page data from init listeners 0 690   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-07 23:34:13 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-07 23:34:13 [info] indexing created file components/logs/2025-04-07.components.log  [object Object] 
2025-04-07 23:34:13 [info] refresh page data from created listeners 0 691   
2025-04-07 23:34:14 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-07 23:34:14 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-07 23:34:14 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-07 23:34:14 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-07 23:34:14 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-07 23:34:16 [info] ignore file modify evnet 学习库/python笔记/面向对象/类，方法，和对象.md   
2025-04-07 23:34:16 [info] trigger 学习库/python笔记/面向对象/类，方法，和对象.md resolve  [object Object] 
2025-04-07 23:34:16 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:16 [info] refresh page data from resolve listeners 0 691   
2025-04-07 23:34:16 [info] ignore file modify evnet 工作库/比赛/智慧路灯/UART串口通信.md   
2025-04-07 23:34:16 [info] trigger 工作库/比赛/智慧路灯/UART串口通信.md resolve  [object Object] 
2025-04-07 23:34:16 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:16 [info] refresh page data from resolve listeners 0 691   
2025-04-07 23:34:17 [info] ignore file modify evnet 学习库/ROS/机器人学/机器人运动学/导论.md   
2025-04-07 23:34:17 [info] trigger 学习库/ROS/机器人学/机器人运动学/导论.md resolve  [object Object] 
2025-04-07 23:34:17 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:17 [info] refresh page data from resolve listeners 0 691   
2025-04-07 23:34:18 [info] indexing created file components/logs/2025-03-30.components.log  [object Object] 
2025-04-07 23:34:18 [info] refresh page data from created listeners 0 692   
2025-04-07 23:34:18 [info] indexing created file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md  [object Object] 
2025-04-07 23:34:18 [info] indexing created ignore file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-04-07 23:34:18 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-04-07 23:34:18 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:18 [info] refresh page data from resolve listeners 0 693   
2025-04-07 23:34:19 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-31-14-22-07.png  [object Object] 
2025-04-07 23:34:19 [info] refresh page data from created listeners 0 694   
2025-04-07 23:34:19 [info] refresh page data from delete listeners 0 693   
2025-04-07 23:34:19 [info] indexing created file 学习库/stm32/1 启动.md  [object Object] 
2025-04-07 23:34:19 [info] indexing created ignore file 学习库/stm32/1 启动.md   
2025-04-07 23:34:20 [info] trigger 学习库/stm32/1 启动.md resolve  [object Object] 
2025-04-07 23:34:20 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:20 [info] refresh page data from resolve listeners 0 694   
2025-04-07 23:34:20 [info] refresh page data from delete listeners 0 693   
2025-04-07 23:34:20 [info] refresh page data from delete listeners 0 692   
2025-04-07 23:34:20 [info] indexing created file 学习库/stm32/2 GPIO.md  [object Object] 
2025-04-07 23:34:20 [info] indexing created ignore file 学习库/stm32/2 GPIO.md   
2025-04-07 23:34:20 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-04-07 23:34:20 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:20 [info] refresh page data from resolve listeners 0 693   
2025-04-07 23:34:21 [info] indexing created file 学习库/Anki/stm32/GPIO.md  [object Object] 
2025-04-07 23:34:21 [info] indexing created ignore file 学习库/Anki/stm32/GPIO.md   
2025-04-07 23:34:21 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-04-07 23:34:21 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:21 [info] refresh page data from resolve listeners 0 694   
2025-04-07 23:34:21 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-15-26.png  [object Object] 
2025-04-07 23:34:21 [info] refresh page data from created listeners 0 695   
2025-04-07 23:34:22 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-22-21.png  [object Object] 
2025-04-07 23:34:22 [info] refresh page data from created listeners 0 696   
2025-04-07 23:34:23 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-34-09.png  [object Object] 
2025-04-07 23:34:23 [info] refresh page data from created listeners 0 697   
2025-04-07 23:34:24 [info] indexing created file components/logs/2025-03-31.components.log  [object Object] 
2025-04-07 23:34:24 [info] refresh page data from created listeners 0 698   
2025-04-07 23:34:24 [info] indexing created file 学习库/c/attachments/6 数组-2025-04-01-09-19-48.gif  [object Object] 
2025-04-07 23:34:24 [info] refresh page data from created listeners 0 699   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-07 23:34:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-07 23:34:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-07 23:34:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-07 23:34:30 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-01-15-47-55.png  [object Object] 
2025-04-07 23:34:30 [info] refresh page data from created listeners 0 700   
2025-04-07 23:34:31 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-04-01-16-19-05.png  [object Object] 
2025-04-07 23:34:31 [info] refresh page data from created listeners 0 701   
2025-04-07 23:34:32 [info] indexing created file components/logs/2025-04-01.components.log  [object Object] 
2025-04-07 23:34:32 [info] refresh page data from created listeners 0 702   
2025-04-07 23:34:32 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-09-31-23.png  [object Object] 
2025-04-07 23:34:32 [info] refresh page data from created listeners 0 703   
2025-04-07 23:34:33 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-09-37-01.png  [object Object] 
2025-04-07 23:34:33 [info] refresh page data from created listeners 0 704   
2025-04-07 23:34:34 [info] indexing created file 学习库/Anki/c/指针.md  [object Object] 
2025-04-07 23:34:34 [info] indexing created ignore file 学习库/Anki/c/指针.md   
2025-04-07 23:34:34 [info] trigger 学习库/Anki/c/指针.md resolve  [object Object] 
2025-04-07 23:34:34 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:34 [info] refresh page data from resolve listeners 0 705   
2025-04-07 23:34:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-04-07 23:34:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-04-07 23:34:34 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:34 [info] refresh page data from resolve listeners 0 705   
2025-04-07 23:34:35 [info] indexing created file 学习库/Anki/c/数组.md  [object Object] 
2025-04-07 23:34:35 [info] indexing created ignore file 学习库/Anki/c/数组.md   
2025-04-07 23:34:35 [info] trigger 学习库/Anki/c/数组.md resolve  [object Object] 
2025-04-07 23:34:35 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:35 [info] refresh page data from resolve listeners 0 706   
2025-04-07 23:34:35 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-02-10-17-26.png  [object Object] 
2025-04-07 23:34:35 [info] refresh page data from created listeners 0 707   
2025-04-07 23:34:35 [info] refresh page data from delete listeners 0 706   
2025-04-07 23:34:36 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-04-02-15-55-30.png  [object Object] 
2025-04-07 23:34:36 [info] refresh page data from created listeners 0 707   
2025-04-07 23:34:37 [info] indexing created file 学习库/stm32/3 串口.md  [object Object] 
2025-04-07 23:34:37 [info] indexing created ignore file 学习库/stm32/3 串口.md   
2025-04-07 23:34:37 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-04-07 23:34:37 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:37 [info] refresh page data from resolve listeners 0 708   
2025-04-07 23:34:37 [info] indexing created file 学习库/stm32/attachments/I2C-2025-04-02-18-28-06.png  [object Object] 
2025-04-07 23:34:37 [info] refresh page data from created listeners 0 709   
2025-04-07 23:34:38 [info] indexing created file 学习库/stm32/attachments/I2C-2025-04-02-18-30-50.png  [object Object] 
2025-04-07 23:34:38 [info] refresh page data from created listeners 0 710   
2025-04-07 23:34:39 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-19-34-34.png  [object Object] 
2025-04-07 23:34:39 [info] refresh page data from created listeners 0 711   
2025-04-07 23:34:39 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-10-09.png  [object Object] 
2025-04-07 23:34:39 [info] refresh page data from created listeners 0 712   
2025-04-07 23:34:40 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-23-20.png  [object Object] 
2025-04-07 23:34:40 [info] refresh page data from created listeners 0 713   
2025-04-07 23:34:40 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-28-13.png  [object Object] 
2025-04-07 23:34:40 [info] refresh page data from created listeners 0 714   
2025-04-07 23:34:41 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-41-01.png  [object Object] 
2025-04-07 23:34:41 [info] refresh page data from created listeners 0 715   
2025-04-07 23:34:42 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-44-58.png  [object Object] 
2025-04-07 23:34:42 [info] refresh page data from created listeners 0 716   
2025-04-07 23:34:42 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-02-20-49-26.png  [object Object] 
2025-04-07 23:34:42 [info] refresh page data from created listeners 0 717   
2025-04-07 23:34:43 [info] indexing created file 学习库/stm32/attachments/Pasted Image 20250402205837_778.png  [object Object] 
2025-04-07 23:34:43 [info] refresh page data from created listeners 0 718   
2025-04-07 23:34:44 [info] indexing created file 日记库/day/2025-04-02.md  [object Object] 
2025-04-07 23:34:44 [info] indexing created ignore file 日记库/day/2025-04-02.md   
2025-04-07 23:34:44 [info] trigger 日记库/day/2025-04-02.md resolve  [object Object] 
2025-04-07 23:34:44 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:44 [info] refresh page data from resolve listeners 0 719   
2025-04-07 23:34:45 [info] indexing created file components/logs/2025-04-02.components.log  [object Object] 
2025-04-07 23:34:45 [info] refresh page data from created listeners 0 720   
2025-04-07 23:34:45 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-17-01-29.png  [object Object] 
2025-04-07 23:34:45 [info] refresh page data from created listeners 0 721   
2025-04-07 23:34:46 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-19-55-10.png  [object Object] 
2025-04-07 23:34:46 [info] refresh page data from created listeners 0 722   
2025-04-07 23:34:47 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-03-19-55-20.png  [object Object] 
2025-04-07 23:34:47 [info] refresh page data from created listeners 0 723   
2025-04-07 23:34:48 [info] indexing created file 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md  [object Object] 
2025-04-07 23:34:48 [info] indexing created ignore file 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md   
2025-04-07 23:34:48 [info] trigger 学习库/stm32/attachments/Drawing 2025-04-02 20.58.18.excalidraw.md resolve  [object Object] 
2025-04-07 23:34:48 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:48 [info] refresh page data from resolve listeners 0 724   
2025-04-07 23:34:49 [info] indexing created file components/logs/2025-04-03.components.log  [object Object] 
2025-04-07 23:34:49 [info] refresh page data from created listeners 0 725   
2025-04-07 23:34:49 [info] indexing created file 学习库/c/8 字符串.md  [object Object] 
2025-04-07 23:34:49 [info] indexing created ignore file 学习库/c/8 字符串.md   
2025-04-07 23:34:49 [info] trigger 学习库/c/8 字符串.md resolve  [object Object] 
2025-04-07 23:34:49 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:49 [info] refresh page data from resolve listeners 0 726   
2025-04-07 23:34:50 [info] ignore file modify evnet 学习库/c/3 运算符.md   
2025-04-07 23:34:50 [info] trigger 学习库/c/3 运算符.md resolve  [object Object] 
2025-04-07 23:34:50 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:50 [info] refresh page data from resolve listeners 0 726   
2025-04-07 23:34:50 [info] indexing created file 学习库/c/9 结构体.md  [object Object] 
2025-04-07 23:34:50 [info] indexing created ignore file 学习库/c/9 结构体.md   
2025-04-07 23:34:50 [info] trigger 学习库/c/9 结构体.md resolve  [object Object] 
2025-04-07 23:34:50 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:50 [info] refresh page data from resolve listeners 0 727   
2025-04-07 23:34:52 [info] indexing created file 学习库/c/attachments/7 指针-2025-04-04-11-36-08.png  [object Object] 
2025-04-07 23:34:52 [info] refresh page data from created listeners 0 728   
2025-04-07 23:34:52 [info] indexing created file 学习库/c/7 指针.md  [object Object] 
2025-04-07 23:34:52 [info] indexing created ignore file 学习库/c/7 指针.md   
2025-04-07 23:34:52 [info] trigger 学习库/c/7 指针.md resolve  [object Object] 
2025-04-07 23:34:52 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:52 [info] refresh page data from resolve listeners 0 729   
2025-04-07 23:34:53 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-04-07 23:34:53 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-04-07 23:34:53 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:53 [info] refresh page data from resolve listeners 0 729   
2025-04-07 23:34:54 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-04-16-51-01.png  [object Object] 
2025-04-07 23:34:54 [info] refresh page data from created listeners 0 730   
2025-04-07 23:34:54 [info] ignore file modify evnet 学习库/Deep learning/训练实践/一些基础.md   
2025-04-07 23:34:54 [info] trigger 学习库/Deep learning/训练实践/一些基础.md resolve  [object Object] 
2025-04-07 23:34:54 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:54 [info] refresh page data from resolve listeners 0 730   
2025-04-07 23:34:55 [info] indexing created file components/logs/2025-04-04.components.log  [object Object] 
2025-04-07 23:34:55 [info] refresh page data from created listeners 0 731   
2025-04-07 23:34:55 [info] indexing created file 工作库/项目/舌诊/分割.md  [object Object] 
2025-04-07 23:34:55 [info] indexing created ignore file 工作库/项目/舌诊/分割.md   
2025-04-07 23:34:55 [info] trigger 工作库/项目/舌诊/分割.md resolve  [object Object] 
2025-04-07 23:34:55 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:55 [info] refresh page data from resolve listeners 0 732   
2025-04-07 23:34:56 [info] indexing created file components/logs/2025-04-06.components.log  [object Object] 
2025-04-07 23:34:56 [info] refresh page data from created listeners 0 733   
2025-04-07 23:34:57 [info] indexing created file 学习库/linux/wsl2.md  [object Object] 
2025-04-07 23:34:57 [info] indexing created ignore file 学习库/linux/wsl2.md   
2025-04-07 23:34:57 [info] trigger 学习库/linux/wsl2.md resolve  [object Object] 
2025-04-07 23:34:57 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:57 [info] refresh page data from resolve listeners 0 734   
2025-04-07 23:34:57 [info] indexing created file 学习库/stm32/attachments/4 I2C-2025-04-07-10-45-57.png  [object Object] 
2025-04-07 23:34:57 [info] refresh page data from created listeners 0 735   
2025-04-07 23:34:58 [info] indexing created file 学习库/stm32/4 I2C.md  [object Object] 
2025-04-07 23:34:58 [info] indexing created ignore file 学习库/stm32/4 I2C.md   
2025-04-07 23:34:58 [info] trigger 学习库/stm32/4 I2C.md resolve  [object Object] 
2025-04-07 23:34:58 [info] index finished after resolve  [object Object] 
2025-04-07 23:34:58 [info] refresh page data from resolve listeners 0 736   
