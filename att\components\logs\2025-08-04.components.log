2025-08-04 08:17:32.210 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-04 08:17:32.218 [info] indexing created file components/logs/2025-08-04.components.log  [object Object] 
2025-08-04 08:17:32.221 [info] refresh page data from created listeners 0 1044   
2025-08-04 08:17:32.241 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-04 08:17:32.245 [info] index finished after resolve  [object Object] 
2025-08-04 08:17:32.245 [info] refresh page data from resolve listeners 0 1044   
2025-08-04 08:20:49.063 [info] indexing created file 学习库/linux/常用Linux命令_优化版.md  [object Object] 
2025-08-04 08:20:49.063 [info] indexing created ignore file 学习库/linux/常用Linux命令_优化版.md   
2025-08-04 08:20:49.104 [info] trigger 学习库/linux/常用Linux命令_优化版.md resolve  [object Object] 
2025-08-04 08:20:49.105 [info] index finished after resolve  [object Object] 
2025-08-04 08:20:49.107 [info] refresh page data from resolve listeners 0 1045   
2025-08-04 08:21:16.197 [debug] ignore file modify evnet 学习库/linux/常用Linux命令_优化版.md   
2025-08-04 08:21:16.256 [info] trigger 学习库/linux/常用Linux命令_优化版.md resolve  [object Object] 
2025-08-04 08:21:16.257 [info] index finished after resolve  [object Object] 
2025-08-04 08:21:16.257 [info] refresh page data from resolve listeners 0 1045   
2025-08-04 08:24:09.231 [info] indexing created file 学习库/linux/01-文件与目录操作.md  [object Object] 
2025-08-04 08:24:09.232 [info] indexing created ignore file 学习库/linux/01-文件与目录操作.md   
2025-08-04 08:24:09.282 [info] trigger 学习库/linux/01-文件与目录操作.md resolve  [object Object] 
2025-08-04 08:24:09.292 [info] index finished after resolve  [object Object] 
2025-08-04 08:24:09.293 [info] refresh page data from resolve listeners 0 1046   
2025-08-04 08:24:57.753 [info] indexing created file 学习库/linux/02-文件内容查看与处理.md  [object Object] 
2025-08-04 08:24:57.753 [info] indexing created ignore file 学习库/linux/02-文件内容查看与处理.md   
2025-08-04 08:24:57.754 [info] trigger 学习库/linux/01-文件与目录操作.md resolve  [object Object] 
2025-08-04 08:24:57.772 [info] trigger 学习库/linux/02-文件内容查看与处理.md resolve  [object Object] 
2025-08-04 08:24:57.783 [info] index finished after resolve  [object Object] 
2025-08-04 08:24:57.784 [info] refresh page data from resolve listeners 0 1047   
2025-08-04 08:25:52.213 [info] indexing created file 学习库/linux/03-系统信息与进程管理.md  [object Object] 
2025-08-04 08:25:52.213 [info] indexing created ignore file 学习库/linux/03-系统信息与进程管理.md   
2025-08-04 08:25:52.216 [info] trigger 学习库/linux/01-文件与目录操作.md resolve  [object Object] 
2025-08-04 08:25:52.216 [info] trigger 学习库/linux/02-文件内容查看与处理.md resolve  [object Object] 
2025-08-04 08:25:52.258 [info] trigger 学习库/linux/03-系统信息与进程管理.md resolve  [object Object] 
2025-08-04 08:25:52.265 [info] index finished after resolve  [object Object] 
2025-08-04 08:25:52.266 [info] refresh page data from resolve listeners 0 1048   
2025-08-04 08:26:55.371 [info] indexing created file 学习库/linux/04-网络命令.md  [object Object] 
2025-08-04 08:26:55.372 [info] indexing created ignore file 学习库/linux/04-网络命令.md   
2025-08-04 08:26:55.374 [info] trigger 学习库/linux/01-文件与目录操作.md resolve  [object Object] 
2025-08-04 08:26:55.388 [info] trigger 学习库/linux/03-系统信息与进程管理.md resolve  [object Object] 
2025-08-04 08:26:55.430 [info] trigger 学习库/linux/04-网络命令.md resolve  [object Object] 
2025-08-04 08:26:55.431 [info] index finished after resolve  [object Object] 
2025-08-04 08:26:55.432 [info] refresh page data from resolve listeners 0 1049   
2025-08-04 08:27:53.982 [debug] ignore file modify evnet 学习库/linux/01-文件与目录操作.md   
2025-08-04 08:27:53.995 [info] trigger 学习库/linux/01-文件与目录操作.md resolve  [object Object] 
2025-08-04 08:27:53.996 [info] index finished after resolve  [object Object] 
2025-08-04 08:27:53.996 [info] refresh page data from resolve listeners 0 1049   
2025-08-04 08:28:01.827 [info] indexing created file 学习库/linux/05-权限与安全管理.md  [object Object] 
2025-08-04 08:28:01.827 [info] indexing created ignore file 学习库/linux/05-权限与安全管理.md   
2025-08-04 08:28:01.830 [info] trigger 学习库/linux/03-系统信息与进程管理.md resolve  [object Object] 
2025-08-04 08:28:01.831 [info] trigger 学习库/linux/04-网络命令.md resolve  [object Object] 
2025-08-04 08:28:01.865 [info] trigger 学习库/linux/05-权限与安全管理.md resolve  [object Object] 
2025-08-04 08:28:01.868 [info] index finished after resolve  [object Object] 
2025-08-04 08:28:01.869 [info] refresh page data from resolve listeners 0 1050   
2025-08-04 08:29:18.485 [info] indexing created file 学习库/linux/06-Vim编辑器.md  [object Object] 
2025-08-04 08:29:18.485 [info] indexing created ignore file 学习库/linux/06-Vim编辑器.md   
2025-08-04 08:29:18.488 [info] trigger 学习库/linux/02-文件内容查看与处理.md resolve  [object Object] 
2025-08-04 08:29:18.544 [info] trigger 学习库/linux/06-Vim编辑器.md resolve  [object Object] 
2025-08-04 08:29:18.545 [info] index finished after resolve  [object Object] 
2025-08-04 08:29:18.546 [info] refresh page data from resolve listeners 0 1051   
2025-08-04 08:30:22.969 [info] indexing created file 学习库/linux/07-软件包管理与系统维护.md  [object Object] 
2025-08-04 08:30:22.969 [info] indexing created ignore file 学习库/linux/07-软件包管理与系统维护.md   
2025-08-04 08:30:23.012 [info] trigger 学习库/linux/07-软件包管理与系统维护.md resolve  [object Object] 
2025-08-04 08:30:23.017 [info] index finished after resolve  [object Object] 
2025-08-04 08:30:23.018 [info] refresh page data from resolve listeners 0 1052   
2025-08-04 08:31:08.872 [info] indexing created file 学习库/linux/00-Linux学习指南.md  [object Object] 
2025-08-04 08:31:08.872 [info] indexing created ignore file 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:31:08.907 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:31:08.921 [info] index finished after resolve  [object Object] 
2025-08-04 08:31:08.921 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:34:15.821 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:34:15.838 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:34:15.839 [info] index finished after resolve  [object Object] 
2025-08-04 08:34:15.839 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:34:18.381 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:34:18.392 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:34:18.394 [info] index finished after resolve  [object Object] 
2025-08-04 08:34:18.399 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:34:32.869 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:34:32.884 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:34:32.886 [info] index finished after resolve  [object Object] 
2025-08-04 08:34:32.887 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:34:37.812 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:34:37.830 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:34:37.831 [info] index finished after resolve  [object Object] 
2025-08-04 08:34:37.832 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:35:21.202 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:35:21.456 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:35:21.464 [info] index finished after resolve  [object Object] 
2025-08-04 08:35:21.465 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:35:40.342 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:35:40.531 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:35:40.619 [info] index finished after resolve  [object Object] 
2025-08-04 08:35:40.620 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:35:47.066 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:35:47.195 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:35:47.203 [info] index finished after resolve  [object Object] 
2025-08-04 08:35:47.203 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:13.448 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:13.456 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:13.457 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:13.458 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:18.507 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:18.513 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:18.514 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:18.515 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:22.345 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:22.353 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:22.353 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:22.354 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:24.602 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:24.608 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:24.609 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:24.610 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:28.127 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:28.132 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:28.133 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:28.134 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:36:30.435 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:36:30.473 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:36:30.475 [info] index finished after resolve  [object Object] 
2025-08-04 08:36:30.476 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:37:00.023 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:37:00.085 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:37:00.088 [info] index finished after resolve  [object Object] 
2025-08-04 08:37:00.089 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:37:10.214 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:37:10.272 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:37:10.279 [info] index finished after resolve  [object Object] 
2025-08-04 08:37:10.279 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:37:12.807 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:37:12.890 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:37:12.896 [info] index finished after resolve  [object Object] 
2025-08-04 08:37:12.897 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:37:20.950 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:37:20.957 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:37:20.958 [info] index finished after resolve  [object Object] 
2025-08-04 08:37:20.958 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:37:23.660 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:37:23.664 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:37:23.666 [info] index finished after resolve  [object Object] 
2025-08-04 08:37:23.667 [info] refresh page data from resolve listeners 0 1053   
2025-08-04 08:39:37.187 [info] indexing created file 学习库/linux/导航.components  [object Object] 
2025-08-04 08:39:37.249 [info] refresh page data from created listeners 0 1054   
2025-08-04 08:39:55.434 [info] query  [object Object] 
2025-08-04 08:39:55.434 [info] query  [object Object] 
2025-08-04 08:39:55.434 [info] query  [object Object] 
2025-08-04 08:39:55.434 [info] query  [object Object] 
2025-08-04 08:39:55.434 [info] query  [object Object] 
2025-08-04 08:39:55.435 [info] query  [object Object] 
2025-08-04 08:39:55.436 [info] query  [object Object] 
2025-08-04 08:39:55.436 [info] query  [object Object] 
2025-08-04 08:39:55.436 [info] query  [object Object] 
2025-08-04 08:39:55.487 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:39:55.488 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:39:55.515 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:39:55.516 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:39:55.522 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:39:55.524 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:39:55.590 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:39:55.596 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:40:16.866 [debug] ignore file modify evnet 工作库/工作库.components   
2025-08-04 08:40:24.468 [info] query  [object Object] 
2025-08-04 08:40:24.468 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.469 [info] query  [object Object] 
2025-08-04 08:40:24.531 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:40:24.563 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:40:24.602 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:40:24.615 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:40:24.618 [info] query changed, compare cost 1ms, data length diff 0/7   
2025-08-04 08:40:51.657 [debug] ignore file modify evnet 学习库/linux/导航.components   
2025-08-04 08:41:00.131 [info] refresh page data from rename listeners 0 1054   
2025-08-04 08:41:08.880 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:41:18.493 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:41:34.810 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:41:45.942 [info] query  [object Object] 
2025-08-04 08:41:45.943 [info] query  [object Object] 
2025-08-04 08:41:45.943 [info] query  [object Object] 
2025-08-04 08:41:45.943 [info] query  [object Object] 
2025-08-04 08:41:45.943 [info] query  [object Object] 
2025-08-04 08:41:45.943 [info] query  [object Object] 
2025-08-04 08:41:45.944 [info] query  [object Object] 
2025-08-04 08:41:45.979 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:41:46.013 [info] query changed, compare cost 0ms, data length diff 0/17   
2025-08-04 08:41:46.014 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:41:46.076 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:41:46.142 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:45:43.837 [debug] ignore file modify evnet 工作库/工作库.components   
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.941 [info] query  [object Object] 
2025-08-04 08:45:43.942 [info] query  [object Object] 
2025-08-04 08:45:43.942 [info] query  [object Object] 
2025-08-04 08:45:43.942 [info] query  [object Object] 
2025-08-04 08:45:43.955 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:45:43.958 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:45:44.011 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:45:44.012 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:45:44.020 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:45:44.021 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:45:44.023 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:45:44.027 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:04.002 [info] indexing created file 工作库/工作库 1.components  [object Object] 
2025-08-04 08:46:04.073 [info] refresh page data from created listeners 9 1055   
2025-08-04 08:46:04.254 [info] query  [object Object] 
2025-08-04 08:46:04.254 [info] query  [object Object] 
2025-08-04 08:46:04.254 [info] query  [object Object] 
2025-08-04 08:46:04.254 [info] query  [object Object] 
2025-08-04 08:46:04.254 [info] query  [object Object] 
2025-08-04 08:46:04.255 [info] query  [object Object] 
2025-08-04 08:46:04.255 [info] query  [object Object] 
2025-08-04 08:46:04.256 [info] query  [object Object] 
2025-08-04 08:46:04.256 [info] query  [object Object] 
2025-08-04 08:46:04.273 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:04.274 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:04.295 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:46:04.296 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:46:04.303 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:46:04.304 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:04.322 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:46:04.323 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:09.575 [debug] ignore file modify evnet 工作库/工作库 1.components   
2025-08-04 08:46:09.859 [info] query  [object Object] 
2025-08-04 08:46:09.859 [info] query  [object Object] 
2025-08-04 08:46:09.859 [info] query  [object Object] 
2025-08-04 08:46:09.859 [info] query  [object Object] 
2025-08-04 08:46:09.859 [info] query  [object Object] 
2025-08-04 08:46:09.860 [info] query  [object Object] 
2025-08-04 08:46:09.861 [info] query  [object Object] 
2025-08-04 08:46:09.861 [info] query  [object Object] 
2025-08-04 08:46:09.861 [info] query  [object Object] 
2025-08-04 08:46:09.877 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:09.878 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:09.899 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:46:09.899 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:46:09.900 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:46:09.901 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:09.901 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:46:09.902 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:20.708 [info] refresh page data from rename listeners 9 1055   
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:20.820 [info] query  [object Object] 
2025-08-04 08:46:21.710 [info] query  [object Object] 
2025-08-04 08:46:21.710 [info] query  [object Object] 
2025-08-04 08:46:28.690 [info] refresh page data from delete listeners 9 1054   
2025-08-04 08:46:28.826 [info] query  [object Object] 
2025-08-04 08:46:28.827 [info] query  [object Object] 
2025-08-04 08:46:28.827 [info] query  [object Object] 
2025-08-04 08:46:28.841 [info] query  [object Object] 
2025-08-04 08:46:28.841 [info] query  [object Object] 
2025-08-04 08:46:28.841 [info] query  [object Object] 
2025-08-04 08:46:28.841 [info] query  [object Object] 
2025-08-04 08:46:29.694 [info] query  [object Object] 
2025-08-04 08:46:29.694 [info] query  [object Object] 
2025-08-04 08:46:38.583 [info] refresh page data from rename listeners 9 1054   
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:38.699 [info] query  [object Object] 
2025-08-04 08:46:39.587 [info] query  [object Object] 
2025-08-04 08:46:39.587 [info] query  [object Object] 
2025-08-04 08:46:40.274 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.373 [info] query  [object Object] 
2025-08-04 08:46:40.374 [info] query  [object Object] 
2025-08-04 08:46:40.374 [info] query  [object Object] 
2025-08-04 08:46:40.374 [info] query  [object Object] 
2025-08-04 08:46:40.387 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:40.387 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:40.404 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:46:40.405 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:46:40.441 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:46:40.442 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:46:40.487 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:46:40.489 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:46:53.739 [info] query  [object Object] 
2025-08-04 08:46:53.869 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:47:08.289 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:47:08.379 [info] query  [object Object] 
2025-08-04 08:47:08.379 [info] query  [object Object] 
2025-08-04 08:47:08.379 [info] query  [object Object] 
2025-08-04 08:47:08.379 [info] query  [object Object] 
2025-08-04 08:47:08.379 [info] query  [object Object] 
2025-08-04 08:47:08.380 [info] query  [object Object] 
2025-08-04 08:47:08.380 [info] query  [object Object] 
2025-08-04 08:47:08.380 [info] query  [object Object] 
2025-08-04 08:47:08.380 [info] query  [object Object] 
2025-08-04 08:47:08.417 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:47:08.418 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:47:08.439 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:47:08.439 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:47:08.450 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:47:08.450 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:47:08.454 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:47:08.455 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:47:38.443 [info] query  [object Object] 
2025-08-04 08:47:38.573 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:48:09.185 [info] query  [object Object] 
2025-08-04 08:48:09.355 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.082 [info] query  [object Object] 
2025-08-04 08:48:17.134 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:48:17.156 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:48:17.206 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:48:17.224 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:48:17.354 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:48:23.211 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:48:23.709 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:48:27.275 [debug] ignore file modify evnet 学习库/linux/00-Linux学习指南.md   
2025-08-04 08:48:27.284 [info] trigger 学习库/linux/00-Linux学习指南.md resolve  [object Object] 
2025-08-04 08:48:27.285 [info] index finished after resolve  [object Object] 
2025-08-04 08:48:27.286 [info] refresh page data from resolve listeners 8 1054   
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:27.394 [info] query  [object Object] 
2025-08-04 08:48:28.292 [info] query  [object Object] 
2025-08-04 08:48:28.292 [info] query  [object Object] 
2025-08-04 08:48:31.056 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.147 [info] query  [object Object] 
2025-08-04 08:48:31.166 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:48:31.166 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:48:31.230 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:48:31.231 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:48:31.243 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:48:31.243 [info] query changed, compare cost 0ms, data length diff 0/2   
2025-08-04 08:48:31.262 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:48:55.189 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:48:55.783 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:01.660 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:02.659 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:07.959 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:07.968 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:08.543 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:11.370 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:11.882 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:13.637 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:14.158 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:31.905 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:32.340 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:36.369 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:49:39.900 [debug] ignore file modify evnet 学习库/linux/00-导航.components   
2025-08-04 08:51:47.828 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:47.829 [info] query  [object Object] 
2025-08-04 08:51:49.369 [info] components database created cost 0 ms   
2025-08-04 08:51:49.370 [info] components index initializing...   
2025-08-04 08:51:49.639 [info] start to batch put pages: 8   
2025-08-04 08:51:49.646 [info] batch persist cost 8  7 
2025-08-04 08:51:49.688 [info] components index initialized, 1054 files cost 319 ms   
2025-08-04 08:51:49.688 [info] refresh page data from init listeners 8 1054   
2025-08-04 08:51:49.796 [info] query  [object Object] 
2025-08-04 08:51:49.796 [info] query  [object Object] 
2025-08-04 08:51:49.796 [info] query  [object Object] 
2025-08-04 08:51:49.796 [info] query  [object Object] 
2025-08-04 08:51:49.796 [info] query  [object Object] 
2025-08-04 08:51:49.797 [info] query  [object Object] 
2025-08-04 08:51:49.800 [info] query changed, compare cost 0ms, data length diff 0/10   
2025-08-04 08:51:49.801 [info] query changed, compare cost 0ms, data length diff 0/3   
2025-08-04 08:51:49.986 [info] query changed, compare cost 0ms, data length diff 0/7   
2025-08-04 08:51:49.987 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-08-04 08:51:49.999 [info] query changed, compare cost 0ms, data length diff 0/1   
2025-08-04 08:51:51.464 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-04 08:51:51.810 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-04 08:51:52.188 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-04 08:51:52.192 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-04 08:51:52.196 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-04 08:51:52.207 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-04 08:54:14.682 [info] refresh page data from reload listeners 0 0   
2025-08-04 08:54:14.682 [info] start to clear object store page   
2025-08-04 08:54:14.694 [info] Cleared all data in oject store: page   
2025-08-04 08:54:14.694 [info] clear object store cost 12 ms   
2025-08-04 08:54:14.694 [info] components index initializing...   
2025-08-04 08:54:14.895 [info] start to batch put pages: 1054   
2025-08-04 08:54:15.058 [info] batch persist cost 1054  163 
2025-08-04 08:54:15.098 [info] components index initialized, 1054 files cost 416 ms   
2025-08-04 08:54:15.098 [info] refresh page data from init listeners 0 1054   
2025-08-04 08:54:21.133 [info] indexing <NAME_EMAIL>_components_license.md  [object Object] 
2025-08-04 08:54:21.133 [info] indexing created <NAME_EMAIL>_components_license.md   
2025-08-04 08:54:21.429 [info] trigger 250804_w15814452020@gmail.com_components_license.md resolve  [object Object] 
2025-08-04 08:54:21.433 [info] index finished after resolve  [object Object] 
2025-08-04 08:54:21.434 [info] refresh page data from resolve listeners 0 1055   
2025-08-04 08:54:54.287 [info] refresh page data from rename listeners 0 1055   
