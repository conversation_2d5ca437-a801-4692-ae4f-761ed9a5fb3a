2025-03-26 07:46:16 [info] refresh page data from delete listeners 0 664   
2025-03-26 07:46:16 [info] indexing created file components/logs/2025-03-26.components.log  [object Object] 
2025-03-26 07:46:16 [info] refresh page data from created listeners 0 665   
2025-03-26 07:46:17 [info] refresh page data from delete listeners 0 664   
2025-03-26 07:46:17 [info] refresh page data from delete listeners 0 663   
2025-03-26 07:46:17 [info] refresh page data from delete listeners 0 662   
2025-03-26 07:46:18 [info] refresh page data from delete listeners 0 661   
2025-03-26 07:46:18 [info] refresh page data from delete listeners 0 659   
2025-03-26 07:46:18 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-03-26 07:47:13 [info] refresh page data from delete listeners 0 657   
2025-03-26 07:47:13 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-03-26 07:47:13 [info] refresh page data from delete listeners 0 655   
2025-03-26 07:47:13 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-03-26 07:47:13 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 07:47:13 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 07:47:13 [info] index finished after resolve  [object Object] 
2025-03-26 07:47:13 [info] refresh page data from resolve listeners 0 655   
2025-03-26 07:47:14 [info] ignore file modify evnet 学习库/二级/数据库.md   
2025-03-26 07:47:14 [info] trigger 学习库/二级/数据库.md resolve  [object Object] 
2025-03-26 07:47:14 [info] index finished after resolve  [object Object] 
2025-03-26 07:47:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 07:47:14 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-26 07:47:14 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-26 07:47:14 [info] index finished after resolve  [object Object] 
2025-03-26 07:47:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 07:48:13 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-26 07:48:13 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-26 07:48:13 [info] index finished after resolve  [object Object] 
2025-03-26 07:48:13 [info] refresh page data from resolve listeners 0 655   
2025-03-26 07:48:14 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-26 07:48:14 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-26 07:48:14 [info] index finished after resolve  [object Object] 
2025-03-26 07:48:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 10:21:12 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-26 10:21:12 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-26 10:21:12 [info] index finished after resolve  [object Object] 
2025-03-26 10:21:12 [info] refresh page data from resolve listeners 0 655   
2025-03-26 10:21:14 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-26 10:21:14 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-26 10:21:14 [info] index finished after resolve  [object Object] 
2025-03-26 10:21:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 10:29:15 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-26 10:29:15 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-26 10:29:15 [info] index finished after resolve  [object Object] 
2025-03-26 10:29:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 10:29:18 [info] ignore file modify evnet 学习库/c/4 流程控制语句.md   
2025-03-26 10:29:18 [info] trigger 学习库/c/4 流程控制语句.md resolve  [object Object] 
2025-03-26 10:29:18 [info] index finished after resolve  [object Object] 
2025-03-26 10:29:18 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:24 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:24 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:24 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:24 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:27 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:27 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:27 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:27 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:29 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:29 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:29 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:29 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:32 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:32 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:32 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:32 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:35 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:36 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:36 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:36 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:48:39 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:48:39 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:48:39 [info] index finished after resolve  [object Object] 
2025-03-26 14:48:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:15 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:15 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:15 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:18 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:18 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:18 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:18 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:21 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:21 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:21 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:21 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:23 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:23 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:23 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:23 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:25 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:25 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:25 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:25 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:28 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:28 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:28 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:28 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:30 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:30 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:30 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:30 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:32 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:32 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:32 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:32 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:34 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:34 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:34 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:34 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:36 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:36 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:36 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:36 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:39 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:39 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:39 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:41 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:41 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:41 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:41 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:44 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:44 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:44 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:44 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:50 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:50 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:50 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:50 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:49:55 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:49:55 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:49:55 [info] index finished after resolve  [object Object] 
2025-03-26 14:49:55 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:04 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:04 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:04 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:04 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:07 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:07 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:07 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:07 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:16 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:16 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:16 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:16 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:18 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:18 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:18 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:18 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:20 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:20 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:20 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:20 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:23 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:23 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:23 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:23 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:25 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:25 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:25 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:25 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:27 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:27 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:27 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:27 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:31 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:31 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:31 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:31 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:35 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:35 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:35 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:35 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:37 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:37 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:37 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:37 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:41 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:41 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:41 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:41 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:45 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:45 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:45 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:45 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:48 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:48 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:48 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:48 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:50 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:50 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:50 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:50 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:50:53 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:50:53 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:50:53 [info] index finished after resolve  [object Object] 
2025-03-26 14:50:53 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:02 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:02 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:02 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:04 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:04 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:04 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:04 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:06 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:06 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:06 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:06 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:08 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:08 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:08 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:08 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:11 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:11 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:11 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:11 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:14 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:14 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:14 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:17 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:17 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:17 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:17 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:20 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:20 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:20 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:20 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:26 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:26 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:26 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:26 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:28 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:28 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:28 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:28 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:31 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:31 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:31 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:31 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:34 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:34 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:34 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:34 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:36 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:36 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:36 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:36 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:39 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:39 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:39 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:41 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:41 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:41 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:41 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:46 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:46 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:46 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:46 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:48 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:48 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:48 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:48 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:50 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:50 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:50 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:50 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:53 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:53 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:53 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:53 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:51:56 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:51:56 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:51:56 [info] index finished after resolve  [object Object] 
2025-03-26 14:51:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:02 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:02 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:02 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:10 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:10 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:10 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:10 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:12 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:12 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:12 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:12 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:15 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:15 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:15 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:17 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:17 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:17 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:17 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:19 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:19 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:19 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:19 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:22 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:22 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:22 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:24 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:24 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:24 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:24 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:26 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:26 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:26 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:26 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:28 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:28 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:28 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:28 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:30 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:30 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:30 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:30 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:34 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:34 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:34 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:34 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:37 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:37 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:37 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:37 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:40 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:40 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:40 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:40 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:43 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:43 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:43 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:43 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:47 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:47 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:47 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:47 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:49 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:49 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:49 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:49 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:52 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:52 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:52 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:52 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:56 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:56 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:56 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:52:58 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:52:58 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:52:58 [info] index finished after resolve  [object Object] 
2025-03-26 14:52:58 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:00 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:00 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:00 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:00 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:02 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:02 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:02 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:04 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:04 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:04 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:05 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:07 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:07 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:07 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:07 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:09 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:09 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:09 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:09 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:12 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:12 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:12 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:12 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:15 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:15 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:15 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:17 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:17 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:17 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:17 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:19 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:19 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:19 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:19 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:36 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:36 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:36 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:36 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:40 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:40 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:40 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:40 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:53:45 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-03-26 14:53:45 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-03-26 14:53:45 [info] index finished after resolve  [object Object] 
2025-03-26 14:53:45 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:15 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:15 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:15 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:22 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:22 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:22 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:27 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:27 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:27 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:27 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:29 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:29 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:29 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:29 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:31 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:31 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:31 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:31 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:34 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:34 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:34 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:34 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:38 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:38 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:38 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:38 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:45 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:45 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:45 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:45 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:48 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:48 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:48 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:48 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:51 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:51 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:51 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:51 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:54:53 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:54:53 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:54:53 [info] index finished after resolve  [object Object] 
2025-03-26 14:54:53 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:10 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:10 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:10 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:10 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:12 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:12 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:12 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:12 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:15 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:15 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:15 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:17 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:17 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:17 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:17 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:19 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:19 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:19 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:19 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:21 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:21 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:21 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:21 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:23 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:23 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:23 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:23 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:25 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:25 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:25 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:25 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:30 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:30 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:30 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:30 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:38 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:38 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:38 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:38 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:40 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:40 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:40 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:40 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:43 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:43 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:43 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:43 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:48 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:48 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:48 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:48 [info] refresh page data from resolve listeners 0 655   
2025-03-26 14:55:56 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-26 14:55:56 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-26 14:55:56 [info] index finished after resolve  [object Object] 
2025-03-26 14:55:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:32 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:32 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:32 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:32 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:35 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:35 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:35 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:35 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:37 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:37 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:37 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:37 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:39 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:39 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:39 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:56 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:56 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:56 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:54:58 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:54:58 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:54:58 [info] index finished after resolve  [object Object] 
2025-03-26 16:54:58 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:02 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:02 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:02 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:07 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:07 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:07 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:07 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:10 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:10 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:10 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:10 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:12 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:12 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:12 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:12 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:15 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:15 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:15 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:39 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:39 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:39 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:46 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:46 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:46 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:46 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:55:58 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:55:58 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:55:58 [info] index finished after resolve  [object Object] 
2025-03-26 16:55:58 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:56:00 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:56:00 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:56:00 [info] index finished after resolve  [object Object] 
2025-03-26 16:56:00 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:56:02 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:56:02 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:56:02 [info] index finished after resolve  [object Object] 
2025-03-26 16:56:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:56:22 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:56:22 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:56:22 [info] index finished after resolve  [object Object] 
2025-03-26 16:56:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:06 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:06 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:06 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:06 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:14 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:14 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:14 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:14 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:16 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:16 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:16 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:16 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:18 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:19 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:19 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:19 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:21 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:21 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:21 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:21 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:23 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:23 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:23 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:23 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:26 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:26 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:26 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:26 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:31 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:31 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:31 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:31 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:39 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:39 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:39 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:39 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:57:42 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:57:42 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:57:42 [info] index finished after resolve  [object Object] 
2025-03-26 16:57:42 [info] refresh page data from resolve listeners 0 655   
2025-03-26 16:58:00 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 16:58:00 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 16:58:00 [info] index finished after resolve  [object Object] 
2025-03-26 16:58:00 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:02 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:02 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:02 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:02 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:06 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:06 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:06 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:06 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:09 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:09 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:09 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:09 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:15 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:15 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:15 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:15 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:18 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:18 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:18 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:18 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:22 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:22 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:22 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:25 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:25 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:25 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:25 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:29 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:29 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:29 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:29 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:31 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:31 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:31 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:31 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:40 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:40 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:40 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:40 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:42 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:42 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:42 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:42 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:46 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:46 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:46 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:46 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:51 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:51 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:51 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:51 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:53 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:53 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:53 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:53 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:55 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:56 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:56 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:01:58 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:01:58 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:01:58 [info] index finished after resolve  [object Object] 
2025-03-26 17:01:58 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:02:09 [info] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-03-26 17:02:09 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-03-26 17:02:09 [info] index finished after resolve  [object Object] 
2025-03-26 17:02:09 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:18:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:18:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:18:44 [info] index finished after resolve  [object Object] 
2025-03-26 17:18:44 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:18:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:18:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:18:47 [info] index finished after resolve  [object Object] 
2025-03-26 17:18:47 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:18:49 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:18:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:18:49 [info] index finished after resolve  [object Object] 
2025-03-26 17:18:49 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:18:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:18:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:18:51 [info] index finished after resolve  [object Object] 
2025-03-26 17:18:51 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:18:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:18:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:18:59 [info] index finished after resolve  [object Object] 
2025-03-26 17:18:59 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:19:01 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:19:01 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:19:01 [info] index finished after resolve  [object Object] 
2025-03-26 17:19:01 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:06 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:06 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:08 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:08 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:10 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:10 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:13 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:13 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:16 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:16 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:20 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:20 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:22 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:24 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:24 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:28 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:28 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:30 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:30 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:47 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:47 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:53 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:53 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:20:56 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:20:56 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:20:56 [info] index finished after resolve  [object Object] 
2025-03-26 17:20:56 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:00 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:00 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:13 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:13 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:17 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:17 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:20 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:20 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:22 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:22 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:25 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:25 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:27 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:27 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:30 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:30 [info] refresh page data from resolve listeners 0 655   
2025-03-26 17:21:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 17:21:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 17:21:32 [info] index finished after resolve  [object Object] 
2025-03-26 17:21:32 [info] refresh page data from resolve listeners 0 655   
2025-03-26 20:04:42 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-04-42.png  [object Object] 
2025-03-26 20:04:42 [info] refresh page data from created listeners 0 656   
2025-03-26 20:04:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:04:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:04:42 [info] index finished after resolve  [object Object] 
2025-03-26 20:04:42 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:20 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:20 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:22 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:25 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:25 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:29 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:29 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:31 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:31 [info] refresh page data from resolve listeners 0 656   
2025-03-26 20:08:32 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-08-31.png  [object Object] 
2025-03-26 20:08:32 [info] refresh page data from created listeners 0 657   
2025-03-26 20:08:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:34 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:34 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:39 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:43 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:43 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:45 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:45 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:47 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:47 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:49 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:49 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:49 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:52 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:52 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:52 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:52 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:54 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:54 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:56 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:56 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:56 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:56 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:08:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:08:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:08:58 [info] index finished after resolve  [object Object] 
2025-03-26 20:08:58 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:00 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:00 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:06 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:06 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:09 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:09 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:09 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:09 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:13 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:13 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:16 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:16 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:18 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:18 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:22 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:29 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:29 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:32 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:34 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:34 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:39 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:41 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:41 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:43 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:43 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:46 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:46 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:46 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:46 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:48 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:48 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:53 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:53 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:09:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:09:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:09:57 [info] index finished after resolve  [object Object] 
2025-03-26 20:09:57 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:10:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:10:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:10:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:10:02 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:10:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:10:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:10:04 [info] index finished after resolve  [object Object] 
2025-03-26 20:10:04 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:10:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:10:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:10:07 [info] index finished after resolve  [object Object] 
2025-03-26 20:10:07 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:25 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:25 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:27 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:27 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:29 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:29 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:31 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:31 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:33 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:36 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:36 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:38 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:38 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:11:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:11:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:11:41 [info] index finished after resolve  [object Object] 
2025-03-26 20:11:41 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:06 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:06 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:08 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:08 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:12 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:12 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:12 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:12 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:14 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:14 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:17 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:17 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:19 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:19 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:19 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:21 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:21 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:26 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:28 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:30 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:32 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:35 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:35 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:38 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:38 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:40 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:40 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:42 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:42 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:12:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:12:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:12:44 [info] index finished after resolve  [object Object] 
2025-03-26 20:12:44 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:45 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:45 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:48 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:48 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:50 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:50 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:52 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:52 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:52 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:52 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:55 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:55 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:13:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:13:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:13:58 [info] index finished after resolve  [object Object] 
2025-03-26 20:13:58 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:14:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:14:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:14:00 [info] index finished after resolve  [object Object] 
2025-03-26 20:14:00 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:14:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:14:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:14:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:14:02 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:15:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:24 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:24 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:15:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:26 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:15:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:28 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:15:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:31 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:31 [info] refresh page data from resolve listeners 0 657   
2025-03-26 20:15:31 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-15-31.png  [object Object] 
2025-03-26 20:15:31 [info] refresh page data from created listeners 0 658   
2025-03-26 20:15:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:33 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:15:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:37 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:37 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:15:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:39 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:15:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:15:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:15:41 [info] index finished after resolve  [object Object] 
2025-03-26 20:15:41 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:17 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:17 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:20 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:20 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:22 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:28 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:33 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:35 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:35 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:16:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:16:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:16:37 [info] index finished after resolve  [object Object] 
2025-03-26 20:16:37 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:26 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:28 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:30 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:32 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:36 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:36 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:38 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:38 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:40 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:40 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:43 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:43 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:46 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:46 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:46 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:46 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:48 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:48 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:50 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:50 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:54 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:54 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:17:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:17:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:17:59 [info] index finished after resolve  [object Object] 
2025-03-26 20:17:59 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:18:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:18:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:18:04 [info] index finished after resolve  [object Object] 
2025-03-26 20:18:04 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:39 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:43 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:43 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:53 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:53 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:55 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:55 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:57 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:57 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:19:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:19:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:19:59 [info] index finished after resolve  [object Object] 
2025-03-26 20:19:59 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:02 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:08 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:08 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:10 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:10 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:16 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:16 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:18 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:18 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:26 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:34 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:34 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:37 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:37 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:39 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:43 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:43 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:45 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:45 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:53 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:53 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:55 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:55 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:20:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:20:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:20:58 [info] index finished after resolve  [object Object] 
2025-03-26 20:20:58 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:00 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:00 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:02 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:05 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:05 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:07 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:07 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:09 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:09 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:09 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:09 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:11 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:11 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:13 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:13 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:16 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:16 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:18 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:18 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:20 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:20 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:22 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:25 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:25 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:27 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:27 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:29 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:29 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:32 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:37 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:37 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:21:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:21:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:21:40 [info] index finished after resolve  [object Object] 
2025-03-26 20:21:40 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:23:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:23:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:23:54 [info] index finished after resolve  [object Object] 
2025-03-26 20:23:54 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:23:56 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:23:56 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:23:56 [info] index finished after resolve  [object Object] 
2025-03-26 20:23:56 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:23:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:23:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:23:59 [info] index finished after resolve  [object Object] 
2025-03-26 20:23:59 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:24:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:24:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:24:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:24:02 [info] refresh page data from resolve listeners 0 658   
2025-03-26 20:24:12 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-24-12.png  [object Object] 
2025-03-26 20:24:12 [info] refresh page data from created listeners 0 659   
2025-03-26 20:24:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:24:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:24:14 [info] index finished after resolve  [object Object] 
2025-03-26 20:24:14 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:24:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:24:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:24:16 [info] index finished after resolve  [object Object] 
2025-03-26 20:24:16 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:24:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:24:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:24:18 [info] index finished after resolve  [object Object] 
2025-03-26 20:24:18 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:25:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:25:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:25:51 [info] index finished after resolve  [object Object] 
2025-03-26 20:25:51 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:25:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:25:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:25:54 [info] index finished after resolve  [object Object] 
2025-03-26 20:25:54 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:25:56 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:25:56 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:25:56 [info] index finished after resolve  [object Object] 
2025-03-26 20:25:56 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:25:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:25:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:25:58 [info] index finished after resolve  [object Object] 
2025-03-26 20:25:58 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:00 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:00 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:02 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:02 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:04 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:04 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:07 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:07 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:09 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:09 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:09 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:09 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:11 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:11 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:14 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:14 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:18 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:18 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:20 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:20 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:22 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:28 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:30 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:32 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:26:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:26:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:26:41 [info] index finished after resolve  [object Object] 
2025-03-26 20:26:41 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:26 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:26 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:26 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:28 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:28 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:28 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:30 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:30 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:30 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:33 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:33 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:33 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:37 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:37 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:37 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:37 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:40 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:40 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:40 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:40 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:42 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:42 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:42 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:42 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:45 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:45 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:45 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:45 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:47 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:47 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:47 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:47 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:49 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:49 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:49 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:49 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:51 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:51 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:51 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:51 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:53 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:53 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:53 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:53 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:28:56 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:28:56 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:28:56 [info] index finished after resolve  [object Object] 
2025-03-26 20:28:56 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:29:04 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:29:04 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:29:04 [info] index finished after resolve  [object Object] 
2025-03-26 20:29:04 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:29:07 [info] ignore file modify evnet 学习库/stm32/启动.md   
2025-03-26 20:29:07 [info] trigger 学习库/stm32/启动.md resolve  [object Object] 
2025-03-26 20:29:07 [info] index finished after resolve  [object Object] 
2025-03-26 20:29:07 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:47:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:47:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:47:51 [info] index finished after resolve  [object Object] 
2025-03-26 20:47:51 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:47:53 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:47:53 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:47:53 [info] index finished after resolve  [object Object] 
2025-03-26 20:47:53 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:47:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:47:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:47:55 [info] index finished after resolve  [object Object] 
2025-03-26 20:47:55 [info] refresh page data from resolve listeners 0 659   
2025-03-26 20:49:12 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-49-12.png  [object Object] 
2025-03-26 20:49:12 [info] refresh page data from created listeners 0 660   
2025-03-26 20:49:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:14 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:14 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:17 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:17 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:19 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:19 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:19 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:24 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:24 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:26 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:26 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:28 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:28 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:30 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:33 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:36 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:36 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:39 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:39 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:41 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:41 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:44 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:44 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:47 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:47 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:49 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:49 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:49 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:54 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:54 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:56 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:56 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:56 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:56 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:49:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:49:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:49:58 [info] index finished after resolve  [object Object] 
2025-03-26 20:49:58 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:50:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:50:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:50:33 [info] index finished after resolve  [object Object] 
2025-03-26 20:50:33 [info] refresh page data from resolve listeners 0 660   
2025-03-26 20:50:34 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-20-50-34.png  [object Object] 
2025-03-26 20:50:34 [info] refresh page data from created listeners 0 661   
2025-03-26 20:50:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:50:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:50:35 [info] index finished after resolve  [object Object] 
2025-03-26 20:50:35 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:52:10 [info] trigger 学习库/stm32/attachments/GPIO-2025-03-26-20-50-34.png resolve  [object Object] 
2025-03-26 20:52:10 [info] index finished after resolve  [object Object] 
2025-03-26 20:52:10 [info] refresh page data from modify listeners 0 661   
2025-03-26 20:52:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:52:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:52:25 [info] index finished after resolve  [object Object] 
2025-03-26 20:52:25 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:52:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:52:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:52:27 [info] index finished after resolve  [object Object] 
2025-03-26 20:52:27 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:53:01 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:53:01 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:53:01 [info] index finished after resolve  [object Object] 
2025-03-26 20:53:01 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:53:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:53:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:53:04 [info] index finished after resolve  [object Object] 
2025-03-26 20:53:04 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:06 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:06 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:06 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:06 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:10 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:10 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:14 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:14 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:22 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:27 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:27 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:30 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:30 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:32 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:32 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:34 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:34 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:38 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:38 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:40 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:40 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:42 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:42 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:54:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:54:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:54:44 [info] index finished after resolve  [object Object] 
2025-03-26 20:54:44 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:13 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:13 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:15 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:15 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:19 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:19 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:22 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:22 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:24 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:24 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:34 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:34 [info] refresh page data from resolve listeners 0 661   
2025-03-26 20:55:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 20:55:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 20:55:38 [info] index finished after resolve  [object Object] 
2025-03-26 20:55:38 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:02:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:02:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:02:08 [info] index finished after resolve  [object Object] 
2025-03-26 21:02:08 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:02:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:02:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:02:11 [info] index finished after resolve  [object Object] 
2025-03-26 21:02:11 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:02:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:02:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:02:15 [info] index finished after resolve  [object Object] 
2025-03-26 21:02:15 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:02:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:02:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:02:17 [info] index finished after resolve  [object Object] 
2025-03-26 21:02:17 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:05:18 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-05-18.png  [object Object] 
2025-03-26 21:05:18 [info] refresh page data from created listeners 0 662   
2025-03-26 21:05:19 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:05:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:05:20 [info] index finished after resolve  [object Object] 
2025-03-26 21:05:20 [info] refresh page data from resolve listeners 0 662   
2025-03-26 21:07:19 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-07-19.png  [object Object] 
2025-03-26 21:07:19 [info] refresh page data from created listeners 0 663   
2025-03-26 21:07:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:21 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:21 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:25 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:25 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:27 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:27 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:29 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:29 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:29 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:29 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:31 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:31 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:33 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:33 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:36 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:36 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:38 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:38 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:40 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:40 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:42 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:42 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:44 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:44 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:47 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:47 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:47 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:47 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:07:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:07:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:07:50 [info] index finished after resolve  [object Object] 
2025-03-26 21:07:50 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:08:40 [info] refresh page data from delete listeners 0 662   
2025-03-26 21:08:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:08:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:08:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:08:42 [info] index finished after resolve  [object Object] 
2025-03-26 21:08:42 [info] refresh page data from resolve listeners 0 662   
2025-03-26 21:08:43 [info] refresh page data from delete listeners 0 661   
2025-03-26 21:08:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:08:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:08:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:08:44 [info] index finished after resolve  [object Object] 
2025-03-26 21:08:44 [info] refresh page data from resolve listeners 0 661   
2025-03-26 21:08:56 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-08-56.png  [object Object] 
2025-03-26 21:08:57 [info] refresh page data from created listeners 0 662   
2025-03-26 21:08:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:08:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:08:58 [info] index finished after resolve  [object Object] 
2025-03-26 21:08:58 [info] refresh page data from resolve listeners 0 662   
2025-03-26 21:19:05 [info] indexing created file 学习库/stm32/attachments/GPIO-2025-03-26-21-19-05.png  [object Object] 
2025-03-26 21:19:05 [info] refresh page data from created listeners 0 663   
2025-03-26 21:19:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:07 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:07 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:10 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:10 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:21 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:21 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:23 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:23 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:25 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:25 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:27 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:27 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:27 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:30 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:30 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:32 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:32 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:34 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:34 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:34 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:36 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:36 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:39 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:39 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:41 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:41 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:43 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:43 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:43 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:43 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:46 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:46 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:46 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:46 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:49 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:49 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:49 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:49 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:51 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:51 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:55 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:55 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:55 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:55 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:57 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:57 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:19:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:19:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:19:59 [info] index finished after resolve  [object Object] 
2025-03-26 21:19:59 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:20:01 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:20:01 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:20:01 [info] index finished after resolve  [object Object] 
2025-03-26 21:20:01 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:20:03 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:20:03 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:20:03 [info] index finished after resolve  [object Object] 
2025-03-26 21:20:03 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:40:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:40:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:40:44 [info] index finished after resolve  [object Object] 
2025-03-26 21:40:44 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:40:46 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:40:46 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:40:46 [info] index finished after resolve  [object Object] 
2025-03-26 21:40:46 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:40:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:40:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:40:48 [info] index finished after resolve  [object Object] 
2025-03-26 21:40:48 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:40:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:40:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:40:50 [info] index finished after resolve  [object Object] 
2025-03-26 21:40:50 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:40:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:40:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:40:59 [info] index finished after resolve  [object Object] 
2025-03-26 21:40:59 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:02 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:02 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:02 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:02 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:05 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:05 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:07 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:07 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:10 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:10 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:10 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:10 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:12 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:12 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:12 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:12 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:15 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:15 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:15 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:15 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:17 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:17 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:17 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:17 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:19 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:19 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:19 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:22 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:22 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:24 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:24 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:24 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:24 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:26 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:26 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:26 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:26 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:28 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:28 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:30 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:30 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:33 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:33 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:36 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:36 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:36 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:36 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:38 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:38 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:40 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:40 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:42 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:42 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:45 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:45 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:45 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:45 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:48 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:48 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:50 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:50 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:52 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:52 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:52 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:52 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:54 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:54 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:41:58 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:41:58 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:41:58 [info] index finished after resolve  [object Object] 
2025-03-26 21:41:58 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:07 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:07 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:07 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:07 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:09 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:09 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:09 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:09 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:11 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:11 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:13 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:13 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:16 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:16 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:18 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:18 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:21 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:21 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:23 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:23 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:25 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:25 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:28 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:28 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:30 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:30 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:32 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:32 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:32 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:32 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:35 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:35 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:39 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:39 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:39 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:39 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:41 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:41 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:41 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:41 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:46 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:46 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:46 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:46 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:48 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:48 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:48 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:48 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:51 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:51 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:51 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:51 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:54 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:54 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:57 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:57 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:57 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:57 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:42:59 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:42:59 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:42:59 [info] index finished after resolve  [object Object] 
2025-03-26 21:42:59 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:01 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:01 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:01 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:01 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:03 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:03 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:03 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:03 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:05 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:05 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:05 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:05 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:08 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:08 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:08 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:08 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:11 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:11 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:13 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:13 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:16 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:16 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:18 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:18 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:18 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:20 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:20 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:20 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:20 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:43:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:43:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:43:23 [info] index finished after resolve  [object Object] 
2025-03-26 21:43:23 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:22 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:22 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:22 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:22 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:25 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:25 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:33 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:33 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:35 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:35 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:38 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:38 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:38 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:38 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:40 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:40 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:40 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:40 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:42 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:42 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:42 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:42 [info] refresh page data from resolve listeners 0 663   
2025-03-26 21:49:44 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-26 21:49:44 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-26 21:49:44 [info] index finished after resolve  [object Object] 
2025-03-26 21:49:44 [info] refresh page data from resolve listeners 0 663   
