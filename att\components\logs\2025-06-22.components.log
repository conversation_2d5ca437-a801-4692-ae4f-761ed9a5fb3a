2025-06-22 16:39:56.132 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-06-22 16:39:56.138 [info] components database created cost 7 ms   
2025-06-22 16:39:56.139 [info] components index initializing...   
2025-06-22 16:39:56.616 [info] start to batch put pages: 5   
2025-06-22 16:39:56.619 [info] batch persist cost 5  3 
2025-06-22 16:39:56.657 [info] components index initialized, 907 files cost 526 ms   
2025-06-22 16:39:56.658 [info] refresh page data from init listeners 0 907   
2025-06-22 16:39:57.618 [info] indexing created file components/logs/2025-06-22.components.log  [object Object] 
2025-06-22 16:39:57.646 [info] refresh page data from created listeners 0 908   
2025-06-22 16:39:57.915 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-22 16:39:58.216 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-22 16:39:59.152 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-22 16:39:59.186 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-22 16:39:59.210 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-22 16:39:59.213 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-22 16:40:01.652 [info] indexing created file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md  [object Object] 
2025-06-22 16:40:01.653 [info] indexing created ignore file 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md   
2025-06-22 16:40:01.712 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md resolve  [object Object] 
2025-06-22 16:40:01.716 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:01.717 [info] refresh page data from resolve listeners 0 909   
2025-06-22 16:40:03.439 [info] indexing created file components/logs/2025-06-17.components.log  [object Object] 
2025-06-22 16:40:03.442 [info] refresh page data from created listeners 0 910   
2025-06-22 16:40:03.491 [info] refresh page data from delete listeners 0 909   
2025-06-22 16:40:05.064 [info] indexing created file 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md  [object Object] 
2025-06-22 16:40:05.064 [info] indexing created ignore file 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md   
2025-06-22 16:40:05.123 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集（Dataset，DataLoader）.md resolve  [object Object] 
2025-06-22 16:40:05.126 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:05.128 [info] refresh page data from resolve listeners 0 910   
2025-06-22 16:40:07.915 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-09-35-32.png  [object Object] 
2025-06-22 16:40:07.918 [info] refresh page data from created listeners 0 911   
2025-06-22 16:40:10.571 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-10-12-17.png  [object Object] 
2025-06-22 16:40:10.577 [info] refresh page data from created listeners 0 912   
2025-06-22 16:40:12.444 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-10-31-59.png  [object Object] 
2025-06-22 16:40:12.448 [info] refresh page data from created listeners 0 913   
2025-06-22 16:40:13.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-06-22 16:40:13.089 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-06-22 16:40:13.093 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:13.095 [info] refresh page data from resolve listeners 0 913   
2025-06-22 16:40:16.328 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-11-22-04.png  [object Object] 
2025-06-22 16:40:16.334 [info] refresh page data from created listeners 0 914   
2025-06-22 16:40:18.062 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-06-22 16:40:18.086 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-06-22 16:40:18.088 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:18.090 [info] refresh page data from resolve listeners 0 914   
2025-06-22 16:40:20.148 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-15-20-52.png  [object Object] 
2025-06-22 16:40:20.155 [info] refresh page data from created listeners 0 915   
2025-06-22 16:40:20.793 [info] indexing created file crossentropy_analysis.py  [object Object] 
2025-06-22 16:40:20.798 [info] refresh page data from created listeners 0 916   
2025-06-22 16:40:21.305 [info] indexing created file likelihood_demo.py  [object Object] 
2025-06-22 16:40:21.308 [info] refresh page data from created listeners 0 917   
2025-06-22 16:40:22.144 [info] indexing created file logits_explanation.py  [object Object] 
2025-06-22 16:40:22.148 [info] refresh page data from created listeners 0 918   
2025-06-22 16:40:22.650 [info] indexing created file nll_vs_crossentropy.py  [object Object] 
2025-06-22 16:40:22.656 [info] refresh page data from created listeners 0 919   
2025-06-22 16:40:23.330 [info] indexing created file comparison  [object Object] 
2025-06-22 16:40:23.334 [info] refresh page data from created listeners 0 920   
2025-06-22 16:40:24.249 [info] trigger 学习库/Deep learning/pytorch/LinearModel.py resolve  [object Object] 
2025-06-22 16:40:24.252 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:24.256 [info] refresh page data from modify listeners 0 920   
2025-06-22 16:40:27.686 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-16-47-41.png  [object Object] 
2025-06-22 16:40:27.689 [info] refresh page data from created listeners 0 921   
2025-06-22 16:40:30.047 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-12-30.png  [object Object] 
2025-06-22 16:40:30.052 [info] refresh page data from created listeners 0 922   
2025-06-22 16:40:30.574 [info] indexing created file tensor_reshape_demo.py  [object Object] 
2025-06-22 16:40:30.578 [info] refresh page data from created listeners 0 923   
2025-06-22 16:40:33.279 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-33-09.png  [object Object] 
2025-06-22 16:40:33.282 [info] refresh page data from created listeners 0 924   
2025-06-22 16:40:35.574 [info] indexing created file deep_network_flow.py  [object Object] 
2025-06-22 16:40:35.578 [info] refresh page data from created listeners 0 925   
2025-06-22 16:40:38.811 [info] indexing created file 学习库/Deep learning/pytorch/attachments/8. 多分类问题 (Softmax Classifier)-2025-06-18-17-46-45.png  [object Object] 
2025-06-22 16:40:38.819 [info] refresh page data from created listeners 0 926   
2025-06-22 16:40:40.355 [info] indexing created file 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md  [object Object] 
2025-06-22 16:40:40.355 [info] indexing created ignore file 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md   
2025-06-22 16:40:40.391 [info] trigger 学习库/Deep learning/pytorch/8. 多分类问题 (Softmax Classifier).md resolve  [object Object] 
2025-06-22 16:40:40.394 [info] index finished after resolve  [object Object] 
2025-06-22 16:40:40.396 [info] refresh page data from resolve listeners 0 927   
2025-06-22 16:40:43.407 [info] indexing created file components/logs/2025-06-18.components.log  [object Object] 
2025-06-22 16:40:43.419 [info] refresh page data from created listeners 0 928   
2025-06-22 16:40:44.113 [info] indexing created file components/logs/2025-06-21.components.log  [object Object] 
2025-06-22 16:40:44.116 [info] refresh page data from created listeners 0 929   
