2025-06-25 10:01:46.991 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-06-25 10:01:46.991 [info] components database created cost 1 ms   
2025-06-25 10:01:46.992 [info] components index initializing...   
2025-06-25 10:01:47.192 [info] start to batch put pages: 2   
2025-06-25 10:01:47.231 [info] batch persist cost 2  39 
2025-06-25 10:01:47.271 [info] components index initialized, 930 files cost 281 ms   
2025-06-25 10:01:47.271 [info] refresh page data from init listeners 0 930   
2025-06-25 10:01:48.642 [info] indexing created file components/logs/2025-06-25.components.log  [object Object] 
2025-06-25 10:01:48.648 [info] refresh page data from created listeners 0 931   
2025-06-25 10:01:48.823 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-25 10:01:49.376 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-25 10:01:49.528 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-25 10:01:49.538 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-25 10:01:49.542 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-25 10:01:49.546 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-25 10:02:20.301 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:20.344 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:20.347 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:20.348 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:27.930 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:27.974 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:27.975 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:27.976 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:31.168 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:31.201 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:31.202 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:31.203 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:33.678 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:33.715 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:33.717 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:33.717 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:35.783 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:35.821 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:35.822 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:35.824 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:51.782 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:51.818 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:51.820 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:51.821 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:02:56.923 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:02:56.982 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:02:56.988 [info] index finished after resolve  [object Object] 
2025-06-25 10:02:56.989 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:01.951 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:01.991 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:01.993 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:01.994 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:04.134 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:04.191 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:04.193 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:04.194 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:06.293 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:06.350 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:06.352 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:06.352 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:08.410 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:08.474 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:08.477 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:08.478 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:10.725 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:10.787 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:10.788 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:10.789 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:13.626 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:13.721 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:13.723 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:13.724 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:31.343 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:31.348 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:31.349 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:31.350 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:38.637 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:38.689 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:38.694 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:38.697 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:51.973 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:52.010 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:52.013 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:52.014 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:54.242 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:54.289 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:54.290 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:54.291 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:56.285 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:56.343 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:56.343 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:56.344 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:03:58.581 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:03:58.634 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:03:58.636 [info] index finished after resolve  [object Object] 
2025-06-25 10:03:58.637 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:04:00.758 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:04:00.814 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:04:00.815 [info] index finished after resolve  [object Object] 
2025-06-25 10:04:00.816 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:04:02.983 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:04:03.034 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:04:03.036 [info] index finished after resolve  [object Object] 
2025-06-25 10:04:03.037 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:04:05.108 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:04:05.114 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:04:05.114 [info] index finished after resolve  [object Object] 
2025-06-25 10:04:05.115 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:04:45.230 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:04:45.273 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:04:45.274 [info] index finished after resolve  [object Object] 
2025-06-25 10:04:45.275 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:05:16.511 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:05:16.545 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:05:16.554 [info] index finished after resolve  [object Object] 
2025-06-25 10:05:16.559 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:05:19.592 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:05:19.597 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:05:19.599 [info] index finished after resolve  [object Object] 
2025-06-25 10:05:19.600 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:01.905 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:01.909 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:01.910 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:01.911 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:18.477 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:18.567 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:18.581 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:18.582 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:24.531 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:24.536 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:24.538 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:24.539 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:31.567 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:31.572 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:31.572 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:31.573 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:38.239 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:38.244 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:38.245 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:38.246 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:07:53.161 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:07:53.166 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:07:53.167 [info] index finished after resolve  [object Object] 
2025-06-25 10:07:53.168 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:08.694 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:08.699 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:08.700 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:08.701 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:10.975 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:10.980 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:10.981 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:10.983 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:33.690 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:33.775 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:33.778 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:33.781 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:42.854 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:42.859 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:42.860 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:42.861 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:45.295 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:45.300 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:45.301 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:45.302 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:08:47.626 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:08:47.630 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:08:47.631 [info] index finished after resolve  [object Object] 
2025-06-25 10:08:47.632 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:09:31.376 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:09:31.380 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:09:31.381 [info] index finished after resolve  [object Object] 
2025-06-25 10:09:31.382 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:09:37.538 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:09:37.541 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:09:37.543 [info] index finished after resolve  [object Object] 
2025-06-25 10:09:37.544 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:09:48.609 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:09:48.615 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:09:48.616 [info] index finished after resolve  [object Object] 
2025-06-25 10:09:48.617 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:09:51.385 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:09:51.394 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:09:51.395 [info] index finished after resolve  [object Object] 
2025-06-25 10:09:51.396 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:03.789 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:03.794 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:03.796 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:03.797 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:07.826 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:07.830 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:07.831 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:07.832 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:10.103 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:10.108 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:10.109 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:10.110 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:28.126 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:28.130 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:28.131 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:28.132 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:40.586 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:40.591 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:40.592 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:40.593 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:47.643 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:47.649 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:47.650 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:47.651 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:10:56.262 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:10:56.267 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:10:56.268 [info] index finished after resolve  [object Object] 
2025-06-25 10:10:56.269 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:11:05.745 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:11:05.749 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:11:05.751 [info] index finished after resolve  [object Object] 
2025-06-25 10:11:05.752 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:12.977 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:12.983 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:12.984 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:12.985 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:20.013 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:20.018 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:20.019 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:20.020 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:23.140 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:23.147 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:23.148 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:23.150 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:25.389 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:25.465 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:25.466 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:25.467 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:30.387 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:30.392 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:30.394 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:30.396 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:12:36.133 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:12:36.138 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:12:36.140 [info] index finished after resolve  [object Object] 
2025-06-25 10:12:36.142 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:26.164 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:26.229 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:26.230 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:26.231 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:31.182 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:31.186 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:31.187 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:31.188 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:36.357 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:36.394 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:36.397 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:36.398 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:43.438 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:43.443 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:43.444 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:43.445 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:46.999 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:47.004 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:47.005 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:47.006 [info] refresh page data from resolve listeners 0 931   
2025-06-25 10:15:49.719 [debug] ignore file modify evnet 工作库/项目/舌诊/裂纹舌.md   
2025-06-25 10:15:49.723 [info] trigger 工作库/项目/舌诊/裂纹舌.md resolve  [object Object] 
2025-06-25 10:15:49.724 [info] index finished after resolve  [object Object] 
2025-06-25 10:15:49.725 [info] refresh page data from resolve listeners 0 931   
