---
tags:
  - 学习
  - linux
  - 命令行
---

# 常用Linux命令参考手册

> [!info] 说明
> 本文档整理了Linux系统中最常用的命令，按功能分类，便于快速查找和学习。

## 📁 文件与目录操作

### 基础操作
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ls` | 列出目录内容 | `-l` 详细信息<br>`-a` 显示隐藏文件<br>`-h` 人类可读格式<br>`-t` 按时间排序 | `ls -la`<br>`ls -lht` |
| `cd` | 切换目录 | `..` 上级目录<br>`~` 家目录<br>`-` 上次目录 | `cd /home/<USER>
| `pwd` | 显示当前路径 | | `pwd` |

### 创建与删除
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `mkdir` | 创建目录 | `-p` 创建多级目录 | `mkdir -p dir1/dir2/dir3` |
| `rmdir` | 删除空目录 | | `rmdir empty_dir` |
| `rm` | 删除文件/目录 | `-r` 递归删除<br>`-f` 强制删除<br>`-i` 交互确认 | `rm -rf directory`<br>`rm -i file.txt` |
| `touch` | 创建空文件 | | `touch newfile.txt` |

### 复制与移动
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `cp` | 复制文件/目录 | `-r` 递归复制<br>`-i` 交互确认<br>`-v` 显示详情 | `cp -r source/ dest/`<br>`cp file1.txt file2.txt` |
| `mv` | 移动/重命名 | `-i` 交互确认<br>`-v` 显示详情 | `mv oldname.txt newname.txt`<br>`mv file.txt /path/to/` |

### 查找与链接
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `find` | 查找文件 | `-name` 按名称<br>`-type` 按类型<br>`-mtime` 按修改时间 | `find . -name "*.txt"`<br>`find /home -type d -name "docs"` |
| `ln` | 创建链接 | `-s` 软链接 | `ln -s /path/to/file symlink`<br>`ln file.txt hardlink` |

## 📖 文件内容查看

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `cat` | 显示文件内容 | `-n` 显示行号 | `cat -n file.txt` |
| `less` | 分页查看文件 | `q` 退出<br>`/` 搜索<br>`G` 末尾 | `less largefile.txt` |
| `more` | 分页查看文件 | 空格键翻页 | `more file.txt` |
| `head` | 显示文件开头 | `-n` 指定行数 | `head -n 20 file.txt` |
| `tail` | 显示文件结尾 | `-n` 指定行数<br>`-f` 实时跟踪 | `tail -f /var/log/syslog`<br>`tail -n 50 file.txt` |
| `grep` | 搜索文本 | `-i` 忽略大小写<br>`-v` 反向匹配<br>`-n` 显示行号 | `grep -in "error" log.txt`<br>`grep -v "debug" file.txt` |
| `wc` | 统计文件信息 | `-l` 行数<br>`-w` 单词数<br>`-c` 字符数 | `wc -l file.txt` |

## 🔧 文本处理

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `sort` | 文本排序 | `-r` 逆序<br>`-n` 数字排序<br>`-u` 去重 | `sort -nr numbers.txt`<br>`sort -u names.txt` |
| `uniq` | 去除重复行 | `-c` 统计重复次数<br>`-d` 只显示重复行 | `uniq -c sorted.txt` |
| `cut` | 提取字段 | `-d` 分隔符<br>`-f` 字段号 | `cut -d ',' -f 1,3 data.csv` |
| `tr` | 字符转换 | | `tr 'a-z' 'A-Z' < file.txt`<br>`tr -d ' ' < file.txt` |
| `sed` | 流编辑器 | `-i` 直接修改文件<br>`s///g` 全局替换 | `sed 's/old/new/g' file.txt`<br>`sed -i 's/foo/bar/g' file.txt` |
| `awk` | 文本处理语言 | | `awk '{print $1, $3}' file.txt`<br>`awk -F',' '{print $2}' data.csv` |

## 💻 系统信息与进程

### 系统信息
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `uname` | 系统信息 | `-a` 全部信息<br>`-s` 系统名<br>`-r` 内核版本 | `uname -a` |
| `whoami` | 当前用户 | | `whoami` |
| `date` | 日期时间 | `+%Y-%m-%d` 格式化 | `date +"%Y-%m-%d %H:%M:%S"` |
| `uptime` | 系统运行时间 | | `uptime` |
| `history` | 命令历史 | | `history | grep ssh` |

### 进程管理
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ps` | 显示进程 | `aux` 详细信息<br>`-ef` 全格式 | `ps aux | grep nginx` |
| `top` | 动态进程信息 | `q` 退出<br>`k` 杀死进程 | `top` |
| `htop` | 增强版top | 需要安装 | `htop` |
| `kill` | 终止进程 | `-9` 强制终止<br>`-15` 正常终止 | `kill -9 1234`<br>`killall firefox` |

### 磁盘与内存
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `df` | 磁盘使用情况 | `-h` 人类可读<br>`-T` 显示文件系统类型 | `df -h` |
| `du` | 目录大小 | `-sh` 总大小<br>`-h` 人类可读 | `du -sh /home/<USER>
| `free` | 内存使用情况 | `-h` 人类可读<br>`-m` MB单位 | `free -h` |

## 🌐 网络命令

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ping` | 测试网络连通性 | `-c` 次数<br>`-i` 间隔 | `ping -c 4 google.com` |
| `wget` | 下载文件 | `-O` 指定文件名<br>`-c` 断点续传 | `wget -O file.zip https://example.com/file.zip` |
| `curl` | 数据传输工具 | `-o` 输出到文件<br>`-L` 跟随重定向 | `curl -L -o page.html https://example.com` |
| `ssh` | 远程登录 | `-p` 端口<br>`-i` 密钥文件 | `ssh -p 2222 <EMAIL>` |
| `scp` | 远程文件复制 | `-r` 递归<br>`-P` 端口 | `scp -r local_dir user@server:/remote/path` |

### 网络状态
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ifconfig` | 网络接口配置 | | `ifconfig eth0` |
| `ip` | 现代网络工具 | `addr` 地址<br>`route` 路由 | `ip addr show`<br>`ip route` |
| `netstat` | 网络连接状态 | `-tulnp` 监听端口 | `netstat -tulnp | grep :80` |
| `ss` | 现代netstat | `-tulnp` 监听端口 | `ss -tulnp` |

## 🔐 权限管理

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `chmod` | 修改文件权限 | `755` 常用权限<br>`+x` 添加执行权限 | `chmod 755 script.sh`<br>`chmod +x file` |
| `chown` | 修改所有者 | `-R` 递归 | `chown user:group file.txt`<br>`chown -R user:group directory/` |
| `sudo` | 超级用户权限 | `-u` 指定用户 | `sudo apt update`<br>`sudo -u www-data command` |

> [!tip] 权限数字说明
> - `4` = 读取 (r)
> - `2` = 写入 (w) 
> - `1` = 执行 (x)
> - `755` = 所有者(rwx) + 组(rx) + 其他(rx)
> - `644` = 所有者(rw) + 组(r) + 其他(r)

## 📦 压缩与归档

| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `tar` | 归档工具 | `-c` 创建<br>`-x` 解压<br>`-v` 详细<br>`-f` 文件<br>`-z` gzip压缩 | `tar -czvf archive.tar.gz files/`<br>`tar -xzvf archive.tar.gz` |
| `gzip` | 压缩文件 | `-d` 解压 | `gzip file.txt`<br>`gzip -d file.txt.gz` |
| `zip` | 创建zip文件 | `-r` 递归 | `zip -r archive.zip directory/` |
| `unzip` | 解压zip文件 | `-d` 指定目录 | `unzip archive.zip -d /target/dir` |

> [!tip] tar常用组合
> - `tar -czvf` = 创建gzip压缩归档
> - `tar -xzvf` = 解压gzip归档
> - `tar -cjvf` = 创建bzip2压缩归档
> - `tar -xjvf` = 解压bzip2归档

## 🛠️ 实用工具

| 命令 | 功能 | 示例 |
|------|------|------|
| `man` | 查看命令手册 | `man ls`<br>`man 5 passwd` |
| `which` | 查找命令路径 | `which python` |
| `whereis` | 查找命令、源码、手册 | `whereis gcc` |
| `alias` | 创建命令别名 | `alias ll='ls -la'`<br>`alias grep='grep --color=auto'` |
| `echo` | 输出文本 | `echo "Hello World"`<br>`echo $PATH` |
| `clear` | 清屏 | `clear` 或 `Ctrl+L` |

## 📋 Ubuntu换源指南

> [!warning] 操作前备份
> 在修改软件源之前，务必备份原始配置文件。

### 1. 备份原始源文件
```bash
sudo cp /etc/apt/sources.list /etc/apt/sources.list.bak
```

### 2. 编辑源文件
```bash
sudo vim /etc/apt/sources.list
```

> [!tip] Vim快速操作
> - 按 `ESC` 进入普通模式
> - 输入 `gg` 跳转到第一行
> - 按 `V` 进入可视化模式
> - 输入 `G` 选中全部内容
> - 按 `d` 删除所有内容

### 3. 添加阿里云源 (Ubuntu 20.04 Focal)
```bash
deb https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
```

### 4. 更新软件包列表
```bash
sudo apt update && sudo apt upgrade
```

> [!info] 其他版本
> 将 `focal` 替换为对应版本代号：
> - Ubuntu 18.04: `bionic`
> - Ubuntu 22.04: `jammy`
> - Ubuntu 24.04: `noble`

## ⌨️ Vim编辑器常用命令

> [!info] 模式说明
> Vim有三种主要模式：
> - **普通模式** (Normal): 默认模式，用于导航和命令
> - **插入模式** (Insert): 用于编辑文本
> - **可视模式** (Visual): 用于选择文本

### 基本操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `i` | 进入插入模式 | 在光标前插入 |
| `a` | 进入插入模式 | 在光标后插入 |
| `o` | 进入插入模式 | 在下方新建行 |
| `O` | 进入插入模式 | 在上方新建行 |
| `ESC` | 退出到普通模式 | |
| `:w` | 保存文件 | |
| `:q` | 退出 | |
| `:wq` | 保存并退出 | |
| `:q!` | 强制退出不保存 | |

### 光标移动
| 命令 | 功能 | 说明 |
|------|------|------|
| `h` `j` `k` `l` | 左下上右移动 | 基本方向键 |
| `w` | 下一个单词开头 | word |
| `b` | 上一个单词开头 | back |
| `e` | 下一个单词结尾 | end |
| `0` | 行首 | |
| `^` | 行首非空字符 | |
| `$` | 行尾 | |
| `gg` | 文件开头 | |
| `G` | 文件结尾 | |
| `:n` | 跳转到第n行 | 如 `:10` |

### 编辑操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `x` | 删除当前字符 | |
| `dd` | 删除当前行 | |
| `dw` | 删除单词 | |
| `d$` | 删除到行尾 | |
| `yy` | 复制当前行 | |
| `yw` | 复制单词 | |
| `p` | 粘贴到光标后 | |
| `P` | 粘贴到光标前 | |
| `u` | 撤销 | |
| `Ctrl+r` | 重做 | |

### 搜索与替换
| 命令 | 功能 | 说明 |
|------|------|------|
| `/pattern` | 向前搜索 | |
| `?pattern` | 向后搜索 | |
| `n` | 下一个匹配 | |
| `N` | 上一个匹配 | |
| `:s/old/new/` | 替换当前行第一个 | |
| `:s/old/new/g` | 替换当前行所有 | |
| `:%s/old/new/g` | 替换全文所有 | |
| `:%s/old/new/gc` | 替换全文所有(确认) | |

### 可视模式
| 命令 | 功能 | 说明 |
|------|------|------|
| `v` | 字符选择模式 | |
| `V` | 行选择模式 | |
| `Ctrl+v` | 块选择模式 | |
| `y` | 复制选中内容 | |
| `d` | 删除选中内容 | |

### 窗口操作
| 命令 | 功能 | 说明 |
|------|------|------|
| `:sp` | 水平分割窗口 | |
| `:vs` | 垂直分割窗口 | |
| `Ctrl+w h/j/k/l` | 切换窗口 | |
| `Ctrl+w q` | 关闭当前窗口 | |

> [!tip] Vim学习建议
> 1. 先掌握基本的移动和编辑命令
> 2. 逐步学习更高级的功能
> 3. 使用 `vimtutor` 命令进行交互式学习
> 4. 配置 `.vimrc` 文件个性化设置

---

> [!success] 总结
> 这份Linux命令参考手册涵盖了日常使用中最重要的命令。建议：
> 1. **循序渐进**：先掌握基础命令，再学习高级功能
> 2. **多加练习**：在实际环境中反复使用这些命令
> 3. **善用帮助**：使用 `man` 命令查看详细文档
> 4. **创建别名**：为常用命令创建简短的别名提高效率
