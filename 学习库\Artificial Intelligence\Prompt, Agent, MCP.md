---
tags:
  - 学习
  - AI
  - Prompt
  - Agent
  - MCP
---

# AI 核心概念：Prompt, Agent, MCP

> 本文档介绍了现代 AI 系统的三个核心概念：Prompt（提示）、Agent（智能体）和 MCP（模型上下文协议），以及它们之间的关系和演进。

---

## 🎯 Prompt（提示）

```ad-info
title: 定义
color: 0, 123, 255
Prompt 是一种用于与 AI 模型进行交互的输入方式。它可以是一个问题、指令或任何形式的文本，旨在引导模型生成相关的输出。
```

### 📝 Prompt 的类型

#### 1. User Prompt（用户提示）
- **定义**：用户输入的文本，用于向 AI 模型提出问题或请求
- **特点**：自然语言形式，模型根据输入生成相应回答

```ad-example
title: 示例
color: 40, 167, 69
**用户输入**："我肚子疼"
**模型回答**："请问你有什么症状？"或"你需要看医生吗？"等通用的回答
```

#### 2. System Prompt（系统提示）
- **定义**：系统预设的文本，用于指导模型的行为和输出
- **作用**：它为模型提供了一个高级框架，使其能够在特定情境下更精准地理解和响应用户意图。

```ad-example
title: 对比示例
color: 40, 167, 69
**System Prompt**："你是用户的女朋友，你需要关心用户的健康"
**User Prompt**："我肚子疼"
**模型回答**："亲爱的，听到你这么说我很难过。能具体告诉我哪里不舒服吗？是绞痛还是隐痛？我们一起来判断情况，看看是否需要马上就医。"
```

---

## 🤖 Agent（智能体）

```ad-warning
title: 从语言模型到智能体的演进
color: 255, 193, 7
传统的大语言模型（LLM）更像一个知识渊博的“对话者”，能告诉我们“如何做”，但无法“直接做”。**AI Agent (智能体)** 则是一个质的飞跃，它是一个被赋予了感知、思考和行动能力的完整系统，能够自主地执行任务以达成目标。
```

### 🧠 Agent 的核心能力

Agent 可以根据用户的指令和上下文信息，自动执行一系列操作来完成任务。其核心能力包括：

#### 1. 感知/观察（Perception/Observation）
- **功能**：智能体通过各种"感官"从环境中收集信息
- **数据源**：
  - 用户输入的自然语言
  - API 返回的数据
  - 摄像头捕捉的图像
  - 其他传感器数据

#### 2. 推理/规划（Reasoning/Planning）
- **核心**：由大型语言模型（LLM）驱动的"大脑"
- **能力**：
  - 处理感知到的信息
  - 进行逻辑推理
  - 将复杂的高层目标 **分解(decompose)** 为可执行的子任务

#### 3. 行动（Action）
- **执行**：根据规划结果执行具体操作
- **重点**：不仅仅是生成文本，更重要的是**工具使用（tool use）**
- **举例**：
  - 调用 API 预订机票或查询天气。
  - 使用浏览器搜索和抓取信息。
  - 数据库查询
  - 控制物理设备，如机器人手臂

### 🎯 自主性（Autonomy）- Agent 的核心特征

```ad-important
title: Agent vs 聊天机器人
color: 220, 53, 69
- **聊天机器人**：被动响应，只对用户直接提示做出反应
- **AI 智能体**：主动执行，被赋予目标后能独立制定并执行计划，遇到意外时调整策略，持续行动直到目标完成
```

### 📋 实际应用示例

```ad-example
title: 机票预订场景
color: 23, 162, 184
**用户指令**："帮我订一张明天从深圳去北京的机票"

**Agent 执行流程**：
1. **思考**：我需要为用户预定机票，首先需要查找航班信息
2. **搜索**：调用“航班查询API”或使用“网络浏览器”工具，搜索“明天 深圳->北京 航班”，获取时刻表、航司、价格等信息。
3. **分析**：根据用户的预算和时间要求，选择最合适的航班
4. **执行**：使用预订工具完成机票的预订
```

---

## 🔗 MCP (模型上下文协议，Model Context Protocol)

```ad-info
title: 什么是 MCP？
color: 111, 66, 193
MCP 就像是 `USB` 或者 `HTTP` 协议一样，提供了一种标准化的方式来连接和使用不同的 LLM 和 Tools。它允许我们在运行时动态地加载和卸载 LLM 和 Tools，从而使得 Agent 的能力更加灵活和可扩展。
```

```ad-col2
title: 传统 Agent 对比 MCP
color:178,22,164
**传统 Agent**：
- **固定配置**：每个 Agent 的 LLM 和 Tools 都是固定的
- **缺乏灵活性**：无法在运行时更换或添加新能力

**MCP**:
- **动态切换 LLM**：比如运行时从 GPT-4 切换到 GPT-3.5
- **动态添加工具**：运行时添加新的 API 或数据库查询工具
- **标准化接口**：提供统一的连接方式

```


### 🏗️ MCP 架构

如下所示 MCP 采用的是 Client-Server 架构，MCP 是中间的通信协议，调用各种工具和资源的 Agent 叫做 MCP-Client，而提供服务和各种资源的是 MCP-Server
```mermaid
graph TB
    A[主机 Host] --> B[MCP 客户端]
    B --> C[MCP 服务器 1]
    B --> D[MCP 服务器 2]
    B --> E[MCP 服务器 N]
    
    C --> F[工具 Tools]
    C --> G[资源 Resources]
    C --> H[提示 Prompts]
    
    D --> I[GitHub 仓库]
    E --> J[PostgreSQL 数据库]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#e8f5e8
    style E fill:#e8f5e8
```

#### 核心组件

1. **主机（Host）**
   - 运行 AI 应用的环境
   - 例如：Claude Desktop、Zed、Cursor 等 IDE

2. **MCP 客户端（MCP Client）**
   - 嵌入在主机应用内部
   - 管理与 MCP 服务器的连接
   - 使用 MCP 协议进行通信

3. **MCP 服务器（MCP Server）**
   - 轻量级、独立的程序
   - 通过标准化接口暴露特定工具或数据源的能力

### 🔧 MCP 的三种核心能力

当 MCP 客户端与服务器进行"握手"时，服务器会返回能力清单：

#### 1. 工具（Tools）
- **定义**：AI 可以调用的具体动作或功能
- **示例**：`github/create_issue`

#### 2. 资源（Resources）
- **定义**：AI 可以读取的数据或内容
- **示例**：本地文件、数据库表格

#### 3. 提示（Prompts）
- **定义**：可供用户或模型复用的提示模板
- **作用**：标准化常用的交互模式



---

## 🔄 具体的实现流程

```ad-flex
title: 联合使用
color:178,22,164

1. 用户输入 User Prompt，女朋友肚子疼怎么办
2. Agent 通过 MCP 向 MCP-Server 获取所有可以用的 TOOLS
3. Agent 将 TOOLS 转换成System-Prompt 或者 Function Calling的形式和 User Prompt一起打包发送给 AI 模型
4. 模型发现了Server 中有一个名为 Web_Browse 的工具，并通过发送给 Agent 请求调用这个工具
5. Agent 收到请求后通过 MCP 协议去调用 Server 里面 Web_Browse, 在搜索到结果后返回给 Agent
6. Agent 转发内容给模型
7. 模型根据内容和自己的思考，生成答案返还给 Agent
8. Agent 将结果返回给用户
![[Prompt, Agent, MCP-2025-07-09-22-16-04.png]]
```
