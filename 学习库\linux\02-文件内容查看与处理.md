---
tags:
  - 学习
  - linux
  - 文本处理
---

# Linux文件内容查看与处理

> [!info] 说明
> 本文档整理了Linux系统中查看和处理文件内容的常用命令，包括文本查看、搜索、编辑和处理工具。

## 📖 文件内容查看

### 基础查看命令
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `cat` | 显示文件内容 | `-n` 显示行号<br>`-b` 非空行显示行号<br>`-A` 显示所有字符 | `cat -n file.txt`<br>`cat file1.txt file2.txt` |
| `less` | 分页查看文件 | `q` 退出<br>`/pattern` 搜索<br>`G` 末尾<br>`g` 开头 | `less largefile.txt` |
| `more` | 分页查看文件 | 空格键翻页<br>`q` 退出 | `more file.txt` |
| `head` | 显示文件开头 | `-n` 指定行数<br>`-c` 指定字节数 | `head -n 20 file.txt`<br>`head -c 100 file.txt` |
| `tail` | 显示文件结尾 | `-n` 指定行数<br>`-f` 实时跟踪<br>`-F` 跟踪文件名 | `tail -f /var/log/syslog`<br>`tail -n 50 file.txt`<br>`tail -F log.txt` |

> [!tip] less命令快捷键
> - `空格键` 或 `f`: 向下翻页
> - `b`: 向上翻页
> - `/pattern`: 向前搜索
> - `?pattern`: 向后搜索
> - `n`: 下一个匹配
> - `N`: 上一个匹配
> - `G`: 跳到文件末尾
> - `g`: 跳到文件开头

### 文本搜索
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `grep` | 搜索文本 | `-i` 忽略大小写<br>`-v` 反向匹配<br>`-n` 显示行号<br>`-r` 递归搜索<br>`-l` 只显示文件名<br>`-c` 统计匹配行数 | `grep -in "error" log.txt`<br>`grep -r "function" src/`<br>`grep -v "debug" file.txt` |
| `egrep` | 扩展正则表达式搜索 | 同grep | `egrep "(error\|warning)" log.txt` |
| `fgrep` | 固定字符串搜索 | 同grep | `fgrep "literal.string" file.txt` |

### 文件统计
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `wc` | 统计文件信息 | `-l` 行数<br>`-w` 单词数<br>`-c` 字符数<br>`-m` 字符数(多字节) | `wc -l file.txt`<br>`wc -wc document.txt` |

## 🔧 文本处理工具

### 排序与去重
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `sort` | 文本排序 | `-r` 逆序<br>`-n` 数字排序<br>`-u` 去重<br>`-k` 指定字段<br>`-t` 分隔符 | `sort -nr numbers.txt`<br>`sort -t',' -k2 data.csv`<br>`sort -u names.txt` |
| `uniq` | 去除重复行 | `-c` 统计重复次数<br>`-d` 只显示重复行<br>`-u` 只显示唯一行 | `sort file.txt \| uniq -c`<br>`uniq -d sorted.txt` |

> [!warning] uniq注意事项
> `uniq` 命令只能去除相邻的重复行，因此通常需要先用 `sort` 排序。

### 字段提取与处理
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `cut` | 提取字段 | `-d` 分隔符<br>`-f` 字段号<br>`-c` 字符位置 | `cut -d ',' -f 1,3 data.csv`<br>`cut -c 1-10 file.txt` |
| `tr` | 字符转换 | `-d` 删除字符<br>`-s` 压缩重复字符 | `tr 'a-z' 'A-Z' < file.txt`<br>`tr -d ' ' < file.txt`<br>`tr -s ' ' < file.txt` |
| `column` | 格式化列显示 | `-t` 表格格式<br>`-s` 分隔符 | `column -t -s',' data.csv` |

### 高级文本处理
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `sed` | 流编辑器 | `-i` 直接修改文件<br>`-e` 多个表达式<br>`-n` 静默模式 | `sed 's/old/new/g' file.txt`<br>`sed -i 's/foo/bar/g' file.txt`<br>`sed -n '1,10p' file.txt` |
| `awk` | 文本处理语言 | `-F` 字段分隔符<br>`-v` 设置变量 | `awk '{print $1, $3}' file.txt`<br>`awk -F',' '{print $2}' data.csv`<br>`awk '{sum+=$1} END {print sum}' numbers.txt` |

## 📝 sed常用操作

### 基本替换
```bash
# 替换第一个匹配
sed 's/old/new/' file.txt

# 替换所有匹配
sed 's/old/new/g' file.txt

# 替换指定行
sed '5s/old/new/' file.txt

# 替换指定范围
sed '1,10s/old/new/g' file.txt
```

### 删除操作
```bash
# 删除空行
sed '/^$/d' file.txt

# 删除指定行
sed '5d' file.txt

# 删除指定范围
sed '1,10d' file.txt

# 删除匹配行
sed '/pattern/d' file.txt
```

### 插入和追加
```bash
# 在指定行前插入
sed '5i\New line' file.txt

# 在指定行后追加
sed '5a\New line' file.txt

# 在匹配行前插入
sed '/pattern/i\New line' file.txt
```

## 🔍 awk常用操作

### 基本语法
```bash
# 打印指定字段
awk '{print $1, $3}' file.txt

# 使用分隔符
awk -F',' '{print $2}' data.csv

# 条件处理
awk '$3 > 100 {print $1, $3}' data.txt

# 计算总和
awk '{sum+=$1} END {print "Total:", sum}' numbers.txt
```

### 内置变量
| 变量 | 含义 | 示例 |
|------|------|------|
| `NR` | 当前行号 | `awk '{print NR, $0}' file.txt` |
| `NF` | 当前行字段数 | `awk '{print NF}' file.txt` |
| `FS` | 字段分隔符 | `awk 'BEGIN{FS=","} {print $1}' file.csv` |
| `RS` | 记录分隔符 | `awk 'BEGIN{RS=";"} {print}' file.txt` |

### 模式匹配
```bash
# 匹配特定模式
awk '/pattern/ {print}' file.txt

# 范围匹配
awk '/start/,/end/ {print}' file.txt

# 字段匹配
awk '$2 == "value" {print}' file.txt

# 正则表达式匹配
awk '$1 ~ /^[0-9]+$/ {print}' file.txt
```

## 🎯 实用示例

### 日志分析
```bash
# 统计访问最多的IP
awk '{print $1}' access.log | sort | uniq -c | sort -nr | head -10

# 查找错误日志
grep -i "error\|warning\|fail" /var/log/syslog

# 统计每小时的访问量
awk '{print substr($4,2,13)}' access.log | sort | uniq -c
```

### 数据处理
```bash
# CSV文件处理
cut -d',' -f1,3 data.csv | sort | uniq

# 计算平均值
awk '{sum+=$1; count++} END {print sum/count}' numbers.txt

# 查找重复行
sort file.txt | uniq -d

# 合并多个文件并去重
cat file1.txt file2.txt | sort | uniq
```

### 文本格式化
```bash
# 转换大小写
tr 'a-z' 'A-Z' < file.txt

# 删除空行
sed '/^$/d' file.txt

# 在每行前添加行号
nl file.txt

# 格式化CSV为表格
column -t -s',' data.csv
```

## 🔗 管道与重定向

### 管道操作
```bash
# 基本管道
cat file.txt | grep "pattern" | sort | uniq

# 复杂管道
ps aux | grep nginx | awk '{print $2}' | xargs kill

# tee命令（同时输出到文件和屏幕）
command | tee output.txt

# 错误重定向
command 2>&1 | tee log.txt
```

### 重定向操作
```bash
# 输出重定向
echo "text" > file.txt          # 覆盖
echo "text" >> file.txt         # 追加

# 输入重定向
sort < unsorted.txt

# 错误重定向
command 2> error.log            # 错误输出到文件
command > output.txt 2>&1       # 标准输出和错误都重定向
```

> [!success] 学习建议
> 1. **循序渐进**: 先掌握基础命令，再学习sed和awk
> 2. **实践练习**: 在实际文件上练习这些命令
> 3. **组合使用**: 学会使用管道组合多个命令
> 4. **正则表达式**: 学习基本的正则表达式语法

---

**相关笔记链接:**
- [[01-文件与目录操作]]
- [[03-系统信息与进程管理]]
- [[06-Vim编辑器]]
