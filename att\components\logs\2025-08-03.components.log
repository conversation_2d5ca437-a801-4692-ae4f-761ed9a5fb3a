2025-08-03 09:20:49.713 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:20:49.722 [info] indexing created file components/logs/2025-08-03.components.log  [object Object] 
2025-08-03 09:20:49.733 [info] refresh page data from created listeners 0 1040   
2025-08-03 09:20:49.743 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:20:49.774 [info] index finished after resolve  [object Object] 
2025-08-03 09:20:49.775 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:20:52.694 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:20:52.767 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:20:52.769 [info] index finished after resolve  [object Object] 
2025-08-03 09:20:52.769 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:21:09.266 [info] refresh page data from delete listeners 0 1039   
2025-08-03 09:21:09.279 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:21:10.988 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:21:11.002 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:21:11.004 [info] index finished after resolve  [object Object] 
2025-08-03 09:21:11.005 [info] refresh page data from resolve listeners 0 1039   
2025-08-03 09:24:40.519 [info] components database created cost 0 ms   
2025-08-03 09:24:40.520 [info] components index initializing...   
2025-08-03 09:24:40.828 [info] start to batch put pages: 7   
2025-08-03 09:24:41.181 [info] batch persist cost 7  353 
2025-08-03 09:24:41.227 [info] components index initialized, 1039 files cost 708 ms   
2025-08-03 09:24:41.227 [info] refresh page data from init listeners 0 1039   
2025-08-03 09:24:45.898 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:24:46.142 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:24:46.590 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-03 09:24:46.602 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-03 09:24:46.607 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-03 09:24:46.621 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-03 09:25:15.950 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:25:15.965 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:25:15.966 [info] index finished after resolve  [object Object] 
2025-08-03 09:25:15.967 [info] refresh page data from resolve listeners 0 1039   
2025-08-03 09:25:18.265 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:25:18.267 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:25:18.268 [info] index finished after resolve  [object Object] 
2025-08-03 09:25:18.268 [info] refresh page data from resolve listeners 0 1039   
2025-08-03 09:29:04.581 [info] indexing created file 学习库/Deep learning/pytorch/attachments/1. 线性模型（Linear Model）-2025-08-03,09-08-04.webp  [object Object] 
2025-08-03 09:29:04.763 [info] refresh page data from created listeners 0 1040   
2025-08-03 09:29:06.646 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:29:06.654 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:29:06.655 [info] index finished after resolve  [object Object] 
2025-08-03 09:29:06.655 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:29:45.846 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:29:45.855 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:29:45.856 [info] index finished after resolve  [object Object] 
2025-08-03 09:29:45.857 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:29:47.959 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-08-03 09:29:47.966 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-08-03 09:29:47.968 [info] index finished after resolve  [object Object] 
2025-08-03 09:29:47.969 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:39:57.848 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:39:57.857 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:39:57.859 [info] index finished after resolve  [object Object] 
2025-08-03 09:39:57.859 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:40:24.175 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:40:24.183 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:40:24.184 [info] index finished after resolve  [object Object] 
2025-08-03 09:40:24.185 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:31.751 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:31.764 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:31.766 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:31.766 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:33.874 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:33.881 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:33.882 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:33.883 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:36.168 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:36.177 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:36.178 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:36.179 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:38.315 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:38.322 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:38.324 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:38.325 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:40.846 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:40.856 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:40.857 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:40.857 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:43.096 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:43.106 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:43.107 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:43.108 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:45.324 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:45.331 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:45.332 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:45.333 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:47.407 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:47.418 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:47.419 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:47.419 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:50:49.789 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:50:49.797 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:50:49.798 [info] index finished after resolve  [object Object] 
2025-08-03 09:50:49.798 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:12.269 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:12.278 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:12.279 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:12.280 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:15.939 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:15.948 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:15.950 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:15.951 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:18.018 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:18.026 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:18.028 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:18.029 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:20.591 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:20.598 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:20.600 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:20.600 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:22.966 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:22.973 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:22.974 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:22.975 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:25.142 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:25.149 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:25.150 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:25.151 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:27.496 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:27.503 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:27.504 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:27.504 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:32.585 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:32.739 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:32.740 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:32.741 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:51:38.351 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:51:38.358 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:51:38.359 [info] index finished after resolve  [object Object] 
2025-08-03 09:51:38.359 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:52:14.294 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:52:14.301 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:52:14.302 [info] index finished after resolve  [object Object] 
2025-08-03 09:52:14.303 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:52:16.668 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:52:16.676 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:52:16.677 [info] index finished after resolve  [object Object] 
2025-08-03 09:52:16.678 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:52:18.807 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:52:18.815 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:52:18.815 [info] index finished after resolve  [object Object] 
2025-08-03 09:52:18.816 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:52:21.119 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:52:21.129 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:52:21.132 [info] index finished after resolve  [object Object] 
2025-08-03 09:52:21.133 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:52:23.377 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:52:23.388 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:52:23.389 [info] index finished after resolve  [object Object] 
2025-08-03 09:52:23.389 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:53:19.755 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-03 09:53:19.764 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-03 09:53:19.765 [info] index finished after resolve  [object Object] 
2025-08-03 09:53:19.765 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:53:21.985 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-03 09:53:21.993 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-03 09:53:21.994 [info] index finished after resolve  [object Object] 
2025-08-03 09:53:21.994 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:53:25.479 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-03 09:53:25.489 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-03 09:53:25.490 [info] index finished after resolve  [object Object] 
2025-08-03 09:53:25.491 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:53:27.673 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-03 09:53:27.683 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-03 09:53:27.684 [info] index finished after resolve  [object Object] 
2025-08-03 09:53:27.685 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:53:29.746 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-08-03 09:53:29.755 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-08-03 09:53:29.758 [info] index finished after resolve  [object Object] 
2025-08-03 09:53:29.759 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:54:41.330 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:54:41.338 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:54:41.339 [info] index finished after resolve  [object Object] 
2025-08-03 09:54:41.339 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:55:16.084 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:55:16.092 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:55:16.094 [info] index finished after resolve  [object Object] 
2025-08-03 09:55:16.094 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:55:18.709 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:55:18.861 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:55:18.864 [info] index finished after resolve  [object Object] 
2025-08-03 09:55:18.866 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:55:20.807 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-08-03 09:55:20.910 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-08-03 09:55:20.913 [info] index finished after resolve  [object Object] 
2025-08-03 09:55:20.914 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:56:10.843 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:10.854 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:10.855 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:10.855 [info] refresh page data from resolve listeners 0 1040   
2025-08-03 09:56:13.405 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,09-08-11.webp  [object Object] 
2025-08-03 09:56:13.406 [info] refresh page data from created listeners 0 1041   
2025-08-03 09:56:15.435 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:15.448 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:15.449 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:15.449 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:29.212 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:29.225 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:29.228 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:29.228 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:32.114 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:32.120 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:32.122 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:32.123 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:39.293 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:39.344 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:39.347 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:39.347 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:41.382 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:41.393 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:41.394 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:41.395 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:46.548 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:46.563 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:46.565 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:46.565 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:49.462 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:49.519 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:49.521 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:49.521 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:52.649 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:52.659 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:52.661 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:52.662 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:55.750 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:55.760 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:55.761 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:55.761 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:57.854 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:56:57.864 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:56:57.866 [info] index finished after resolve  [object Object] 
2025-08-03 09:56:57.866 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:56:59.997 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 09:57:00.074 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 09:57:00.079 [info] index finished after resolve  [object Object] 
2025-08-03 09:57:00.079 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 09:58:38.373 [info] components database created cost 1 ms   
2025-08-03 09:58:38.374 [info] components index initializing...   
2025-08-03 09:58:38.630 [info] start to batch put pages: 5   
2025-08-03 09:58:38.647 [info] batch persist cost 5  17 
2025-08-03 09:58:38.689 [info] components index initialized, 1041 files cost 317 ms   
2025-08-03 09:58:38.689 [info] refresh page data from init listeners 0 1041   
2025-08-03 09:58:39.954 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:58:40.215 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:58:40.599 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-03 09:58:40.611 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-03 09:58:40.616 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-03 09:58:40.620 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-03 09:59:23.485 [info] components database created cost 1 ms   
2025-08-03 09:59:23.486 [info] components index initializing...   
2025-08-03 09:59:23.779 [info] start to batch put pages: 5   
2025-08-03 09:59:23.780 [info] batch persist cost 5  1 
2025-08-03 09:59:23.830 [info] components index initialized, 1041 files cost 346 ms   
2025-08-03 09:59:23.830 [info] refresh page data from init listeners 0 1041   
2025-08-03 09:59:24.854 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:59:25.228 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 09:59:25.796 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-03 09:59:25.801 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-03 09:59:25.810 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-03 09:59:25.814 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-03 10:02:35.429 [info] components database created cost 1 ms   
2025-08-03 10:02:35.430 [info] components index initializing...   
2025-08-03 10:02:35.688 [info] start to batch put pages: 5   
2025-08-03 10:02:35.716 [info] batch persist cost 5  28 
2025-08-03 10:02:35.757 [info] components index initialized, 1041 files cost 329 ms   
2025-08-03 10:02:35.757 [info] refresh page data from init listeners 0 1041   
2025-08-03 10:02:36.864 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 10:02:37.178 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-08-03 10:02:37.632 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-08-03 10:02:37.637 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-08-03 10:02:37.641 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-08-03 10:02:37.656 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-08-03 10:05:22.277 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:22.481 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:22.505 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:22.506 [info] refresh page data from resolve listeners 0 1041   
2025-08-03 10:05:22.566 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-05-21.webp  [object Object] 
2025-08-03 10:05:22.568 [info] refresh page data from created listeners 0 1042   
2025-08-03 10:05:24.635 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:24.650 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:24.651 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:24.652 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:05:37.984 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:37.995 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:37.999 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:38.000 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:05:49.859 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:49.969 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:49.972 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:49.973 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:05:52.160 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:52.271 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:52.273 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:52.273 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:05:54.578 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:05:54.589 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:05:54.593 [info] index finished after resolve  [object Object] 
2025-08-03 10:05:54.593 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:00.798 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:00.913 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:00.924 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:00.927 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:03.055 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:03.064 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:03.067 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:03.068 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:06.784 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:06.793 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:06.795 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:06.796 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:08.869 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:08.878 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:08.880 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:08.881 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:11.529 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:11.668 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:11.689 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:11.690 [info] refresh page data from resolve listeners 0 1042   
2025-08-03 10:06:11.806 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-06-10.webp  [object Object] 
2025-08-03 10:06:11.808 [info] refresh page data from created listeners 0 1043   
2025-08-03 10:06:13.859 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:06:13.868 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:06:13.870 [info] index finished after resolve  [object Object] 
2025-08-03 10:06:13.871 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:07:45.798 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:07:45.969 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:07:45.972 [info] index finished after resolve  [object Object] 
2025-08-03 10:07:45.973 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:07:52.288 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:07:52.506 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:07:52.509 [info] index finished after resolve  [object Object] 
2025-08-03 10:07:52.510 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:07:56.791 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:07:56.922 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:07:56.924 [info] index finished after resolve  [object Object] 
2025-08-03 10:07:56.924 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:08:11.001 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:08:11.009 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:08:11.010 [info] index finished after resolve  [object Object] 
2025-08-03 10:08:11.011 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:08:41.746 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:08:41.753 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:08:41.754 [info] index finished after resolve  [object Object] 
2025-08-03 10:08:41.755 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:08:45.603 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:08:45.610 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:08:45.611 [info] index finished after resolve  [object Object] 
2025-08-03 10:08:45.612 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:08:47.849 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:08:47.862 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:08:47.863 [info] index finished after resolve  [object Object] 
2025-08-03 10:08:47.864 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:11:00.637 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:11:00.737 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:11:00.743 [info] index finished after resolve  [object Object] 
2025-08-03 10:11:00.743 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:11:10.744 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:11:10.754 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:11:10.756 [info] index finished after resolve  [object Object] 
2025-08-03 10:11:10.756 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:11:12.827 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:11:12.834 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:11:12.839 [info] index finished after resolve  [object Object] 
2025-08-03 10:11:12.839 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:11:53.364 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:11:53.373 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:11:53.378 [info] index finished after resolve  [object Object] 
2025-08-03 10:11:53.378 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:11:55.417 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:11:55.426 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:11:55.428 [info] index finished after resolve  [object Object] 
2025-08-03 10:11:55.428 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:01.008 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:01.015 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:01.017 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:01.018 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:03.442 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:03.449 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:03.451 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:03.452 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:05.565 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:05.573 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:05.575 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:05.575 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:09.721 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:09.729 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:09.734 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:09.734 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:13.489 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:13.496 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:13.500 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:13.501 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:15.634 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:15.643 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:15.644 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:15.644 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:32.501 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:12:32.631 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:12:32.633 [info] index finished after resolve  [object Object] 
2025-08-03 10:12:32.633 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:12:59.161 [info] indexing created file 学习库/Deep learning/pytorch/attachments/3. 反向传播（Back Propagation）-2025-08-03,10-08,10-12-57.webp  [object Object] 
2025-08-03 10:12:59.163 [info] refresh page data from created listeners 0 1044   
2025-08-03 10:13:01.214 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:01.225 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:01.227 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:01.227 [info] refresh page data from resolve listeners 0 1044   
2025-08-03 10:13:18.270 [info] refresh page data from delete listeners 0 1043   
2025-08-03 10:13:18.380 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:20.063 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:20.183 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:20.184 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:20.185 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:13:30.262 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:30.270 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:30.272 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:30.273 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:13:32.380 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:32.539 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:32.546 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:32.546 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:13:37.045 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:37.142 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:37.144 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:37.145 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:13:51.495 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:51.554 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:51.562 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:51.562 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:13:53.577 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:13:53.585 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:13:53.587 [info] index finished after resolve  [object Object] 
2025-08-03 10:13:53.588 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:01.203 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:01.212 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:01.214 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:01.214 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:03.506 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:03.516 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:03.517 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:03.517 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:05.682 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:05.692 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:05.694 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:05.696 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:07.886 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:07.894 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:07.897 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:07.898 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:10.157 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:10.167 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:10.169 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:10.169 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:12.279 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:12.287 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:12.291 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:12.292 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:19.152 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:19.160 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:19.160 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:19.161 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:21.887 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:21.897 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:21.900 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:21.900 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:25.199 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:25.206 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:25.208 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:25.208 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:27.244 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:27.251 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:27.252 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:27.253 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:29.598 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:29.605 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:29.610 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:29.611 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:31.635 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:31.645 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:31.647 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:31.648 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:35.145 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:35.152 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:35.156 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:35.157 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:38.469 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:38.480 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:38.481 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:38.481 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:44.270 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:44.279 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:44.281 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:44.282 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:46.311 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:46.318 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:46.319 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:46.321 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:14:48.737 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:14:48.745 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:14:48.749 [info] index finished after resolve  [object Object] 
2025-08-03 10:14:48.749 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:00.431 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:00.440 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:00.444 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:00.444 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:12.425 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:12.432 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:12.433 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:12.433 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:16.766 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:16.773 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:16.774 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:16.775 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:19.587 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:19.594 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:19.597 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:19.598 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:23.014 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:23.022 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:23.025 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:23.025 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:27.718 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:27.727 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:27.729 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:27.729 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:29.907 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:29.918 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:29.920 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:29.921 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:31.954 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:31.961 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:31.962 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:31.963 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:35.974 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:35.982 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:35.983 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:35.983 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:40.194 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:40.197 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:40.199 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:40.199 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:43.229 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:43.237 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:43.238 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:43.239 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:45.280 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:45.288 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:45.290 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:45.290 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:47.490 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:47.498 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:47.500 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:47.501 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:49.530 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:49.538 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:49.539 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:49.540 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:15:59.728 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:15:59.737 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:15:59.738 [info] index finished after resolve  [object Object] 
2025-08-03 10:15:59.739 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:16:01.796 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:16:01.803 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:16:01.804 [info] index finished after resolve  [object Object] 
2025-08-03 10:16:01.805 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:16:03.926 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:16:03.935 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:16:03.937 [info] index finished after resolve  [object Object] 
2025-08-03 10:16:03.937 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:16:15.760 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:16:15.767 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:16:15.768 [info] index finished after resolve  [object Object] 
2025-08-03 10:16:15.768 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:17:35.035 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:17:35.043 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:17:35.047 [info] index finished after resolve  [object Object] 
2025-08-03 10:17:35.047 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:17:37.105 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:17:37.113 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:17:37.114 [info] index finished after resolve  [object Object] 
2025-08-03 10:17:37.115 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:17:39.258 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:17:39.269 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:17:39.270 [info] index finished after resolve  [object Object] 
2025-08-03 10:17:39.270 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 10:17:43.705 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-08-03 10:17:43.713 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-08-03 10:17:43.714 [info] index finished after resolve  [object Object] 
2025-08-03 10:17:43.715 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:31.953 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:31.982 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:31.983 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:31.984 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:35.324 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:35.339 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:35.340 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:35.341 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:39.473 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:39.490 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:39.492 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:39.492 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:42.459 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:42.475 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:42.477 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:42.478 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:45.359 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:45.374 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:45.376 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:45.377 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:47.698 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:47.720 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:47.723 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:47.724 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:01:51.235 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:01:51.254 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:01:51.257 [info] index finished after resolve  [object Object] 
2025-08-03 11:01:51.257 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:02:04.349 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:02:04.363 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:02:04.369 [info] index finished after resolve  [object Object] 
2025-08-03 11:02:04.370 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:02:06.779 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:02:06.791 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:02:06.793 [info] index finished after resolve  [object Object] 
2025-08-03 11:02:06.794 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:02:08.994 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:02:09.020 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:02:09.022 [info] index finished after resolve  [object Object] 
2025-08-03 11:02:09.023 [info] refresh page data from resolve listeners 0 1043   
2025-08-03 11:02:11.089 [debug] ignore file modify evnet 学习库/linux/常用Linux命令.md   
2025-08-03 11:02:11.109 [info] trigger 学习库/linux/常用Linux命令.md resolve  [object Object] 
2025-08-03 11:02:11.111 [info] index finished after resolve  [object Object] 
2025-08-03 11:02:11.112 [info] refresh page data from resolve listeners 0 1043   
