2025-04-12 10:17:59 [info] components database created cost 7 ms   
2025-04-12 10:17:59 [info] components index initializing...   
2025-04-12 10:17:59 [info] start to batch put pages: 6   
2025-04-12 10:18:01 [info] batch persist cost 6  2564 
2025-04-12 10:18:02 [info] components index initialized, 793 files cost 2858 ms   
2025-04-12 10:18:02 [info] refresh page data from init listeners 0 793   
2025-04-12 10:18:03 [info] indexing created file components/logs/2025-04-12.components.log  [object Object] 
2025-04-12 10:18:03 [info] refresh page data from created listeners 0 794   
2025-04-12 10:18:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-12 10:18:04 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-12 10:18:04 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-12 10:18:04 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-12 10:18:04 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-12 10:18:04 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-12 17:00:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:00:17 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:00:17 [info] index finished after resolve  [object Object] 
2025-04-12 17:00:17 [info] refresh page data from resolve listeners 0 794   
2025-04-12 17:01:26 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-01-26.png  [object Object] 
2025-04-12 17:01:27 [info] refresh page data from created listeners 0 795   
2025-04-12 17:01:29 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:01:29 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:01:29 [info] index finished after resolve  [object Object] 
2025-04-12 17:01:29 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:01:47 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:01:47 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:01:47 [info] index finished after resolve  [object Object] 
2025-04-12 17:01:47 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:03 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:03 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:06 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:06 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:08 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:08 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:10 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:16 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:16 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:20 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:20 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:24 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:24 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:24 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:24 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:02:26 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:02:26 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:02:26 [info] index finished after resolve  [object Object] 
2025-04-12 17:02:26 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:25 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:25 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:27 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:27 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:27 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:27 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:29 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:29 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:29 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:29 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:32 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:32 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:32 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:32 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:36 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:36 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:36 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:36 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:38 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:38 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:40 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:40 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:40 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:40 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:51 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:51 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:51 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:53 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:53 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:53 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:53 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:03:55 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:03:55 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:03:55 [info] index finished after resolve  [object Object] 
2025-04-12 17:03:55 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:03 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:03 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:06 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:06 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:09 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:09 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:11 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:11 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:14 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:14 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:14 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:14 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:18 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:18 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:04:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:04:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:04:20 [info] index finished after resolve  [object Object] 
2025-04-12 17:04:20 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:05:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:05:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:05:09 [info] index finished after resolve  [object Object] 
2025-04-12 17:05:09 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:05:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:05:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:05:12 [info] index finished after resolve  [object Object] 
2025-04-12 17:05:12 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:05:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:05:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:05:16 [info] index finished after resolve  [object Object] 
2025-04-12 17:05:16 [info] refresh page data from resolve listeners 0 795   
2025-04-12 17:06:33 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-06-33.png  [object Object] 
2025-04-12 17:06:34 [info] refresh page data from created listeners 0 796   
2025-04-12 17:06:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:06:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:06:35 [info] index finished after resolve  [object Object] 
2025-04-12 17:06:35 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:06:40 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:06:40 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:06:40 [info] index finished after resolve  [object Object] 
2025-04-12 17:06:40 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:07:35 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:07:35 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:07:35 [info] index finished after resolve  [object Object] 
2025-04-12 17:07:35 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:07:40 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:07:40 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:07:40 [info] index finished after resolve  [object Object] 
2025-04-12 17:07:40 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:07:49 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:07:49 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:07:49 [info] index finished after resolve  [object Object] 
2025-04-12 17:07:49 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:09:27 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:09:27 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:09:27 [info] index finished after resolve  [object Object] 
2025-04-12 17:09:27 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:09:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:09:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:09:38 [info] index finished after resolve  [object Object] 
2025-04-12 17:09:38 [info] refresh page data from resolve listeners 0 796   
2025-04-12 17:09:52 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-09-52.png  [object Object] 
2025-04-12 17:09:52 [info] refresh page data from created listeners 0 797   
2025-04-12 17:09:54 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:09:54 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:09:54 [info] index finished after resolve  [object Object] 
2025-04-12 17:09:54 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:29 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:29 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:29 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:29 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:31 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:31 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:31 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:31 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:33 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:33 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:33 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:36 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:36 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:36 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:36 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:39 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:39 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:39 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:39 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:41 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:41 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:43 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:43 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:43 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:43 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:46 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:46 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:46 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:46 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:48 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:48 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:14:50 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:14:51 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:14:51 [info] index finished after resolve  [object Object] 
2025-04-12 17:14:51 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:02 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:02 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:10 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:15 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:15 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:15 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:15 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:19 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:19 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:19 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:19 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:34 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:34 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:34 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:41 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:41 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:54 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:54 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:54 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:54 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:15:56 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:15:56 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:15:56 [info] index finished after resolve  [object Object] 
2025-04-12 17:15:56 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:00 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:00 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:00 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:00 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:02 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:02 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:04 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:04 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:04 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:04 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:07 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:07 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:07 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:07 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:09 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:09 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:11 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:11 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:14 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:14 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:14 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:14 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:16 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:16 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:18 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:18 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:41 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:41 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:16:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:16:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:16:48 [info] index finished after resolve  [object Object] 
2025-04-12 17:16:48 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:02 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:02 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:02 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:02 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:06 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:06 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:08 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:08 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:10 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:12 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:12 [info] refresh page data from resolve listeners 0 797   
2025-04-12 17:17:31 [info] indexing created file 学习库/stm32/attachments/9 定时器-2025-04-12-17-17-31.png  [object Object] 
2025-04-12 17:17:32 [info] refresh page data from created listeners 0 798   
2025-04-12 17:17:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:34 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:34 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:34 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:17:45 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:45 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:45 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:45 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:17:48 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:48 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:48 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:48 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:17:56 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:56 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:56 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:56 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:17:58 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:17:58 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:17:58 [info] index finished after resolve  [object Object] 
2025-04-12 17:17:58 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:00 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:00 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:00 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:00 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:03 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:03 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:06 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:06 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:08 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:08 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:10 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:13 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:13 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:13 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:13 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:16 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:16 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:16 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:16 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:18 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:18 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:21 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:21 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:21 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:21 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:18:58 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:18:58 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:18:58 [info] index finished after resolve  [object Object] 
2025-04-12 17:18:58 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:01 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:01 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:01 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:01 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:03 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:03 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:06 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:06 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:06 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:06 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:08 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:08 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:10 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:12 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:12 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:19:22 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:19:22 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:19:22 [info] index finished after resolve  [object Object] 
2025-04-12 17:19:22 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:21:55 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:21:55 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:21:55 [info] index finished after resolve  [object Object] 
2025-04-12 17:21:55 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:21:57 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:21:57 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:21:57 [info] index finished after resolve  [object Object] 
2025-04-12 17:21:57 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:21:59 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:21:59 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:21:59 [info] index finished after resolve  [object Object] 
2025-04-12 17:21:59 [info] refresh page data from resolve listeners 0 798   
2025-04-12 17:22:10 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 17:22:10 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 17:22:10 [info] index finished after resolve  [object Object] 
2025-04-12 17:22:10 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:18:31 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:18:31 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:18:31 [info] index finished after resolve  [object Object] 
2025-04-12 20:18:31 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:18:37 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:18:37 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:18:37 [info] index finished after resolve  [object Object] 
2025-04-12 20:18:37 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:18:47 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:18:47 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:18:47 [info] index finished after resolve  [object Object] 
2025-04-12 20:18:47 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:18:50 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:18:50 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:18:50 [info] index finished after resolve  [object Object] 
2025-04-12 20:18:50 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:01 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:01 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:01 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:01 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:07 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:07 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:07 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:07 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:12 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:12 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:17 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:17 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:17 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:17 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:23 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:23 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:23 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:23 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:28 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:28 [info] refresh page data from resolve listeners 0 798   
2025-04-12 20:19:31 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 20:19:31 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 20:19:31 [info] index finished after resolve  [object Object] 
2025-04-12 20:19:31 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:42:13 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:42:13 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:42:13 [info] index finished after resolve  [object Object] 
2025-04-12 21:42:13 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:42:18 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:42:18 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:42:18 [info] index finished after resolve  [object Object] 
2025-04-12 21:42:18 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:42:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:42:21 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:42:21 [info] index finished after resolve  [object Object] 
2025-04-12 21:42:21 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:42:23 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:42:23 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:42:23 [info] index finished after resolve  [object Object] 
2025-04-12 21:42:23 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:42:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:42:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:42:25 [info] index finished after resolve  [object Object] 
2025-04-12 21:42:25 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:03 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:03 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:03 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:03 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:08 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:08 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:08 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:08 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:12 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:12 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:12 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:12 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:15 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:15 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:15 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:15 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:20 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:20 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:20 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:20 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:24 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:24 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:24 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:24 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:28 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:28 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:30 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:30 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:30 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:30 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:33 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:33 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:33 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:33 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:38 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:38 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:38 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:38 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:41 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:41 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:41 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:41 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:45:50 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:45:50 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:45:50 [info] index finished after resolve  [object Object] 
2025-04-12 21:45:50 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:09 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:09 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:09 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:09 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:11 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:11 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:11 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:11 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:13 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:13 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:13 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:13 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:21 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:21 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:21 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:21 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:25 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:25 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:25 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:25 [info] refresh page data from resolve listeners 0 798   
2025-04-12 21:46:28 [info] ignore file modify evnet 学习库/stm32/9 定时器.md   
2025-04-12 21:46:28 [info] trigger 学习库/stm32/9 定时器.md resolve  [object Object] 
2025-04-12 21:46:28 [info] index finished after resolve  [object Object] 
2025-04-12 21:46:28 [info] refresh page data from resolve listeners 0 798   
