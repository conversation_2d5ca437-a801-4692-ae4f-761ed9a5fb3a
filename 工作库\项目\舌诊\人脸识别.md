---
tags:
  - 项目
  - 舌诊
  - 毕设
---
# 关键点检测
可以通过人脸的关键点，来确定人脸的朝向 [[学习库/ROS/机器人学/机器人运动学/未命名#3 1 示例一：ZYX动态欧拉角（Roll-Pitch-Yaw）的分步解析|欧拉角]]

## top-down

是一种 two-stage 的检测方法，首先进行人体检测，然后对每一个检测到的人进行关键点检测
- **缺点**：复杂度随着人物数量的增加而增加
- **缺点**：先检测图像中人体的边界框，再在 bounding box 中检测关键点，如果出现遮挡或者小目标表现比较差，[这两个阶段计算的特征不共享，不利于实时应用](zotero://note/u/68WCMVZE/)
- **优点**：准确率高


## bottom-up

是一种基于热图的关键点检测方法，一次性检测所有的关键点，采用 NMS 在热图中寻找局部最大值
- **缺点**：无法进行 End-to-End 的训练，因为后处理是在卷积之外进行的 
- **缺点**：两个个体如果很接近，通过热图的关键点检测就很难区分关键点 （准确率低）
- **优点**：复杂度低，推理快
- **方法**：热图生成方法则是为每个关键点生成一个概率图（即热图）。 在这个热图中，每个像素的值代表了该像素是某个关键点的概率，通常在关键点的位置上会形成一个高斯分布的峰值。网络会为每个关键点类别都生成一个单独的热图。在得到了所有关键点的热图之后，再通过一些后处理方法（如寻找热图中的峰值点，并进行关联）来确定每个人脸的关键点。

![|815x309](./attachments/人脸识别-{DATE}-{TIME}.webp)


## 归一化方式

IPN 和 ION 是在人脸关键点检测任务中两种非常常见的归一化方法，它们主要用于评估模型的精度，尤其是在计算归一化平均误差（Normalized Mean Error, NME）时。

```ad-col2
title: ION (Inter-Ocular Normalization) - 眼间距归一化
color:178,22,164
- **归一化基准**: 使用**双眼外眼角之间的距离**作为归一化的尺度。
- **计算方式**:
    
    1. 首先，获取左眼外眼角 (x_le, y_le) 和右眼外眼角 (x_re, y_re) 的坐标。
        
    2. 计算这两个点之间的欧氏距离 d_ion。  
        d_ion = sqrt((x_le - x_re)² + (y_le - y_re)²)
        
    3. 在计算关键点预测误差时，将每个点的预测坐标与真实坐标之间的欧氏距离，再除以这个 d_ion。
- **优点**:
    
    - **鲁棒性强**: 外眼角是人脸上非常稳定且易于标注的特征点。即使在人眼部分闭合、或者图像分辨率不高的情况下，外眼角的位置也相对容易确定。
        
- **应用**:
    
    - 由于其鲁棒性，ION是目前学术界和工业界在评估人脸关键点模型时**最常用**的归一化方法之一，尤其是在一些主流的公开数据集（如 300W, WFLW）的评测标准中被广泛采用。

![](./attachments/人脸识别-2025-08-02,22_08_44.png)
```

```ad-col2
title: IPN (Inter-Pupillary Normalization) - 瞳孔间距归一化
color:178,22,164
- **归一化基准**: 使用**双眼瞳孔中心之间的距离**作为归一化的尺度。
    
- **计算方式**:
    
    1. 首先，获取左眼瞳孔中心 (x_lp, y_lp) 和右眼瞳孔中心 (x_rp, y_rp) 的坐标。
        
    2. 计算这两个点之间的欧氏距离 d_ipn。  
        d_ipn = sqrt((x_lp - x_rp)² + (y_lp - y_rp)²)
        
    3. 同样地，将关键点的预测误差除以这个 d_ipn。
        
- **缺点**:
    
    - **鲁棒性较差**: 瞳孔的准确位置很难确定。它会受到光照（瞳孔大小变化）、视线方向、眼镜反光以及眼皮遮挡等多种因素的干扰。在“in-the-wild”（自然场景）的图像中，精确标注和检测瞳孔中心是一个巨大的挑战。
        
- **应用**:
    
    - IPN 使用得相对较少，通常出现在一些早期的研究或者是在特定条件（如光照和姿态可控）下采集的数据集中。

![](./attachments/人脸识别-2025-08-02,22_08_51.png)
```

## 参考文献

