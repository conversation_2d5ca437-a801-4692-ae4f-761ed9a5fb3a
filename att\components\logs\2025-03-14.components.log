2025-03-14 00:04:03 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:04:03 [info] indexing created file components/logs/2025-03-14.components.log  [object Object] 
2025-03-14 00:04:03 [info] refresh page data from created listeners 15 536   
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:04 [info] query  [object Object] 
2025-03-14 00:04:05 [info] query  [object Object] 
2025-03-14 00:04:05 [info] query  [object Object] 
2025-03-14 00:04:05 [info] query  [object Object] 
2025-03-14 00:09:04 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:14:05 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:19:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:24:07 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:29:08 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:34:09 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:39:10 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:44:11 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:49:12 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:54:13 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 00:59:14 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:04:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:09:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:14:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:19:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:24:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:29:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:34:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:39:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:44:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:49:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:54:25 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 01:59:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:04:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:09:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:14:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:19:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:24:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:29:32 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:34:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:39:34 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:44:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:49:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:54:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 02:59:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:04:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:09:40 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:14:41 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:19:42 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:24:43 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:29:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:34:45 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:39:46 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:44:47 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:49:48 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:54:49 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 03:59:50 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:04:51 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:09:52 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:14:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:19:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:24:55 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:29:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:34:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:39:58 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:44:59 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:50:00 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 04:55:01 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:00:02 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:05:03 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:10:04 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:15:05 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:20:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:25:07 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:30:08 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:35:09 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:40:10 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:45:11 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:50:12 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 05:55:13 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:00:14 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:05:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:10:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:15:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:20:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:25:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:30:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:35:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:40:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:45:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:50:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 06:55:25 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:00:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:05:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:10:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:15:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:20:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:25:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:30:32 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:35:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:40:34 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:45:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:50:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 07:55:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 08:00:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 08:05:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-14 22:19:21 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-14 22:19:24 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:19:24 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:19:25 [info] components database created cost 3 ms   
2025-03-14 22:19:25 [info] components index initializing...   
2025-03-14 22:19:25 [info] components index initialized, 536 files cost 74 ms   
2025-03-14 22:19:25 [info] refresh page data from init listeners 0 536   
2025-03-14 22:19:25 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:19:25 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:19:25 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:19:25 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:19:25 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:19:25 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:19:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:19:26 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:19:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:19:27 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:19:28 [info] indexing created file Home/attachments/426.png  [object Object] 
2025-03-14 22:19:28 [info] refresh page data from created listeners 0 537   
2025-03-14 22:19:31 [info] ignore file modify evnet 学习库/ROS/ros入门.md   
2025-03-14 22:19:31 [info] trigger 学习库/ROS/ros入门.md resolve  [object Object] 
2025-03-14 22:19:31 [info] index finished after resolve  [object Object] 
2025-03-14 22:19:31 [info] refresh page data from resolve listeners 0 537   
2025-03-14 22:19:33 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-03-14 22:19:33 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-14 22:19:33 [info] index finished after resolve  [object Object] 
2025-03-14 22:19:33 [info] refresh page data from resolve listeners 0 537   
2025-03-14 22:19:33 [info] indexing created file 日记库/day/2025-03-14.md  [object Object] 
2025-03-14 22:19:33 [info] indexing created ignore file 日记库/day/2025-03-14.md   
2025-03-14 22:19:33 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-14 22:19:33 [info] index finished after resolve  [object Object] 
2025-03-14 22:19:33 [info] refresh page data from resolve listeners 0 538   
2025-03-14 22:19:33 [info] refresh page data from delete listeners 0 537   
2025-03-14 22:19:33 [info] refresh page data from delete listeners 0 536   
2025-03-14 22:19:33 [info] refresh page data from delete listeners 0 535   
2025-03-14 22:19:34 [info] refresh page data from delete listeners 0 534   
2025-03-14 22:19:35 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227160400.png  [object Object] 
2025-03-14 22:19:35 [info] refresh page data from created listeners 0 535   
2025-03-14 22:19:38 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227160140.png  [object Object] 
2025-03-14 22:19:38 [info] refresh page data from created listeners 0 536   
2025-03-14 22:19:41 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250228152417.png  [object Object] 
2025-03-14 22:19:41 [info] refresh page data from created listeners 0 537   
2025-03-14 22:19:46 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-14 22:19:46 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-14 22:19:46 [info] index finished after resolve  [object Object] 
2025-03-14 22:19:46 [info] refresh page data from resolve listeners 0 537   
2025-03-14 22:19:48 [info] indexing created file 学习库/Deep learning/训练实践/mmsegmentation/attachments/Pasted image 20250227165524.png  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from created listeners 0 538   
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 537   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 536   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 535   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 534   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 533   
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 532   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 531   
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 530   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 529   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 528   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 527   
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 526   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 525   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 524   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 523   
2025-03-14 22:19:48 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-14 22:19:48 [info] refresh page data from delete listeners 0 522   
2025-03-14 22:19:49 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:50 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706101124.png  [object Object] 
2025-03-14 22:19:50 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:50 [info] refresh page data from created listeners 0 523   
2025-03-14 22:19:53 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706095850.png  [object Object] 
2025-03-14 22:19:53 [info] refresh page data from created listeners 0 524   
2025-03-14 22:19:53 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:56 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706151929.png  [object Object] 
2025-03-14 22:19:56 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:56 [info] refresh page data from created listeners 0 525   
2025-03-14 22:19:58 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008151321.png  [object Object] 
2025-03-14 22:19:58 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:19:58 [info] refresh page data from created listeners 0 526   
2025-03-14 22:20:01 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008152233.png  [object Object] 
2025-03-14 22:20:01 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:20:01 [info] refresh page data from created listeners 0 527   
2025-03-14 22:20:04 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008150458.png  [object Object] 
2025-03-14 22:20:04 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:20:04 [info] refresh page data from created listeners 0 528   
2025-03-14 22:20:07 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240605152225.jpg  [object Object] 
2025-03-14 22:20:07 [info] refresh page data from created listeners 0 529   
2025-03-14 22:20:11 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008095639.png  [object Object] 
2025-03-14 22:20:11 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-14 22:20:11 [info] refresh page data from created listeners 0 530   
2025-03-14 22:20:14 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008153216.png  [object Object] 
2025-03-14 22:20:14 [info] refresh page data from created listeners 0 531   
2025-03-14 22:20:14 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:20:18 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008110538.png  [object Object] 
2025-03-14 22:20:18 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:20:18 [info] refresh page data from created listeners 0 532   
2025-03-14 22:20:24 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008145042.png  [object Object] 
2025-03-14 22:20:24 [info] refresh page data from created listeners 0 533   
2025-03-14 22:20:29 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20241008100523.png  [object Object] 
2025-03-14 22:20:29 [info] refresh page data from created listeners 0 534   
2025-03-14 22:20:29 [info] trigger 学习库/Deep learning/神经网络.md resolve  [object Object] 
2025-03-14 22:20:38 [info] indexing created file 学习库/Deep learning/attachments/Pasted image 20240706093227.png  [object Object] 
2025-03-14 22:20:38 [info] refresh page data from created listeners 0 535   
2025-03-14 22:20:38 [info] trigger 学习库/Deep learning/Attention.md resolve  [object Object] 
2025-03-14 22:20:48 [info] indexing created file 日记库/fleeting_notes/attachments/427.png  [object Object] 
2025-03-14 22:20:48 [info] refresh page data from created listeners 0 536   
2025-03-14 22:20:56 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-03-14 22:20:56 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-14 22:20:56 [info] index finished after resolve  [object Object] 
2025-03-14 22:20:56 [info] refresh page data from resolve listeners 0 536   
2025-03-14 22:21:00 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-03-14 22:21:00 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-14 22:21:00 [info] index finished after resolve  [object Object] 
2025-03-14 22:21:00 [info] refresh page data from resolve listeners 0 536   
2025-03-14 22:21:02 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-14 22:21:02 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-14 22:21:02 [info] index finished after resolve  [object Object] 
2025-03-14 22:21:02 [info] refresh page data from resolve listeners 0 536   
2025-03-14 22:21:02 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:21:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:21:03 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:21:03 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:45 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-14 22:55:47 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [info] components database created cost 152 ms   
2025-03-14 22:55:47 [info] components index initializing...   
2025-03-14 22:55:47 [info] components index initialized, 536 files cost 223 ms   
2025-03-14 22:55:47 [info] refresh page data from init listeners 0 536   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:47 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-14 22:55:48 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:48 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:48 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:55:49 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:49 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:49 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:49 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:54 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-14 22:55:54 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-14 22:55:54 [info] index finished after resolve  [object Object] 
2025-03-14 22:55:54 [info] refresh page data from resolve listeners 0 536   
2025-03-14 22:55:55 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:55 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:55 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:55:55 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-14 22:55:56 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-14 22:56:03 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-14 22:56:03 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-14 22:56:03 [info] index finished after resolve  [object Object] 
2025-03-14 22:56:03 [info] refresh page data from resolve listeners 0 536   
