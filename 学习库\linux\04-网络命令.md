---
tags:
  - 学习
  - linux
  - 网络
  - 网络管理
---

# Linux网络命令

> [!info] 说明
> 本文档整理了Linux系统中网络相关的常用命令，包括网络测试、配置、文件传输和网络监控等功能。

## 🌐 网络连通性测试

### 基础测试命令
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ping` | 测试网络连通性 | `-c` 次数<br>`-i` 间隔<br>`-s` 包大小<br>`-W` 超时时间 | `ping -c 4 google.com`<br>`ping -i 0.5 -c 10 ***********` |
| `ping6` | IPv6连通性测试 | 同ping | `ping6 -c 4 ipv6.google.com` |
| `traceroute` | 路由跟踪 | `-n` 不解析主机名<br>`-m` 最大跳数 | `traceroute google.com`<br>`traceroute -n *******` |
| `tracepath` | 路径MTU发现 | | `tracepath google.com` |
| `mtr` | 网络诊断工具 | `-r` 报告模式<br>`-c` 次数 | `mtr -r -c 10 google.com` |

> [!tip] ping命令技巧
> - `ping -f` 洪水ping（需要root权限）
> - `ping -D` 显示时间戳
> - `ping -a` 发出声音提示
> - `ping -q` 静默模式，只显示统计

### 端口连通性测试
| 命令 | 功能 | 示例 |
|------|------|------|
| `telnet` | 测试端口连通性 | `telnet google.com 80`<br>`telnet *********** 22` |
| `nc` (netcat) | 网络瑞士军刀 | `nc -zv google.com 80`<br>`nc -l 8080` |
| `nmap` | 网络扫描工具 | `nmap -p 80,443 google.com`<br>`nmap -sS ***********/24` |

## 📡 网络配置查看

### 网络接口信息
| 命令         | 功能     | 常用参数                                       | 示例                                                         |
| ---------- | ------ | ------------------------------------------ | ---------------------------------------------------------- |
| `ifconfig` | 网络接口配置 | `-a` 所有接口<br>`up/down` 启用/禁用               | `ifconfig`<br>`ifconfig eth0`<br>`sudo ifconfig eth0 down` |
| `ip`       | 现代网络工具 | `addr` 地址信息<br>`route` 路由信息<br>`link` 链路信息 | `ip addr show`<br>`ip route`<br>`ip link show`             |
| `iwconfig` | 无线网络配置 |                                            | `iwconfig`<br>`iwconfig wlan0`                             |

> [!info] ip vs ifconfig
> `ip` 命令是现代Linux系统推荐使用的网络配置工具，功能更强大且持续维护。`ifconfig` 在一些新系统中可能需要单独安装。

### 路由信息
| 命令 | 功能 | 示例 |
|------|------|------|
| `route` | 路由表 | `route -n`<br>`route add default gw ***********` |
| `ip route` | 现代路由命令 | `ip route show`<br>`ip route add ***********/24 via ***********` |
| `netstat -r` | 路由表显示 | `netstat -rn` |

### DNS配置
| 命令 | 功能 | 示例 |
|------|------|------|
| `nslookup` | DNS查询 | `nslookup google.com`<br>`nslookup *******` |
| `dig` | DNS查询工具 | `dig google.com`<br>`dig @******* google.com MX` |
| `host` | 简单DNS查询 | `host google.com`<br>`host **************` |

## 🔍 网络连接监控

### 连接状态查看
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `netstat` | 网络连接状态 | `-tulnp` 监听端口<br>`-an` 所有连接<br>`-r` 路由表<br>`-i` 接口统计 | `netstat -tulnp`<br>`netstat -an \| grep :80`<br>`netstat -i` |
| `ss` | 现代netstat | `-tulnp` 监听端口<br>`-s` 统计信息<br>`-o` 显示计时器 | `ss -tulnp`<br>`ss -s`<br>`ss -o state established` |
| `lsof` | 列出打开的文件 | `-i` 网络连接<br>`-p` 指定进程 | `lsof -i :80`<br>`lsof -i tcp:22`<br>`lsof -p 1234` |

> [!tip] ss命令优势
> `ss` 命令比 `netstat` 更快，特别是在连接数很多的系统上。它直接从内核获取信息，而不是解析 `/proc` 文件。

### 网络流量监控
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `iftop` | 实时网络流量 | `-i` 指定接口<br>`-n` 不解析主机名 | `sudo iftop -i eth0` |
| `nethogs` | 按进程显示流量 | `-d` 刷新间隔 | `sudo nethogs eth0` |
| `vnstat` | 网络流量统计 | `-i` 指定接口<br>`-d` 按天统计 | `vnstat -i eth0`<br>`vnstat -d` |
| `tcpdump` | 网络包捕获 | `-i` 接口<br>`-w` 写入文件<br>`-r` 读取文件 | `sudo tcpdump -i eth0 port 80`<br>`sudo tcpdump -w capture.pcap` |

## 📁 文件传输

### 远程文件传输
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `scp` | 安全文件复制 | `-r` 递归<br>`-P` 端口<br>`-v` 详细输出<br>`-C` 压缩 | `scp file.txt user@server:/path/`<br>`scp -r local_dir user@server:/remote/`<br>`scp -P 2222 file.txt user@server:` |
| `rsync` | 同步工具 | `-av` 归档详细<br>`--delete` 删除多余文件<br>`--progress` 显示进度 | `rsync -av source/ dest/`<br>`rsync -av --delete local/ user@server:remote/` |
| `sftp` | 安全FTP | | `sftp user@server`<br>`sftp -P 2222 user@server` |

### 网络下载
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `wget` | 下载文件 | `-O` 指定文件名<br>`-c` 断点续传<br>`-r` 递归下载<br>`-b` 后台下载 | `wget -O file.zip https://example.com/file.zip`<br>`wget -c large_file.iso` |
| `curl` | 数据传输工具 | `-o` 输出到文件<br>`-L` 跟随重定向<br>`-C -` 断点续传<br>`-H` 添加头部 | `curl -L -o page.html https://example.com`<br>`curl -H "User-Agent: MyApp" https://api.example.com` |

> [!tip] wget vs curl
> - `wget` 专注于下载，支持递归下载和后台下载
> - `curl` 更适合API调用和复杂的HTTP操作
> - `curl` 支持更多协议（FTP, SFTP, LDAP等）

## 🔐 远程连接

### SSH连接
| 命令 | 功能 | 常用参数 | 示例 |
|------|------|----------|------|
| `ssh` | 安全远程登录 | `-p` 端口<br>`-i` 密钥文件<br>`-L` 本地转发<br>`-R` 远程转发<br>`-X` X11转发 | `ssh user@server`<br>`ssh -p 2222 -i ~/.ssh/key user@server`<br>`ssh -L 8080:localhost:80 user@server` |
| `ssh-keygen` | 生成SSH密钥 | `-t` 密钥类型<br>`-b` 密钥长度<br>`-f` 文件名 | `ssh-keygen -t rsa -b 4096`<br>`ssh-keygen -t ed25519` |
| `ssh-copy-id` | 复制公钥到远程 | `-i` 指定密钥 | `ssh-copy-id user@server`<br>`ssh-copy-id -i ~/.ssh/id_rsa.pub user@server` |

### SSH隧道
```bash
# 本地端口转发（将本地8080端口转发到远程80端口）
ssh -L 8080:localhost:80 user@server

# 远程端口转发（将远程8080端口转发到本地80端口）
ssh -R 8080:localhost:80 user@server

# 动态端口转发（SOCKS代理）
ssh -D 1080 user@server

# 后台运行SSH隧道
ssh -fN -L 8080:localhost:80 user@server
```

## 🛠️ 网络配置

### 临时网络配置
```bash
# 配置IP地址
sudo ip addr add ***********00/24 dev eth0

# 启用/禁用网络接口
sudo ip link set eth0 up
sudo ip link set eth0 down

# 添加路由
sudo ip route add ***********/24 via ***********

# 设置默认网关
sudo ip route add default via ***********
```

### 永久网络配置
```bash
# Ubuntu/Debian (netplan)
sudo nano /etc/netplan/01-network-manager-all.yaml

# CentOS/RHEL
sudo nano /etc/sysconfig/network-scripts/ifcfg-eth0

# 重启网络服务
sudo systemctl restart networking        # Debian/Ubuntu
sudo systemctl restart network          # CentOS/RHEL
```

## 📊 网络诊断

### 网络性能测试
```bash
# 带宽测试（需要安装speedtest-cli）
speedtest-cli

# 网络延迟测试
ping -c 100 google.com | tail -1 | awk '{print $4}' | cut -d '/' -f 2

# 网络吞吐量测试（使用iperf3）
# 服务器端
iperf3 -s

# 客户端
iperf3 -c server_ip
```

### 网络故障排查
```bash
# 检查网络接口状态
ip link show

# 检查IP配置
ip addr show

# 检查路由表
ip route show

# 检查DNS配置
cat /etc/resolv.conf

# 检查网络连通性
ping -c 4 *******

# 检查端口监听
ss -tulnp | grep :80

# 检查防火墙状态
sudo ufw status                    # Ubuntu
sudo firewall-cmd --list-all       # CentOS/RHEL
```

## 🔥 防火墙管理

### UFW (Ubuntu)
```bash
# 启用/禁用防火墙
sudo ufw enable
sudo ufw disable

# 查看状态
sudo ufw status verbose

# 允许/拒绝端口
sudo ufw allow 22
sudo ufw deny 23
sudo ufw allow 80/tcp

# 允许特定IP
sudo ufw allow from ***********00

# 删除规则
sudo ufw delete allow 80
```

### firewalld (CentOS/RHEL)
```bash
# 查看状态
sudo firewall-cmd --state
sudo firewall-cmd --list-all

# 允许服务/端口
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-port=8080/tcp

# 重载配置
sudo firewall-cmd --reload

# 查看可用服务
sudo firewall-cmd --get-services
```

## 📈 网络监控脚本

### 网络连接监控
```bash
#!/bin/bash
# 网络连接监控脚本

echo "网络连接状态 - $(date)"
echo "========================"

# 网络接口状态
echo "网络接口:"
ip -br addr show

# 路由信息
echo -e "\n默认路由:"
ip route | grep default

# 连接统计
echo -e "\n连接统计:"
ss -s

# 监听端口
echo -e "\n监听端口:"
ss -tulnp | grep LISTEN | head -10
```

### 网络流量统计
```bash
#!/bin/bash
# 网络流量统计脚本

INTERFACE="eth0"

echo "网络流量统计 - $INTERFACE"
echo "========================"

# 获取流量数据
RX_BYTES=$(cat /sys/class/net/$INTERFACE/statistics/rx_bytes)
TX_BYTES=$(cat /sys/class/net/$INTERFACE/statistics/tx_bytes)

# 转换为人类可读格式
RX_MB=$((RX_BYTES / 1024 / 1024))
TX_MB=$((TX_BYTES / 1024 / 1024))

echo "接收: ${RX_MB} MB"
echo "发送: ${TX_MB} MB"
echo "总计: $((RX_MB + TX_MB)) MB"
```

> [!success] 网络管理最佳实践
> 1. **定期监控**: 建立网络性能监控机制
> 2. **安全配置**: 合理配置防火墙和SSH安全设置
> 3. **故障排查**: 掌握系统性的网络故障排查方法
> 4. **文档记录**: 记录网络配置变更和故障处理过程

---

**相关笔记链接:**
- [[03-系统信息与进程管理]]
- [[05-权限与安全管理]]
- [[07-软件包管理]]
