2025-05-19 15:07:01 [info] components database created cost 1 ms   
2025-05-19 15:07:01 [info] components index initializing...   
2025-05-19 15:07:01 [info] start to batch put pages: 5   
2025-05-19 15:07:01 [info] batch persist cost 5  24 
2025-05-19 15:07:02 [info] components index initialized, 893 files cost 1221 ms   
2025-05-19 15:07:02 [info] refresh page data from init listeners 0 893   
2025-05-19 15:07:04 [info] indexing created file components/logs/2025-05-19.components.log  [object Object] 
2025-05-19 15:07:04 [info] refresh page data from created listeners 0 894   
2025-05-19 15:07:05 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-19 15:07:05 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-19 15:07:05 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-19 15:07:05 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-19 15:07:05 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-19 15:07:05 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-19 15:07:09 [info] indexing created file 工作库/项目/舌诊/未命名 1.md  [object Object] 
2025-05-19 15:07:09 [info] indexing created ignore file 工作库/项目/舌诊/未命名 1.md   
2025-05-19 15:07:09 [info] trigger 工作库/项目/舌诊/未命名 1.md resolve  [object Object] 
2025-05-19 15:07:09 [info] index finished after resolve  [object Object] 
2025-05-19 15:07:09 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:07:29 [info] refresh page data from rename listeners 0 895   
2025-05-19 15:07:33 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:07:33 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:07:33 [info] index finished after resolve  [object Object] 
2025-05-19 15:07:33 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:07:37 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:07:37 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:07:37 [info] index finished after resolve  [object Object] 
2025-05-19 15:07:37 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:07:39 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:07:39 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:07:39 [info] index finished after resolve  [object Object] 
2025-05-19 15:07:39 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:07:42 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:07:42 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:07:42 [info] index finished after resolve  [object Object] 
2025-05-19 15:07:42 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:08:00 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:08:00 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:08:00 [info] index finished after resolve  [object Object] 
2025-05-19 15:08:00 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:08:02 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:08:02 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:08:02 [info] index finished after resolve  [object Object] 
2025-05-19 15:08:02 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:08:34 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:08:34 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:08:34 [info] index finished after resolve  [object Object] 
2025-05-19 15:08:34 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:08:56 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:08:56 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:08:56 [info] index finished after resolve  [object Object] 
2025-05-19 15:08:56 [info] refresh page data from resolve listeners 0 895   
2025-05-19 15:09:02 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 15:09:02 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 15:09:02 [info] index finished after resolve  [object Object] 
2025-05-19 15:09:02 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:28:27 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:28:27 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:28:27 [info] index finished after resolve  [object Object] 
2025-05-19 16:28:27 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:28:31 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:28:32 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:28:32 [info] index finished after resolve  [object Object] 
2025-05-19 16:28:32 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:28:34 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:28:34 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:28:34 [info] index finished after resolve  [object Object] 
2025-05-19 16:28:34 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:28:42 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:28:42 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:28:42 [info] index finished after resolve  [object Object] 
2025-05-19 16:28:42 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:15 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:15 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:15 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:15 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:29 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:30 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:30 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:30 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:32 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:32 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:32 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:32 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:35 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:35 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:35 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:35 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:37 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:37 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:37 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:37 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:41 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:41 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:41 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:41 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:44 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:44 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:44 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:44 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:48 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:48 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:48 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:48 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:55 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:55 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:55 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:55 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:33:57 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:33:57 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:33:57 [info] index finished after resolve  [object Object] 
2025-05-19 16:33:57 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:03 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:04 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:04 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:04 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:06 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:06 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:06 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:06 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:10 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:10 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:10 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:10 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:23 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:23 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:23 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:23 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:39 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:39 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:39 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:39 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:46 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:46 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:46 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:46 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:34:48 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:34:49 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:34:49 [info] index finished after resolve  [object Object] 
2025-05-19 16:34:49 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:13 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:14 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:14 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:14 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:16 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:17 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:17 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:17 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:30 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:31 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:31 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:31 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:33 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:33 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:33 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:33 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:35 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:35 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:35 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:35 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:41 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:41 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:41 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:41 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:36:44 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:36:44 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:36:44 [info] index finished after resolve  [object Object] 
2025-05-19 16:36:44 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:09 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:09 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:09 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:09 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:12 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:12 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:12 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:12 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:15 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:15 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:15 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:15 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:21 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:21 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:21 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:21 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:25 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:25 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:25 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:25 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:27 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:28 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:28 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:28 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:37:51 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:37:52 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:37:52 [info] index finished after resolve  [object Object] 
2025-05-19 16:37:52 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:38:07 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:38:07 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:38:07 [info] index finished after resolve  [object Object] 
2025-05-19 16:38:07 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:38:10 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:38:11 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:38:11 [info] index finished after resolve  [object Object] 
2025-05-19 16:38:11 [info] refresh page data from resolve listeners 0 895   
2025-05-19 16:38:12 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 16:38:13 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 16:38:13 [info] index finished after resolve  [object Object] 
2025-05-19 16:38:13 [info] refresh page data from resolve listeners 0 895   
2025-05-19 17:08:06 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:08:06 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:08:07 [info] index finished after resolve  [object Object] 
2025-05-19 17:08:07 [info] refresh page data from resolve listeners 0 895   
2025-05-19 17:08:08 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:08:09 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:08:09 [info] index finished after resolve  [object Object] 
2025-05-19 17:08:09 [info] refresh page data from resolve listeners 0 895   
2025-05-19 17:08:11 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:08:11 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:08:11 [info] index finished after resolve  [object Object] 
2025-05-19 17:08:11 [info] refresh page data from resolve listeners 0 895   
2025-05-19 17:08:13 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:08:14 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:08:14 [info] index finished after resolve  [object Object] 
2025-05-19 17:08:14 [info] refresh page data from resolve listeners 0 895   
2025-05-19 17:08:30 [info] indexing created file 工作库/项目/舌诊/attachments/Efficient KAN-2025-05-19-17-08-30.png  [object Object] 
2025-05-19 17:08:30 [info] refresh page data from created listeners 0 896   
2025-05-19 17:08:32 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:08:32 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:08:32 [info] index finished after resolve  [object Object] 
2025-05-19 17:08:32 [info] refresh page data from resolve listeners 0 896   
2025-05-19 17:10:04 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:10:04 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:10:04 [info] index finished after resolve  [object Object] 
2025-05-19 17:10:04 [info] refresh page data from resolve listeners 0 896   
2025-05-19 17:10:21 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:10:21 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:10:21 [info] index finished after resolve  [object Object] 
2025-05-19 17:10:21 [info] refresh page data from resolve listeners 0 896   
2025-05-19 17:10:23 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:10:23 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:10:23 [info] index finished after resolve  [object Object] 
2025-05-19 17:10:23 [info] refresh page data from resolve listeners 0 896   
2025-05-19 17:10:26 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:10:27 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:10:27 [info] index finished after resolve  [object Object] 
2025-05-19 17:10:27 [info] refresh page data from resolve listeners 0 896   
2025-05-19 17:10:30 [info] ignore file modify evnet 工作库/项目/舌诊/Efficient KAN.md   
2025-05-19 17:10:30 [info] trigger 工作库/项目/舌诊/Efficient KAN.md resolve  [object Object] 
2025-05-19 17:10:30 [info] index finished after resolve  [object Object] 
2025-05-19 17:10:30 [info] refresh page data from resolve listeners 0 896   
