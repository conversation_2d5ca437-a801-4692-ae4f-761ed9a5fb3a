2025-05-17 16:58:14 [info] components database created cost 7 ms   
2025-05-17 16:58:14 [info] components index initializing...   
2025-05-17 16:58:19 [info] start to batch put pages: 5   
2025-05-17 16:58:19 [info] batch persist cost 5  877 
2025-05-17 16:58:20 [info] components index initialized, 892 files cost 5610 ms   
2025-05-17 16:58:20 [info] refresh page data from init listeners 0 892   
2025-05-17 16:58:25 [info] indexing created file components/logs/2025-05-17.components.log  [object Object] 
2025-05-17 16:58:25 [info] refresh page data from created listeners 0 893   
2025-05-17 16:58:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-17 16:58:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-17 16:58:31 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-17 16:58:31 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-17 16:58:31 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-17 16:58:31 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-17 17:09:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:09:50 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:09:50 [info] index finished after resolve  [object Object] 
2025-05-17 17:09:50 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:09:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:09:52 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:09:52 [info] index finished after resolve  [object Object] 
2025-05-17 17:09:52 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:09:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:09:54 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:09:54 [info] index finished after resolve  [object Object] 
2025-05-17 17:09:54 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:09:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:09:57 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:09:57 [info] index finished after resolve  [object Object] 
2025-05-17 17:09:57 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:09:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:09:59 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:09:59 [info] index finished after resolve  [object Object] 
2025-05-17 17:09:59 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:01 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:01 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:01 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:03 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:03 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:03 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:06 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:06 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:06 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:08 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:08 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:08 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:11 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:11 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:11 [info] refresh page data from resolve listeners 0 893   
2025-05-17 17:10:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 17:10:16 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 17:10:16 [info] index finished after resolve  [object Object] 
2025-05-17 17:10:16 [info] refresh page data from resolve listeners 0 893   
2025-05-17 21:59:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/7. 加载数据集.md   
2025-05-17 21:59:33 [info] trigger 学习库/Deep learning/pytorch/7. 加载数据集.md resolve  [object Object] 
2025-05-17 21:59:33 [info] index finished after resolve  [object Object] 
2025-05-17 21:59:33 [info] refresh page data from resolve listeners 0 893   
