2025-03-16 00:03:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:03:26 [info] indexing created file components/logs/2025-03-16.components.log  [object Object] 
2025-03-16 00:03:26 [info] refresh page data from created listeners 0 541   
2025-03-16 00:08:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:13:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:18:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:23:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:28:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:33:32 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:38:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:43:34 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:48:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:53:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 00:58:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:03:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:08:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:13:40 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:18:41 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:23:42 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:28:43 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:33:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:38:45 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:43:46 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:48:47 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:53:48 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 01:58:49 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:03:50 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:08:51 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:13:52 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:18:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:23:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:28:55 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:33:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:38:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:43:58 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:48:59 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:54:00 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 02:59:01 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:04:02 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:09:03 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:14:04 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:19:05 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:24:06 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:29:07 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:34:08 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:39:09 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:44:10 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:49:11 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:54:12 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 03:59:13 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:04:14 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:09:15 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:14:16 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:19:17 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:24:18 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:29:19 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:34:20 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:39:21 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:44:22 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:49:23 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:54:24 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 04:59:25 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:04:26 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:09:27 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:14:28 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:19:29 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:24:30 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:29:31 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:34:32 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:39:33 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:44:34 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:49:35 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:54:36 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 05:59:37 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:04:38 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:09:39 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:14:40 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:19:41 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:24:42 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:29:43 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:34:44 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:39:45 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:44:46 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:49:47 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:54:48 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 06:59:49 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:04:50 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:09:51 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:14:52 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:19:53 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:24:54 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:29:55 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:34:56 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:39:57 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:44:58 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:49:59 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 07:55:00 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 08:00:01 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 08:02:19 [info] indexing created file 日记库/day/2025-03-15.md  [object Object] 
2025-03-16 08:02:19 [info] indexing created ignore file 日记库/day/2025-03-15.md   
2025-03-16 08:02:19 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 08:02:19 [info] index finished after resolve  [object Object] 
2025-03-16 08:02:19 [info] refresh page data from resolve listeners 0 542   
2025-03-16 08:02:20 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-03-16 08:02:20 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-16 08:02:20 [info] index finished after resolve  [object Object] 
2025-03-16 08:02:20 [info] refresh page data from resolve listeners 0 542   
2025-03-16 08:02:21 [info] indexing created file Home/attachments/426.png  [object Object] 
2025-03-16 08:02:21 [info] refresh page data from created listeners 0 543   
2025-03-16 08:02:22 [info] indexing created file 日记库/fleeting_notes/attachments/427.png  [object Object] 
2025-03-16 08:02:22 [info] refresh page data from created listeners 0 544   
2025-03-16 08:02:22 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-03-16 08:03:20 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-16 08:03:20 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-16 08:03:20 [info] index finished after resolve  [object Object] 
2025-03-16 08:03:20 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:05:02 [info] auth: net::ERR_CERT_DATE_INVALID   
2025-03-16 08:05:22 [info] ignore file modify evnet 学习库/Anki/python/函数.md   
2025-03-16 08:05:22 [info] trigger 学习库/Anki/python/函数.md resolve  [object Object] 
2025-03-16 08:05:22 [info] index finished after resolve  [object Object] 
2025-03-16 08:05:22 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:07:03 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 08:07:26 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-16 08:07:26 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-16 08:07:26 [info] index finished after resolve  [object Object] 
2025-03-16 08:07:26 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:07:56 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-16 08:07:56 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-16 08:07:56 [info] index finished after resolve  [object Object] 
2025-03-16 08:07:56 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:08:31 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-16 08:08:31 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-16 08:08:31 [info] index finished after resolve  [object Object] 
2025-03-16 08:08:31 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:08:49 [info] ignore file modify evnet 学习库/Anki/python/文件操作.md   
2025-03-16 08:08:49 [info] trigger 学习库/Anki/python/文件操作.md resolve  [object Object] 
2025-03-16 08:08:49 [info] index finished after resolve  [object Object] 
2025-03-16 08:08:49 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:01 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-16 08:09:01 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-16 08:09:01 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:01 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:02 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-16 08:09:02 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-16 08:09:02 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:02 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:06 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-16 08:09:06 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-16 08:09:06 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:06 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:13 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-16 08:09:13 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-16 08:09:13 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:13 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:31 [info] ignore file modify evnet 学习库/Anki/二级/公共基础.md   
2025-03-16 08:09:31 [info] trigger 学习库/Anki/二级/公共基础.md resolve  [object Object] 
2025-03-16 08:09:31 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:31 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:09:40 [info] ignore file modify evnet 学习库/Anki/二级/程序设计题.md   
2025-03-16 08:09:40 [info] trigger 学习库/Anki/二级/程序设计题.md resolve  [object Object] 
2025-03-16 08:09:40 [info] index finished after resolve  [object Object] 
2025-03-16 08:09:40 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:11:42 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-16 08:11:42 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-16 08:11:42 [info] index finished after resolve  [object Object] 
2025-03-16 08:11:42 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:12:03 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 08:14:43 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-16 08:14:43 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-16 08:14:43 [info] index finished after resolve  [object Object] 
2025-03-16 08:14:43 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:15:01 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-16 08:15:01 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-16 08:15:01 [info] index finished after resolve  [object Object] 
2025-03-16 08:15:01 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:15:07 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-16 08:15:07 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-16 08:15:07 [info] index finished after resolve  [object Object] 
2025-03-16 08:15:07 [info] refresh page data from resolve listeners 0 544   
2025-03-16 08:15:10 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-16 08:15:10 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-16 08:15:10 [info] index finished after resolve  [object Object] 
2025-03-16 08:15:10 [info] refresh page data from resolve listeners 0 544   
2025-03-16 19:54:27 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 19:54:29 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 20:02:36 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 20:02:36 [info] components database created cost 38 ms   
2025-03-16 20:02:36 [info] components index initializing...   
2025-03-16 20:02:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 20:02:36 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 20:02:36 [info] components index initialized, 544 files cost 359 ms   
2025-03-16 20:02:36 [info] refresh page data from init listeners 0 544   
2025-03-16 20:02:36 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 20:02:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 20:02:37 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 20:02:37 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 20:02:37 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 20:02:37 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 20:02:41 [info] ignore file modify evnet 学习库/Deep learning/概念库/语义分割/语义分割.md   
2025-03-16 20:02:41 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-03-16 20:02:41 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:41 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:42 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 20:02:42 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 20:02:42 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:42 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:42 [info] ignore file modify evnet 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md   
2025-03-16 20:02:42 [info] trigger 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md resolve  [object Object] 
2025-03-16 20:02:42 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:42 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:42 [info] ignore file modify evnet 学习库/Anki/单词/python.md   
2025-03-16 20:02:43 [info] trigger 学习库/Anki/单词/python.md resolve  [object Object] 
2025-03-16 20:02:43 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:43 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:43 [info] ignore file modify evnet 日记库/day/2025-03-11.md   
2025-03-16 20:02:43 [info] trigger 日记库/day/2025-03-11.md resolve  [object Object] 
2025-03-16 20:02:43 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:43 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:45 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 20:02:45 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 20:02:45 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:45 [info] refresh page data from resolve listeners 0 544   
2025-03-16 20:02:45 [info] indexing created file 学习库/Anki/二级/attachments/选择题-2025-03-16-16-07-52.png  [object Object] 
2025-03-16 20:02:45 [info] refresh page data from created listeners 0 545   
2025-03-16 20:02:46 [info] ignore file modify evnet 学习库/Anki/二级/选择题.md   
2025-03-16 20:02:46 [info] trigger 学习库/Anki/二级/选择题.md resolve  [object Object] 
2025-03-16 20:02:46 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:46 [info] refresh page data from resolve listeners 0 545   
2025-03-16 20:02:46 [info] ignore file modify evnet 日记库/day/2025-03-14.md   
2025-03-16 20:02:46 [info] trigger 日记库/day/2025-03-14.md resolve  [object Object] 
2025-03-16 20:02:46 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:46 [info] refresh page data from resolve listeners 0 545   
2025-03-16 20:02:48 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-16 20:02:48 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 20:02:48 [info] index finished after resolve  [object Object] 
2025-03-16 20:02:48 [info] refresh page data from resolve listeners 0 545   
2025-03-16 20:03:05 [info] indexing created file 学习库/Deep learning/概念库/语义分割/Attachments/语义分割-2025-03-16-08-42-15.png  [object Object] 
2025-03-16 20:03:05 [info] refresh page data from created listeners 0 546   
2025-03-16 20:03:05 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-03-16 20:18:03 [info] ignore file modify evnet 学习库/Anki/python/数据容器.md   
2025-03-16 20:18:03 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-16 20:18:03 [info] index finished after resolve  [object Object] 
2025-03-16 20:18:03 [info] refresh page data from resolve listeners 0 546   
2025-03-16 20:18:05 [info] ignore file modify evnet 学习库/python笔记/变量/运算符.md   
2025-03-16 20:18:05 [info] trigger 学习库/python笔记/变量/运算符.md resolve  [object Object] 
2025-03-16 20:18:05 [info] index finished after resolve  [object Object] 
2025-03-16 20:18:05 [info] refresh page data from resolve listeners 0 546   
2025-03-16 20:18:07 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-19-57-38.png  [object Object] 
2025-03-16 20:18:07 [info] refresh page data from created listeners 0 547   
2025-03-16 20:18:09 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-03-27.png  [object Object] 
2025-03-16 20:18:09 [info] refresh page data from created listeners 0 548   
2025-03-16 20:18:10 [info] indexing created file 学习库/Anki/python/attachments/数据容器-2025-03-16-16-46-29.png  [object Object] 
2025-03-16 20:18:10 [info] refresh page data from created listeners 0 549   
2025-03-16 20:18:10 [info] trigger 学习库/Anki/python/数据容器.md resolve  [object Object] 
2025-03-16 20:18:12 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-10-17.png  [object Object] 
2025-03-16 20:18:12 [info] refresh page data from created listeners 0 550   
2025-03-16 20:18:13 [info] indexing created file 日记库/day/attachments/2025-03-13-2025-03-16-20-11-35.png  [object Object] 
2025-03-16 20:18:13 [info] refresh page data from created listeners 0 551   
2025-03-16 20:18:14 [info] refresh page data from delete listeners 0 550   
2025-03-16 20:18:14 [info] refresh page data from delete listeners 0 549   
2025-03-16 20:18:14 [info] refresh page data from delete listeners 0 548   
2025-03-16 20:18:15 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 20:18:15 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 20:18:15 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 20:18:15 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 20:18:15 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 20:18:15 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 20:18:15 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 20:18:15 [info] index finished after resolve  [object Object] 
2025-03-16 20:18:15 [info] refresh page data from resolve listeners 0 548   
2025-03-16 20:18:16 [info] indexing created file 日记库/day/2025-03-13.md  [object Object] 
2025-03-16 20:18:16 [info] indexing created ignore file 日记库/day/2025-03-13.md   
2025-03-16 20:18:16 [info] trigger 日记库/day/2025-03-13.md resolve  [object Object] 
2025-03-16 20:18:16 [info] index finished after resolve  [object Object] 
2025-03-16 20:18:16 [info] refresh page data from resolve listeners 0 549   
2025-03-16 23:30:18 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:30:19 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 23:30:19 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:30:19 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:30:19 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:30:19 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:30:22 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:30:22 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:30:22 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:22 [info] refresh page data from resolve listeners 0 549   
2025-03-16 23:30:26 [info] indexing created file 日记库/day/2025-03-15.md  [object Object] 
2025-03-16 23:30:26 [info] indexing created ignore file 日记库/day/2025-03-15.md   
2025-03-16 23:30:26 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 23:30:26 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:26 [info] refresh page data from resolve listeners 0 550   
2025-03-16 23:30:26 [info] refresh page data from delete listeners 0 549   
2025-03-16 23:30:27 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-20-44-07.png  [object Object] 
2025-03-16 23:30:27 [info] refresh page data from created listeners 0 550   
2025-03-16 23:30:27 [info] indexing created file 日记库/day/attachments/2025-03-16-2025-03-16-21-07-35.png  [object Object] 
2025-03-16 23:30:27 [info] refresh page data from created listeners 0 551   
2025-03-16 23:30:28 [info] indexing created file 日记库/day/2025-03-16.md  [object Object] 
2025-03-16 23:30:28 [info] indexing created ignore file 日记库/day/2025-03-16.md   
2025-03-16 23:30:28 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:30:28 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:28 [info] refresh page data from resolve listeners 0 552   
2025-03-16 23:30:33 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:30:33 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:30:33 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:33 [info] refresh page data from resolve listeners 0 552   
2025-03-16 23:30:39 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:30:39 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:30:39 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:39 [info] refresh page data from resolve listeners 0 552   
2025-03-16 23:30:54 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:30:54 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:30:54 [info] index finished after resolve  [object Object] 
2025-03-16 23:30:54 [info] refresh page data from resolve listeners 0 552   
2025-03-16 23:31:39 [info] indexing created file 日记库/day/attachments/1207929781.png  [object Object] 
2025-03-16 23:31:39 [info] refresh page data from created listeners 0 553   
2025-03-16 23:31:45 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:31:46 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:31:46 [info] index finished after resolve  [object Object] 
2025-03-16 23:31:46 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:31:54 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:31:54 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:31:54 [info] index finished after resolve  [object Object] 
2025-03-16 23:31:54 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:31:55 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:31:56 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:31:56 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:31:56 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:31:58 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:31:58 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:31:58 [info] index finished after resolve  [object Object] 
2025-03-16 23:31:58 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:32:04 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:32:04 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:32:04 [info] index finished after resolve  [object Object] 
2025-03-16 23:32:04 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:32:17 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:32:17 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:32:17 [info] index finished after resolve  [object Object] 
2025-03-16 23:32:17 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:32:23 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:32:23 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:32:23 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:32:24 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:32:24 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:32:24 [info] index finished after resolve  [object Object] 
2025-03-16 23:32:24 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:32:24 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:32:24 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:32:24 [info] index finished after resolve  [object Object] 
2025-03-16 23:32:24 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:32:24 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:32:24 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:32:24 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:32:25 [info] ignore file modify evnet 日记库/day/2025-03-12.md   
2025-03-16 23:32:25 [info] trigger 日记库/day/2025-03-12.md resolve  [object Object] 
2025-03-16 23:32:25 [info] index finished after resolve  [object Object] 
2025-03-16 23:32:25 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:41:33 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 23:41:35 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:41:35 [info] components database created cost 1 ms   
2025-03-16 23:41:35 [info] components index initializing...   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [info] components index initialized, 553 files cost 64 ms   
2025-03-16 23:41:35 [info] refresh page data from init listeners 0 553   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:35 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-03-16 23:41:36 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:41:36 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:41:36 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:41:37 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:41:37 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:41:37 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:41:37 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:41:42 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:41:42 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:41:42 [info] index finished after resolve  [object Object] 
2025-03-16 23:41:42 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:41:50 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:41:50 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:41:50 [info] index finished after resolve  [object Object] 
2025-03-16 23:41:50 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:41:52 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:41:52 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:41:52 [info] index finished after resolve  [object Object] 
2025-03-16 23:41:52 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:41:56 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:41:56 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:41:56 [info] index finished after resolve  [object Object] 
2025-03-16 23:41:56 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:41:59 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:41:59 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:41:59 [info] index finished after resolve  [object Object] 
2025-03-16 23:41:59 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:42:05 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:42:05 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:42:05 [info] index finished after resolve  [object Object] 
2025-03-16 23:42:05 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:42:07 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:42:07 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:42:07 [info] index finished after resolve  [object Object] 
2025-03-16 23:42:07 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:42:12 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:42:12 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:42:12 [info] index finished after resolve  [object Object] 
2025-03-16 23:42:12 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:42:15 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:42:15 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:42:15 [info] index finished after resolve  [object Object] 
2025-03-16 23:42:15 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:42:23 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:42:23 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:42:23 [info] index finished after resolve  [object Object] 
2025-03-16 23:42:23 [info] refresh page data from resolve listeners 0 553   
2025-03-16 23:52:02 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 23:52:04 [info] indexing created file 日记库/day/attachments/S50316-23514890_com.xingin.xhs.png  [object Object] 
2025-03-16 23:52:04 [info] refresh page data from created listeners 0 554   
2025-03-16 23:52:08 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:52:08 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:52:08 [info] index finished after resolve  [object Object] 
2025-03-16 23:52:08 [info] refresh page data from resolve listeners 0 554   
2025-03-16 23:52:08 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:52:08 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:52:08 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:52:08 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-16 23:52:08 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:52:09 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:52:09 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-16 23:52:09 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-16 23:52:09 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-16 23:52:09 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-16 23:55:58 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:55:58 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:55:58 [info] index finished after resolve  [object Object] 
2025-03-16 23:55:58 [info] refresh page data from resolve listeners 0 554   
2025-03-16 23:56:32 [info] indexing created file 日记库/day/attachments/S50316-23561981_com.xingin.xhs.png  [object Object] 
2025-03-16 23:56:32 [info] refresh page data from created listeners 0 555   
2025-03-16 23:56:51 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-16 23:56:51 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 23:56:51 [info] index finished after resolve  [object Object] 
2025-03-16 23:56:51 [info] refresh page data from resolve listeners 0 555   
2025-03-16 23:56:54 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:56:54 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:56:54 [info] index finished after resolve  [object Object] 
2025-03-16 23:56:54 [info] refresh page data from resolve listeners 0 555   
2025-03-16 23:56:58 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-16 23:56:58 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 23:56:58 [info] index finished after resolve  [object Object] 
2025-03-16 23:56:58 [info] refresh page data from resolve listeners 0 555   
2025-03-16 23:57:03 [info] auth: Request Failed. SSLHandshakeException Chain validation failed   
2025-03-16 23:57:04 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:57:04 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:57:04 [info] index finished after resolve  [object Object] 
2025-03-16 23:57:04 [info] refresh page data from resolve listeners 0 555   
2025-03-16 23:58:19 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:58:19 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:58:19 [info] index finished after resolve  [object Object] 
2025-03-16 23:58:19 [info] refresh page data from resolve listeners 0 555   
2025-03-16 23:58:36 [info] indexing created file 日记库/day/attachments/S50316-23582502_com.xingin.xhs.png  [object Object] 
2025-03-16 23:58:36 [info] refresh page data from created listeners 0 556   
2025-03-16 23:58:39 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:58:39 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:58:39 [info] index finished after resolve  [object Object] 
2025-03-16 23:58:39 [info] refresh page data from resolve listeners 0 556   
2025-03-16 23:58:41 [info] ignore file modify evnet 日记库/day/2025-03-15.md   
2025-03-16 23:58:41 [info] trigger 日记库/day/2025-03-15.md resolve  [object Object] 
2025-03-16 23:58:41 [info] index finished after resolve  [object Object] 
2025-03-16 23:58:41 [info] refresh page data from resolve listeners 0 556   
2025-03-16 23:58:44 [info] ignore file modify evnet 日记库/day/2025-03-16.md   
2025-03-16 23:58:44 [info] trigger 日记库/day/2025-03-16.md resolve  [object Object] 
2025-03-16 23:58:44 [info] index finished after resolve  [object Object] 
2025-03-16 23:58:44 [info] refresh page data from resolve listeners 0 556   
