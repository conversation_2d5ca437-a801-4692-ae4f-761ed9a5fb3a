2025-03-31 09:50:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:27 [info] indexing created file components/logs/2025-03-31.components.log  [object Object] 
2025-03-31 09:50:27 [info] refresh page data from created listeners 0 687   
2025-03-31 09:50:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:27 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:27 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:29 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:29 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:31 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:31 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:33 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:33 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:36 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:36 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:38 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:38 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:40 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:40 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:43 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:43 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:50:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:50:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:50:45 [info] index finished after resolve  [object Object] 
2025-03-31 09:50:45 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:51:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:51:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:51:55 [info] index finished after resolve  [object Object] 
2025-03-31 09:51:55 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:51:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:51:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:51:58 [info] index finished after resolve  [object Object] 
2025-03-31 09:51:58 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:06 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:06 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:08 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:08 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:14 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:14 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:16 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:16 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:18 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:18 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:20 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:20 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:52:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:52:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:52:23 [info] index finished after resolve  [object Object] 
2025-03-31 09:52:23 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:10 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:10 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:12 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:12 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:17 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:17 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:20 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:20 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:22 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:22 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:25 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:25 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:27 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:27 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:31 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:31 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:34 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:34 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:36 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:36 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:46 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:46 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:48 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:48 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:51 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:51 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:53 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:53 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:55 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:55 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:57 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:57 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:53:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:53:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:53:59 [info] index finished after resolve  [object Object] 
2025-03-31 09:53:59 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:54:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:54:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:54:01 [info] index finished after resolve  [object Object] 
2025-03-31 09:54:01 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:54:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:54:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:54:05 [info] index finished after resolve  [object Object] 
2025-03-31 09:54:05 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:54:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:54:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:54:09 [info] index finished after resolve  [object Object] 
2025-03-31 09:54:09 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:54:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:54:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:54:17 [info] index finished after resolve  [object Object] 
2025-03-31 09:54:17 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:55:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:55:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:55:18 [info] index finished after resolve  [object Object] 
2025-03-31 09:55:18 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:58:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:58:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:58:02 [info] index finished after resolve  [object Object] 
2025-03-31 09:58:02 [info] refresh page data from resolve listeners 0 687   
2025-03-31 09:58:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 09:58:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 09:58:06 [info] index finished after resolve  [object Object] 
2025-03-31 09:58:06 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:16 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:16 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:32 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:32 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:34 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:34 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:39 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:39 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:41 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:41 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:45 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:45 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:48 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:48 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:50 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:50 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:05:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:05:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:05:53 [info] index finished after resolve  [object Object] 
2025-03-31 10:05:53 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:06:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:06:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:06:52 [info] index finished after resolve  [object Object] 
2025-03-31 10:06:52 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:06:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:06:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:06:54 [info] index finished after resolve  [object Object] 
2025-03-31 10:06:54 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:06:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:06:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:06:57 [info] index finished after resolve  [object Object] 
2025-03-31 10:06:57 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:06:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:06:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:06:59 [info] index finished after resolve  [object Object] 
2025-03-31 10:06:59 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:01 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:03 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:03 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:08 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:08 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:10 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:10 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:15 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:15 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:19 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:19 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:21 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:21 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:24 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:07:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:07:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:07:25 [info] index finished after resolve  [object Object] 
2025-03-31 10:07:25 [info] refresh page data from resolve listeners 0 687   
2025-03-31 10:16:24 [info] indexing created file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md  [object Object] 
2025-03-31 10:16:24 [info] indexing created ignore file Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:16:24 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:16:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:16:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:16:25 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-31 10:16:25 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-31 10:16:25 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-31 10:16:25 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-31 10:16:25 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-31 10:16:58 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:16:58 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:16:58 [info] index finished after resolve  [object Object] 
2025-03-31 10:16:58 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:17:14 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:17:14 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:17:14 [info] index finished after resolve  [object Object] 
2025-03-31 10:17:14 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:17:29 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:17:29 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:17:29 [info] index finished after resolve  [object Object] 
2025-03-31 10:17:29 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:17:31 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:17:31 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:17:31 [info] index finished after resolve  [object Object] 
2025-03-31 10:17:31 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:17:33 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:17:33 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:17:33 [info] index finished after resolve  [object Object] 
2025-03-31 10:17:33 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:17:50 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:17:50 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:17:50 [info] index finished after resolve  [object Object] 
2025-03-31 10:17:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:18:05 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:18:05 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:18:05 [info] index finished after resolve  [object Object] 
2025-03-31 10:18:05 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:18:21 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:18:21 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:18:21 [info] index finished after resolve  [object Object] 
2025-03-31 10:18:21 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:18:37 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:18:37 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:18:37 [info] index finished after resolve  [object Object] 
2025-03-31 10:18:37 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:18:53 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:18:53 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:18:53 [info] index finished after resolve  [object Object] 
2025-03-31 10:18:53 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:19:09 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:19:09 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:19:09 [info] index finished after resolve  [object Object] 
2025-03-31 10:19:09 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:19:24 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:19:24 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:19:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:19:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:19:56 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:19:56 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:19:56 [info] index finished after resolve  [object Object] 
2025-03-31 10:19:56 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:20:13 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:20:13 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:20:13 [info] index finished after resolve  [object Object] 
2025-03-31 10:20:13 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:20:30 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:20:30 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:20:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:20:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:20:36 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:20:36 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:20:36 [info] index finished after resolve  [object Object] 
2025-03-31 10:20:36 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:20:52 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:20:52 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:20:52 [info] index finished after resolve  [object Object] 
2025-03-31 10:20:52 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:21:07 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:21:07 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:21:07 [info] index finished after resolve  [object Object] 
2025-03-31 10:21:07 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:21:15 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:21:15 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:21:15 [info] index finished after resolve  [object Object] 
2025-03-31 10:21:15 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:22:01 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:22:01 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:22:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:22:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:22:14 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-31-10-22-14.gif  [object Object] 
2025-03-31 10:22:14 [info] refresh page data from created listeners 0 689   
2025-03-31 10:22:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:22:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:22:16 [info] index finished after resolve  [object Object] 
2025-03-31 10:22:16 [info] refresh page data from resolve listeners 0 689   
2025-03-31 10:22:59 [info] refresh page data from delete listeners 0 688   
2025-03-31 10:22:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:23:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:23:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:23:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:23:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:23:07 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-31 10:23:08 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-31 10:23:08 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-31 10:23:08 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-31 10:23:08 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-31 10:23:26 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-03-31 10:23:27 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-03-31 10:23:27 [info] ignore file modify evnet Home/components/view/remember.components   
2025-03-31 10:23:27 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-03-31 10:23:27 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-03-31 10:23:42 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:23:42 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:23:42 [info] index finished after resolve  [object Object] 
2025-03-31 10:23:42 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:24:00 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:24:00 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:24:00 [info] index finished after resolve  [object Object] 
2025-03-31 10:24:00 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:24:16 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:24:16 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:24:16 [info] index finished after resolve  [object Object] 
2025-03-31 10:24:16 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:24:18 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:24:18 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:24:18 [info] index finished after resolve  [object Object] 
2025-03-31 10:24:18 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:24:34 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:24:34 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:24:34 [info] index finished after resolve  [object Object] 
2025-03-31 10:24:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:24:44 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:24:44 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:24:44 [info] index finished after resolve  [object Object] 
2025-03-31 10:24:44 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:01 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:01 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:12 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:12 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:12 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:12 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:19 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:19 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:19 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:19 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:24 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:24 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:30 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:30 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:25:47 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:25:47 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:25:47 [info] index finished after resolve  [object Object] 
2025-03-31 10:25:47 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:27:11 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:27:11 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:27:11 [info] index finished after resolve  [object Object] 
2025-03-31 10:27:11 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:27:13 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:27:13 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:27:13 [info] index finished after resolve  [object Object] 
2025-03-31 10:27:13 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:27:30 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:27:30 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:27:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:27:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:27:46 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:27:46 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:27:46 [info] index finished after resolve  [object Object] 
2025-03-31 10:27:46 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:28:19 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:28:19 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:28:19 [info] index finished after resolve  [object Object] 
2025-03-31 10:28:19 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:28:34 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:28:34 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:28:34 [info] index finished after resolve  [object Object] 
2025-03-31 10:28:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:28:50 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:28:50 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:28:50 [info] index finished after resolve  [object Object] 
2025-03-31 10:28:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:29:08 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:29:08 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:29:08 [info] index finished after resolve  [object Object] 
2025-03-31 10:29:08 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:29:27 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:29:27 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:29:27 [info] index finished after resolve  [object Object] 
2025-03-31 10:29:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:29:43 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:29:43 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:29:43 [info] index finished after resolve  [object Object] 
2025-03-31 10:29:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:29:59 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:29:59 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:29:59 [info] index finished after resolve  [object Object] 
2025-03-31 10:29:59 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:30:15 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:30:15 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:30:15 [info] index finished after resolve  [object Object] 
2025-03-31 10:30:15 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:30:31 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:30:31 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:30:31 [info] index finished after resolve  [object Object] 
2025-03-31 10:30:31 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:30:47 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:30:47 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:30:47 [info] index finished after resolve  [object Object] 
2025-03-31 10:30:47 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:31:25 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:31:25 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:31:25 [info] index finished after resolve  [object Object] 
2025-03-31 10:31:25 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:31:40 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:31:40 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:31:40 [info] index finished after resolve  [object Object] 
2025-03-31 10:31:40 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:32:06 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-31-10-32-06.png  [object Object] 
2025-03-31 10:32:07 [info] refresh page data from created listeners 0 689   
2025-03-31 10:32:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:32:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:07 [info] index finished after resolve  [object Object] 
2025-03-31 10:32:07 [info] refresh page data from resolve listeners 0 689   
2025-03-31 10:32:15 [info] refresh page data from delete listeners 0 688   
2025-03-31 10:32:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:32:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:17 [info] index finished after resolve  [object Object] 
2025-03-31 10:32:17 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:32:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:32:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:18 [info] index finished after resolve  [object Object] 
2025-03-31 10:32:18 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:32:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:32:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:33 [info] index finished after resolve  [object Object] 
2025-03-31 10:32:33 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:32:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:32:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:32:36 [info] index finished after resolve  [object Object] 
2025-03-31 10:32:36 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:34:11 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:34:11 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:34:11 [info] index finished after resolve  [object Object] 
2025-03-31 10:34:11 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:34:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:34:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:34:26 [info] index finished after resolve  [object Object] 
2025-03-31 10:34:26 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:34:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:34:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:34:32 [info] index finished after resolve  [object Object] 
2025-03-31 10:34:32 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:34:55 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:34:55 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:34:55 [info] index finished after resolve  [object Object] 
2025-03-31 10:34:55 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:01 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:35:01 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:35:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:05 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:35:05 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:35:05 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:05 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:27 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:29 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:29 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:31 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:31 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:34 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:42 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:42 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:50 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:52 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:52 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:35:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:35:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:35:54 [info] index finished after resolve  [object Object] 
2025-03-31 10:35:54 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:04 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:04 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:06 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:06 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:09 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:09 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:11 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:11 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:15 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:15 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:17 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:17 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:23 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:23 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:26 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:26 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:28 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:28 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:32 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:32 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:35 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:35 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:37 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:37 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:37 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:42 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:42 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:42 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:45 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:45 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:47 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:47 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:49 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:49 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:52 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:52 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:54 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:54 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:36:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:36:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:36:56 [info] index finished after resolve  [object Object] 
2025-03-31 10:36:56 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:02 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:02 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:05 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:05 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:07 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:07 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:09 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:09 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:11 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:11 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:15 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:15 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:22 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:22 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:25 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:25 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:31 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:31 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:31 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:31 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:34 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:36 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:36 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:39 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:39 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:41 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:41 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:45 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:45 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:51 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:51 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:54 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:54 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:56 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:56 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:56 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:56 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:37:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:37:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:37:58 [info] index finished after resolve  [object Object] 
2025-03-31 10:37:58 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:38:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:38:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:38:00 [info] index finished after resolve  [object Object] 
2025-03-31 10:38:00 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:38:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:38:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:38:07 [info] index finished after resolve  [object Object] 
2025-03-31 10:38:07 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:37 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:37 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:39 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:39 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:41 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:41 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:43 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:46 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:46 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:48 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:48 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:50 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:53 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:53 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:53 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:53 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:55 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:55 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:57 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:57 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:39:59 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:39:59 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:39:59 [info] index finished after resolve  [object Object] 
2025-03-31 10:39:59 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:04 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:04 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:04 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:04 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:16 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:16 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:18 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:18 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:20 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:20 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:20 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:20 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:23 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:23 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:23 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:23 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:27 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:32 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:32 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:35 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:35 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:45 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:45 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:40:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:40:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:40:58 [info] index finished after resolve  [object Object] 
2025-03-31 10:40:58 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:06 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:06 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:08 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:08 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:11 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:11 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:12 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:12 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:21 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:21 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:41:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:41:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:41:25 [info] index finished after resolve  [object Object] 
2025-03-31 10:41:25 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:42:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:42:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:42:27 [info] index finished after resolve  [object Object] 
2025-03-31 10:42:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:42:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:42:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:42:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:42:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:42:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:42:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:42:43 [info] index finished after resolve  [object Object] 
2025-03-31 10:42:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:46:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:46:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:46:03 [info] index finished after resolve  [object Object] 
2025-03-31 10:46:03 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:46:06 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:46:06 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:46:06 [info] index finished after resolve  [object Object] 
2025-03-31 10:46:06 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:46:24 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:46:24 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:46:24 [info] index finished after resolve  [object Object] 
2025-03-31 10:46:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:46:30 [info] ignore file modify evnet Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md   
2025-03-31 10:46:30 [info] trigger Excalidraw/Drawing 2025-03-31 10.16.24.excalidraw.md resolve  [object Object] 
2025-03-31 10:46:30 [info] index finished after resolve  [object Object] 
2025-03-31 10:46:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:46:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:46:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:46:47 [info] index finished after resolve  [object Object] 
2025-03-31 10:46:47 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:47:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:47:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:47:01 [info] index finished after resolve  [object Object] 
2025-03-31 10:47:01 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:47:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:47:18 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:47:18 [info] index finished after resolve  [object Object] 
2025-03-31 10:47:18 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:47:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:47:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:47:27 [info] index finished after resolve  [object Object] 
2025-03-31 10:47:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 10:47:42 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 10:47:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 10:47:43 [info] index finished after resolve  [object Object] 
2025-03-31 10:47:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 11:17:25 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 11:17:25 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 11:17:25 [info] index finished after resolve  [object Object] 
2025-03-31 11:17:25 [info] refresh page data from resolve listeners 0 688   
2025-03-31 11:17:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 11:17:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 11:17:26 [info] index finished after resolve  [object Object] 
2025-03-31 11:17:26 [info] refresh page data from resolve listeners 0 688   
2025-03-31 11:17:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 11:17:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 11:17:34 [info] index finished after resolve  [object Object] 
2025-03-31 11:17:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 11:17:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 11:17:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 11:17:39 [info] index finished after resolve  [object Object] 
2025-03-31 11:17:39 [info] refresh page data from resolve listeners 0 688   
2025-03-31 11:17:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 11:17:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 11:17:43 [info] index finished after resolve  [object Object] 
2025-03-31 11:17:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:14:29 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:14:29 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:14:29 [info] index finished after resolve  [object Object] 
2025-03-31 14:14:29 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:14:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:14:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:14:32 [info] index finished after resolve  [object Object] 
2025-03-31 14:14:32 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:19:35 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:19:35 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:19:35 [info] index finished after resolve  [object Object] 
2025-03-31 14:19:35 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:19:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:19:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:19:43 [info] index finished after resolve  [object Object] 
2025-03-31 14:19:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:19:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:19:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:19:45 [info] index finished after resolve  [object Object] 
2025-03-31 14:19:45 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:19:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:19:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:19:47 [info] index finished after resolve  [object Object] 
2025-03-31 14:19:47 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:22 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:22 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:24 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:27 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:27 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:27 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:27 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:30 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:32 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:32 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:32 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:32 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:34 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:34 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:34 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:34 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:37 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:37 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:37 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:37 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:39 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:39 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:39 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:39 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:41 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:41 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:43 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:45 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:45 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:48 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:48 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:50 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:52 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:52 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:52 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:52 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:55 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:55 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:55 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:55 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:20:57 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:20:57 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:20:57 [info] index finished after resolve  [object Object] 
2025-03-31 14:20:57 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:00 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:00 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:00 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:00 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:02 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:02 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:02 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:02 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:05 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:05 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:08 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:08 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:10 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:10 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:13 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:13 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:13 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:15 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:15 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:15 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:15 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:17 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:17 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:19 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:19 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:19 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:21 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:21 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:21 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:21 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:24 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:24 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:26 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:26 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:28 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:28 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:30 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:30 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:33 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:33 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:36 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:36 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:38 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:38 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:41 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:41 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:43 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:43 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:46 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:46 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:46 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:46 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:48 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:48 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:21:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:21:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:21:50 [info] index finished after resolve  [object Object] 
2025-03-31 14:21:50 [info] refresh page data from resolve listeners 0 688   
2025-03-31 14:22:07 [info] indexing created file 学习库/c/attachments/6 数组-2025-03-31-14-22-07.png  [object Object] 
2025-03-31 14:22:07 [info] refresh page data from created listeners 0 689   
2025-03-31 14:22:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:22:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:22:08 [info] index finished after resolve  [object Object] 
2025-03-31 14:22:08 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:22:41 [info] trigger 学习库/c/attachments/6 数组-2025-03-31-14-22-07.png resolve  [object Object] 
2025-03-31 14:22:41 [info] index finished after resolve  [object Object] 
2025-03-31 14:22:41 [info] refresh page data from modify listeners 0 689   
2025-03-31 14:23:07 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:07 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:07 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:07 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:09 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:09 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:09 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:09 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:12 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:12 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:16 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:16 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:16 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:16 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:18 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:19 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:19 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:19 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:22 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:22 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:22 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:22 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:24 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:24 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:24 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:24 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:26 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:26 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:26 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:26 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:28 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:28 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:28 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:28 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:30 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:30 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:30 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:30 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:33 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:33 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:33 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:33 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:36 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:36 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:36 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:36 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:38 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:38 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:41 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:41 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:41 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:41 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:48 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:48 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:48 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:48 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:23:50 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:23:50 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:23:50 [info] index finished after resolve  [object Object] 
2025-03-31 14:23:50 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:05 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:05 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:08 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:08 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:08 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:08 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:10 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:10 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:10 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:10 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:12 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:12 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:12 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:12 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:14 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:14 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:14 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:14 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:17 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:17 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:17 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:17 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:38 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:38 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:38 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:38 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:40 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:40 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:40 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:40 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:43 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:43 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:43 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:43 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:45 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:45 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:45 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:45 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:47 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:47 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:47 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:47 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:49 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:49 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:49 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:49 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:51 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:51 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:51 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:51 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:27:54 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:27:54 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:27:54 [info] index finished after resolve  [object Object] 
2025-03-31 14:27:54 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:30:58 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:30:58 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:30:58 [info] index finished after resolve  [object Object] 
2025-03-31 14:30:58 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:31:01 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:31:01 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:31:01 [info] index finished after resolve  [object Object] 
2025-03-31 14:31:01 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:31:03 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:31:03 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:31:03 [info] index finished after resolve  [object Object] 
2025-03-31 14:31:03 [info] refresh page data from resolve listeners 0 689   
2025-03-31 14:31:05 [info] ignore file modify evnet 学习库/c/6 数组.md   
2025-03-31 14:31:05 [info] trigger 学习库/c/6 数组.md resolve  [object Object] 
2025-03-31 14:31:05 [info] index finished after resolve  [object Object] 
2025-03-31 14:31:05 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:05:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:05:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:05:35 [info] index finished after resolve  [object Object] 
2025-03-31 15:05:35 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:04 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:04 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:04 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:04 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:13 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:13 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:13 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:13 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:23 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:23 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:25 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:25 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:27 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:28 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:28 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:31 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:31 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:31 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:31 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:33 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:33 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:33 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:33 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:35 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:35 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:35 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:35 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:37 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:37 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:09:54 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:09:54 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:09:54 [info] index finished after resolve  [object Object] 
2025-03-31 15:09:54 [info] refresh page data from resolve listeners 0 689   
2025-03-31 15:10:00 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 15:10:00 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 15:10:00 [info] index finished after resolve  [object Object] 
2025-03-31 15:10:00 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:39:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:39:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:39:23 [info] index finished after resolve  [object Object] 
2025-03-31 19:39:23 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:11 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:11 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:11 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:11 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:14 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:14 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:14 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:14 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:16 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:16 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:16 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:16 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:18 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:19 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:19 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:19 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:21 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:21 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:21 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:21 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:23 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:23 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:23 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:23 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:25 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:25 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:25 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:25 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:28 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:28 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:28 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:28 [info] refresh page data from resolve listeners 0 689   
2025-03-31 19:42:30 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 19:42:30 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 19:42:30 [info] index finished after resolve  [object Object] 
2025-03-31 19:42:30 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:29:27 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-31 20:29:27 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-31 20:29:27 [info] index finished after resolve  [object Object] 
2025-03-31 20:29:27 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:29:41 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-31 20:29:41 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-31 20:29:41 [info] index finished after resolve  [object Object] 
2025-03-31 20:29:41 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:29:44 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-31 20:29:44 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-31 20:29:44 [info] index finished after resolve  [object Object] 
2025-03-31 20:29:44 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:29:50 [info] ignore file modify evnet 学习库/stm32/串口.md   
2025-03-31 20:29:50 [info] trigger 学习库/stm32/串口.md resolve  [object Object] 
2025-03-31 20:29:50 [info] index finished after resolve  [object Object] 
2025-03-31 20:29:50 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:53:03 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 20:53:03 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:53:03 [info] index finished after resolve  [object Object] 
2025-03-31 20:53:03 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:53:50 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 20:53:50 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:53:50 [info] index finished after resolve  [object Object] 
2025-03-31 20:53:50 [info] refresh page data from resolve listeners 0 689   
2025-03-31 20:57:29 [info] indexing created file 学习库/Anki/stm32/未命名.md  [object Object] 
2025-03-31 20:57:29 [info] indexing created ignore file 学习库/Anki/stm32/未命名.md   
2025-03-31 20:57:29 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-31 20:57:29 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-31 20:57:29 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-31 20:57:29 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-31 20:57:29 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-31 20:57:30 [info] trigger 学习库/Anki/stm32/未命名.md resolve  [object Object] 
2025-03-31 20:57:30 [info] index finished after resolve  [object Object] 
2025-03-31 20:57:30 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:57:34 [info] refresh page data from rename listeners 0 690   
2025-03-31 20:57:34 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-03-31 20:57:34 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:57:34 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-03-31 20:57:34 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-03-31 20:57:34 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-03-31 20:57:34 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-03-31 20:57:37 [info] ignore file modify evnet 学习库/stm32/GPIO.md   
2025-03-31 20:57:37 [info] trigger 学习库/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:57:37 [info] index finished after resolve  [object Object] 
2025-03-31 20:57:37 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:58:46 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:58:46 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:58:46 [info] index finished after resolve  [object Object] 
2025-03-31 20:58:46 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:58:52 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:58:52 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:58:52 [info] index finished after resolve  [object Object] 
2025-03-31 20:58:52 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:58:55 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:58:55 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:58:55 [info] index finished after resolve  [object Object] 
2025-03-31 20:58:55 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:58:59 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:58:59 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:58:59 [info] index finished after resolve  [object Object] 
2025-03-31 20:58:59 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:02 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:02 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:02 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:02 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:05 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:05 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:05 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:05 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:36 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:36 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:36 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:36 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:39 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:39 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:39 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:39 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:41 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:41 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:41 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:41 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:44 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:44 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:44 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:44 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:46 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:46 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:46 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:46 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:49 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:49 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:49 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:49 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:51 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:51 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:51 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:51 [info] refresh page data from resolve listeners 0 690   
2025-03-31 20:59:53 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 20:59:53 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 20:59:53 [info] index finished after resolve  [object Object] 
2025-03-31 20:59:53 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:00:16 [info] refresh page data from rename listeners 0 690   
2025-03-31 21:00:23 [info] refresh page data from rename listeners 0 690   
2025-03-31 21:00:23 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:00:23 [info] index finished after resolve  [object Object] 
2025-03-31 21:00:23 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:00:25 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:00:25 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:00:25 [info] index finished after resolve  [object Object] 
2025-03-31 21:00:25 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:00:33 [info] refresh page data from rename listeners 0 690   
2025-03-31 21:00:33 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:00:33 [info] index finished after resolve  [object Object] 
2025-03-31 21:00:33 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:00:34 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:00:34 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:00:34 [info] index finished after resolve  [object Object] 
2025-03-31 21:00:34 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:08 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:08 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:08 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:08 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:25 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:25 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:25 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:25 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:27 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:27 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:27 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:27 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:29 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:29 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:29 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:29 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:31 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:31 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:31 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:31 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:34 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:34 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:34 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:34 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:38 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:38 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:38 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:38 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:40 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:40 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:40 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:40 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:43 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:43 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:43 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:49 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:49 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:49 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:49 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:52 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:52 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:52 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:52 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:01:54 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:01:54 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:01:54 [info] index finished after resolve  [object Object] 
2025-03-31 21:01:54 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:03 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:03 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:03 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:03 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:05 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:05 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:05 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:05 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:08 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:08 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:08 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:08 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:10 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:10 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:10 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:12 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:12 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:12 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:12 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:14 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:14 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:14 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:14 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:16 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:16 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:16 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:16 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:18 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:18 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:18 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:18 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:21 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:21 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:21 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:21 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:02:30 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:02:30 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:02:30 [info] index finished after resolve  [object Object] 
2025-03-31 21:02:30 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:03:04 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:03:04 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:03:04 [info] index finished after resolve  [object Object] 
2025-03-31 21:03:04 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:03:41 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:03:41 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:03:41 [info] index finished after resolve  [object Object] 
2025-03-31 21:03:41 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:03:47 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:03:47 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:03:47 [info] index finished after resolve  [object Object] 
2025-03-31 21:03:47 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:03:52 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:03:52 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:03:52 [info] index finished after resolve  [object Object] 
2025-03-31 21:03:52 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:03:56 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:03:56 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:03:56 [info] index finished after resolve  [object Object] 
2025-03-31 21:03:56 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:05 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:04:05 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:04:05 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:05 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:15 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:04:15 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:04:15 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:15 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:20 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:04:20 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:04:20 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:20 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:39 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:04:39 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:04:39 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:39 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:42 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:04:42 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:04:42 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:42 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:48 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:04:48 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:04:48 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:48 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:04:54 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:04:54 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:04:54 [info] index finished after resolve  [object Object] 
2025-03-31 21:04:54 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:00 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:05:00 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:05:00 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:00 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:05 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:05:05 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:05:05 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:05 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:16 [info] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-03-31 21:05:16 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-03-31 21:05:16 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:16 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:21 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:05:21 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:05:21 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:21 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:29 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:05:29 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:05:29 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:29 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:05:32 [info] ignore file modify evnet 学习库/Anki/stm32/GPIO.md   
2025-03-31 21:05:32 [info] trigger 学习库/Anki/stm32/GPIO.md resolve  [object Object] 
2025-03-31 21:05:32 [info] index finished after resolve  [object Object] 
2025-03-31 21:05:32 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:09 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:09 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:09 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:09 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:11 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:11 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:11 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:11 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:15 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:15 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:15 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:15 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:21 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:21 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:21 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:21 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:23 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:23 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:23 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:23 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:25 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:25 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:25 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:25 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:28 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:28 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:28 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:28 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:30 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:30 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:30 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:30 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:32 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:32 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:32 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:32 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:35 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:37 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:37 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:37 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:37 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:40 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:40 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:40 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:40 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:43 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:43 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:43 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:45 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:45 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:45 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:45 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:49 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:49 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:49 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:49 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:13:59 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:13:59 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:13:59 [info] index finished after resolve  [object Object] 
2025-03-31 21:13:59 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:14:03 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:14:03 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:14:03 [info] index finished after resolve  [object Object] 
2025-03-31 21:14:03 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:14:05 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:14:05 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:14:05 [info] index finished after resolve  [object Object] 
2025-03-31 21:14:05 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:14:08 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:14:08 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:14:08 [info] index finished after resolve  [object Object] 
2025-03-31 21:14:08 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:14:10 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:14:10 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:14:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:14:10 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:14:13 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:14:13 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:14:13 [info] index finished after resolve  [object Object] 
2025-03-31 21:14:13 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:15:26 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:26 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:26 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:26 [info] refresh page data from resolve listeners 0 690   
2025-03-31 21:15:26 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-15-26.png  [object Object] 
2025-03-31 21:15:26 [info] refresh page data from created listeners 0 691   
2025-03-31 21:15:28 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:28 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:28 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:28 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:31 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:31 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:31 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:31 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:33 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:34 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:34 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:34 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:36 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:36 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:36 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:36 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:38 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:38 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:38 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:38 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:47 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:47 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:47 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:47 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:51 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:51 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:51 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:51 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:53 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:53 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:53 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:53 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:56 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:56 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:56 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:56 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:15:58 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:15:58 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:15:58 [info] index finished after resolve  [object Object] 
2025-03-31 21:15:58 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:00 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:00 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:00 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:00 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:02 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:02 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:02 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:02 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:04 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:04 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:04 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:04 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:06 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:06 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:06 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:06 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:08 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:08 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:08 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:08 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:13 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:13 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:13 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:13 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:15 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:15 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:15 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:15 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:17 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:17 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:17 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:17 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:19 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:19 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:19 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:19 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:22 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:22 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:22 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:22 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:24 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:24 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:24 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:24 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:26 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:26 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:26 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:26 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:28 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:28 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:28 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:28 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:31 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:31 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:31 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:31 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:33 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:33 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:33 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:33 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:35 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:37 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:37 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:37 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:37 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:39 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:39 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:39 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:39 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:42 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:42 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:42 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:42 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:44 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:44 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:44 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:44 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:46 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:46 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:46 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:46 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:48 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:48 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:48 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:48 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:51 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:51 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:51 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:51 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:53 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:53 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:53 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:53 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:56 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:56 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:56 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:56 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:16:58 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:16:58 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:16:58 [info] index finished after resolve  [object Object] 
2025-03-31 21:16:58 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:00 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:00 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:00 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:00 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:11 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:11 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:11 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:11 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:14 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:14 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:14 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:14 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:16 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:16 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:16 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:16 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:20 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:20 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:20 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:20 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:17:26 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:17:26 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:17:26 [info] index finished after resolve  [object Object] 
2025-03-31 21:17:26 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:21:58 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:21:58 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:21:58 [info] index finished after resolve  [object Object] 
2025-03-31 21:21:58 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:03 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:03 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:03 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:03 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:05 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:05 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:05 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:05 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:08 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:08 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:08 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:08 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:10 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:10 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:10 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:13 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:13 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:13 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:13 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:15 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:15 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:15 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:15 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:17 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:17 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:17 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:17 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:20 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:20 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:20 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:20 [info] refresh page data from resolve listeners 0 691   
2025-03-31 21:22:21 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-22-21.png  [object Object] 
2025-03-31 21:22:21 [info] refresh page data from created listeners 0 692   
2025-03-31 21:22:23 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:23 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:23 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:23 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:26 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:26 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:26 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:26 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:28 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:28 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:28 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:28 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:31 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:31 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:31 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:31 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:33 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:33 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:33 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:33 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:35 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:42 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:42 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:42 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:42 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:47 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:47 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:47 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:47 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:49 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:49 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:49 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:49 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:51 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:51 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:51 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:51 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:54 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:54 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:54 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:54 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:56 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:56 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:56 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:56 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:22:58 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:22:58 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:22:58 [info] index finished after resolve  [object Object] 
2025-03-31 21:22:58 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:00 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:00 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:00 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:00 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:02 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:02 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:02 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:02 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:04 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:04 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:04 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:04 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:06 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:06 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:06 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:06 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:09 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:09 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:09 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:09 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:12 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:12 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:12 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:12 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:18 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:18 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:18 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:18 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:33 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:33 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:33 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:33 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:36 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:36 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:36 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:36 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:39 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:39 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:39 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:39 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:41 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:41 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:41 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:41 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:44 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:44 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:44 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:44 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:47 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:47 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:47 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:47 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:49 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:49 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:49 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:49 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:52 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:52 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:52 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:52 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:23:54 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:23:54 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:23:54 [info] index finished after resolve  [object Object] 
2025-03-31 21:23:54 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:24:36 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:24:36 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:24:36 [info] index finished after resolve  [object Object] 
2025-03-31 21:24:36 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:24:38 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:24:38 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:24:38 [info] index finished after resolve  [object Object] 
2025-03-31 21:24:38 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:24:41 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:24:41 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:24:41 [info] index finished after resolve  [object Object] 
2025-03-31 21:24:41 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:24:44 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:24:44 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:24:44 [info] index finished after resolve  [object Object] 
2025-03-31 21:24:44 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:24:46 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:24:46 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:24:46 [info] index finished after resolve  [object Object] 
2025-03-31 21:24:46 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:10 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:10 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:10 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:12 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:12 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:12 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:12 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:19 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:19 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:19 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:19 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:26 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:26 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:26 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:26 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:29 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:29 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:29 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:29 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:31 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:31 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:31 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:31 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:33 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:33 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:33 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:33 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:35 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:37 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:37 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:37 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:37 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:39 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:39 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:39 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:39 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:43 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:43 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:43 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:45 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:45 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:45 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:45 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:48 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:48 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:48 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:48 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:52 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:52 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:52 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:52 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:25:55 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:25:55 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:25:55 [info] index finished after resolve  [object Object] 
2025-03-31 21:25:55 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:26:04 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:26:04 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:26:04 [info] index finished after resolve  [object Object] 
2025-03-31 21:26:04 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:28:28 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:28:28 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:28:28 [info] index finished after resolve  [object Object] 
2025-03-31 21:28:28 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:28:30 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:28:30 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:28:30 [info] index finished after resolve  [object Object] 
2025-03-31 21:28:30 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:28:32 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:28:32 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:28:32 [info] index finished after resolve  [object Object] 
2025-03-31 21:28:32 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:28:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:28:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:28:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:28:35 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:28:37 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:28:37 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:28:37 [info] index finished after resolve  [object Object] 
2025-03-31 21:28:37 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:10 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:10 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:10 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:12 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:12 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:12 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:12 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:14 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:14 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:14 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:14 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:16 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:16 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:16 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:16 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:18 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:18 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:18 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:18 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:20 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:20 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:20 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:20 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:23 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:23 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:23 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:23 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:30 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:30 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:30 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:30 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:32 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:32 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:32 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:32 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:30:35 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:30:35 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:30:35 [info] index finished after resolve  [object Object] 
2025-03-31 21:30:35 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:31:43 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:31:43 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:31:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:31:43 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:31:50 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:31:50 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:31:50 [info] index finished after resolve  [object Object] 
2025-03-31 21:31:50 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:31:55 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:31:55 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:31:55 [info] index finished after resolve  [object Object] 
2025-03-31 21:31:55 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:32:00 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:32:00 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:32:00 [info] index finished after resolve  [object Object] 
2025-03-31 21:32:00 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:32:27 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:32:27 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:32:27 [info] index finished after resolve  [object Object] 
2025-03-31 21:32:27 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:32:36 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:32:36 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:32:36 [info] index finished after resolve  [object Object] 
2025-03-31 21:32:36 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:32:43 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:32:43 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:32:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:32:43 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:33:06 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:33:06 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:33:06 [info] index finished after resolve  [object Object] 
2025-03-31 21:33:06 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:33:15 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:33:15 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:33:15 [info] index finished after resolve  [object Object] 
2025-03-31 21:33:15 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:33:39 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:33:39 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:33:39 [info] index finished after resolve  [object Object] 
2025-03-31 21:33:39 [info] refresh page data from resolve listeners 0 692   
2025-03-31 21:34:09 [info] indexing created file 学习库/stm32/attachments/3 串口-2025-03-31-21-34-09.png  [object Object] 
2025-03-31 21:34:09 [info] refresh page data from created listeners 0 693   
2025-03-31 21:34:10 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:34:10 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:34:10 [info] index finished after resolve  [object Object] 
2025-03-31 21:34:10 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:36:32 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:36:32 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:36:32 [info] index finished after resolve  [object Object] 
2025-03-31 21:36:32 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:36:38 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:36:38 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:36:38 [info] index finished after resolve  [object Object] 
2025-03-31 21:36:38 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:36:40 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:36:41 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:36:41 [info] index finished after resolve  [object Object] 
2025-03-31 21:36:41 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:36:43 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:36:43 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:36:43 [info] index finished after resolve  [object Object] 
2025-03-31 21:36:43 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:36:45 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:36:46 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:36:46 [info] index finished after resolve  [object Object] 
2025-03-31 21:36:46 [info] refresh page data from resolve listeners 0 693   
2025-03-31 21:54:36 [info] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-03-31 21:54:36 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-03-31 21:54:36 [info] index finished after resolve  [object Object] 
2025-03-31 21:54:36 [info] refresh page data from resolve listeners 0 693   
